const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const HumanBehaviorSimulator = require('./src/utils/HumanBehaviorSimulator');
const https = require('https');
const http = require('http');
const { URL } = require('url');
const ProxyPool = require('./ProxyPool');
const TokenManager = require('./TokenManager');
const StateManager = require('./StateManager');

// 尝试加载配置文件
let defaultConfig = {};
try {
    defaultConfig = require('./config');
    console.log('✅ 已加载配置文件: config.js');
} catch (error) {
    console.log('⚠️ 未找到配置文件 config.js，将使用默认配置');
}

class PlaywrightCrawler {
    constructor(config = {}) {
        // 合并配置
        const mergedConfig = { ...defaultConfig, ...config };

        this.config = {
            baseURL: 'https://zj.stzy.com/create-paper/chapter',
            apiURL: 'https://qms.stzy.com/matrix/zw-search/api/v1/homeEs/question/textbookQuery',
            tokensFile: mergedConfig.tokensFile || './tokens.json',
            progressFile: mergedConfig.progressFile || './progress.json',
            paramsFile: mergedConfig.paramsFile || './params.json',
            headless: mergedConfig.headless !== false,
            browserTimeout: mergedConfig.browserTimeout || 100000,
            pageTimeout: mergedConfig.pageTimeout || 100000,
            proxy: mergedConfig.proxy || null,
            proxyUrl: mergedConfig.proxyUrl || null,
            minDelay: mergedConfig.minDelay || 1000,
            maxDelay: mergedConfig.maxDelay || 3000,
            startPage: mergedConfig.startPage || 1,
            maxPages: mergedConfig.maxPages || 1000,
            instanceId: mergedConfig.instanceId || `crawler_${process.pid}_${Math.random().toString(36).substr(2, 5)}`,
            // 缓存策略配置
            enableCache: mergedConfig.enableCache !== false,
            cacheMaxSize: mergedConfig.cacheMaxSize || 100, // MB
            // 防ban策略配置
            enableAntiBot: mergedConfig.enableAntiBot !== false,
            requestInterval: mergedConfig.requestInterval || 3000, // 请求间隔基础时间
            behaviorSimulation: mergedConfig.behaviorSimulation !== false,
            scrollBehavior: mergedConfig.scrollBehavior !== false,
            mouseBehavior: mergedConfig.mouseBehavior !== false,
        };

        // 浏览器相关状态
        this.browser = null;
        this.context = null;
        this.page = null;
        this.currentProxy = null;
        this.currentTokenData = null;
        this.proxyHealthTimer = null;
        this.tokenExpired = false;

        // 当前任务状态
        this.currentCombination = null;
        this.currentPage = this.config.startPage;
        this.outputDir = null;

        // 初始化人类行为模拟器
        this.humanBehavior = new HumanBehaviorSimulator({
            logger: console,
            enableAntiDetection: this.config.enableAntiBot !== false,
            minDelay: 100,
            maxDelay: 500,
            randomMoveProbability: 0.3,
            scrollProbability: 0.2,
            hoverProbability: 0.4
        });
        this.isSettingsConfigured = false;

        // 进程退出标志
        this.isExiting = false;

        // 防ban策略相关状态
        this.lastRequestTime = 0;
        this.requestCount = 0;
        this.requestHistory = []; // 记录最近的请求时间
        this.userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/120.0'
        ];

        // 初始化管理器
        this.tokenManager = new TokenManager({
            tokensFile: this.config.tokensFile,
            instanceId: this.config.instanceId
        });

        this.stateManager = new StateManager({
            progressFile: this.config.progressFile,
            paramsFile: this.config.paramsFile,
            instanceId: this.config.instanceId
        });

        // 创建截图保存目录
        this.screenshotDir = './screenshots';
        this.ensureScreenshotDir();

        this.initProxyPool();
        this.setupProcessExitHandlers();
    }

    // 设置进程退出处理器
    setupProcessExitHandlers() {
        const handleExit = (signal) => {
            if (this.isExiting) return; // 防止重复执行
            this.isExiting = true;
            
            console.log(`\n🚨 [${this.config.instanceId}] 收到退出信号 ${signal}，强制释放Token...`);
            this.forceReleaseToken();
            
            // 强制退出
            setTimeout(() => {
                console.log(`⚡ [${this.config.instanceId}] 强制退出`);
                process.exit(0);
            }, 1000); // 给1秒时间清理
        };

        // 监听各种退出信号
        process.on('SIGINT', () => handleExit('SIGINT'));
        process.on('SIGTERM', () => handleExit('SIGTERM'));
        process.on('SIGBREAK', () => handleExit('SIGBREAK'));
        process.on('SIGHUP', () => handleExit('SIGHUP'));
        
        // 监听进程意外退出
        process.on('exit', () => {
            if (!this.isExiting) {
                console.log(`🚨 [${this.config.instanceId}] 进程意外退出，尝试清理Token...`);
                this.forceReleaseToken();
            }
        });

        // 监听未捕获的异常
        process.on('uncaughtException', (error) => {
            console.error(`🚨 [${this.config.instanceId}] 未捕获异常:`, error.message);
            this.forceReleaseToken();
            process.exit(1);
        });

        process.on('unhandledRejection', (reason, promise) => {
            console.error(`🚨 [${this.config.instanceId}] 未处理的Promise拒绝:`, reason);
            this.forceReleaseToken();
            process.exit(1);
        });
    }

    // 强制释放Token（同步方式）
    forceReleaseToken() {
        if (!this.tokenManager) {
            return;
        }

        try {
            console.log(`⚡ [${this.config.instanceId}] 强制释放Token...`);
            
            // 使用TokenManager的快速释放方法
            const released = this.tokenManager.forceReleaseCurrentToken();
            
            if (released) {
                console.log(`✅ [${this.config.instanceId}] Token已强制释放`);
            } else {
                console.log(`⚠️ [${this.config.instanceId}] 没有Token需要释放`);
            }
            
        } catch (error) {
            console.error(`❌ [${this.config.instanceId}] 强制释放Token失败: ${error.message}`);
        }
    }

    initProxyPool() {
        // 检查是否配置了静态代理或动态代理
        if (this.config.proxy || this.config.proxyUrl) {
            this.proxyPool = new ProxyPool({
                staticProxy: this.config.proxy, // 传递静态代理配置
                proxyUrl: this.config.proxyUrl, // 动态代理获取URL
                proxyValidityDuration: 8,
                cleanupInterval: 60000,
                stateFile: './proxy_pool_state.json',
                autoSave: true,
                saveInterval: 30000
            });
            console.log('🏊 代理池已启用');
            if (this.config.proxy) {
                console.log('🔧 静态代理已配置');
            }
            if (this.config.proxyUrl) {
                console.log('🌐 动态代理API已配置');
            }
        } else {
            this.proxyPool = null;
            console.log('⚠️ 未配置代理，代理功能未启用');
        }
    }

    // 获取随机的视口大小
    getRandomViewport() {
        const viewports = [
            { width: 1920, height: 1080 },
            { width: 1366, height: 768 },
            { width: 1536, height: 864 },
            { width: 1440, height: 900 },
            { width: 1600, height: 900 },
            { width: 1280, height: 720 },
            { width: 1024, height: 768 }
        ];
        return viewports[Math.floor(Math.random() * viewports.length)];
    }

    // 智能请求间隔控制
    async intelligentDelay() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        
        // 计算基础延迟时间
        let baseDelay = this.config.requestInterval;
        
        // 根据请求频率动态调整延迟
        if (this.requestCount > 10) {
            // 每10个请求增加延迟时间
            const extraDelay = Math.floor(this.requestCount / 10) * 1000;
            baseDelay += extraDelay;
        }
        
        // 添加随机延迟（±30%）
        const randomFactor = 0.7 + Math.random() * 0.6; // 0.7 到 1.3
        const finalDelay = Math.floor(baseDelay * randomFactor);
        
        // 确保最小间隔
        const requiredDelay = Math.max(0, finalDelay - timeSinceLastRequest);
        
        if (requiredDelay > 500) {
            // 只在延迟较长时输出日志
            console.log(`⏱️ [${this.config.instanceId}] 延迟 ${requiredDelay}ms`);
            await new Promise(resolve => setTimeout(resolve, requiredDelay));
        } else if (requiredDelay > 0) {
            await new Promise(resolve => setTimeout(resolve, requiredDelay));
        }
        
        this.lastRequestTime = Date.now();
        this.requestCount++;
        
        // 记录请求历史（只保留最近10分钟的记录）
        this.requestHistory.push(this.lastRequestTime);
        const tenMinutesAgo = this.lastRequestTime - 10 * 60 * 1000;
        this.requestHistory = this.requestHistory.filter(time => time > tenMinutesAgo);
        
        // 每100个请求输出一次频率统计
        if (this.requestCount % 100 === 0) {
            this.logRequestFrequency();
        }
    }

    // 记录请求频率统计
    logRequestFrequency() {
        try {
            const now = Date.now();
            const oneMinuteAgo = now - 60 * 1000;
            const fiveMinutesAgo = now - 5 * 60 * 1000;
            
            const requestsInLastMinute = this.requestHistory.filter(time => time > oneMinuteAgo).length;
            const requestsInLast5Minutes = this.requestHistory.filter(time => time > fiveMinutesAgo).length;
            
            console.log(`📊 [${this.config.instanceId}] 请求频率统计:`);
            console.log(`   总请求数: ${this.requestCount}`);
            console.log(`   最近1分钟: ${requestsInLastMinute} 个请求`);
            console.log(`   最近5分钟: ${requestsInLast5Minutes} 个请求 (平均 ${(requestsInLast5Minutes / 5).toFixed(1)}/分钟)`);
            console.log(`   最近10分钟: ${this.requestHistory.length} 个请求 (平均 ${(this.requestHistory.length / 10).toFixed(1)}/分钟)`);
        } catch (error) {
            console.warn(`⚠️ [${this.config.instanceId}] 记录请求频率时出错: ${error.message}`);
        }
    }

    // 模拟人类鼠标行为
    async simulateMouseBehavior(page) {
        if (!this.config.mouseBehavior || !page) return;
        
        try {
            // 随机鼠标移动
            const viewport = await page.viewportSize();
            if (viewport) {
                const x = Math.random() * viewport.width;
                const y = Math.random() * viewport.height;
                await page.mouse.move(x, y, { steps: Math.floor(Math.random() * 10) + 5 });
                
                // 偶尔进行点击（在安全区域）
                if (Math.random() < 0.1) {
                    await page.mouse.click(x, y);
                    await page.waitForTimeout(100 + Math.random() * 200);
                }
            }
        } catch (error) {
            // 忽略鼠标行为错误
        }
    }

    // 模拟滚动行为
    async simulateScrollBehavior(page) {
        if (!this.config.scrollBehavior || !page) return;
        
        try {
            // 随机滚动
            const scrollDirection = Math.random() > 0.5 ? 1 : -1;
            const scrollAmount = Math.floor(Math.random() * 300) + 100;
            
            await page.mouse.wheel(0, scrollDirection * scrollAmount);
            await page.waitForTimeout(200 + Math.random() * 300);
            
            // 偶尔滚动回去
            if (Math.random() < 0.3) {
                await page.mouse.wheel(0, -scrollDirection * scrollAmount * 0.5);
                await page.waitForTimeout(100 + Math.random() * 200);
            }
        } catch (error) {
            // 忽略滚动行为错误
        }
    }

    // 设置真实的浏览器指纹
    async setupBrowserFingerprint(page) {
        if (!this.config.enableAntiBot || !page) return;
        
        try {
            // 注入反检测脚本
            await page.addInitScript(() => {
                // 隐藏webdriver标识
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                
                // 模拟更真实的插件列表
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        {
                            0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
                            description: "Portable Document Format",
                            filename: "internal-pdf-viewer",
                            length: 1,
                            name: "Chrome PDF Plugin"
                        },
                        {
                            0: {type: "application/pdf", suffixes: "pdf", description: "Portable Document Format"},
                            description: "Portable Document Format", 
                            filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                            length: 1,
                            name: "Chrome PDF Viewer"
                        }
                    ],
                });
                
                // 设置语言
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en'],
                });
                
                // 模拟内存信息
                Object.defineProperty(navigator, 'deviceMemory', {
                    get: () => 8,
                });
                
                // 模拟硬件并发
                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: () => 4,
                });
            });
            
            console.log(`🎭 [${this.config.instanceId}] 浏览器指纹已设置`);
        } catch (error) {
            console.warn(`⚠️ [${this.config.instanceId}] 设置浏览器指纹失败: ${error.message}`);
        }
    }



    async assignProxyToToken(tokenData) {
        if (this.proxyPool) {
            try {
                const proxyResult = await this.proxyPool.assignProxyToToken(tokenData.id);
                if (proxyResult) {
                    const proxyType = proxyResult.isStatic ? '静态' : '动态';
                    console.log(`🔗 为Token[${tokenData.id}]分配${proxyType}代理: ${proxyResult.proxyUrl}`);
                    
                    // 解析代理URL为Playwright格式
                    const parsedProxy = this.parseProxyUrl(proxyResult.proxyUrl);
                    if (parsedProxy) {
                        // 添加代理类型信息
                        parsedProxy.isStatic = proxyResult.isStatic;
                        parsedProxy.proxyId = proxyResult.proxyId;
                        parsedProxy.proxyString = proxyResult.proxyString;
                        
                        // 在Node.js端校验代理
                        console.log(`🔍 验证代理有效性...`);
                        const isValid = await this.testProxyWithNodeJS(parsedProxy);
                        if (!isValid) {
                            console.log(`❌ 代理验证失败，尝试获取新代理...`);
                            // 释放无效代理
                            this.proxyPool.releaseProxyFromToken(tokenData.id);
                            // 递归尝试获取新代理
                            return await this.assignProxyToToken(tokenData);
                        }
                        console.log(`✅ 代理验证通过`);
                    }
                    return parsedProxy;
                } else {
                    console.log(`⚠️ 无法为Token[${tokenData.id}]获取代理，将使用无代理模式`);
                    return null;
                }
            } catch (error) {
                console.error(`❌ 为Token[${tokenData.id}]分配代理失败: ${error.message}`);
                return null;
            }
        }

        // 如果没有代理池但配置了静态代理，直接使用静态代理（兼容性处理）
        if (this.config.proxy) {
            const proxyUrl = `http://${this.config.proxy.host}:${this.config.proxy.port}`;
            console.log(`🔗 为Token[${tokenData.id}]使用静态代理: ${proxyUrl}`);
            
            // 校验静态代理
            console.log(`🔍 验证静态代理有效性...`);
            const isValid = await this.testProxyWithNodeJS(this.config.proxy);
            if (!isValid) {
                console.log(`❌ 静态代理验证失败`);
                return null;
            }
            console.log(`✅ 静态代理验证通过`);
            return this.config.proxy;
        }

        return null;
    }

    parseProxyUrl(proxyUrl) {
        try {
            const cleanProxy = proxyUrl.replace(/^https?:\/\//, '');
            if (cleanProxy.includes('@')) {
                const [auth, server] = cleanProxy.split('@');
                const [username, password] = auth.split(':');
                return {
                    server: `http://${server}`,
                    username: username,
                    password: password
                };
            } else {
                return {
                    server: `http://${cleanProxy}`
                };
            }
        } catch (error) {
            console.error('❌ 解析代理URL失败:', error.message);
            return null;
        }
    }

    // 检查当前页面是否为登录页面（token失效的标志）
    isLoginPage(url) {
        return url.includes('login') || 
               url.includes('auth') || 
               /https:\/\/www\.stzy\.com\/login\//.test(url);
    }

    // 检查页面是否包含必要的爬取元素
    async checkPageElements(page) {
        try {
            console.log('🔍 检查页面关键元素...');
            
            // 检查是否被重定向到登录页面
            const currentUrl = page.url();
            if (this.isLoginPage(currentUrl)) {
                console.log(`🚫 页面已跳转到登录页面: ${currentUrl}`);
                return { valid: false, reason: '页面跳转到登录页面', isLoginRedirect: true };
            }

            // 检查关键元素是否存在
            const elementsToCheck = [
                { selector: '#textbook_tree', name: '教材树容器' },
                { selector: '.ant-dropdown-trigger', name: '下拉菜单触发器' },
                { selector: 'ul.ant-pagination', name: '分页组件' },
                { selector: 'input.pagination_jump_page_input', name: '页面跳转输入框' }
            ];

            const missingElements = [];
            const existingElements = [];

            for (const element of elementsToCheck) {
                try {
                    const count = await page.locator(element.selector).count();
                    if (count > 0) {
                        existingElements.push(element.name);
                    } else {
                        missingElements.push(element.name);
                    }
                } catch (error) {
                    console.warn(`⚠️ 检查元素 ${element.name} 时出错: ${error.message}`);
                    missingElements.push(element.name);
                }
            }

            console.log(`✅ 找到的元素: ${existingElements.join(', ')}`);
            if (missingElements.length > 0) {
                console.log(`❌ 缺失的元素: ${missingElements.join(', ')}`);
            }

            // 如果关键元素都缺失，认为可能是网络问题或token失效
            if (missingElements.length >= 3) {
                return { 
                    valid: false, 
                    reason: `缺失关键元素: ${missingElements.join(', ')}`,
                    missingElements: missingElements,
                    existingElements: existingElements,
                    possibleNetworkIssue: true // 标记可能是网络问题
                };
            }

            // 额外检查：页面是否显示错误信息
            const errorSelectors = [
                '.ant-result-error',
                '.error-page', 
                '.login-form',
                '.ant-empty',
                '[class*="error"]',
                '[class*="forbidden"]'
            ];

            for (const selector of errorSelectors) {
                try {
                    const count = await page.locator(selector).count();
                    if (count > 0) {
                        console.log(`🚫 检测到错误元素: ${selector}`);
                        return { 
                            valid: false, 
                            reason: `页面显示错误元素: ${selector}`,
                            errorElement: selector,
                            isTokenError: true // 标记是token错误
                        };
                    }
                } catch (error) {
                    // 忽略检查错误
                }
            }

            return { valid: true, reason: '页面元素检查通过' };

        } catch (error) {
            console.error(`❌ 检查页面元素时出错: ${error.message}`);
            return { 
                valid: false, 
                reason: `页面元素检查异常: ${error.message}`,
                error: error.message,
                possibleNetworkIssue: true // 异常情况也可能是网络问题
            };
        }
    }

    // 检查页面是否显示"没有查询到您想要的内容"（表示当前参数组合已完成）
    async checkNoContentFound(page) {
        try {
            console.log('🔍 检查是否显示"没有查询到您想要的内容"...');
            
            // 等待网络请求全部响应完成，确保页面内容已完全加载
            console.log('⏳ 等待网络请求完成...');
            await page.waitForLoadState('networkidle', { timeout: 30000 });
            
            // 额外等待一段时间确保页面内容已渲染完成
            await page.waitForTimeout(2000);
            
            // 查找 class="w120 h120" 的 img 元素
            const imgElements = await page.locator('img.w120.h120').all();
            
            if (imgElements.length === 0) {
                return { noContentFound: false, reason: '未找到指定的img元素' };
            }
            
            console.log(`🖼️ 找到 ${imgElements.length} 个 w120 h120 的img元素`);
            
            // 检查每个img元素的兄弟元素
            for (let i = 0; i < imgElements.length; i++) {
                const img = imgElements[i];
                
                try {
                    // 获取img元素的父元素
                    const parent = img.locator('..');
                    
                    // 在父元素中查找p元素（兄弟元素）
                    const siblingPs = await parent.locator('p').all();
                    
                    for (const p of siblingPs) {
                        const textContent = await p.textContent();
                        if (textContent && textContent.includes('没有查询到您想要的内容')) {
                            // 检查元素是否真正显示在页面上
                            const isVisible = await p.isVisible();
                            
                            console.log(`🎯 检测到"没有查询到您想要的内容"提示`);
                            console.log(`   可见性: ${isVisible}`);
                            
                            if (isVisible) {
                                console.log(`✅ 确认提示已显示在页面上`);
                                return { 
                                    noContentFound: true, 
                                    reason: '页面显示没有查询到内容',
                                    message: textContent.trim(),
                                    isVisible: isVisible
                                };
                            } else {
                                console.log(`⚠️ 提示存在但未显示，继续检查其他元素`);
                            }
                        }
                    }
                } catch (error) {
                    console.warn(`⚠️ 检查第 ${i + 1} 个img元素的兄弟元素时出错: ${error.message}`);
                }
            }
            
            return { noContentFound: false, reason: '未找到"没有查询到您想要的内容"提示' };
            
        } catch (error) {
            console.warn(`⚠️ 检查"没有查询到您想要的内容"时出错: ${error.message}`);
            return { noContentFound: false, reason: `检查异常: ${error.message}` };
        }
    }

    // 带重试的页面元素检查方法
    async checkPageElementsWithRetry(page, pageContext, maxRetries = 3) {
        let lastResult = null;
        
        console.log(`🔍 开始检查页面元素 (上下文: ${pageContext})`);
        
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            console.log(`📍 第 ${attempt}/${maxRetries} 次元素检查...`);
            
            const result = await this.checkPageElements(page);
            lastResult = result;
            
            if (result.valid) {
                if (attempt > 1) {
                    console.log(`✅ 第 ${attempt} 次检查成功，页面元素正常`);
                } else {
                    console.log(`✅ 页面元素检查通过`);
                }
                return result;
            }
            
            console.log(`❌ 第 ${attempt} 次检查失败: ${result.reason}`);
            
            // 详细分析失败原因
            if (result.isLoginRedirect) {
                console.log(`🚫 检测到登录页面跳转，立即标记需要切换token`);
                await this.saveDebugScreenshot(page, 'login_redirect_detected', pageContext);
                return { ...result, needSwitchToken: true };
            }
            
            if (result.isTokenError) {
                console.log(`🚫 检测到明确的token错误，立即标记需要切换token`);
                await this.saveDebugScreenshot(page, 'token_error_detected', pageContext);
                return { ...result, needSwitchToken: true };
            }
            
            // 记录元素状态详情
            if (result.missingElements && result.missingElements.length > 0) {
                console.log(`🔍 缺失的元素: ${result.missingElements.join(', ')}`);
            }
            if (result.existingElements && result.existingElements.length > 0) {
                console.log(`✅ 存在的元素: ${result.existingElements.join(', ')}`);
            }
            
            // 如果可能是网络问题，尝试不同的恢复策略
            if (result.possibleNetworkIssue && attempt < maxRetries) {
                console.log(`🔄 疑似网络问题，尝试恢复策略...`);
                
                // 先检查加载状态
                const loadingCheck = await this.checkLoadingState(page, 3000);
                if (loadingCheck.needAction) {
                    console.log(`⚠️ 页面仍在加载中，可能是加载延迟`);
                    await this.saveDebugScreenshot(page, 'loading_during_element_check', `${pageContext}_attempt_${attempt}`);
                    
                    const timeoutResult = await this.handleLoadingTimeout(page, `元素检查-${pageContext}`);
                    if (timeoutResult.needSwitchToken) {
                        console.log(`🚫 加载超时处理建议切换token`);
                        return { ...result, needSwitchToken: true };
                    }
                    // 如果处理成功，继续重试
                } else {
                    // 没有加载问题，尝试刷新页面
                    try {
                        console.log(`🔄 尝试刷新页面以恢复元素...`);
                        await page.reload({ 
                            waitUntil: 'networkidle',
                            timeout: this.config.pageTimeout 
                        });
                        
                        await page.waitForTimeout(2000);
                        console.log(`🔄 页面刷新完成`);
                        
                    } catch (refreshError) {
                        console.warn(`⚠️ 页面刷新失败: ${refreshError.message}`);
                        await this.saveDebugScreenshot(page, 'page_refresh_failed', `${pageContext}_attempt_${attempt}`);
                        
                        if (attempt === maxRetries) {
                            console.log(`🚫 最后一次重试且刷新失败，可能是token问题`);
                            return { ...result, needSwitchToken: true };
                        }
                    }
                }
            } else if (attempt < maxRetries) {
                console.log(`⏳ 等待 3 秒后重试...`);
                await page.waitForTimeout(3000);
            }
        }
        
        // 所有重试都失败了
        console.log(`❌ 经过 ${maxRetries} 次重试，页面元素检查仍然失败`);
        await this.saveDebugScreenshot(page, 'element_check_all_failed', pageContext);
        
        // 最终分析失败原因
        if (lastResult && lastResult.possibleNetworkIssue) {
            console.log(`🚫 多次重试网络问题仍未解决，可能是token问题`);
            
            // 最后一次token状态检查
            const finalTokenCheck = await this.checkTokenStatus(page);
            if (finalTokenCheck.isTokenBanned) {
                console.log(`🚫 最终确认token被封禁: ${finalTokenCheck.reason}`);
                return { ...lastResult, needSwitchToken: true, reason: `多次重试失败 + token封禁: ${finalTokenCheck.reason}` };
            }
        }
        
        return lastResult || { 
            valid: false, 
            reason: `多次重试后页面元素检查失败 (${pageContext})`, 
            needSwitchToken: true 
        };
    }



    // 将目录名编码为文件系统安全的名称（可逆）
    encodeDirName(name) {
        if (!name) return 'unknown';
        
        // 定义需要编码的字符映射
        const charMap = {
            '<': '_LT_',
            '>': '_GT_',
            ':': '_COL_',
            '"': '_QUO_',
            '/': '_SLA_',
            '\\': '_BSL_',
            '|': '_PIP_',
            '?': '_QST_',
            '*': '_AST_',
            '\n': '_LF_',      // 换行符
            '\r': '_CR_',      // 回车符
            '\t': '_TAB_',     // 制表符
            '\0': '_NULL_',    // 空字符
            '\b': '_BS_',      // 退格符
            '\f': '_FF_',      // 换页符
            '\v': '_VT_',      // 垂直制表符
        };
        
        let encoded = name.trim();
        
        // 替换特殊字符
        for (const [char, replacement] of Object.entries(charMap)) {
            encoded = encoded.replace(new RegExp('\\' + char, 'g'), replacement);
        }
        
        // 确保目录名不为空且不以点开头
        if (encoded === '' || encoded.startsWith('.')) {
            encoded = '_' + encoded;
        }
        
        return encoded;
    }
    
    // 将编码的目录名解码为原始名称（可逆）
    decodeDirName(encodedName) {
        if (!encodedName || encodedName === 'unknown') return encodedName;
        
        // 定义解码映射（与编码映射相反）
        const charMap = {
            '_LT_': '<',
            '_GT_': '>',
            '_COL_': ':',
            '_QUO_': '"',
            '_SLA_': '/',
            '_BSL_': '\\',
            '_PIP_': '|',
            '_QST_': '?',
            '_AST_': '*',
            '_LF_': '\n',       // 换行符
            '_CR_': '\r',       // 回车符
            '_TAB_': '\t',      // 制表符
            '_NULL_': '\0',     // 空字符
            '_BS_': '\b',       // 退格符
            '_FF_': '\f',       // 换页符
            '_VT_': '\v',       // 垂直制表符
            '_SPC_': ' ',
            '_DOT_': '.'
        };
        
        let decoded = encodedName;
        
        // 还原特殊字符
        for (const [replacement, char] of Object.entries(charMap)) {
            decoded = decoded.replace(new RegExp(replacement, 'g'), char);
        }
        
        // 移除开头添加的下划线（如果存在）
        if (decoded.startsWith('_') && encodedName.startsWith('_')) {
            // 只有当原始编码名以_开头时才移除
            decoded = decoded.substring(1);
        }
        
        return decoded.trim();
    }
    
    // 为了向后兼容，保留旧方法名作为别名
    sanitizeDirName(name) {
        return this.encodeDirName(name);
    }
    
    // 生成目录路径并同时返回原始路径信息（用于验证和调试）
    generateOutputPath(combination) {
        const pathComponents = [
            { original: combination.studyPhaseName, encoded: this.encodeDirName(combination.studyPhaseName) },
            { original: combination.subjectName, encoded: this.encodeDirName(combination.subjectName) },
            { original: combination.textbookVersionName, encoded: this.encodeDirName(combination.textbookVersionName) },
            { original: combination.ceciName, encoded: this.encodeDirName(combination.ceciName) },
            { original: combination.catalogName, encoded: this.encodeDirName(combination.catalogName) }
        ];
        
        const encodedPath = path.join('./', ...pathComponents.map(c => c.encoded));
        
        return {
            encodedPath: encodedPath,
            pathComponents: pathComponents,
            originalPath: pathComponents.map(c => c.original).join('/') // 用于显示和调试
        };
    }
    
    // 验证目录路径是否与给定的参数组合匹配
    validateDirectoryPath(dirPath, combination) {
        try {
            const pathInfo = this.generateOutputPath(combination);
            const normalizedDirPath = path.normalize(dirPath);
            const normalizedExpectedPath = path.normalize(pathInfo.encodedPath);
            
            return normalizedDirPath === normalizedExpectedPath;
        } catch (error) {
            console.warn(`⚠️ 验证目录路径时出错: ${error.message}`);
            return false;
        }
    }
    
    // 从目录路径中提取并还原原始的参数组合信息（用于调试和验证）
    extractCombinationFromPath(dirPath) {
        try {
            // 移除开头的 './' 并分割路径
            const cleanPath = dirPath.replace(/^\.\//, '');
            const pathParts = cleanPath.split(path.sep);
            
            if (pathParts.length !== 5) {
                throw new Error(`路径组件数量不正确，期望5个，实际${pathParts.length}个`);
            }
            
            return {
                studyPhaseName: this.decodeDirName(pathParts[0]),
                subjectName: this.decodeDirName(pathParts[1]),
                textbookVersionName: this.decodeDirName(pathParts[2]),
                ceciName: this.decodeDirName(pathParts[3]),
                catalogName: this.decodeDirName(pathParts[4])
            };
        } catch (error) {
            console.warn(`⚠️ 从路径提取参数组合信息时出错: ${error.message}`);
            return null;
        }
    }

    // 设置当前任务的参数组合
    setCurrentCombination(combination) {
        this.currentCombination = combination;
        
        // 生成输出目录路径
        const pathInfo = this.generateOutputPath(combination);
        this.outputDir = pathInfo.encodedPath;
        
        // 确保输出目录存在
        this.ensureOutputDir();
        
        // 重置状态
        this.currentPage = this.config.startPage;
        this.isSettingsConfigured = false;
        
        console.log(`📂 [${this.config.instanceId}] 设置当前任务: ${combination.studyPhaseName}/${combination.subjectName}/${combination.textbookVersionName}/${combination.ceciName}/${combination.catalogName}`);
        console.log(`📁 输出目录: ${this.outputDir}`);
    }

    ensureOutputDir() {
        if (!this.outputDir) {
            this.outputDir = './output';
            console.log(`⚠️ 输出目录未设置，使用默认目录: ${this.outputDir}`);
        }
        
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
            console.log(`✅ 创建输出目录: ${this.outputDir}`);
        }
    }

    // 确保截图目录存在
    ensureScreenshotDir() {
        if (!fs.existsSync(this.screenshotDir)) {
            fs.mkdirSync(this.screenshotDir, { recursive: true });
        }
    }

    // 保存调试截图
    async saveDebugScreenshot(page, reason, elementInfo = '') {
        try {
            this.ensureScreenshotDir();
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `debug_${timestamp}_${reason}.png`;
            const filepath = path.join(this.screenshotDir, filename);
            
            await page.screenshot({
                path: filepath,
                fullPage: true
            });
            
            console.log(`📷 保存调试截图: ${filepath} (${reason}${elementInfo ? ': ' + elementInfo : ''})`);
            return filepath;
        } catch (error) {
            console.warn(`⚠️ 保存调试截图失败: ${error.message}`);
            return null;
        }
    }

    // 检测页面加载状态
    async checkLoadingState(page, timeoutMs = 10000) {
        try {
            console.log(`🔍 检测页面加载状态 (超时: ${timeoutMs}ms)`);
            
            const startTime = Date.now();
            let isLoading = false;
            let loadingElement = null;

            // 检测是否有加载图片
            const checkLoading = async () => {
                try {
                    // 查找 w80 h30 class 的图片元素
                    const images = await page.locator('img.w80.h30').all();
                    
                    for (const img of images) {
                        const src = await img.getAttribute('src');
                        const isVisible = await img.isVisible();
                        
                        if (src === 'https://zj.stzy.com/images/create-paper/loading.gif' && isVisible) {
                            return { isLoading: true, element: img };
                        }
                    }
                    
                    return { isLoading: false, element: null };
                } catch (error) {
                    return { isLoading: false, element: null };
                }
            };

            // 初始检查
            const initialCheck = await checkLoading();
            if (!initialCheck.isLoading) {
                return { 
                    isLoading: false, 
                    reason: '页面无加载状态',
                    needAction: false 
                };
            }

            console.log(`⏳ 检测到加载图片，开始监控...`);
            loadingElement = initialCheck.element;
            isLoading = true;

            // 持续监控加载状态
            while (isLoading && (Date.now() - startTime) < timeoutMs) {
                await page.waitForTimeout(1000); // 每秒检查一次
                
                const currentCheck = await checkLoading();
                if (!currentCheck.isLoading) {
                    const duration = Date.now() - startTime;
                    console.log(`✅ 页面加载完成 (耗时: ${duration}ms)`);
                    return { 
                        isLoading: false, 
                        reason: '加载完成',
                        duration: duration,
                        needAction: false 
                    };
                }
            }

            // 如果超时仍在加载
            if (isLoading) {
                const duration = Date.now() - startTime;
                console.log(`⚠️ 页面加载超时 (${duration}ms)，可能是token封禁或网络问题`);
                
                // 保存调试截图
                await this.saveDebugScreenshot(page, 'loading_timeout', `duration_${duration}ms`);
                
                return { 
                    isLoading: true, 
                    reason: '加载超时',
                    duration: duration,
                    needAction: true 
                };
            }

            return { 
                isLoading: false, 
                reason: '检测完成',
                needAction: false 
            };

        } catch (error) {
            console.warn(`⚠️ 检测页面加载状态时出错: ${error.message}`);
            return { 
                isLoading: false, 
                reason: `检测异常: ${error.message}`,
                needAction: false 
            };
        }
    }

    // 处理加载超时的情况
    async handleLoadingTimeout(page, pageContext = '') {
        try {
            console.log(`🔄 处理加载超时情况 (${pageContext})`);
            
            // 先尝试检测是否是token问题
            const tokenCheckResult = await this.checkTokenStatus(page);
            
            if (tokenCheckResult.isTokenBanned) {
                console.log(`🚫 检测到token被封禁: ${tokenCheckResult.reason}`);
                return {
                    action: 'switchToken',
                    reason: tokenCheckResult.reason,
                    needSwitchToken: true
                };
            }
            
            // 如果不是token问题，尝试刷新页面
            console.log(`🔄 可能是网络问题，尝试刷新页面...`);
            
            try {
                await page.reload({ 
                    waitUntil: 'networkidle', 
                    timeout: this.config.pageTimeout 
                });
                
                // 刷新后等待一段时间
                await page.waitForTimeout(3000);
                
                // 再次检查加载状态
                const loadingCheck = await this.checkLoadingState(page, 5000);
                
                if (loadingCheck.needAction) {
                    console.log(`⚠️ 刷新后仍然加载超时，可能是token问题`);
                    return {
                        action: 'switchToken',
                        reason: '刷新后仍然加载超时',
                        needSwitchToken: true
                    };
                }
                
                console.log(`✅ 页面刷新成功，需要重新配置参数`);
                return {
                    action: 'reconfigure',
                    reason: '页面刷新成功',
                    needReconfigure: true
                };
                
            } catch (refreshError) {
                console.error(`❌ 页面刷新失败: ${refreshError.message}`);
                return {
                    action: 'switchToken',
                    reason: `页面刷新失败: ${refreshError.message}`,
                    needSwitchToken: true
                };
            }

        } catch (error) {
            console.error(`❌ 处理加载超时时出错: ${error.message}`);
            return {
                action: 'switchToken',
                reason: `处理超时失败: ${error.message}`,
                needSwitchToken: true
            };
        }
    }

    // 检查token状态
    async checkTokenStatus(page) {
        try {
            // 检查当前URL是否被重定向到登录页
            const currentUrl = page.url();
            if (this.isLoginPage(currentUrl)) {
                return {
                    isTokenBanned: true,
                    reason: `页面重定向到登录页: ${currentUrl}`
                };
            }

            // 检查页面是否显示错误信息
            const errorSelectors = [
                '.ant-result-error',
                '.error-page', 
                '.login-form',
                '.ant-empty',
                '[class*="error"]',
                '[class*="forbidden"]',
                '[class*="expired"]'
            ];

            for (const selector of errorSelectors) {
                try {
                    const errorElement = page.locator(selector);
                    const count = await errorElement.count();
                    if (count > 0) {
                        const isVisible = await errorElement.first().isVisible();
                        if (isVisible) {
                            return {
                                isTokenBanned: true,
                                reason: `页面显示错误元素: ${selector}`
                            };
                        }
                    }
                } catch (error) {
                    // 忽略单个选择器的检查错误
                }
            }

            // 检查页面关键元素是否存在
            const keyElements = [
                '#textbook_tree',
                '.ant-dropdown-trigger',
                'ul.ant-pagination'
            ];

            let missingCount = 0;
            for (const selector of keyElements) {
                try {
                    const count = await page.locator(selector).count();
                    if (count === 0) {
                        missingCount++;
                    }
                } catch (error) {
                    missingCount++;
                }
            }

            // 如果大部分关键元素都缺失，可能是token问题
            if (missingCount >= 2) {
                return {
                    isTokenBanned: true,
                    reason: `关键页面元素缺失 (${missingCount}/${keyElements.length})`
                };
            }

            return {
                isTokenBanned: false,
                reason: 'token状态正常'
            };

        } catch (error) {
            return {
                isTokenBanned: false,
                reason: `token状态检查异常: ${error.message}`
            };
        }
    }

    // 模拟人类滑动到元素
    async humanScrollToElement(element, page) {
        try {
            // 获取元素位置
            const box = await element.boundingBox();
            if (!box) return;

            const viewport = await page.viewportSize();
            const targetY = box.y + box.height / 2;
            const currentScrollY = await page.evaluate(() => window.scrollY);
            
            // 计算需要滚动的距离
            const scrollDistance = targetY - viewport.height / 2 - currentScrollY;
            
            if (Math.abs(scrollDistance) > 50) {
                // 分段滚动，模拟人类行为
                const steps = Math.min(Math.abs(scrollDistance) / 100, 8);
                const stepDistance = scrollDistance / steps;
                
                for (let i = 0; i < steps; i++) {
                    await page.mouse.wheel(0, stepDistance);
                    await page.waitForTimeout(100 + Math.random() * 100);
                }
            }
            
            // 确保元素在视口中
            await element.scrollIntoViewIfNeeded();
            await page.waitForTimeout(200 + Math.random() * 200);
            
        } catch (error) {
            console.warn(`⚠️ 滑动到元素失败: ${error.message}`);
        }
    }

    // 模拟人为点击 (使用新的HumanBehaviorSimulator)
    async humanClick(element, page, description = '') {
        try {
            await this.humanBehavior.humanClick(page, element, { description });
        } catch (error) {
            console.error(`❌ 模拟人为点击失败 ${description}: ${error.message}`);
            throw error;
        }
    }

    // 模拟人为输入 (使用新的HumanBehaviorSimulator)
    async humanType(element, text, page, description = '') {
        try {
            await this.humanBehavior.humanType(page, element, text, { description });
        } catch (error) {
            console.error(`❌ 模拟人为输入失败 ${description}: ${error.message}`);
            throw error;
        }
    }

    // 模拟人类清空输入框的多种方式
    async humanClearInput(element, page) {
        try {
            // 获取当前输入框的值，检查是否需要清空
            const currentValue = await element.inputValue();
            if (!currentValue || currentValue.trim() === '') {
                return; // 输入框已经是空的，不需要清空
            }

            console.log(`🧹 清空输入框现有内容: "${currentValue}"`);

            // 随机选择清空方式，模拟不同用户的操作习惯
            const clearMethod = Math.floor(Math.random() * 4);
            
            switch (clearMethod) {
                case 0:
                    // 方式1: Ctrl+A 全选后直接输入（最常见）
                    console.log(`   使用方式1: Ctrl+A 全选`);
                    await page.keyboard.press('Control+a');
                    await page.waitForTimeout(100 + Math.random() * 100);
                    break;
                    
                case 1:
                    // 方式2: 三击选中全部内容
                    console.log(`   使用方式2: 三击选中`);
                    await element.click({ clickCount: 3 });
                    await page.waitForTimeout(150 + Math.random() * 100);
                    break;
                    
                case 2:
                    // 方式3: 多次按 Backspace 删除（模拟一个字符一个字符删除）
                    console.log(`   使用方式3: 逐字符删除`);
                    const valueLength = currentValue.length;
                    // 随机决定是删除全部还是部分后再全选
                    const deleteCount = Math.random() < 0.7 ? valueLength : Math.floor(valueLength / 2);
                    
                    for (let i = 0; i < deleteCount; i++) {
                        await page.keyboard.press('Backspace');
                        // 模拟删除时的不规律节奏
                        const deleteDelay = 30 + Math.random() * 80;
                        await page.waitForTimeout(deleteDelay);
                    }
                    
                    // 如果没有全部删除，再全选剩余内容
                    if (deleteCount < valueLength) {
                        await page.keyboard.press('Control+a');
                        await page.waitForTimeout(50);
                    }
                    break;
                    
                case 3:
                    // 方式4: Home键到开头，然后Shift+End选中全部
                    console.log(`   使用方式4: Home+Shift+End 选中`);
                    await page.keyboard.press('Home');
                    await page.waitForTimeout(50 + Math.random() * 50);
                    await page.keyboard.press('Shift+End');
                    await page.waitForTimeout(100 + Math.random() * 100);
                    break;
            }
            
            // 最后确认选中状态，如果内容还在就强制清空
            const afterClearValue = await element.inputValue();
            if (afterClearValue && afterClearValue.trim() !== '') {
                console.log(`   兜底清空: 内容仍存在，强制清空`);
                await page.keyboard.press('Control+a');
                await page.waitForTimeout(50);
                await page.keyboard.press('Delete');
                await page.waitForTimeout(50);
            }
            
        } catch (error) {
            console.warn(`⚠️ 清空输入框时出错，使用简单方式: ${error.message}`);
            // 如果上述方法失败，使用最基础的方法
            await page.keyboard.press('Control+a');
            await page.waitForTimeout(100);
        }
    }

    createSelectInfo() {
        return {
            dictCode: this.currentCombination.subjectCode,
            dictName: this.currentCombination.subjectName,
            studyPhaseCode: this.currentCombination.studyPhaseCode,
            studyPhaseName: this.currentCombination.studyPhaseName,
            childList: null,
            gray: false
        };
    }

    async createBrowserAndPage() {
        try {
            // 从TokenManager获取可用token
            const tokenData = await this.tokenManager.assignTokenToInstance();
            console.log(`🔍 [${this.config.instanceId}] 使用Token[${tokenData.id}]创建浏览器并验证...`);
            
            const proxyConfig = await this.assignProxyToToken(tokenData);

            // 构建浏览器启动参数，包含缓存策略
            const browserArgs = [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ];

            // 启用缓存策略
            if (this.config.enableCache) {
                browserArgs.push(
                    '--aggressive-cache-discard',
                    `--disk-cache-size=${this.config.cacheMaxSize * 1024 * 1024}`, // 转换为字节
                    '--memory-pressure-off',
                    '--max_old_space_size=4096'
                );
                console.log(`📦 [${this.config.instanceId}] 浏览器缓存已启用 (${this.config.cacheMaxSize}MB)`);
            } else {
                browserArgs.push('--disable-background-timer-throttling', '--disable-renderer-backgrounding');
            }

            // 防反爬检测参数
            if (this.config.enableAntiBot) {
                browserArgs.push(
                    '--disable-blink-features=AutomationControlled',
                    '--disable-features=VizDisplayCompositor,UseOzonePlatform',
                    '--flag-switches-begin',
                    '--disable-ipc-flooding-protection',
                    '--flag-switches-end'
                );
            }

            const browserConfig = {
                headless: this.config.headless,
                args: browserArgs,
                // 设置更真实的浏览器环境
                chromiumSandbox: false
            };

            if (proxyConfig) {
                browserConfig.proxy = proxyConfig;
                this.currentProxy = proxyConfig;
            }

            this.browser = await chromium.launch(browserConfig);

            // 随机选择用户代理
            const randomUserAgent = this.userAgents[Math.floor(Math.random() * this.userAgents.length)];
            
            // 创建更真实的浏览器上下文
            const contextOptions = {
                userAgent: randomUserAgent,
                viewport: this.getRandomViewport(),
                ignoreHTTPSErrors: true,
                // 启用JavaScript
                javaScriptEnabled: true,
                // 设置语言和地区
                locale: 'zh-CN',
                timezoneId: 'Asia/Shanghai',
                // 启用设备像素比模拟
                deviceScaleFactor: Math.random() > 0.5 ? 1 : 2,
                // 设置权限
                permissions: ['notifications']
            };

            // 根据用户代理设置额外的浏览器特征
            if (randomUserAgent.includes('Windows')) {
                contextOptions.platform = 'Win32';
            } else if (randomUserAgent.includes('Macintosh')) {
                contextOptions.platform = 'MacIntel';
            } else if (randomUserAgent.includes('Linux')) {
                contextOptions.platform = 'Linux x86_64';
            }

            this.context = await this.browser.newContext(contextOptions);

            console.log(`🎭 [${this.config.instanceId}] 使用用户代理: ${randomUserAgent.substring(0, 50)}...`);

            this.currentTokenData = tokenData;

            // 注入关键cookie
            await this.context.addCookies([
                {
                    name: 'ACCESS_TOKEN',
                    value: tokenData.token,
                    domain: '.stzy.com',
                    path: '/',
                    httpOnly: false,
                    secure: true,
                    sameSite: 'Lax'
                },
                {
                    name: 'selectInfo',
                    value: JSON.stringify(this.createSelectInfo()),
                    domain: '.stzy.com',
                    path: '/',
                    httpOnly: false,
                    secure: true,
                    sameSite: 'Lax'
                }
            ]);

            this.page = await this.context.newPage();
            this.page.setDefaultTimeout(this.config.pageTimeout);
            this.page.setDefaultNavigationTimeout(this.config.pageTimeout);

            // 设置浏览器指纹和反检测
            await this.setupBrowserFingerprint(this.page);

            // 设置额外的请求头来模拟真实浏览器
            await this.page.setExtraHTTPHeaders({
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Sec-CH-UA': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                'Sec-CH-UA-Mobile': '?0',
                'Sec-CH-UA-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1'
            });

            // 添加页面导航监听器，检测登录页面跳转
            this.page.on('framenavigated', (frame) => {
                // 添加空值检查，防止页面清理后仍然触发监听器
                if (!this.page) {
                    return;
                }
                
                if (frame === this.page.mainFrame()) {
                    const currentUrl = frame.url();
                    if (this.isLoginPage(currentUrl)) {
                        console.log(`🚫 检测到页面跳转到登录页面: ${currentUrl}`);
                        this.tokenExpired = true;
                    }
                }
            });

            // 验证token有效性
            let has403Error = false;
            const responseListener = async (response) => {
                if (response.url().includes('api')) {
                    try {
                        if (response.status() === 403) {
                            has403Error = true;
                        } else {
                            try {
                                const responseData = await response.json();
                                if (responseData && responseData.code === 403) {
                                    has403Error = true;
                                }
                            } catch (error) {
                                // 忽略JSON解析错误
                            }
                        }
                    } catch (error) {
                        // 忽略响应处理错误
                    }
                }
            };

            this.page.on('response', responseListener);

            // 尝试访问需要token的页面
            console.log(`🌐 验证Token[${tokenData.id}]，请求页面...`);
            await this.page.goto(this.config.baseURL, {
                waitUntil: 'networkidle',
                timeout: 160000
            });

            this.page.off('response', responseListener);

            // 检查页面元素有效性
            const elementCheck = await this.checkPageElements(this.page);
            if (!elementCheck.valid) {
                await this.cleanupBrowser();
                throw new Error(`Token[${tokenData.id}]无效: ${elementCheck.reason}`);
            }

            // 检查是否有403错误
            if (has403Error) {
                await this.cleanupBrowser();
                throw new Error(`Token[${tokenData.id}]被封禁，检测到403错误`);
            }

            console.log(`✅ Token[${tokenData.id}]验证成功，浏览器实例已创建`);
            
            // 启动代理健康检查
            this.startProxyHealthCheck();
            
            return { browser: this.browser, context: this.context, page: this.page };

        } catch (error) {
            console.error(`❌ 创建浏览器实例失败: ${error.message}`);

            // 根据错误类型标记token
            if (this.currentTokenData) {
                if (error.message.includes('403错误') || error.message.includes('被封禁')) {
                    await this.tokenManager.markTokenInvalid(this.currentTokenData.id, error.message);
                } else if (error.message.includes('无效')) {
                    await this.tokenManager.markTokenInvalid(this.currentTokenData.id, error.message);
                }
            }

            // 确保清理浏览器资源
            await this.cleanupBrowser();
            throw error;
        }
    }

    // 清理浏览器资源
    async cleanupBrowser() {
        try {
            // 停止代理健康检查
            this.stopProxyHealthCheck();
            
            // 释放代理（如果有）
            if (this.proxyPool && this.currentTokenData) {
                this.proxyPool.releaseProxyFromToken(this.currentTokenData.id);
            }
            
            // 清理页面引用
            this.page = null;
            
            // 清理context
            if (this.context) {
                try {
                    await this.context.close();
                } catch (contextError) {
                    console.warn(`⚠️ 关闭context时出错: ${contextError.message}`);
                }
                this.context = null;
            }
            
            // 清理browser
            if (this.browser) {
                try {
                    await this.browser.close();
                } catch (browserError) {
                    console.warn(`⚠️ 关闭browser时出错: ${browserError.message}`);
                }
                this.browser = null;
            }
            
            this.currentProxy = null;
            this.currentTokenData = null;
            this.tokenExpired = false;
        } catch (cleanupError) {
            console.warn(`⚠️ 清理浏览器资源时出错: ${cleanupError.message}`);
            // 强制清空所有引用
            this.page = null;
            this.context = null;
            this.browser = null;
            this.currentProxy = null;
            this.currentTokenData = null;
            this.tokenExpired = false;
        }
    }

    /**
     * Set up early 403 detection listener
     */
    setupEarly403Detection(page) {
        let has403Error = false;
        let tokenValidationError = null;

        const earlyResponseListener = async (response) => {
            try {
                // Check for 403 status code
                if (response.status() === 403) {
                    has403Error = true;
                    tokenValidationError = `HTTP 403 status from ${response.url()}`;
                    console.log(`🚫 检测到403状态码: ${response.url()}`);
                    return;
                }

                // Check for 403 in response body for API calls
                if (response.url().includes('api') || response.url().includes('stzy.com')) {
                    try {
                        const responseData = await response.json();
                        if (responseData && responseData.code === 403) {
                            has403Error = true;
                            tokenValidationError = `API response code 403 from ${response.url()}`;
                            console.log(`🚫 检测到API响应403错误: ${response.url()}`);
                        }
                    } catch (jsonError) {
                        // Ignore JSON parsing errors for non-JSON responses
                    }
                }
            } catch (error) {
                // Ignore response processing errors
            }
        };

        return {
            listener: earlyResponseListener,
            hasError: () => has403Error,
            getError: () => tokenValidationError,
            cleanup: () => page.off('response', earlyResponseListener)
        };
    }

    /**
     * Setup continuous network listening for API response capture
     * This method sets up persistent listening that doesn't block execution
     */
    setupContinuousNetworkListening(page) {
        console.log(`🔧 设置持续网络监听以捕获所有API请求`);

        // Store captured responses for later retrieval
        if (!this.capturedResponses) {
            this.capturedResponses = new Map();
        }

        const responseHandler = async (response) => {
            // Check for API patterns
            const isApiCall = response.url().includes(this.config.apiURL) ||
                            response.url().includes('/api/') ||
                            response.url().includes('textbookQuery') ||
                            response.url().includes('homeEs');

            if (isApiCall) {
                try {
                    // 获取请求信息
                    const request = response.request();
                    const requestData = request.postData();

                    if (requestData) {
                        try {
                            const requestJson = JSON.parse(requestData);

                            // 验证请求参数是否匹配当前组合
                            if (this.isRequestMatchingCurrentCombination(requestJson)) {
                                const responseData = await response.json();

                                // 检查响应中的403错误
                                if (responseData && responseData.code === 403) {
                                    console.log(`🚫 检测到403错误，令牌可能被禁用`);
                                    // Store error response
                                    const pageNum = requestJson.pageNum || 1;
                                    this.capturedResponses.set(pageNum, {
                                        page: pageNum,
                                        url: response.url(),
                                        status: response.status(),
                                        data: responseData,
                                        requestData: requestJson,
                                        timestamp: new Date().toISOString(),
                                        tokenBanned: true
                                    });
                                    return;
                                }

                                // Store successful response
                                const pageNum = requestJson.pageNum || 1;
                                this.capturedResponses.set(pageNum, {
                                    page: pageNum,
                                    url: response.url(),
                                    status: response.status(),
                                    data: responseData,
                                    requestData: requestJson,
                                    timestamp: new Date().toISOString()
                                });

                                console.log(`✅ 捕获第${pageNum}页API响应 (${responseData.data?.list?.length || 0}条数据)`);
                            }
                        } catch (parseError) {
                            console.warn(`⚠️ 解析请求数据失败: ${parseError.message}`);
                        }
                    }
                } catch (error) {
                    console.error(`❌ 解析API响应失败: ${error.message}`);
                }
            }
        };

        // Set up the response listener
        page.on('response', responseHandler);

        // Store the handler for potential cleanup
        if (!this.responseHandlers) {
            this.responseHandlers = [];
        }
        this.responseHandlers.push({ page, handler: responseHandler });

        console.log('✅ 持续网络监听已设置完成');
    }

    /**
     * Get captured response for a specific page
     */
    getCapturedResponse(pageNum) {
        if (!this.capturedResponses) {
            return null;
        }
        return this.capturedResponses.get(pageNum);
    }

    /**
     * Wait for captured response for a specific page
     */
    async waitForCapturedResponse(pageNum, timeout = 30000) {
        const startTime = Date.now();

        while (Date.now() - startTime < timeout) {
            const response = this.getCapturedResponse(pageNum);
            if (response) {
                return response;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.warn(`⏰ 等待第${pageNum}页响应超时`);
        return {
            page: pageNum,
            url: null,
            status: 408,
            data: null,
            requestData: null,
            timestamp: new Date().toISOString(),
            timeout: true
        };
    }

    setupNetworkListening(page, pageNum) {
        let responsePromise = null;
        let responseResolver = null;
        let captured = false;

        responsePromise = new Promise((resolve, reject) => {
            responseResolver = resolve;

            const responseHandler = async (response) => {
                if (captured) return; // 已经捕获过了，忽略后续响应

                if (response.url().includes(this.config.apiURL)) {
                    try {
                        // 获取请求信息
                        const request = response.request();
                        const requestData = request.postData();

                        if (requestData) {
                            try {
                                const requestJson = JSON.parse(requestData);

                                // 验证请求参数是否匹配当前组合
                                if (this.isRequestMatchingCurrentCombination(requestJson)) {
                                    const responseData = await response.json();

                                    // 检查响应中的403错误
                                    if (responseData && responseData.code === 403) {
                                        console.log(`🚫 第 ${pageNum} 页检测到403错误，token可能被封禁`);
                                        // 保存截图用于调试
                                        if (this.page) {
                                            this.saveDebugScreenshot(this.page, 'token_banned_403', `page_${pageNum}`).catch(() => {});
                                        }
                                        captured = true;
                                        page.off('response', responseHandler);
                                        resolve({
                                            page: pageNum,
                                            url: response.url(),
                                            status: response.status(),
                                            data: responseData,
                                            requestData: requestJson,
                                            timestamp: new Date().toISOString(),
                                            tokenBanned: true
                                        });
                                        return;
                                    }

                                    captured = true;
                                    page.off('response', responseHandler); // 移除监听器

                                    resolve({
                                        page: pageNum,
                                        url: response.url(),
                                        status: response.status(),
                                        data: responseData,
                                        requestData: requestJson,
                                        timestamp: new Date().toISOString()
                                    });
                                }
                            } catch (parseError) {
                                console.warn(`⚠️ 解析请求数据失败: ${parseError.message}`);
                            }
                        }
                    } catch (error) {
                        console.error(`❌ 解析第 ${pageNum} 页API响应失败:`, error.message);
                    }
                }
            };

            page.on('response', responseHandler);

            // 设置超时，避免无限等待
            setTimeout(() => {
                if (!captured) {
                    page.off('response', responseHandler);
                    console.log(`⏰ 第 ${pageNum} 页响应捕获超时，跳过此页`);
                    resolve({
                        page: pageNum,
                        url: null,
                        status: 408,
                        data: null,
                        requestData: null,
                        timestamp: new Date().toISOString(),
                        timeout: true
                    });
                }
            }, 30000); // 30秒超时
        });

        return responsePromise;
    }

    isRequestMatchingCurrentCombination(requestData) {
        try {
            // 检查关键参数是否匹配
            const combination = this.currentCombination;

            // 检查学段代码
            if (requestData.studyPhaseCode && requestData.studyPhaseCode !== combination.studyPhaseCode) {
                return false;
            }

            // 检查学科代码
            if (requestData.subjectCode && requestData.subjectCode !== combination.subjectCode) {
                return false;
            }

            // 检查教材版本代码
            if (requestData.textbookVersionCode && requestData.textbookVersionCode !== combination.textbookVersionCode) {
                return false;
            }

            // 检查册次代码
            if (requestData.ceciCode && requestData.ceciCode !== combination.ceciCode) {
                return false;
            }

            // 检查目录代码 - 验证treeIds数组是否包含catalogCode
            if (requestData.treeIds && Array.isArray(requestData.treeIds)) {
                if (!requestData.treeIds.includes(combination.catalogCode)) {
                    console.log(`⚠️ treeIds数组不包含目标catalogCode: ${combination.catalogCode}`);
                    return false;
                }
            }

            return true;
        } catch (error) {
            console.warn(`⚠️ 验证请求参数匹配时出错: ${error.message}`);
            return false;
        }
    }

    async clickSettingElements(page) {
        try {
            console.log('🔧 配置教材版本和册次设置...');

            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(2000);

            // 检查页面关键元素，如果失败则尝试刷新页面
            const elementCheckResult = await this.checkPageElementsWithRetry(page, 'settings', 2);
            if (!elementCheckResult.valid) {
                await this.saveDebugScreenshot(page, 'element_check_failed', elementCheckResult.reason);
                if (elementCheckResult.needSwitchToken) {
                    throw new Error(`页面元素检查失败，token已失效: ${elementCheckResult.reason}`);
                } else {
                    throw new Error(`页面元素检查失败，可能网络问题: ${elementCheckResult.reason}`);
                }
            }

            // 查找并触发下拉菜单
            const selectSubjectElement = page.locator('#textbook_tree');
            try {
                await selectSubjectElement.waitFor({ state: 'visible', timeout: 30000 });
            } catch (error) {
                await this.saveDebugScreenshot(page, 'textbook_tree_not_found');
                throw new Error('未找到教材树容器');
            }

            const triggerElement = selectSubjectElement.locator('.ant-dropdown-trigger');
            
            // 使用模拟人为点击触发下拉菜单
            await this.humanClick(triggerElement, page, '下拉菜单触发器');
            await page.waitForTimeout(1000);

            // 检测页面加载状态
            const loadingCheck = await this.checkLoadingState(page, 5000); // 5秒超时
            if (loadingCheck.needAction) {
                const timeoutResult = await this.handleLoadingTimeout(page, '下拉菜单操作后');
                if (timeoutResult.needSwitchToken) {
                    throw new Error(`下拉菜单加载超时，需要切换token: ${timeoutResult.reason}`);
                } else if (timeoutResult.needReconfigure) {
                    // 页面刷新后需要重新开始配置
                    throw new Error(`下拉菜单操作后页面刷新，需要重新配置参数`);
                }
            }

            // 查找下拉菜单内容
            const dropdownContents = await page.locator('.ant-dropdown-content').all();
            if (dropdownContents.length === 0) {
                await this.saveDebugScreenshot(page, 'dropdown_not_found');
                throw new Error('下拉菜单未出现');
            }

            let foundTextbookVersion = false;
            let foundCeci = false;

            for (let i = 0; i < dropdownContents.length; i++) {
                const dropdown = dropdownContents[i];
                await triggerElement.hover();
                await page.waitForTimeout(200);

                const options = await dropdown.locator('.flex_warp').all();

                for (const option of options) {
                    await triggerElement.hover();
                    await page.waitForTimeout(100);

                    const pointerOptions = await option.locator('.pointer').all();

                    for (const pointerOption of pointerOptions) {
                        const pointerTextContent = await pointerOption.textContent();
                        if (!pointerTextContent) continue;

                        // 查找教材版本
                        if (!foundTextbookVersion && pointerTextContent.trim() === this.currentCombination.textbookVersionName) {
                            console.log(`📌 选择教材版本: ${pointerTextContent.trim()}`);
                            await triggerElement.hover();
                            await page.waitForTimeout(100);
                            await this.humanClick(pointerOption, page, '教材版本');
                            foundTextbookVersion = true;
                            await triggerElement.hover();
                            await page.waitForTimeout(200);
                            break;
                        }

                        // 查找册次
                        if (!foundCeci && pointerTextContent.trim() === this.currentCombination.ceciName) {
                            console.log(`📌 选择册次: ${pointerTextContent.trim()}`);
                            await triggerElement.hover();
                            await page.waitForTimeout(100);
                            await this.humanClick(pointerOption, page, '册次');
                            foundCeci = true;
                            await triggerElement.hover();
                            await page.waitForTimeout(200);
                            break;
                        }
                    }
                }

                if (foundTextbookVersion && foundCeci) {
                    break;
                }
            }

            // 使用人类行为模拟取消hover状态
            await this.humanBehavior.randomMouseMovement(page);
            await page.waitForTimeout(this.humanBehavior.randomDelay(300, 700));

            try {
                const bodyElement = page.locator('body');
                await this.humanBehavior.humanHover(page, bodyElement, {
                    description: '移动到页面空白区域',
                    duration: this.humanBehavior.randomDelay(300, 600)
                });
            } catch (error) {
                // 忽略错误
            }

            // 确认选择结果
            const isSelectionConfirmed = await this.confirmSelection(page, triggerElement);
            if (!isSelectionConfirmed) {
                await this.saveDebugScreenshot(page, 'selection_confirm_failed');
                throw new Error('教材版本和册次选择确认失败');
            }

            await page.waitForLoadState('networkidle');

            // 在点击catalog之前设置持续网络监听，以捕获所有API请求
            console.log('🔧 设置持续网络监听以捕获所有API请求');
            this.setupContinuousNetworkListening(this.page);

            // 点击目录 - 这将触发第一页的 question/textbookQuery 请求
            console.log('📂 即将点击目录，这将触发第1页的API请求');
            await this.clickCatalogElement(page);

            console.log('✅ 教材版本、册次和目录设置完成，第1页请求已发送');

        } catch (error) {
            console.error('❌ 设置配置失败:', error.message);
            throw error;
        }
    }

    async confirmSelection(page, triggerElement, maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                await page.waitForTimeout(1000);

                const titleElements = await triggerElement.locator('[title]').all();
                let foundTextbookVersion = false;
                let foundCeci = false;

                for (const element of titleElements) {
                    const titleValue = await element.getAttribute('title');
                    if (titleValue) {
                        if (titleValue.includes(this.currentCombination.textbookVersionName)) {
                            foundTextbookVersion = true;
                        }
                        if (titleValue.includes(this.currentCombination.ceciName)) {
                            foundCeci = true;
                        }
                    }
                }

                if (foundTextbookVersion && foundCeci) {
                    return true;
                } else {
                    if (attempt < maxRetries) {
                        await page.waitForTimeout(2000);
                    }
                }

            } catch (error) {
                console.warn(`⚠️ 选择确认第 ${attempt} 次失败: ${error.message}`);
                if (attempt < maxRetries) {
                    await page.waitForTimeout(2000);
                }
            }
        }

        console.error(`❌ 教材版本和册次选择确认失败 (${maxRetries} 次重试)`);
        return false;
    }



    async clickCatalogElement(page) {
        try {
            console.log(`📂 选择目录: ${this.currentCombination.catalogName} (将触发第1页API请求)`);

            // 检查页面关键元素
            const elementCheckResult = await this.checkPageElementsWithRetry(page, 'catalog', 2);
            if (!elementCheckResult.valid) {
                await this.saveDebugScreenshot(page, 'catalog_element_check_failed', elementCheckResult.reason);
                if (elementCheckResult.needSwitchToken) {
                    throw new Error(`页面元素检查失败，token已失效: ${elementCheckResult.reason}`);
                } else {
                    throw new Error(`页面元素检查失败，可能网络问题: ${elementCheckResult.reason}`);
                }
            }

            // 等待教材树容器加载
            const textbookTree = page.locator('#textbook_tree');
            try {
                await textbookTree.waitFor({ state: 'visible', timeout: 30000 });
            } catch (error) {
                await this.saveDebugScreenshot(page, 'textbook_tree_not_visible');
                throw new Error('教材树容器未加载');
            }

            // 查找tree组件
            const treeWrapper = textbookTree.locator('.tree-component-wrapper');
            try {
                await treeWrapper.waitFor({ state: 'visible', timeout: 30000 });
            } catch (error) {
                await this.saveDebugScreenshot(page, 'tree_wrapper_not_found');
                throw new Error('树组件包装器未找到');
            }

            const treeUl = treeWrapper.locator('ul[role="tree"]');
            try {
                await treeUl.waitFor({ state: 'visible', timeout: 30000 });
            } catch (error) {
                await this.saveDebugScreenshot(page, 'tree_ul_not_found');
                throw new Error('目录树列表未找到');
            }

            // 查找目标目录元素
            const titleElements = await treeUl.locator('[title]').all();

            let targetElement = null;
            for (const element of titleElements) {
                const titleValue = await element.getAttribute('title');
                if (titleValue && titleValue.trim() === this.currentCombination.catalogName) {
                    targetElement = element;
                    break;
                }
            }

            if (!targetElement) {
                await this.saveDebugScreenshot(page, 'target_catalog_not_found', this.currentCombination.catalogName);
                throw new Error(`未找到目录: ${this.currentCombination.catalogName}`);
            }

            // 使用模拟人为点击选择目录
            await this.humanClick(targetElement, page, '目录元素');
            await page.waitForLoadState('networkidle');

            // 检测页面加载状态
            const loadingCheck = await this.checkLoadingState(page, 8000); // 8秒超时
            if (loadingCheck.needAction) {
                const timeoutResult = await this.handleLoadingTimeout(page, '目录选择后');
                if (timeoutResult.needSwitchToken) {
                    throw new Error(`目录选择加载超时，需要切换token: ${timeoutResult.reason}`);
                } else if (timeoutResult.needReconfigure) {
                    // 页面刷新后需要重新配置
                    throw new Error(`目录选择后页面刷新，需要重新配置参数`);
                }
            }

            // 确认选择结果
            const isSelected = await this.confirmCatalogSelection(targetElement);
            if (!isSelected) {
                await this.saveDebugScreenshot(page, 'catalog_selection_failed');
                throw new Error('目录选择确认失败');
            }

            console.log(`✅ 目录选择完成: ${this.currentCombination.catalogName}，第1页API请求已发送`);

        } catch (error) {
            console.error('❌ 目录选择失败:', error.message);
            throw error;
        }
    }

    async confirmCatalogSelection(targetElement, maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                await this.page.waitForTimeout(1000);

                const ancestorLi = targetElement.locator('xpath=ancestor::li[contains(@class, "ant-tree-treenode")]').first();

                if (await ancestorLi.count() > 0) {
                    const hasSelectedClass = await ancestorLi.evaluate(el => el.classList.contains('ant-tree-treenode-selected'));

                    if (hasSelectedClass) {
                        return true;
                    } else {
                        if (attempt < maxRetries) {
                            // 重新使用模拟人为点击
                            await this.humanClick(targetElement, this.page, '重试目录选择');
                            await this.page.waitForTimeout(2000);
                        }
                    }
                } else {
                    if (attempt < maxRetries) {
                        await this.page.waitForTimeout(2000);
                    }
                }

            } catch (error) {
                console.warn(`⚠️ 目录选择确认第 ${attempt} 次失败: ${error.message}`);
                if (attempt < maxRetries) {
                    await this.page.waitForTimeout(2000);
                }
            }
        }

        console.error(`❌ 目录选择确认失败 (${maxRetries} 次重试)`);
        return false;
    }

    async getMaxPageFromPagination(page) {
        try {
            // 等待分页组件加载
            const pagination = page.locator('ul.ant-pagination');
            await pagination.waitFor({ state: 'visible', timeout: 30000 });

            // 查找所有有title属性的li元素
            const titleElements = await pagination.locator('li[title]').all();

            let maxPage = 1;
            for (const element of titleElements) {
                const titleValue = await element.getAttribute('title');
                if (titleValue) {
                    // 检查title值是否为纯数字
                    const pageNumber = parseInt(titleValue.trim());
                    if (!isNaN(pageNumber) && pageNumber > maxPage) {
                        maxPage = pageNumber;
                    }
                }
            }

            console.log(`📄 最大页数: ${maxPage}`);
            return maxPage;

        } catch (error) {
            console.error('❌ 获取最大页数失败:', error.message);
            return 1000; // 失败时使用默认值
        }
    }

    getMissingPages() {
        try {
            // 检查目录下的文件，找出缺失的页面
            if (!fs.existsSync(this.outputDir)) {
                console.log(`📁 输出目录不存在，所有页面都需要爬取`);
                return [];
            }

            const files = fs.readdirSync(this.outputDir);
            const existingPages = new Set();

            // 解析已存在的页面文件
            files.forEach(file => {
                const match = file.match(/^(\d+)\.json$/);
                if (match) {
                    existingPages.add(parseInt(match[1]));
                }
            });

            console.log(`📋 找到 ${existingPages.size} 个已存在的页面文件`);
            return existingPages;

        } catch (error) {
            console.error('❌ 检查已存在页面时出错:', error.message);
            return new Set();
        }
    }

    async crawlPage(pageNum) {
        try {
            console.log(`🚀 开始爬取第 ${pageNum} 页...`);

            // 检查页面文件是否已存在
            const filepath = path.join(this.outputDir, `${pageNum}.json`);
            if (fs.existsSync(filepath)) {
                console.log(`⏭️ 第 ${pageNum} 页已存在，跳过`);
                return { success: true, hasData: true, count: 0, skipped: true };
            }

            if (!this.page) {
                throw new Error('浏览器实例未初始化');
            }

            // 检查token是否已过期
            if (this.tokenExpired) {
                console.log(`🚫 第 ${pageNum} 页检测到token已过期，需要切换token`);
                return { success: false, hasData: false, count: 0, error: 'Token expired', needSwitchToken: true };
            }

            // 在进行任何操作前执行智能延迟
            await this.intelligentDelay();

            // 只在第一页时设置教材版本、册次和目录（点击catalog后会触发第1页API请求）
            if (pageNum === 1 || !this.isSettingsConfigured) {
                console.log(`🔗 正在导航到: ${this.config.baseURL}`);

                // Set up early 403 detection before navigation
                const detection = this.setupEarly403Detection(this.page);
                this.page.on('response', detection.listener);

                try {
                    await this.page.goto(this.config.baseURL, {
                        waitUntil: 'networkidle',
                        timeout: this.config.pageTimeout
                    });

                    // Check for early 403 detection after page load
                    if (detection.hasError()) {
                        detection.cleanup();
                        console.log(`🚫 页面导航时检测到token失效: ${detection.getError()}`);
                        return { success: false, hasData: false, count: 0, error: `导航时token验证失败: ${detection.getError()}`, needSwitchToken: true };
                    }

                    // Keep listener active for continued monitoring

                } catch (error) {
                    // Remove listener on error
                    detection.cleanup();
                    throw error;
                }

                // 检测页面加载状态
                const loadingCheck = await this.checkLoadingState(this.page);
                if (loadingCheck.needAction) {
                    const timeoutResult = await this.handleLoadingTimeout(this.page, `第${pageNum}页导航后`);
                    if (timeoutResult.needSwitchToken) {
                        console.log(`🚫 第 ${pageNum} 页加载超时，需要切换token: ${timeoutResult.reason}`);
                        return { success: false, hasData: false, count: 0, error: 'Loading timeout', needSwitchToken: true };
                    } else if (timeoutResult.needReconfigure) {
                        // 页面刷新成功，重置配置状态
                        this.isSettingsConfigured = false;
                    }
                }

                // 模拟人类行为
                if (this.config.behaviorSimulation) {
                    await this.simulateMouseBehavior(this.page);
                    await this.simulateScrollBehavior(this.page);
                }

                // 检查页面关键元素，如果失败则尝试刷新页面
                const elementCheckResult = await this.checkPageElementsWithRetry(this.page, pageNum, 3);
                if (!elementCheckResult.valid) {
                    if (elementCheckResult.needSwitchToken) {
                        console.log(`🚫 第 ${pageNum} 页多次重试后仍检测到token失效: ${elementCheckResult.reason}`);
                        return { success: false, hasData: false, count: 0, error: 'Token expired', needSwitchToken: true };
                    } else {
                        console.log(`❌ 第 ${pageNum} 页页面元素检查失败: ${elementCheckResult.reason}`);
                        return { success: false, hasData: false, count: 0, error: 'Page elements check failed' };
                    }
                }

                await this.clickSettingElements(this.page);
                this.isSettingsConfigured = true;
                console.log(`✅ 教材版本、册次和目录设置完成`);
            }

            let updatedMaxPages = null;
            if (pageNum > 1) {
                // 跳转前模拟一些人类行为
                if (this.config.behaviorSimulation) {
                    await this.simulateMouseBehavior(this.page);
                    if (Math.random() < 0.3) { // 30% 的概率滚动
                        await this.simulateScrollBehavior(this.page);
                    }
                }
                
                updatedMaxPages = await this.handlePagination(this.page, pageNum);
            }

            // 等待API响应捕获完成
            console.log(`⏳ 等待第${pageNum}页API响应...`);
            const apiResponse = await this.waitForCapturedResponse(pageNum);

            // 检查是否超时
            if (apiResponse.timeout) {
                console.log(`⏰ 第 ${pageNum} 页响应超时`);
                return { success: false, hasData: false, count: 0, error: 'Response timeout' };
            }

            // 在API响应后再次检测页面加载状态
            const finalLoadingCheck = await this.checkLoadingState(this.page, 3000); // 3秒快速检查
            if (finalLoadingCheck.needAction) {
                console.log(`⚠️ 第 ${pageNum} 页API响应后仍在加载，可能有问题`);
                const timeoutResult = await this.handleLoadingTimeout(this.page, `第${pageNum}页API响应后`);
                if (timeoutResult.needSwitchToken) {
                    return { success: false, hasData: false, count: 0, error: 'Post-API loading timeout', needSwitchToken: true };
                } else if (timeoutResult.needReconfigure) {
                    return { success: false, hasData: false, count: 0, error: 'Post-API page refresh', needReconfigure: true };
                }
            }

            // 检查是否token被封禁
            if (apiResponse.tokenBanned) {
                console.log(`🚫 第 ${pageNum} 页检测到token被封禁，需要切换token`);
                return { success: false, hasData: false, count: 0, error: 'Token banned', needSwitchToken: true };
            }

            // 检查页面是否显示"没有查询到您想要的内容"
            const noContentCheck = await this.checkNoContentFound(this.page);
            if (noContentCheck.noContentFound) {
                console.log(`🏁 第 ${pageNum} 页检测到"没有查询到您想要的内容"，当前参数组合已完成`);
                return { 
                    success: true, 
                    hasData: false, 
                    count: 0, 
                    noContentFound: true,
                    message: noContentCheck.message,
                    reason: noContentCheck.reason
                };
            }

            if (apiResponse.data && apiResponse.data.data && apiResponse.data.data.list) {
                const list = apiResponse.data.data.list;

                if (list.length > 0) {
                    // 检查requestData中的treeIds是否为空数组，如果是则不保存
                    if (apiResponse.requestData && 
                        apiResponse.requestData.params && 
                        Array.isArray(apiResponse.requestData.params.treeIds) && 
                        apiResponse.requestData.params.treeIds.length === 0) {
                        console.log(`⏭️ 第 ${pageNum} 页treeIds为空数组，跳过保存`);
                        return { success: true, hasData: false, count: 0, skipped: true };
                    }

                    // 使用API响应中的真实pageNum
                    const realPageNum = apiResponse.data.data.pageNum + 1; // API返回的pageNum从0开始，文件名从1开始
                    
                    await this.savePageData(realPageNum, list, apiResponse.data, apiResponse.requestData);

                    console.log(`💾 第 ${pageNum} 页保存成功 (真实页码: ${realPageNum}, ${list.length} 条记录)`);
                    return { success: true, hasData: true, count: list.length, updatedMaxPages: updatedMaxPages };
                } else {
                    console.log(`⚠️ 第 ${pageNum} 页没有数据`);
                    return { success: true, hasData: false, count: 0 };
                }
            } else {
                console.log(`⚠️ 第 ${pageNum} 页响应格式异常`);
                return { success: false, hasData: false, count: 0, error: 'Invalid response format' };
            }

        } catch (error) {
            console.error(`❌ 爬取第 ${pageNum} 页失败:`, error.message);
            return { success: false, hasData: false, count: 0, error: error.message };
        }
    }

    async savePageData(pageNum, list, fullResponse, requestData = null) {
        const filename = `${pageNum}.json`;
        const filepath = path.join(this.outputDir, filename);

        // 验证当前目录路径与参数组合是否匹配
        const pathValid = this.validateDirectoryPath(this.outputDir, this.currentCombination);
        if (!pathValid) {
            console.warn(`⚠️ 保存第 ${pageNum} 页时，目录路径与参数组合不匹配`);
            console.warn(`   目录: ${this.outputDir}`);
            console.warn(`   组合: ${this.currentCombination.studyPhaseName}/${this.currentCombination.subjectName}/${this.currentCombination.textbookVersionName}/${this.currentCombination.ceciName}/${this.currentCombination.catalogName}`);
        }

        // 生成路径信息用于验证和调试
        const pathInfo = this.generateOutputPath(this.currentCombination);

        const jsonData = {
            fullResponse: fullResponse,
            requestData: requestData, // 保存请求参数用于验证
            crawlInfo: {
                pageNum: pageNum, // 这个pageNum现在是基于API响应的真实页码
                timestamp: new Date().toISOString(),
                combination: this.currentCombination,
                recordCount: list.length,
                // 添加路径信息，便于后续验证和调试
                pathInfo: {
                    encodedPath: pathInfo.encodedPath,
                    originalPath: pathInfo.originalPath,
                    pathComponents: pathInfo.pathComponents
                }
            }
        };

        return new Promise((resolve, reject) => {
            fs.writeFile(filepath, JSON.stringify(jsonData, null, 2), 'utf8', (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    }

    async handlePagination(page, targetPage) {
        try {
            console.log(`📄 跳转到第 ${targetPage} 页`);

            // 如果不在题目页面，先导航到题目页面
            if (!page.url().includes(this.config.baseURL)) {
                await page.goto(this.config.baseURL, {
                    waitUntil: 'networkidle',
                    timeout: this.config.pageTimeout
                });
                
                const elementCheckResult = await this.checkPageElementsWithRetry(page, 'pagination', 2);
                if (!elementCheckResult.valid) {
                    await this.saveDebugScreenshot(page, 'pagination_element_check_failed', elementCheckResult.reason);
                    if (elementCheckResult.needSwitchToken) {
                        throw new Error(`Token已失效: ${elementCheckResult.reason}`);
                    } else {
                        throw new Error(`页面元素检查失败，可能网络问题: ${elementCheckResult.reason}`);
                    }
                }
            }

            const pageInput = page.locator('input.pagination_jump_page_input');
            try {
                await pageInput.waitFor({ state: 'visible', timeout: 30000 });
            } catch (error) {
                await this.saveDebugScreenshot(page, 'pagination_input_not_found');
                throw new Error('分页输入框未找到');
            }

            // 模拟页面跳转前的行为
            await this.humanBehavior.beforePageNavigation(page);

            // 使用模拟人为输入
            await this.humanType(pageInput, targetPage.toString(), page, '分页输入');

            // 模拟按回车键前的短暂停顿
            await page.waitForTimeout(this.humanBehavior.randomDelay(200, 500));
            await page.keyboard.press('Enter');

            // 等待页面加载，使用随机延迟
            await page.waitForTimeout(this.humanBehavior.randomDelay(1500, 2500));
            await page.waitForLoadState('networkidle');

            // 检测页面加载状态
            const loadingCheck = await this.checkLoadingState(page);
            if (loadingCheck.needAction) {
                const timeoutResult = await this.handleLoadingTimeout(page, `页面跳转到${targetPage}`);
                if (timeoutResult.needSwitchToken) {
                    throw new Error(`页面跳转加载超时，需要切换token: ${timeoutResult.reason}`);
                } else if (timeoutResult.needReconfigure) {
                    // 页面刷新后需要重新配置，但由于在分页操作中，直接抛出错误让上层处理
                    throw new Error(`页面跳转后刷新，需要重新配置参数`);
                }
            }

            // 验证跳转结果
            const jumpResult = await this.verifyPageJump(page, targetPage);
            if (!jumpResult.success) {
                const failureReason = jumpResult.reason || '未知原因';
                console.log(`❌ 页面跳转验证失败: ${failureReason}`);
                
                if (jumpResult.tokenExpired) {
                    throw new Error(`页面跳转验证失败，token已失效: ${failureReason}`);
                }
                throw new Error(`页面跳转验证失败: ${failureReason}`);
            }

            // 跳转完成后的行为模拟
            if (this.config.behaviorSimulation) {
                await this.simulateMouseBehavior(page);
                if (Math.random() < 0.4) {
                    await this.simulateScrollBehavior(page);
                }
            }
            
            await this.randomDelay();
            
            return jumpResult.maxPages;

        } catch (error) {
            console.error(`❌ 页面跳转失败 (第${targetPage}页):`, error.message);
            throw error;
        }
    }

    async verifyPageJump(page, targetPage) {
        console.log(`🔍 开始验证页面跳转到第 ${targetPage} 页`);
        
        try {
            // 第一步：检测页面加载状态
            console.log(`📍 步骤1: 检测页面加载状态...`);
            const loadingCheck = await this.checkLoadingState(page, 5000);
            if (loadingCheck.needAction) {
                console.log(`⚠️ 页面跳转后仍在加载状态，可能卡顿`);
                await this.saveDebugScreenshot(page, 'page_jump_loading_stuck', `target_${targetPage}`);
                
                const timeoutResult = await this.handleLoadingTimeout(page, `页面跳转验证-第${targetPage}页`);
                if (timeoutResult.needSwitchToken) {
                    console.log(`🚫 页面跳转验证时检测到token问题: ${timeoutResult.reason}`);
                    return { success: false, maxPages: null, tokenExpired: true, reason: timeoutResult.reason };
                }
                // 如果刷新成功，继续验证
            }

            // 第二步：检查页面关键元素
            console.log(`📍 步骤2: 检查页面关键元素...`);
            const elementCheckResult = await this.checkPageElementsWithRetry(page, 'jump_verify', 2);
            if (!elementCheckResult.valid) {
                console.log(`❌ 页面关键元素检查失败: ${elementCheckResult.reason}`);
                await this.saveDebugScreenshot(page, 'page_jump_elements_failed', `target_${targetPage}`);
                
                // 进一步分析失败原因
                const tokenCheck = await this.checkTokenStatus(page);
                if (tokenCheck.isTokenBanned) {
                    console.log(`🚫 页面跳转验证检测到token被封禁: ${tokenCheck.reason}`);
                    return { 
                        success: false, 
                        maxPages: null, 
                        tokenExpired: true, 
                        reason: `元素检查失败 + token封禁: ${tokenCheck.reason}` 
                    };
                }
                
                return { 
                    success: false, 
                    maxPages: null, 
                    tokenExpired: elementCheckResult.needSwitchToken || false,
                    reason: `页面关键元素检查失败: ${elementCheckResult.reason}`
                };
            }
            console.log(`✅ 页面关键元素检查通过`);

            // 第三步：检查分页输入框
            console.log(`📍 步骤3: 检查分页输入框...`);
            const pageInput = page.locator('input.pagination_jump_page_input');
            
            try {
                await pageInput.waitFor({ state: 'visible', timeout: 10000 });
                console.log(`✅ 分页输入框已找到`);
            } catch (inputError) {
                console.log(`❌ 分页输入框未找到: ${inputError.message}`);
                await this.saveDebugScreenshot(page, 'page_jump_input_missing', `target_${targetPage}`);
                
                // 检查是否是token问题
                const tokenCheck = await this.checkTokenStatus(page);
                if (tokenCheck.isTokenBanned) {
                    console.log(`🚫 输入框缺失且token被封禁: ${tokenCheck.reason}`);
                    return { 
                        success: false, 
                        maxPages: null, 
                        tokenExpired: true, 
                        reason: `分页输入框缺失 + token封禁: ${tokenCheck.reason}` 
                    };
                }
                
                return { 
                    success: false, 
                    maxPages: null, 
                    reason: `分页输入框未找到: ${inputError.message}` 
                };
            }

            // 第四步：验证输入框的值
            console.log(`📍 步骤4: 验证输入框值...`);
            let inputValue;
            try {
                inputValue = await pageInput.inputValue();
                console.log(`📝 输入框当前值: "${inputValue}"`);
            } catch (valueError) {
                console.log(`❌ 无法获取输入框值: ${valueError.message}`);
                await this.saveDebugScreenshot(page, 'page_jump_input_value_error', `target_${targetPage}`);
                return { 
                    success: false, 
                    maxPages: null, 
                    reason: `无法获取输入框值: ${valueError.message}` 
                };
            }
            
            if (inputValue.trim() !== targetPage.toString()) {
                console.log(`❌ 输入框值验证失败: 期望 "${targetPage}", 实际 "${inputValue}"`);
                await this.saveDebugScreenshot(page, 'page_jump_input_mismatch', `expect_${targetPage}_actual_${inputValue}`);
                
                // 检查是否是页面加载问题
                const loadingRecheck = await this.checkLoadingState(page, 3000);
                if (loadingRecheck.needAction) {
                    console.log(`⚠️ 输入框值不匹配且页面仍在加载，可能是加载延迟`);
                    return { 
                        success: false, 
                        maxPages: null, 
                        reason: `输入框值不匹配且页面仍在加载` 
                    };
                }
                
                return { 
                    success: false, 
                    maxPages: null, 
                    reason: `输入框值不匹配: 期望${targetPage}, 实际${inputValue}` 
                };
            }
            console.log(`✅ 输入框值验证通过`);

            // 第五步：检查激活的分页项
            console.log(`📍 步骤5: 检查激活的分页项...`);
            const activePage = page.locator('.ant-pagination-item-active');
            let activePageText;
            
            try {
                await activePage.waitFor({ state: 'visible', timeout: 10000 });
                activePageText = await activePage.textContent();
                console.log(`📄 激活页面显示: "${activePageText}"`);
            } catch (activeError) {
                console.log(`❌ 激活分页项未找到: ${activeError.message}`);
                await this.saveDebugScreenshot(page, 'page_jump_active_missing', `target_${targetPage}`);
                
                // 检查是否是token问题
                const tokenCheck = await this.checkTokenStatus(page);
                if (tokenCheck.isTokenBanned) {
                    console.log(`🚫 激活分页项缺失且token被封禁: ${tokenCheck.reason}`);
                    return { 
                        success: false, 
                        maxPages: null, 
                        tokenExpired: true, 
                        reason: `激活分页项缺失 + token封禁: ${tokenCheck.reason}` 
                    };
                }
                
                return { 
                    success: false, 
                    maxPages: null, 
                    reason: `激活分页项未找到: ${activeError.message}` 
                };
            }
            
            if (activePageText && activePageText.trim() !== targetPage.toString()) {
                console.log(`❌ 激活页面验证失败: 期望 "${targetPage}", 实际 "${activePageText.trim()}"`);
                await this.saveDebugScreenshot(page, 'page_jump_active_mismatch', `expect_${targetPage}_actual_${activePageText.trim()}`);
                return { 
                    success: false, 
                    maxPages: null, 
                    reason: `激活页面不匹配: 期望${targetPage}, 实际${activePageText.trim()}` 
                };
            }
            console.log(`✅ 激活页面验证通过`);

            // 第六步：获取最新的最大页数
            console.log(`📍 步骤6: 获取最新的最大页数...`);
            let maxPages = null;
            try {
                maxPages = await this.getMaxPageFromPagination(page);
                console.log(`📄 成功获取最大页数: ${maxPages}`);
            } catch (error) {
                console.warn(`⚠️ 获取最大页数失败: ${error.message}`);
                // 获取最大页数失败不影响验证结果，只是警告
            }

            console.log(`✅ 页面跳转验证完全成功 (目标页: ${targetPage})`);
            return { success: true, maxPages: maxPages };

        } catch (error) {
            console.error(`❌ 页面跳转验证过程中发生异常: ${error.message}`);
            await this.saveDebugScreenshot(page, 'page_jump_exception', `target_${targetPage}`);
            
            // 检查异常是否与token相关
            const tokenCheck = await this.checkTokenStatus(page);
            if (tokenCheck.isTokenBanned) {
                console.log(`🚫 页面跳转验证异常且检测到token问题: ${tokenCheck.reason}`);
                return { 
                    success: false, 
                    maxPages: null, 
                    tokenExpired: true, 
                    reason: `验证异常 + token问题: ${error.message}` 
                };
            }
            
            return { 
                success: false, 
                maxPages: null, 
                reason: `验证过程异常: ${error.message}` 
            };
        }
    }

    async randomDelay() {
        // 使用更智能的延迟策略
        if (this.config.enableAntiBot) {
            await this.intelligentDelay();
        } else {
            // 保持原有的随机延迟逻辑
            const delay = Math.floor(Math.random() * (this.config.maxDelay - this.config.minDelay + 1)) + this.config.minDelay;
            console.log(`⏱️ 等待 ${delay}ms...`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    // 处理单个任务的方法（新的核心方法）
    async processSingleTask(combination) {
        const startTime = Date.now();
        console.log(`\n🎯 [${this.config.instanceId}] 开始处理任务: ${combination.studyPhaseName}/${combination.subjectName}/${combination.textbookVersionName}/${combination.ceciName}/${combination.catalogName}`);

        try {
            // 设置当前任务
            this.setCurrentCombination(combination);

            // 创建浏览器实例（内部会自动获取token）
            await this.createBrowserAndPage();
            console.log(`✅ 浏览器实例创建成功，开始爬取页面`);

            // 获取最大页数（默认值）
            let maxPages = this.config.maxPages;
            let consecutiveEmptyPages = 0;
            const maxConsecutiveEmptyPages = 3;

            // 爬取页面
            let currentPage = this.config.startPage;

            while (currentPage <= maxPages) {
                try {
                    const result = await this.crawlPage(currentPage);

                    if (result.success) {
                        // 检查是否检测到"没有查询到您想要的内容"
                        if (result.noContentFound) {
                            console.log(`🏁 第 ${currentPage} 页检测到"没有查询到您想要的内容"，任务已完成`);
                            console.log(`💬 提示信息: ${result.message || '没有查询到您想要的内容'}`);
                            break;
                        }

                        // 如果是第一页且没有检测到"没有查询到您想要的内容"，获取实际的最大页数
                        if (currentPage === 1 && maxPages === this.config.maxPages) {
                            try {
                                maxPages = await this.getMaxPageFromPagination(this.page);
                                console.log(`📄 更新最大页数为: ${maxPages}`);
                            } catch (error) {
                                console.warn(`⚠️ 获取最大页数失败，使用默认值: ${error.message}`);
                            }
                        }

                        // 如果页面跳转后获得了新的最大页数，更新maxPages
                        if (result.updatedMaxPages && result.updatedMaxPages > maxPages) {
                            console.log(`📄 更新最大页数从 ${maxPages} 到 ${result.updatedMaxPages}`);
                            maxPages = result.updatedMaxPages;
                        }

                        if (result.hasData && result.count > 0) {
                            consecutiveEmptyPages = 0;
                            console.log(`✅ 第 ${currentPage} 页处理完成，获得 ${result.count} 条记录`);
                            await this.randomDelay();
                        } else if (!result.skipped) {
                            consecutiveEmptyPages++;
                            console.log(`⚠️ 第 ${currentPage} 页无数据`);
                        }

                        if (consecutiveEmptyPages >= maxConsecutiveEmptyPages) {
                            console.log(`📄 连续 ${maxConsecutiveEmptyPages} 页无数据，任务已完成`);
                            break;
                        }
                    } else {
                        if (result.needSwitchToken) {
                            console.log(`🚫 检测到token问题，尝试重新创建浏览器...`);
                            await this.cleanupBrowser();
                            await this.createBrowserAndPage();
                            this.isSettingsConfigured = false; // 重置设置状态
                            console.log(`✅ 新浏览器实例创建成功，重新尝试当前页面`);
                            continue;
                        } else if (result.needReconfigure) {
                            console.log(`🔄 检测到需要重新配置，重置配置状态...`);
                            this.isSettingsConfigured = false;
                            continue; // 重新尝试当前页面
                        } else {
                            console.error(`❌ 第 ${currentPage} 页处理失败: ${result.error}`);
                            throw new Error(`页面处理失败: ${result.error}`);
                        }
                    }
                } catch (error) {
                    console.error(`❌ 爬取第 ${currentPage} 页时出错: ${error.message}`);
                    
                    // 检查是否是需要重新配置的错误
                    if (error.message.includes('需要重新配置参数')) {
                        console.log(`🔄 检测到需要重新配置参数，重置配置状态...`);
                        this.isSettingsConfigured = false;
                        continue; // 重新尝试当前页面
                    }
                    
                    // 对于token相关错误，尝试重新创建浏览器
                    if (this.isTokenRelatedError(error)) {
                        console.log(`🚫 检测到token相关错误，尝试重新创建浏览器...`);
                        await this.cleanupBrowser();
                        await this.createBrowserAndPage();
                        this.isSettingsConfigured = false;
                        continue;
                    } else {
                        throw error; // 其他错误直接抛出
                    }
                }

                currentPage++;

                // 检查是否达到最大页数
                if (currentPage > maxPages) {
                    console.log(`📄 已达到最大页数 ${maxPages}，任务完成`);
                    break;
                }
            }

            const endTime = Date.now();
            const duration = (endTime - startTime) / 1000;
            console.log(`✅ 任务处理完成，耗时: ${duration.toFixed(2)} 秒`);
            
            // 标记任务完成
            await this.stateManager.updateCombinationStatus(combination, 'completed', {
                totalPages: currentPage - 1,
                maxPages: maxPages,
                duration: duration
            });

        } catch (error) {
            console.error(`❌ 任务处理失败: ${error.message}`);
            
            // 标记任务失败
            await this.stateManager.updateCombinationStatus(combination, 'failed', {
                error: error.message,
                timestamp: new Date().toISOString()
            });
            
            throw error;
        } finally {
            // 确保清理浏览器资源和释放Token
            await this.cleanupBrowser();
            await this.ensureTokenReleased();
        }
    }

    // 确保Token被释放的方法
    async ensureTokenReleased() {
        if (this.tokenManager && this.tokenManager.currentTokenId) {
            try {
                console.log(`🔄 [${this.config.instanceId}] 确保释放Token[${this.tokenManager.currentTokenId}]...`);
                await this.tokenManager.releaseToken(this.tokenManager.currentTokenId);
                console.log(`✅ [${this.config.instanceId}] Token已确认释放`);
            } catch (error) {
                console.warn(`⚠️ [${this.config.instanceId}] 异步释放Token失败，尝试强制释放: ${error.message}`);
                // 如果异步释放失败，使用同步强制释放
                this.tokenManager.forceReleaseCurrentToken();
            }
        }
    }

    // 判断是否是token相关错误
    isTokenRelatedError(error) {
        const tokenErrorKeywords = [
            '403', '认证', 'auth', 'Need switch token',
            'Token expired', 'Token已失效', '页面元素检查失败',
            '缺失关键元素', '页面显示错误元素', 'login',
            'Loading timeout', '加载超时', 'token被封禁',
            '需要切换token'
        ];
        
        return tokenErrorKeywords.some(keyword => 
            error.message.includes(keyword)
        );
    }

    // 主启动方法 - 持续从StateManager获取任务
    async start() {
        const instanceId = this.config.instanceId;
        console.log(`🚀 [${instanceId}] 爬虫实例启动...`);

        const globalStartTime = Date.now();
        let tasksProcessed = 0;
        let tasksCompleted = 0;
        let tasksFailed = 0;

        try {
            while (true) {
                // 从StateManager获取下一个待处理的任务
                const nextCombination = await this.stateManager.getNextCombination();
                
                if (!nextCombination) {
                    console.log(`🎉 [${instanceId}] 没有更多待处理的任务，实例结束`);
                    break;
                }

                tasksProcessed++;
                
                try {
                    await this.processSingleTask(nextCombination);
                    tasksCompleted++;
                } catch (error) {
                    console.error(`❌ [${instanceId}] 任务处理失败: ${error.message}`);
                    tasksFailed++;
                    
                    // 等待一段时间后继续处理下一个任务
                    await new Promise(resolve => setTimeout(resolve, 5000));
                }
            }

            const globalEndTime = Date.now();
            const totalDuration = (globalEndTime - globalStartTime) / 1000;

            console.log(`\n🎉 [${instanceId}] 实例处理完成!`);
            console.log(`📊 [${instanceId}] 统计信息:`);
            console.log(`   处理任务总数: ${tasksProcessed}`);
            console.log(`   成功完成: ${tasksCompleted}`);
            console.log(`   失败: ${tasksFailed}`);
            console.log(`   总耗时: ${totalDuration.toFixed(2)} 秒`);

        } catch (error) {
            console.error(`❌ [${instanceId}] 实例运行过程中发生错误:`, error.message);
        } finally {
            // 确保在任何情况下都清理资源
            await this.stop();
        }
    }

    async stop() {
        if (this.isExiting) return; // 防止重复执行
        this.isExiting = true;
        
        console.log(`🛑 [${this.config.instanceId}] 正在停止爬虫实例...`);

        try {
            // 先确保释放Token
            await this.ensureTokenReleased();
            
            // 清理浏览器资源
            await this.cleanupBrowser();
            console.log('🔧 已清理浏览器资源');
        } catch (cleanupError) {
            console.warn(`⚠️ 清理浏览器资源时出错: ${cleanupError.message}`);
        }

        // 销毁代理池
        if (this.proxyPool) {
            try {
                this.proxyPool.destroy();
                this.proxyPool = null;
            } catch (error) {
                console.warn(`⚠️ 销毁代理池时出错: ${error.message}`);
            }
        }

        // 销毁TokenManager（会自动释放token和停止心跳）
        if (this.tokenManager) {
            try {
                await this.tokenManager.destroy();
            } catch (error) {
                console.warn(`⚠️ [${this.config.instanceId}] 销毁TokenManager时出错: ${error.message}`);
                // 如果异步销毁失败，尝试强制释放
                this.forceReleaseToken();
            }
            this.tokenManager = null;
        }

        console.log(`✅ [${this.config.instanceId}] 爬虫实例已停止`);
    }

    // 移除原有的saveTokenState方法，改用TokenManager

    // 启动代理健康检查
    startProxyHealthCheck() {
        if (!this.currentProxy || this.proxyHealthTimer) {
            return;
        }

        console.log('🩺 启动代理健康检查');
        
        this.proxyHealthTimer = setInterval(async () => {
            await this.checkProxyHealth();
        }, 30000); // 每10秒检查一次
    }

    // 停止代理健康检查
    stopProxyHealthCheck() {
        if (this.proxyHealthTimer) {
            clearInterval(this.proxyHealthTimer);
            this.proxyHealthTimer = null;
        }
    }

    // 检查代理健康状态 - 在Node.js端进行，不依赖浏览器实例
    async checkProxyHealth() {
        if (!this.currentProxy) {
            return;
        }

        try {
            const isHealthy = await this.testProxyWithNodeJS(this.currentProxy);
            
            if (!isHealthy) {
                throw new Error('代理无法正常响应');
            }

            // 仅在检测失败后恢复时输出日志
            // console.log(`✅ 代理健康检查通过`);

        } catch (error) {
            console.log(`❌ 代理健康检查失败: ${error.message}`);
            
            // 安全地调用代理切换
            try {
                await this.switchProxy();
            } catch (switchError) {
                console.error(`❌ 代理切换过程中出错: ${switchError.message}`);
                // 如果代理切换失败，停止健康检查避免无限循环
                this.stopProxyHealthCheck();
            }
        }
    }

    // 使用Node.js原生HTTP模块测试代理
    async testProxyWithNodeJS(proxyConfig) {
        return new Promise((resolve) => {
            try {
                const testUrl = 'https://httpbin.org/ip';
                const url = new URL(testUrl);
                const proxyUrl = new URL(proxyConfig.server);
                
                const options = {
                    hostname: proxyUrl.hostname,
                    port: proxyUrl.port || (proxyUrl.protocol === 'https:' ? 443 : 80),
                    method: 'CONNECT',
                    path: `${url.hostname}:${url.port || 443}`,
                    timeout: 8000
                };

                // 如果代理需要认证
                if (proxyConfig.username && proxyConfig.password) {
                    const auth = Buffer.from(`${proxyConfig.username}:${proxyConfig.password}`).toString('base64');
                    options.headers = {
                        'Proxy-Authorization': `Basic ${auth}`
                    };
                }

                const req = http.request(options);
                
                req.on('connect', (res, socket, head) => {
                    if (res.statusCode === 200) {
                        // 代理连接成功，发送HTTPS请求
                        const httpsOptions = {
                            socket: socket,
                            path: url.pathname,
                            method: 'GET',
                            headers: {
                                'Host': url.hostname,
                                'User-Agent': 'Mozilla/5.0 (compatible; ProxyChecker/1.0)'
                            }
                        };

                        const httpsReq = https.request(httpsOptions, (httpsRes) => {
                            let data = '';
                            
                            httpsRes.on('data', (chunk) => {
                                data += chunk;
                            });
                            
                            httpsRes.on('end', () => {
                                try {
                                    const responseData = JSON.parse(data);
                                    if (responseData && responseData.origin) {
                                        resolve(true);
                                    } else {
                                        resolve(false);
                                    }
                                } catch (parseError) {
                                    resolve(false);
                                }
                            });
                        });

                        httpsReq.on('error', () => {
                            resolve(false);
                        });

                        httpsReq.on('timeout', () => {
                            resolve(false);
                        });

                        httpsReq.setTimeout(8000);
                        httpsReq.end();
                    } else {
                        resolve(false);
                    }
                });

                req.on('error', () => {
                    resolve(false);
                });

                req.on('timeout', () => {
                    resolve(false);
                });

                req.setTimeout(8000);
                req.end();

            } catch (error) {
                resolve(false);
            }
        });
    }

    // 更换代理
    async switchProxy() {
        if (!this.currentTokenData) {
            console.log('⚠️ 无当前token数据，无法更换代理');
            return;
        }

        // 检查浏览器实例是否存在
        if (!this.browser) {
            console.log('⚠️ 浏览器实例不存在，尝试重新创建');
            await this.recreateBrowserInstance();
            return;
        }

        try {
            console.log('🔄 代理失效，正在更换代理...');
            
            // 停止健康检查
            this.stopProxyHealthCheck();

            // 释放当前代理（如果有）
            if (this.proxyPool && this.currentTokenData) {
                this.proxyPool.releaseProxyFromToken(this.currentTokenData.id);
                console.log(`🔓 已释放Token[${this.currentTokenData.id}]的旧代理`);
            }

            // 获取新代理 - 这里已经包含了Node.js端的验证
            const newProxyConfig = await this.assignProxyToToken(this.currentTokenData);
            
            if (!newProxyConfig || 
                (this.currentProxy && 
                 newProxyConfig.server === this.currentProxy.server)) {
                console.log('⚠️ 无法获取有效的新代理');
                return;
            }

            // 保存当前状态
            const isSettingsConfigured = this.isSettingsConfigured;
            
            // 关闭当前context
            if (this.context) {
                await this.context.close();
                this.context = null;
            }

            // 再次检查浏览器实例是否仍然存在
            if (!this.browser) {
                console.log('⚠️ 浏览器实例在context关闭后丢失，重新创建整个实例');
                await this.recreateBrowserInstance();
                return;
            }

            // 创建新的context with新代理
            this.context = await this.browser.newContext({
                userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
                viewport: { width: 1280, height: 720 },
                ignoreHTTPSErrors: true,
                proxy: newProxyConfig
            });

            // 注入关键cookie
            await this.context.addCookies([
                {
                    name: 'ACCESS_TOKEN',
                    value: this.currentTokenData.token,
                    domain: '.stzy.com',
                    path: '/',
                    httpOnly: false,
                    secure: true,
                    sameSite: 'Lax'
                },
                {
                    name: 'selectInfo',
                    value: JSON.stringify(this.createSelectInfo()),
                    domain: '.stzy.com',
                    path: '/',
                    httpOnly: false,
                    secure: true,
                    sameSite: 'Lax'
                }
            ]);

            // 创建新页面
            this.page = await this.context.newPage();
            this.page.setDefaultTimeout(this.config.pageTimeout);
            this.page.setDefaultNavigationTimeout(this.config.pageTimeout);

            // 更新当前代理
            this.currentProxy = newProxyConfig;
            
            // 恢复设置状态
            this.isSettingsConfigured = isSettingsConfigured;

            console.log(`✅ 代理更换成功: ${newProxyConfig.server}`);
            
            // 重新启动健康检查
            this.startProxyHealthCheck();

        } catch (error) {
            console.error(`❌ 更换代理失败: ${error.message}`);
            // 如果更换失败，尝试重新创建整个浏览器实例
            await this.recreateBrowserInstance();
        }
    }

    // 重新创建浏览器实例
    async recreateBrowserInstance() {
        try {
            console.log('🔄 重新创建浏览器实例...');
            
            // 检查当前token数据是否存在
            if (!this.currentTokenData) {
                console.error('❌ 当前token数据不存在，无法重新创建浏览器实例');
                throw new Error('Need switch token');
            }
            
            const tokenData = this.currentTokenData;
            const isSettingsConfigured = this.isSettingsConfigured;
            
            // 清理当前资源
            await this.cleanupBrowser();
            
            // 重新创建
            await this.createBrowserAndPage(tokenData);
            
            // 恢复设置状态
            this.isSettingsConfigured = isSettingsConfigured;
            
            console.log('✅ 浏览器实例重新创建成功');
            
        } catch (error) {
            console.error(`❌ 重新创建浏览器实例失败: ${error.message}`);
            // 标记需要切换token
            throw new Error('Need switch token');
        }
    }



    // 检测和修复现有目录结构的方法（工具方法）
    static async analyzeExistingDirectories(baseDir = './') {
        const results = {
            validDirectories: [],
            invalidDirectories: [],
            conflictingDirectories: [],
            recommendations: []
        };

        try {
            const entries = fs.readdirSync(baseDir, { withFileTypes: true });
            const directories = entries.filter(entry => entry.isDirectory());

            console.log(`🔍 分析 ${baseDir} 目录下的 ${directories.length} 个子目录...`);

            for (const dir of directories) {
                const dirPath = path.join(baseDir, dir.name);
                
                try {
                    // 创建临时实例用于路径解析
                    const tempCrawler = new PlaywrightCrawler();
                    const extractedInfo = tempCrawler.extractCombinationFromPath(dirPath);
                    
                    if (extractedInfo) {
                        // 验证提取的信息是否可以重新生成相同的路径
                        const regeneratedPath = tempCrawler.generateOutputPath(extractedInfo);
                        const normalizedOriginal = path.normalize(dirPath);
                        const normalizedRegenerated = path.normalize(regeneratedPath.encodedPath);
                        
                        if (normalizedOriginal === normalizedRegenerated) {
                            results.validDirectories.push({
                                path: dirPath,
                                info: extractedInfo,
                                originalPath: regeneratedPath.originalPath
                            });
                        } else {
                            results.invalidDirectories.push({
                                path: dirPath,
                                extractedInfo: extractedInfo,
                                expectedPath: regeneratedPath.encodedPath,
                                reason: 'Path regeneration mismatch'
                            });
                        }
                    } else {
                        results.invalidDirectories.push({
                            path: dirPath,
                            reason: 'Cannot extract combination info from path'
                        });
                    }
                } catch (error) {
                    results.invalidDirectories.push({
                        path: dirPath,
                        reason: `Analysis error: ${error.message}`
                    });
                }
            }

            // 检测冲突的目录（相同的原始路径映射到不同的编码路径）
            const pathGroups = {};
            results.validDirectories.forEach(item => {
                const originalPath = item.originalPath;
                if (!pathGroups[originalPath]) {
                    pathGroups[originalPath] = [];
                }
                pathGroups[originalPath].push(item);
            });

            Object.entries(pathGroups).forEach(([originalPath, items]) => {
                if (items.length > 1) {
                    results.conflictingDirectories.push({
                        originalPath: originalPath,
                        paths: items.map(item => item.path)
                    });
                }
            });

            // 生成建议
            if (results.invalidDirectories.length > 0) {
                results.recommendations.push(`发现 ${results.invalidDirectories.length} 个无效目录，建议手动检查或重命名`);
            }
            
            if (results.conflictingDirectories.length > 0) {
                results.recommendations.push(`发现 ${results.conflictingDirectories.length} 个冲突的目录组，建议合并或重新组织`);
            }
            
            if (results.validDirectories.length > 0) {
                results.recommendations.push(`${results.validDirectories.length} 个目录结构正常，可以正常使用`);
            }

            // 输出分析结果
            console.log('\n📊 目录结构分析结果:');
            console.log(`✅ 有效目录: ${results.validDirectories.length}`);
            console.log(`❌ 无效目录: ${results.invalidDirectories.length}`);
            console.log(`⚠️ 冲突目录组: ${results.conflictingDirectories.length}`);

            if (results.invalidDirectories.length > 0) {
                console.log('\n❌ 无效目录详情:');
                results.invalidDirectories.forEach((item, index) => {
                    console.log(`   ${index + 1}. ${item.path} - ${item.reason}`);
                });
            }

            if (results.conflictingDirectories.length > 0) {
                console.log('\n⚠️ 冲突目录详情:');
                results.conflictingDirectories.forEach((item, index) => {
                    console.log(`   ${index + 1}. 原始路径: ${item.originalPath}`);
                    console.log(`      对应的目录: ${item.paths.join(', ')}`);
                });
            }

            console.log('\n💡 建议:');
            results.recommendations.forEach((rec, index) => {
                console.log(`   ${index + 1}. ${rec}`);
            });

        } catch (error) {
            console.error(`❌ 分析目录结构时出错: ${error.message}`);
        }

        return results;
    }

    // 测试编码/解码功能的方法
    static testEncodingDecoding() {
        console.log('🧪 测试目录名编码/解码功能...\n');

        const testCases = [
            '正常名称',
            '包含<特殊>字符',
            '文件/路径:问题',
            'Windows|Unix*问题?',
            '带有"引号"的名称',
            '带有\\反斜杠的名称',
            '  前后有空格  ',
            '.隐藏文件夹',
            ''
        ];

        const crawler = new PlaywrightCrawler();
        let allPassed = true;

        testCases.forEach((testCase, index) => {
            console.log(`测试 ${index + 1}: "${testCase}"`);
            
            try {
                const encoded = crawler.encodeDirName(testCase);
                const decoded = crawler.decodeDirName(encoded);
                const trimmedOriginal = testCase ? testCase.trim() : 'unknown';
                
                console.log(`   编码后: "${encoded}"`);
                console.log(`   解码后: "${decoded}"`);
                
                if (decoded === trimmedOriginal || (testCase === '' && decoded === 'unknown')) {
                    console.log(`   ✅ 通过\n`);
                } else {
                    console.log(`   ❌ 失败 - 不匹配`);
                    console.log(`   期望: "${trimmedOriginal}"`);
                    console.log(`   实际: "${decoded}"\n`);
                    allPassed = false;
                }
            } catch (error) {
                console.log(`   ❌ 错误: ${error.message}\n`);
                allPassed = false;
            }
        });

        console.log(allPassed ? '🎉 所有测试通过!' : '❌ 存在测试失败');
        return allPassed;
    }
}

module.exports = PlaywrightCrawler;

// 运行单个爬虫实例
async function runSingleInstance(config = {}) {
    const instanceId = config.instanceId || `crawler_${process.pid}_${Math.random().toString(36).substr(2, 5)}`;
    const startTime = Date.now();
    
    console.log(`🚀 [${instanceId}] 爬虫实例启动...`);
    
    const crawler = new PlaywrightCrawler({
        ...config,
        instanceId: instanceId
    });
    await crawler.stateManager.syncWithParams();

    // PlaywrightCrawler已经设置了自己的信号处理器，这里不需要重复设置
    // 只需要确保正常停止
    let isGracefulShutdown = false;

    const signalHandler = async () => {
        if (isGracefulShutdown) return;
        isGracefulShutdown = true;
        
        console.log(`\n[${instanceId}] 收到中断信号，正在优雅停止...`);
        try {
            await crawler.stop();
        } catch (error) {
            console.error(`❌ [${instanceId}] 停止时出错: ${error.message}`);
        }
        process.exit(0);
    };

    // 使用once避免重复监听
    process.once('SIGINT', signalHandler);

    try {
        await crawler.start();
        
        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);
        console.log(`✅ [${instanceId}] 实例处理完成，耗时: ${duration}秒`);
    } catch (error) {
        console.error(`❌ [${instanceId}] 处理过程中发生错误:`, error.message);
        throw error;
    } finally {
        // 确保清理
        if (!isGracefulShutdown) {
            try {
                await crawler.stop();
            } catch (error) {
                console.error(`❌ [${instanceId}] 最终清理时出错: ${error.message}`);
            }
        }
        process.removeListener('SIGINT', signalHandler);
    }
}

// 并行处理主函数
async function runParallel(instanceCount = 3) {
    const globalStartTime = Date.now();

    console.log('🚀 开始并行爬取模式...');
    console.log(`🏭 并行实例数: ${instanceCount}`);

    // 创建所有实例的Promise
    const instancePromises = Array.from({ length: instanceCount }, (_, index) => {
        return new Promise(async (resolve, reject) => {
            try {
                const instanceId = `instance_${index + 1}`;
                
                // 为了确保真正并行，添加一个小的随机延迟启动
                const randomDelay = Math.random() * 1000; // 0-1秒随机延迟
                await new Promise(r => setTimeout(r, randomDelay));
                
                console.log(`🎯 [${instanceId}] 实例开始执行 (延迟${randomDelay.toFixed(0)}ms)`);
                await runSingleInstance({ instanceId });
                resolve(instanceId);
            } catch (error) {
                console.error(`❌ [instance_${index + 1}] 实例执行失败:`, error.message);
                reject(error);
            }
        });
    });

    try {
        // 并行执行所有实例，使用Promise.allSettled避免一个失败影响其他
        console.log(`⚡ ${instanceCount} 个实例已全部启动，开始并行处理...`);
        const results = await Promise.allSettled(instancePromises);
        
        // 统计执行结果
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;
        
        const globalEndTime = Date.now();
        const totalDuration = ((globalEndTime - globalStartTime) / 1000).toFixed(2);
        
        console.log('\n🎊 所有实例执行完成！');
        console.log(`📊 执行统计:`);
        console.log(`   ✅ 成功: ${successful} 个实例`);
        console.log(`   ❌ 失败: ${failed} 个实例`);
        console.log(`   ⏱️ 总耗时: ${totalDuration} 秒`);
        
        // 显示失败的实例详情
        if (failed > 0) {
            console.log('\n❌ 失败实例详情:');
            results.forEach((result, index) => {
                if (result.status === 'rejected') {
                    console.log(`   [instance_${index + 1}]: ${result.reason.message}`);
                }
            });
        }
        
    } catch (error) {
        console.error('\n❌ 并行处理过程中发生未预期错误:', error);
    }
}

// 清理进度文件的工具函数
function clearProgress() {
    try {
        const progressFile = './progress.json';

        if (fs.existsSync(progressFile)) {
            fs.unlinkSync(progressFile);
            console.log(`🗑️ 已删除进度文件: ${progressFile}`);
            console.log(`✅ 已清理进度文件，可以重新开始完整的爬取流程`);
        } else {
            console.log('ℹ️ 没有找到需要清理的进度文件');
        }
    } catch (error) {
        console.error('❌ 清理进度文件时出错:', error.message);
    }
}

// 重置token状态的工具函数
function resetTokenStatus() {
    try {
        const tokensFile = './tokens.json';
        if (!fs.existsSync(tokensFile)) {
            console.log('ℹ️ 没有找到tokens.json文件');
            return;
        }

        const tokensData = fs.readFileSync(tokensFile, 'utf8');
        const tokenConfig = JSON.parse(tokensData);

        if (!tokenConfig.tokens || !Array.isArray(tokenConfig.tokens)) {
            console.log('❌ tokens.json格式错误');
            return;
        }

        let resetCount = 0;
        tokenConfig.tokens.forEach(tokenObj => {
            if (typeof tokenObj === 'object' && tokenObj.token) {
                // 重置所有状态标记
                if (tokenObj.isValid !== undefined) {
                    tokenObj.isValid = null;
                    resetCount++;
                }
                delete tokenObj.lastValidated;
                delete tokenObj.bannedTime;
                delete tokenObj.bannedReason;
                delete tokenObj.invalidReason;
                delete tokenObj.bannedUntil;
                delete tokenObj.lastBanCheck;
                delete tokenObj.assignedTo;
                delete tokenObj.assignedAt;
            }
        });

        if (resetCount > 0) {
            // 备份原文件
            const backupFile = `./tokens_backup_${Date.now()}.json`;
            fs.writeFileSync(backupFile, tokensData, 'utf8');
            console.log(`📋 已备份原tokens文件到: ${backupFile}`);

            // 保存重置后的文件
            fs.writeFileSync(tokensFile, JSON.stringify(tokenConfig, null, 2), 'utf8');
            console.log(`✅ 已重置 ${resetCount} 个token的状态，所有token将重新验证`);
        } else {
            console.log('ℹ️ 没有找到需要重置的token状态');
        }
    } catch (error) {
        console.error('❌ 重置token状态时出错:', error.message);
    }
}

// 清理僵尸Token的工具函数
async function cleanupZombieTokens() {
    try {
        console.log('🧹 开始清理僵尸Token...');
        
        const tokenManager = new TokenManager({
            tokensFile: './tokens.json',
            instanceId: 'cleanup_tool'
        });
        
        const cleanedCount = await tokenManager.cleanupZombieTokens();
        
        // 显示统计信息
        const stats = tokenManager.getTokenStats();
        console.log('\n📊 Token状态统计:');
        console.log(`   总计: ${stats.total}`);
        console.log(`   可用: ${stats.available}`);
        console.log(`   已分配: ${stats.assigned}`);
        console.log(`   无效: ${stats.invalid}`);
        console.log(`   被封禁: ${stats.banned}`);
        
        if (cleanedCount > 0) {
            console.log(`\n✅ 清理完成，共释放了 ${cleanedCount} 个僵尸Token`);
        } else {
            console.log('\n✅ 没有找到需要清理的僵尸Token');
        }
        
    } catch (error) {
        console.error('❌ 清理僵尸Token时出错:', error.message);
    }
}

// 显示Token状态的工具函数
function showTokenStatus() {
    try {
        console.log('🔍 查看Token状态...\n');
        
        const tokenManager = new TokenManager({
            tokensFile: './tokens.json',
            instanceId: 'status_tool'
        });
        
        const stats = tokenManager.getTokenStats();
        console.log('📊 Token状态概览:');
        console.log(`   总计: ${stats.total}`);
        console.log(`   🟢 可用: ${stats.available}`);
        console.log(`   🟡 已分配: ${stats.assigned}`);
        console.log(`   🔴 无效: ${stats.invalid}`);
        console.log(`   🟠 被封禁: ${stats.banned}`);
        
        // 显示详细信息
        if (fs.existsSync('./tokens.json')) {
            const tokens = tokenManager._loadTokensInternal();
            const now = Date.now();
            
            console.log('\n📋 详细Token信息:');
            console.log('='.repeat(80));
            
            tokens.forEach((token, index) => {
                let status = '🟢 可用';
                let extraInfo = '';
                
                if (token.isValid === false) {
                    status = '🔴 无效';
                    extraInfo = token.invalidReason ? ` (${token.invalidReason})` : '';
                } else if (token.bannedUntil && now < new Date(token.bannedUntil).getTime()) {
                    status = '🟠 被封禁';
                    const bannedUntil = new Date(token.bannedUntil).toLocaleString();
                    extraInfo = ` (至 ${bannedUntil})`;
                } else if (token.assignedTo) {
                    status = '🟡 已分配';
                    extraInfo = ` (给 ${token.assignedTo})`;
                    
                    // 检查心跳状态
                    const heartbeatFile = path.join('./heartbeats', `${token.assignedTo}.heartbeat`);
                    if (fs.existsSync(heartbeatFile)) {
                        try {
                            const heartbeatData = JSON.parse(fs.readFileSync(heartbeatFile, 'utf8'));
                            const heartbeatAge = now - new Date(heartbeatData.timestamp).getTime();
                            if (heartbeatAge > 30000) { // 30秒
                                extraInfo += ` ⚠️ 心跳异常 (${Math.round(heartbeatAge/1000)}秒前)`;
                            } else {
                                extraInfo += ` ✅ 心跳正常`;
                            }
                        } catch (error) {
                            extraInfo += ` ❌ 心跳文件损坏`;
                        }
                    } else {
                        extraInfo += ` ❌ 无心跳文件`;
                    }
                }
                
                console.log(`   Token[${token.id}]: ${status}${extraInfo}`);
            });
            
            console.log('='.repeat(80));
        }
        
    } catch (error) {
        console.error('❌ 查看Token状态时出错:', error.message);
    }
}

// 显示进度统计的工具函数
function showAllProgress() {
    try {
        const progressFile = './progress.json';

        if (!fs.existsSync(progressFile)) {
            console.log('ℹ️ 没有找到进度文件');
            return;
        }

        console.log('\n📊 进度统计:');
        console.log('='.repeat(80));

        try {
            const progressData = JSON.parse(fs.readFileSync(progressFile, 'utf8'));
            const combinations = progressData.combinations || {};
            const total = progressData.totalCombinations || Object.keys(combinations).length;
            const completed = Object.values(combinations).filter(c => c.status === 'completed').length;
            const processing = Object.values(combinations).filter(c => c.status === 'processing').length;
            const pending = Object.values(combinations).filter(c => c.status === 'pending').length;
            const failed = Object.values(combinations).filter(c => c.status === 'failed').length;

            console.log(`\n📄 ${progressFile}:`);
            console.log(`   创建时间: ${progressData.createdAt || 'unknown'}`);
            console.log(`   最后更新: ${progressData.lastUpdated || 'unknown'}`);
            console.log(`   总任务数: ${total}`);
            console.log(`   待处理: ${pending}`);
            console.log(`   处理中: ${processing}`);
            console.log(`   已完成: ${completed} (${total > 0 ? ((completed / total) * 100).toFixed(1) : 0}%)`);
            console.log(`   失败: ${failed}`);

            // 显示最近的一些任务历史
            const recentTasks = Object.values(combinations)
                .filter(c => c.history && c.history.length > 0)
                .sort((a, b) => {
                    const aTime = a.history[a.history.length - 1].timestamp;
                    const bTime = b.history[b.history.length - 1].timestamp;
                    return new Date(bTime) - new Date(aTime);
                })
                .slice(0, 5);

            if (recentTasks.length > 0) {
                console.log('\n📝 最近活动的任务:');
                recentTasks.forEach((task, index) => {
                    const lastHistory = task.history[task.history.length - 1];
                    const combination = task.combination;
                    console.log(`   ${index + 1}. ${combination.studyPhaseName}/${combination.subjectName}/${combination.textbookVersionName}/${combination.ceciName}/${combination.catalogName}`);
                    console.log(`      状态: ${task.status}, 最后更新: ${lastHistory.timestamp}, 实例: ${lastHistory.instanceId}`);
                });
            }

        } catch (error) {
            console.warn(`⚠️ 读取 ${progressFile} 失败: ${error.message}`);
        }

        console.log('\n='.repeat(80));
    } catch (error) {
        console.error('❌ 显示进度统计时出错:', error.message);
    }
}

async function main() {
    // 从命令行参数判断运行模式
    const args = process.argv.slice(2);
    const parallelFlag = args.includes('--parallel') || args.includes('-p');
    const instanceCountArg = args.find(arg => arg.startsWith('--instances='));
    const instanceCount = instanceCountArg ? parseInt(instanceCountArg.split('=')[1]) : 3;
    const clearProgressFlag = args.includes('--clear-progress');
    const showProgressFlag = args.includes('--show-progress');
    const resetTokenFlag = args.includes('--reset-tokens');
    const cleanupTokensFlag = args.includes('--cleanup-tokens');
    const showTokenStatusFlag = args.includes('--show-tokens');
    const analyzeDirectoriesFlag = args.includes('--analyze-directories');
    const testEncodingFlag = args.includes('--test-encoding');

    // 处理工具命令
    if (clearProgressFlag) {
        clearProgress();
        return;
    }

    if (showProgressFlag) {
        showAllProgress();
        return;
    }

    if (resetTokenFlag) {
        resetTokenStatus();
        return;
    }

    if (cleanupTokensFlag) {
        await cleanupZombieTokens();
        return;
    }

    if (showTokenStatusFlag) {
        showTokenStatus();
        return;
    }

    if (analyzeDirectoriesFlag) {
        await PlaywrightCrawler.analyzeExistingDirectories('./');
        return;
    }

    if (testEncodingFlag) {
        PlaywrightCrawler.testEncodingDecoding();
        return;
    }

    if (parallelFlag) {
        console.log(`🔀 使用并行模式 (${instanceCount} 个实例)`);
        await runParallel(instanceCount);
    } else {
        console.log('🔧 使用单实例模式');
        await runSingleInstance();
    }
}

if (require.main === module) {
    main().catch(console.error);
}