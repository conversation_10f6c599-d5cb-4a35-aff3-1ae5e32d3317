/**
 * 测试人类行为模拟功能
 * 验证各种防检测策略是否正常工作
 */

const { EnhancedPlaywrightCrawler } = require('./src/scrapers/EnhancedPlaywrightCrawler');
const PlaywrightCrawler = require('./PlaywrightCrawler');

async function testHumanBehaviorSimulation() {
    console.log('🧪 开始测试人类行为模拟功能...');
    console.log('📋 测试内容：');
    console.log('   - 点击操作的人类行为模拟');
    console.log('   - 悬停操作的自然化');
    console.log('   - 输入操作的打字模拟');
    console.log('   - 分页跳转的行为模拟');
    console.log('   - 随机延迟和防检测策略');

    // 测试EnhancedPlaywrightCrawler
    await testEnhancedCrawlerBehavior();
    
    // 测试PlaywrightCrawler
    await testOriginalCrawlerBehavior();
}

async function testEnhancedCrawlerBehavior() {
    console.log('\n🔬 测试 EnhancedPlaywrightCrawler 的人类行为模拟...');
    
    const config = {
        baseURL: 'https://zj.stzy.com/create-paper/chapter',
        headless: false, // 显示浏览器以观察行为
        enableAntiDetection: true,
        minDelay: 100,
        maxDelay: 500,
        maxRetries: 1,
        requestTimeout: 30000,
        pageTimeout: 30000
    };

    const logger = {
        debug: (msg, data) => console.log('DEBUG:', msg, data ? JSON.stringify(data, null, 2) : ''),
        info: (msg, data) => console.log('INFO:', msg, data ? JSON.stringify(data, null, 2) : ''),
        warn: (msg, data) => console.log('WARN:', msg, data ? JSON.stringify(data, null, 2) : ''),
        error: (msg, data) => console.log('ERROR:', msg, data ? JSON.stringify(data, null, 2) : '')
    };

    const crawler = new EnhancedPlaywrightCrawler(config, logger);

    try {
        console.log('🚀 初始化爬虫...');
        await crawler.initialize();

        console.log('🎭 测试人类行为模拟组件...');
        
        // 测试随机延迟
        console.log('⏱️ 测试随机延迟生成...');
        for (let i = 0; i < 5; i++) {
            const delay = crawler.humanBehavior.randomDelay(100, 500);
            console.log(`   随机延迟 ${i + 1}: ${delay}ms`);
        }

        // 测试贝塞尔缓动函数
        console.log('📈 测试贝塞尔缓动函数...');
        for (let i = 0; i <= 10; i++) {
            const t = i / 10;
            const eased = crawler.humanBehavior.easeInOutCubic(t);
            console.log(`   t=${t.toFixed(1)} -> eased=${eased.toFixed(3)}`);
        }

        console.log('✅ EnhancedPlaywrightCrawler 人类行为模拟组件测试完成');

    } catch (error) {
        console.error('❌ EnhancedPlaywrightCrawler 测试失败:', error.message);
    } finally {
        await crawler.cleanup();
        console.log('🧹 EnhancedPlaywrightCrawler 测试清理完成');
    }
}

async function testOriginalCrawlerBehavior() {
    console.log('\n🔬 测试 PlaywrightCrawler 的人类行为模拟...');
    
    const config = {
        baseURL: 'https://zj.stzy.com/create-paper/chapter',
        apiURL: 'https://qms.stzy.com/matrix/zw-search/api/v1/homeEs/question/textbookQuery',
        tokensFile: './tokens.json',
        headless: false, // 显示浏览器以观察行为
        enableAntiBot: true,
        behaviorSimulation: true,
        scrollBehavior: true,
        mouseBehavior: true,
        maxRetries: 1,
        pageTimeout: 30000
    };

    const crawler = new PlaywrightCrawler(config);

    try {
        console.log('🚀 初始化爬虫...');
        
        console.log('🎭 测试人类行为模拟组件...');
        
        // 测试随机延迟
        console.log('⏱️ 测试随机延迟生成...');
        for (let i = 0; i < 5; i++) {
            const delay = crawler.humanBehavior.randomDelay(100, 500);
            console.log(`   随机延迟 ${i + 1}: ${delay}ms`);
        }

        // 测试行为概率
        console.log('🎲 测试行为概率...');
        let randomMoveCount = 0;
        let scrollCount = 0;
        let hoverCount = 0;
        
        for (let i = 0; i < 100; i++) {
            if (Math.random() < crawler.humanBehavior.config.randomMoveProbability) {
                randomMoveCount++;
            }
            if (Math.random() < crawler.humanBehavior.config.scrollProbability) {
                scrollCount++;
            }
            if (Math.random() < crawler.humanBehavior.config.hoverProbability) {
                hoverCount++;
            }
        }
        
        console.log(`   随机移动触发次数: ${randomMoveCount}/100 (期望: ~30)`);
        console.log(`   滚动触发次数: ${scrollCount}/100 (期望: ~20)`);
        console.log(`   悬停触发次数: ${hoverCount}/100 (期望: ~40)`);

        console.log('✅ PlaywrightCrawler 人类行为模拟组件测试完成');

    } catch (error) {
        console.error('❌ PlaywrightCrawler 测试失败:', error.message);
    } finally {
        await crawler.cleanup();
        console.log('🧹 PlaywrightCrawler 测试清理完成');
    }
}

async function demonstrateBehaviorDifferences() {
    console.log('\n🔍 演示人类行为模拟的效果差异...');
    
    console.log('📊 传统机器人行为 vs 人类模拟行为:');
    console.log('');
    console.log('传统机器人行为特征:');
    console.log('  ❌ 固定的延迟时间');
    console.log('  ❌ 总是点击元素中心');
    console.log('  ❌ 瞬间完成输入');
    console.log('  ❌ 规律性的操作间隔');
    console.log('  ❌ 没有鼠标移动轨迹');
    console.log('');
    console.log('人类模拟行为特征:');
    console.log('  ✅ 随机化的延迟时间');
    console.log('  ✅ 随机点击位置（避开中心）');
    console.log('  ✅ 逐字符输入，模拟打字速度');
    console.log('  ✅ 不规律的操作间隔');
    console.log('  ✅ 自然的鼠标移动轨迹');
    console.log('  ✅ 模拟悬停、滚动等行为');
    console.log('  ✅ 随机的清空输入方式');
    console.log('  ✅ 偶尔的"思考"停顿');
    console.log('');
    console.log('防检测策略:');
    console.log('  🛡️ 用户代理字符串轮换');
    console.log('  🛡️ 视口大小变化');
    console.log('  🛡️ 请求间隔随机化');
    console.log('  🛡️ 模拟人类疲劳和注意力分散');
    console.log('  🛡️ 避免规律性行为模式');
}

// 运行测试
if (require.main === module) {
    testHumanBehaviorSimulation()
        .then(() => {
            console.log('\n🎯 人类行为模拟测试完成');
            return demonstrateBehaviorDifferences();
        })
        .then(() => {
            console.log('\n✨ 所有测试完成！');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ 测试失败:', error);
            process.exit(1);
        });
}

module.exports = { 
    testHumanBehaviorSimulation,
    testEnhancedCrawlerBehavior,
    testOriginalCrawlerBehavior,
    demonstrateBehaviorDifferences
};
