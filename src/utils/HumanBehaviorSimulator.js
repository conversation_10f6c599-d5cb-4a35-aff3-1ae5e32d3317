/**
 * 人类行为模拟器 - 防止机器人检测
 * 模拟真实用户的各种交互行为
 */

class HumanBehaviorSimulator {
    constructor(config = {}) {
        this.config = {
            // 基础延迟配置
            minDelay: config.minDelay || 100,
            maxDelay: config.maxDelay || 500,
            
            // 鼠标移动配置
            mouseMovementSteps: config.mouseMovementSteps || 10,
            mouseMovementDelay: config.mouseMovementDelay || 50,
            
            // 滚动配置
            scrollSteps: config.scrollSteps || 5,
            scrollDelay: config.scrollDelay || 100,
            
            // 行为概率配置
            randomMoveProbability: config.randomMoveProbability || 0.3,
            scrollProbability: config.scrollProbability || 0.2,
            hoverProbability: config.hoverProbability || 0.4,
            
            // 防检测配置
            enableAntiDetection: config.enableAntiDetection !== false,
            
            ...config
        };
        
        this.logger = config.logger || console;
    }

    /**
     * 生成随机延迟时间
     */
    randomDelay(min = null, max = null) {
        const minTime = min || this.config.minDelay;
        const maxTime = max || this.config.maxDelay;
        return Math.floor(Math.random() * (maxTime - minTime + 1)) + minTime;
    }

    /**
     * 模拟人类鼠标移动轨迹
     */
    async simulateMouseMovement(page, fromX, fromY, toX, toY) {
        const steps = this.config.mouseMovementSteps;
        const delay = this.config.mouseMovementDelay;
        
        for (let i = 0; i <= steps; i++) {
            const progress = i / steps;
            // 使用贝塞尔曲线模拟自然的鼠标移动
            const easeProgress = this.easeInOutCubic(progress);
            
            const currentX = fromX + (toX - fromX) * easeProgress;
            const currentY = fromY + (toY - fromY) * easeProgress;
            
            // 添加轻微的随机偏移，模拟手抖
            const jitterX = (Math.random() - 0.5) * 2;
            const jitterY = (Math.random() - 0.5) * 2;
            
            await page.mouse.move(currentX + jitterX, currentY + jitterY);
            await page.waitForTimeout(delay + Math.random() * 20);
        }
    }

    /**
     * 贝塞尔缓动函数
     */
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    /**
     * 模拟人类点击行为
     */
    async humanClick(page, element, options = {}) {
        try {
            const description = options.description || '元素';
            this.logger.debug(`🖱️ 模拟人类点击: ${description}`);

            // 1. 滚动到元素可见区域
            await this.scrollToElement(page, element);
            
            // 2. 获取元素位置
            const box = await element.boundingBox();
            if (!box) {
                throw new Error('无法获取元素位置');
            }

            // 3. 随机选择点击位置（避免总是点击中心）
            const clickX = box.x + box.width * (0.3 + Math.random() * 0.4);
            const clickY = box.y + box.height * (0.3 + Math.random() * 0.4);

            // 4. 模拟鼠标移动到目标位置
            const currentMouse = await page.evaluate(() => ({ x: 0, y: 0 }));
            await this.simulateMouseMovement(page, currentMouse.x, currentMouse.y, clickX, clickY);

            // 5. 悬停一段时间（模拟用户思考）
            await page.waitForTimeout(this.randomDelay(200, 800));

            // 6. 执行点击
            await element.click({
                position: {
                    x: clickX - box.x,
                    y: clickY - box.y
                },
                force: false,
                timeout: 10000
            });

            // 7. 点击后短暂停留
            await page.waitForTimeout(this.randomDelay(100, 300));

            this.logger.debug(`✅ 点击完成: ${description}`);

        } catch (error) {
            this.logger.error(`❌ 模拟点击失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 模拟人类悬停行为
     */
    async humanHover(page, element, options = {}) {
        try {
            const description = options.description || '元素';
            const duration = options.duration || this.randomDelay(500, 1500);
            
            this.logger.debug(`🖱️ 模拟悬停: ${description}`);

            // 滚动到元素
            await this.scrollToElement(page, element);

            // 获取元素位置
            const box = await element.boundingBox();
            if (!box) {
                throw new Error('无法获取元素位置');
            }

            // 随机悬停位置
            const hoverX = box.x + box.width * (0.2 + Math.random() * 0.6);
            const hoverY = box.y + box.height * (0.2 + Math.random() * 0.6);

            // 移动到悬停位置
            await page.mouse.move(hoverX, hoverY);
            
            // 保持悬停
            await page.waitForTimeout(duration);

            this.logger.debug(`✅ 悬停完成: ${description}`);

        } catch (error) {
            this.logger.error(`❌ 悬停失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 模拟人类输入行为
     */
    async humanType(page, element, text, options = {}) {
        try {
            const description = options.description || '输入框';
            this.logger.debug(`⌨️ 模拟人类输入: ${description}`);

            // 点击输入框获得焦点
            await this.humanClick(page, element, { description: `${description}输入框` });

            // 清空现有内容
            await this.clearInput(page, element);

            // 逐字符输入，模拟真实打字速度
            for (let i = 0; i < text.length; i++) {
                const char = text[i];
                await page.keyboard.type(char);
                
                // 模拟不同的打字速度
                let delay = this.randomDelay(80, 200);
                
                // 某些字符打字更慢（如数字、符号）
                if (/[0-9\.\-\+\*\/\(\)]/.test(char)) {
                    delay += this.randomDelay(50, 150);
                }
                
                // 偶尔停顿更久（模拟思考）
                if (Math.random() < 0.1) {
                    delay += this.randomDelay(300, 800);
                }
                
                await page.waitForTimeout(delay);
            }

            // 输入完成后短暂停留
            await page.waitForTimeout(this.randomDelay(200, 500));

            this.logger.debug(`✅ 输入完成: ${description}`);

        } catch (error) {
            this.logger.error(`❌ 输入失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 清空输入框（模拟多种清空方式）
     */
    async clearInput(page, element) {
        const clearMethod = Math.floor(Math.random() * 4);
        
        switch (clearMethod) {
            case 0:
                // Ctrl+A 全选后删除
                await page.keyboard.press('Control+a');
                await page.waitForTimeout(this.randomDelay(100, 200));
                break;
            case 1:
                // 三击选中
                await element.click({ clickCount: 3 });
                await page.waitForTimeout(this.randomDelay(150, 250));
                break;
            case 2:
                // 双击选中
                await element.click({ clickCount: 2 });
                await page.waitForTimeout(this.randomDelay(100, 200));
                break;
            case 3:
                // 移动到末尾后Shift+Home选中
                await page.keyboard.press('End');
                await page.waitForTimeout(50);
                await page.keyboard.press('Shift+Home');
                await page.waitForTimeout(this.randomDelay(100, 200));
                break;
        }
    }

    /**
     * 模拟滚动到元素
     */
    async scrollToElement(page, element, options = {}) {
        try {
            const behavior = options.behavior || 'smooth';
            
            // 使用JavaScript滚动，更自然
            await element.evaluate((el, behavior) => {
                el.scrollIntoView({ 
                    behavior: behavior,
                    block: 'center',
                    inline: 'center'
                });
            }, behavior);

            // 等待滚动完成
            await page.waitForTimeout(this.randomDelay(300, 800));

        } catch (error) {
            this.logger.warn(`⚠️ 滚动到元素失败: ${error.message}`);
        }
    }

    /**
     * 模拟随机鼠标移动（防止检测）
     */
    async randomMouseMovement(page) {
        if (Math.random() > this.config.randomMoveProbability) {
            return;
        }

        try {
            const viewport = page.viewportSize();
            const targetX = Math.random() * viewport.width;
            const targetY = Math.random() * viewport.height;
            
            await page.mouse.move(targetX, targetY);
            await page.waitForTimeout(this.randomDelay(100, 300));
            
        } catch (error) {
            // 忽略错误
        }
    }

    /**
     * 模拟页面滚动行为
     */
    async simulateScrollBehavior(page) {
        if (Math.random() > this.config.scrollProbability) {
            return;
        }

        try {
            const scrollDirection = Math.random() > 0.5 ? 1 : -1;
            const scrollAmount = Math.random() * 300 + 100;
            
            for (let i = 0; i < this.config.scrollSteps; i++) {
                await page.mouse.wheel(0, scrollDirection * scrollAmount / this.config.scrollSteps);
                await page.waitForTimeout(this.config.scrollDelay);
            }
            
        } catch (error) {
            // 忽略错误
        }
    }

    /**
     * 模拟页面跳转前的行为
     */
    async beforePageNavigation(page) {
        if (!this.config.enableAntiDetection) return;

        // 随机鼠标移动
        await this.randomMouseMovement(page);
        
        // 随机滚动
        if (Math.random() < 0.3) {
            await this.simulateScrollBehavior(page);
        }
        
        // 短暂停留
        await page.waitForTimeout(this.randomDelay(500, 1500));
    }

    /**
     * 模拟页面加载后的行为
     */
    async afterPageLoad(page) {
        if (!this.config.enableAntiDetection) return;

        // 等待页面稳定
        await page.waitForTimeout(this.randomDelay(1000, 2000));
        
        // 随机鼠标移动
        await this.randomMouseMovement(page);
        
        // 模拟阅读时间
        await page.waitForTimeout(this.randomDelay(800, 2000));
    }
}

module.exports = HumanBehaviorSimulator;
