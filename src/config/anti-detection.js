/**
 * 防检测策略配置
 * 用于配置各种人类行为模拟和防机器人检测的策略
 */

module.exports = {
    // 基础行为模拟配置
    behavior: {
        // 启用防检测功能
        enableAntiDetection: true,
        
        // 基础延迟配置 (毫秒)
        delays: {
            min: 100,
            max: 500,
            typing: {
                min: 80,
                max: 200,
                punctuation: 150, // 标点符号额外延迟
                thinking: 800     // 偶尔的思考停顿
            },
            clicking: {
                beforeClick: 300,
                afterClick: 200,
                hover: 500
            },
            navigation: {
                beforeNavigation: 1000,
                afterNavigation: 2000,
                pageLoad: 3000
            }
        },
        
        // 鼠标行为配置
        mouse: {
            // 鼠标移动步数
            movementSteps: 10,
            // 每步延迟
            movementDelay: 50,
            // 随机抖动范围
            jitterRange: 2,
            // 随机移动概率
            randomMoveProbability: 0.3
        },
        
        // 滚动行为配置
        scroll: {
            // 滚动步数
            steps: 5,
            // 每步延迟
            delay: 100,
            // 滚动概率
            probability: 0.2,
            // 滚动方向随机性
            randomDirection: true
        },
        
        // 悬停行为配置
        hover: {
            // 悬停概率
            probability: 0.4,
            // 悬停持续时间范围
            duration: {
                min: 500,
                max: 1500
            }
        }
    },
    
    // 浏览器指纹防护
    fingerprint: {
        // 用户代理字符串轮换
        userAgents: [
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ],
        
        // 视口大小变化
        viewports: [
            { width: 1920, height: 1080 },
            { width: 1366, height: 768 },
            { width: 1440, height: 900 },
            { width: 1536, height: 864 },
            { width: 1280, height: 720 }
        ],
        
        // 语言设置
        languages: ['zh-CN', 'zh', 'en-US', 'en'],
        
        // 时区设置
        timezones: [
            'Asia/Shanghai',
            'Asia/Beijing',
            'Asia/Chongqing'
        ]
    },
    
    // 网络行为模拟
    network: {
        // 请求间隔随机化
        requestInterval: {
            base: 3000,
            variance: 2000
        },
        
        // 模拟网络延迟
        networkDelay: {
            min: 100,
            max: 800
        },
        
        // 请求头随机化
        headers: {
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1'
        }
    },
    
    // 页面交互策略
    interaction: {
        // 点击策略
        clicking: {
            // 避免总是点击中心点
            clickPositionVariance: 0.4,
            // 双击概率
            doubleClickProbability: 0.05,
            // 右键概率
            rightClickProbability: 0.02
        },
        
        // 输入策略
        typing: {
            // 打字错误概率
            typosProbability: 0.03,
            // 退格修正概率
            backspaceProbability: 0.05,
            // 复制粘贴概率
            pasteProbability: 0.1
        },
        
        // 表单填写策略
        forms: {
            // 清空方式多样化
            clearMethods: [
                'ctrl_a',      // Ctrl+A
                'triple_click', // 三击
                'double_click', // 双击
                'shift_home'   // Shift+Home
            ],
            // 各种清空方式的权重
            clearMethodWeights: [0.4, 0.3, 0.2, 0.1]
        }
    },
    
    // 检测规避策略
    evasion: {
        // 避免规律性行为
        avoidPatterns: true,
        
        // 模拟人类疲劳
        simulateFatigue: {
            enabled: true,
            // 操作变慢的概率随时间增加
            fatigueIncrease: 0.1,
            // 最大疲劳延迟倍数
            maxFatigueMultiplier: 2.0
        },
        
        // 模拟注意力分散
        simulateDistraction: {
            enabled: true,
            // 分散注意力的概率
            distractionProbability: 0.05,
            // 分散注意力时的额外延迟
            distractionDelay: 2000
        },
        
        // 随机暂停
        randomPauses: {
            enabled: true,
            // 暂停概率
            pauseProbability: 0.1,
            // 暂停时长范围
            pauseDuration: {
                min: 1000,
                max: 5000
            }
        }
    },
    
    // 环境变量覆盖
    overrides: {
        // 开发环境下可以禁用某些策略
        development: {
            enableAntiDetection: true,
            reducedDelays: false,
            skipRandomBehaviors: false
        },
        
        // 生产环境下的严格策略
        production: {
            enableAntiDetection: true,
            strictMode: true,
            enhancedEvasion: true
        }
    }
};
