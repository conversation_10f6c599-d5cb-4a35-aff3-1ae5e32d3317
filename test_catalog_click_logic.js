/**
 * 测试点击catalog后第一页请求的逻辑
 * 验证修改后的监听逻辑是否正确工作
 */

const { EnhancedPlaywrightCrawler } = require('./src/scrapers/EnhancedPlaywrightCrawler');

async function testCatalogClickLogic() {
    console.log('🧪 开始测试点击catalog后第一页请求的逻辑...');
    
    const crawler = new EnhancedPlaywrightCrawler({
        headless: false, // 显示浏览器以便观察
        browserTimeout: 60000,
        pageTimeout: 30000
    });

    try {
        // 模拟参数
        const testParameters = {
            studyPhaseCode: "1",
            studyPhaseName: "小学",
            subjectCode: "1",
            subjectName: "语文",
            textbookVersionCode: "1",
            textbookVersionName: "人教版",
            ceciCode: "1",
            ceciName: "上册",
            catalogCode: "test_catalog",
            catalogName: "测试目录"
        };

        console.log('📋 测试参数:', testParameters);
        
        // 测试爬取第一页（这会触发配置和第一页请求）
        console.log('🚀 开始测试第一页爬取...');
        const result = await crawler.crawlPages(testParameters, 1, 1);
        
        console.log('📊 测试结果:', {
            success: result.success,
            totalCount: result.totalCount,
            pagesProcessed: result.pagesProcessed,
            errors: result.errors
        });

        if (result.success && result.totalCount > 0) {
            console.log('✅ 测试成功：点击catalog后的第一页请求被正确捕获和处理');
        } else {
            console.log('❌ 测试失败：第一页请求未被正确处理');
        }

    } catch (error) {
        console.error('❌ 测试过程中出错:', error.message);
    } finally {
        await crawler.cleanup();
        console.log('🧹 测试清理完成');
    }
}

// 运行测试
if (require.main === module) {
    testCatalogClickLogic().catch(console.error);
}

module.exports = { testCatalogClickLogic };
