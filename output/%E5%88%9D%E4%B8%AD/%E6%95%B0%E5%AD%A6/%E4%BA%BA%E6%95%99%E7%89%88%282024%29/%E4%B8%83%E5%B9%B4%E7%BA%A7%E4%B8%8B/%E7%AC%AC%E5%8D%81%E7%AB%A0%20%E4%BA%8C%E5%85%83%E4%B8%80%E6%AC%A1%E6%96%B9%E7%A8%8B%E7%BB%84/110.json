{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 109, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "570063525453799424", "questionArticle": "<p>1．年关将至，幸福小区物业决定采购<i>A</i>，<i>B</i>两种型号的灯笼．若购买3个<i>A</i>型灯笼和4个<i>B</i>型灯笼共需要116元；购买6个<i>A</i>型灯笼和5个<i>B</i>型灯笼共需要172元．</p><p>(1)求<i>A</i>，<i>B</i>两种型号灯笼的单价；</p><p>(2)若需要购买<i>A</i>，<i>B</i>两种型号的灯笼共200个，总费用不超过3000元，至少需要购买<i>A</i>型灯笼多少个？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁铁岭 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570063505409220608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "570063505409220608", "title": "2025年辽宁省铁岭市九年级中学生能力训练模拟数学试卷（二）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570063848146771968", "questionArticle": "<p>2．每年的4月24日是中国航天日，某市计划在今年4月份开展中学生航模比赛，比赛组织方需要购买一批<i>A</i>，<i>B</i>两种型号的动力部件，购买记录如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p><i>A</i>型号（件）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p><i>B</i>型号（件）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>合计金额（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>第一次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p>30</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p>20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>720</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>第二次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p>20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p>15</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>510</p></td></tr></table><p>(1)分别求<i>A</i>，<i>B</i>两种型号动力部件的单价；</p><p>(2)若组织方计划再次购买<i>A</i>，<i>B</i>两种型号的动力部件共30件，恰逢<i>A</i>型号动力部件7.5折促销，<i>B</i>型号动力部件单价不变，若计划购买金额不超过400元，则最多可购买<i>B</i>型号动力部件多少件？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东日照 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570063824033718272", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "570063824033718272", "title": "2025年山东省日照市岚山区中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570062305007804416", "questionArticle": "<p>3．为了有效落实中小学每天60分钟大课间体育活动，某中学为七年级各班购买了一些彩色鸡毛毽子和跳绳，表格是部分班级购买的情况：</p><table style=\"border: solid 1px;border-collapse: collapse; width:233.25pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">班级</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">毽子（个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">跳绳（根）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">费用总计（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 701 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 8 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 6 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 178 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 705 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 14 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 3 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 139 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 706 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ a $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ b $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 165 $ </p></td></tr></table><p>注： $ a $ ， $ b $ 都不为 $ 0 $ ．</p><p>(1)求购买一个毽子、一根跳绳各需多少元？</p><p>(2)直接写出表中 $ a $ ， $ b $ 的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16420|16437", "keyPointNames": "二元一次方程的解|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570062280227856384", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "570062280227856384", "title": "2025年安徽省中考名校大联考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570062542002757632", "questionArticle": "<p>4．某电器超市销售每台进价分别为160元、120元的<i>A</i>、<i>B</i>两种型号的电风扇，超市第一周卖出3台<i>A</i>种型号和4台<i>B</i>种型号电风扇销售额为1200元，第二周卖出5台<i>A</i>种型号和6台<i>B</i>种型号电风扇销售额为1900元（进价、售价均保持不变，利润 $ = $ 销售收入 $ - $ 进货成本）：</p><p>(1)求<i>A</i>、<i>B</i>两种型号的电风扇的销售单价；</p><p>(2)若超市准备用不多于7480元的金额再采购这两种型号的电风扇共50台，求<i>A</i>种型号的电风扇最多能采购多少台？</p><p>(3)在（2）的条件下，超市销售完这50台电风扇能否实现利润超过1860元的目标？若能，请给出相应的采购方案；若不能，请说明理由．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570062502836346880", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "570062502836346880", "title": "2025年 福建省厦门外国语学校湖里分校中考数学模拟试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570267138599985152", "questionArticle": "<p>5．解二元一次方程组：</p><p>(1) $ \\begin{cases} x-y=3 \\\\ 3x-8y=4 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 3x+2y=12 \\\\ 5x-6y=-8 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏镇江 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267118328913920", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267118328913920", "title": "江苏省镇江市2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570267132795068416", "questionArticle": "<p>6．已知 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ 是方程 $ ax+by=3 $ 的解，则代数式 $ 2a+b-3 $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏镇江 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267118328913920", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267118328913920", "title": "江苏省镇江市2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570267046379823104", "questionArticle": "<p>7．对于关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ （其中 $ a{{}_{ 1 } } $ ， $ b{{}_{ 1 } } $ ， $ c{{}_{ 1 } } $ ， $ a{{}_{ 2 } } $ ， $ b{{}_{ 2 } } $ ， $ c{{}_{ 2 } } $ 是常数），给出如下定义：若该方程组的解满足 $ \\left  | { x+y } \\right  | =1 $ ，则称这个方程组为“开心”方程组．</p><p>(1)下列方程组是“开心”方程组的是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>（只填写序号）；</p><p> $ ①\\begin{cases} x+y=0 \\\\ 2x-y=0 \\end{cases}  $ ； $ ②\\begin{cases} x+y=1 \\\\ 2x-y=2 \\end{cases}  $ ； $ ③\\begin{cases} x-y=-1 \\\\ 3x+5y=7 \\end{cases}  $ ；</p><p>(2)若关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} 2x+5y=4k+3 \\\\ 5x+2y=5-k \\end{cases}  $ 是“开心”方程组，求 $ k $ 的值；</p><p>(3)若对于任意的有理数 $ m $ ，关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} 2amx+\\left ( { b-1 } \\right ) y=m \\\\ x+2y=4 \\end{cases}  $ 都是“开心”方程组，求 $ ab $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长郡 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16258|16423|16424", "keyPointNames": "绝对值方程|代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267012338851840", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267012338851840", "title": "湖南省长沙市长郡教育集团2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570267045125726208", "questionArticle": "<p>8．人教版（2024）七年级下册数学教材第103页有一个这样的探究题：如图，丝路纺织厂与 $ A $ ， $ B $ 两地由公路、铁路相连．这家纺织厂从 $ A $ 地购进一批长绒棉运回工厂，制成纺织面料运往 $ B $ 地．已知公路运价为 $ 0.5 $ 元 $ /\\left ( { { \\rm{ t } }\\cdot { \\rm{ k } }{ \\rm{ m } } } \\right )  $ ，铁路运价为 $ 0.2 $ 元 $ /\\left ( { { \\rm{ t } }\\cdot { \\rm{ k } }{ \\rm{ m } } } \\right )  $ ，且这两次运输共支出公路运费5200元，铁路运费16640元．问：这家纺织厂购进的长绒棉和制成的纺织面料各多少 $ { \\rm{ t } } $ ？</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/02/2/1/0/0/0/573543436562767872/images/img_1.png\" style='vertical-align:middle;' width=\"327\" alt=\"试题资源网 https://stzy.com\"></p><p>小郡同学在看到这个探究题后，设购进 $ x{ \\rm{ t } } $ 长绒棉，制成 $ y{ \\rm{ t } } $ 纺织面料．</p><p>根据题中数量关系列出了以下表格：</p><table style=\"border: solid 1px;border-collapse: collapse; width:271.5pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ x{ \\rm{ t } } $ 长绒棉</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ y{ \\rm{ t } } $ 纺织面料</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>合计</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>公路运费/元</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 0.5x\\cdot 10=5x $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>铁路运费/元</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 0.2x\\cdot 120=24x $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td></tr></table><p>(1)请完成表格的填写（4空都需填写含有 $ x $ 或 $ y $ 的表达式）．</p><p>(2)请帮小郡同学完成接下来的解题过程．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长郡 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267012338851840", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267012338851840", "title": "湖南省长沙市长郡教育集团2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570267126520389632", "questionArticle": "<p>9．下列是二元一次方程 $ 2x+y=10 $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $　　　　B． $ \\begin{cases} x=2 \\\\ y=3 \\end{cases}  $　　　　C． $ \\begin{cases} x=3 \\\\ y=4 \\end{cases}  $　　　　D． $ \\begin{cases} x=4 \\\\ y=3 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏镇江 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267118328913920", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267118328913920", "title": "江苏省镇江市2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570267034832904192", "questionArticle": "<p>10．解下列方程组：</p><p>(1) $ \\begin{cases} y=x+3 \\\\ 7x+5y=9 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 2x-5y=7 \\\\ 4x-3y=7 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长郡 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267012338851840", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267012338851840", "title": "湖南省长沙市长郡教育集团2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 110, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 110, "timestamp": "2025-07-01T02:13:48.591Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}