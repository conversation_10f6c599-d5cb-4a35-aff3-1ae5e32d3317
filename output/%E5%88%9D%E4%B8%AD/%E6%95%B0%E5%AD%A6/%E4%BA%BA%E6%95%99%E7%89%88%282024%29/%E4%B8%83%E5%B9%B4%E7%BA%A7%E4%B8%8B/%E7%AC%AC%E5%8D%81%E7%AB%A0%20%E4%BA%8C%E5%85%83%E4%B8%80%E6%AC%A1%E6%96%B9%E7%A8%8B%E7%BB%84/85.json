{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 84, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "578018357514907648", "questionArticle": "<p>1．《九章算术》中有这样一个题：今有甲乙二人持钱不知其数．甲得乙半而钱五十，乙得甲太半而钱亦五十．问甲、乙持钱各几何？其意思为：今有甲乙二人，不如其钱包里有多少钱，若乙把其一半的钱给甲，则甲的数为50；而甲把其 $ \\dfrac { 2 } { 3 } $ 的钱给乙．则乙的钱数也为50，问甲、乙各有多少钱？设甲的钱数为x，乙的钱数为y，则可建立方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=50 \\\\ \\dfrac { 2 } { 3 }x+y=50 \\end{cases}  $ B． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=50 \\\\ x+\\dfrac { 2 } { 3 }y=50 \\end{cases}  $ C． $ \\begin{cases} \\dfrac { 1 } { 2 }x+y=50 \\\\ \\dfrac { 2 } { 3 }x+y=50 \\end{cases}  $ D． $ \\begin{cases} \\dfrac { 1 } { 2 }x+y=50 \\\\ x+\\dfrac { 2 } { 3 }y=50 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|320000|-1|150000|350000|450000|430000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建厦外 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 13, "referenceNum": 9, "createTime": "2025-05-15", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578018344772612096", "questionFeatureName": "数学文化题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "578018344772612096", "title": "福建省厦门外国语学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}, {"id": "449187960094760960", "title": "2024年广东省深圳市罗湖外语实验学校中考三模数学试题", "paperCategory": 1}, {"id": "203144492878176256", "title": "江苏省南通市如皋市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "202427964612452352", "title": "湖南省株洲市攸县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "174200049047478272", "title": "2022年七年级下册湘教版数学第一章1.3二元一次方程组的应用课时练习", "paperCategory": 1}, {"id": "164732116424499200", "title": "山东省东营市垦利区2020-2021学年中考一模数学试题", "paperCategory": 1}, {"id": "128791403719598080", "title": "广西柳州市2020年6月九年级下学期模拟数学试题", "paperCategory": 1}, {"id": "1017674896642048", "title": "内蒙古通辽市科尔沁区2020年九年级学业模拟数学试题", "paperCategory": 1}, {"id": "161425573364932608", "title": "2020年广东省广州市育才中学中考数学一模试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577297974713163776", "questionArticle": "<p>2．随着自媒体的盛行，网购及直播带货成为一种趋势，某农产基地准备借助自媒体对某种水果做营销，采用线上及线下两种销售方式，统计销售情况发现，该水果的销售量和总收入如表（总收入 $ = $ 销售量 $ \\times  $ 单价）： </p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 41.8pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 142.45pt;\"><p>线上销售水果量（单位： $ { \\rm{ k } }{ \\rm{ g } } $ ）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 142.45pt;\"><p>线下销售水果量（单位： $ { \\rm{ k } }{ \\rm{ g } } $ ）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100.65pt;\"><p>总收入（单位：元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 41.8pt;\"><p>第一批</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 142.45pt;\"><p> $ 40 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 142.45pt;\"><p> $ 60 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100.65pt;\"><p> $ 1380 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 41.8pt;\"><p>第二批</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 142.45pt;\"><p> $ 60 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 142.45pt;\"><p> $ 40 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100.65pt;\"><p> $ 1320 $ </p></td></tr></table><p>（1）求该水果线上、线下的销售单价各是多少元 $ /{ \\rm{ k } }{ \\rm{ g } } $ ；</p><p>（2）若某公司计划从该地采购该水果 $ 1000{ \\rm{ k } }{ \\rm{ g } } $ ，因保质期问题，准备采用线上、线下相结合的方式，因实际需要，线下采购该水果量不得少于线上采购该水果量的 $ \\dfrac { 1 } { 9 } $ ，请你帮该公司算一算，当线下采购多少 $ { \\rm{ k } }{ \\rm{ g } } $ 水果时最省钱？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南驻马店市第二初级中学 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-14", "keyPointIds": "16438|16486|16544", "keyPointNames": "和差倍分问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577297940567334912", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577297940567334912", "title": "2025年河南省驻马店市第二初级中学中考 一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575475673084502016", "questionArticle": "<p>3．某水果店销售苹果和梨，已知购买3千克苹果和1千克梨共需30元；购买1千克苹果和4千克梨共需32元．</p><p>（1）求每千克苹果和每千克梨的售价；</p><p>（2）若购买苹果和梨共12千克，且总价不超过80元，问最多购买多少千克苹果？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东淄博 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-14", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575475648182919168", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "575475648182919168", "title": "2025年山东省淄博市淄川区中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575475893616812032", "questionArticle": "<p>4．晋祠天龙山新晋为太原首个国家5<i>A</i>级景区，这是太原旅游业发展的一个重要里程碑．已知天龙山门票的单价旺季比淡季贵20元，旺季3张门票的总价和淡季4张门票的总价相同．设旺季门票的单价为 $ x $ 元/张，淡季门票的单价为 $ y $ 元/张，则 $ x，y $ 满足的方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-y=20 \\\\ 4x=3y \\end{cases}  $ B． $ \\begin{cases} x-y=20 \\\\ 3x=4y \\end{cases}  $ C． $ \\begin{cases} y-x=20 \\\\ 4x=3y \\end{cases}  $ D． $ \\begin{cases} y-x=20 \\\\ 3x=4y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西太原 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-14", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575475878689284096", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575475878689284096", "title": "山西太原市2025年中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "575476424485679104", "questionArticle": "<p>5．解方程组：  $ \\begin{cases} 3x-2y=9 \\\\ x-y=4 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江宁波 · 一模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575476397721825280", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575476397721825280", "title": "2025年浙江省宁波市中考数学一模试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575476014299525120", "questionArticle": "<p>6．方程组 $ \\begin{cases} 3x+y=-1 \\\\ y-2x=4 \\end{cases}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=-1 \\\\ y=2 \\end{cases}  $ B． $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ C． $ \\begin{cases} x=1 \\\\ y=-4 \\end{cases}  $ D． $ \\begin{cases} x=0 \\\\ y=4 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津西青 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-14", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575475998138871808", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "575475998138871808", "title": "2025年 天津市西青区当城中学一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "575476575237353472", "questionArticle": "<p>7．解二元一次方程组： $ \\begin{cases} 2x+y=3， \\\\ 3x-y=7． \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江台州 · 一模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575476550121861120", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575476550121861120", "title": "2025年浙江省台州市中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575476784868663296", "questionArticle": "<p>8．3月14日为“国际数学日”，某校在这一天开展数学主题活动，活动分为“智趣挑战”和“巧手闯关”两个项目．若学生参加两个项目得分之和不低于100分，且“智趣挑战”得分不低于55分，则可获得一份校园文创奖品．参加活动时，在正式计分前可先体验一次．小明在体验两个项目时共得90分；在正式计分时，“智趣挑战”项目的得分比体验时增加了 $ 20\\% $ ，“巧手闯关”项目的得分比体验时增加了 $ 10\\% $ ，共得104分．请判断小明是否可以获得校园文创奖品，并说明理由．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京海淀 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-14", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575476745530286080", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575476745530286080", "title": "2025学年北京市海淀区中考数学一模试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577682548794368000", "questionArticle": "<p>9．某商店决定购进<i>A</i>、<i>B</i>两种北京冬奥会纪念品．若购进<i>A</i>种纪念品10件，<i>B</i>种纪念品5件，需要1000元；若购进<i>A</i>种纪念品5件，<i>B</i>种纪念品3件，需要550元．</p><p>（1）求购进<i>A</i>、<i>B</i>两种纪念品的单价；</p><p>（2）若该商店决定拿出1万元全部用来购进这两种纪念品，考虑市场需求，要求购进<i>A</i>种纪念品的数量不少于<i>B</i>种纪念品数量的6倍，且购进<i>B</i>种纪念品数量不少于20件，那么该商店共有几种进货方案？</p><p>（3）若销售每件<i>A</i>种纪念品可获利润20元，每件<i>B</i>种纪念品可获利润30元，在第（2）问的各种进货方案中，哪一种方案获利最大？求出最大利润．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|320000|360000|150000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2022内蒙古呼伦贝尔 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 13, "referenceNum": 4, "createTime": "2025-05-14", "keyPointIds": "16437|16490|16544", "keyPointNames": "销售利润问题|一元一次不等式组的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "444252993476665344", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "444252993476665344", "title": "2022年内蒙古呼伦贝尔市、兴安盟中考数学真题", "paperCategory": 1}, {"id": "577682519660732416", "title": "江西省九江外国语学校2024−2025学年八年级下学期期中考试数学试题", "paperCategory": 1}, {"id": "569704886910099456", "title": "山东省枣庄市第十五中学2024—2025学年下学期八年级数学期中考试模拟试题", "paperCategory": 1}, {"id": "408037105719353344", "title": "江苏省镇江市2023-2024学年八年级上学期期末数学模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577683776551038976", "questionArticle": "<p>10．实数 $ x $ ， $ y $ 满足方程组 $ \\begin{cases} 2x+y=7 \\\\ x+2y=8 \\end{cases}  $ ，则 $ x+y $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3B．−5C．5D．−3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江台州市书生中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-05-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683765515825152", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "577683765515825152", "title": "浙江省台州市书生中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "205678781205356544", "title": "黑龙江省大庆市肇源县2021-2022学年八年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 85, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 85, "timestamp": "2025-07-01T02:10:52.055Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}