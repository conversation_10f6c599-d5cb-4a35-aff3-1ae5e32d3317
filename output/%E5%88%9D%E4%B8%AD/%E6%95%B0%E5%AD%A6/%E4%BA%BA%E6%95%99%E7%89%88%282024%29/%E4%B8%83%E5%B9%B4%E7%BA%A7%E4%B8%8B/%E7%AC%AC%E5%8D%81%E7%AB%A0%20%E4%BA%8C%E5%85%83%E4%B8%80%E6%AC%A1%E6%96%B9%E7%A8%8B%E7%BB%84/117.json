{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 116, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "569342223860408320", "questionArticle": "<p>1．解方程组： $ \\begin{cases} \\dfrac { x-1 } { 3 }-\\dfrac { y-1 } { 6 }=1 \\\\ 2x+y=13 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东淄博 · 一模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-23", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569342198518423552", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569342198518423552", "title": "2025年山东省淄博市高新区中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569342030679154688", "questionArticle": "<p>2．为迎接暑期旅游旺季的到来，某景区商店计划采购一批太阳帽和太阳伞进行售卖，以便游客购买．已知采购4顶太阳帽和3把太阳伞共需要100元，采购6顶太阳帽和4把太阳伞共需要140元．</p><p>(1)求每顶太阳帽和每把太阳伞的进价．</p><p>(2)若该景区商店将太阳帽的售价定为15元/顶，太阳伞的售价定为30元/把，计划购进太阳帽和太阳伞共600顶（把），且购进太阳帽的数量不少于太阳伞数量的2倍，则该景区商店如何设计进货方案，可使销售所获利润最大？最大利润为多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广西桂林 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-04-23", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569342095783141376", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "569342095783141376", "title": "2025年广西壮族自治区桂林市中考一模数学试题", "paperCategory": 1}, {"id": "569342004825464832", "title": "2025年广西防城港市九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569341830732488704", "questionArticle": "<p>3．随着“低碳生活，绿色出行”理念的普及，新能源汽车正逐渐成为人们喜爱的交通工具．某汽车销售公司计划购进一批新能源汽车尝试进行销售．据了解，2辆<i>A</i>型汽车、3辆<i>B</i>型汽车的进价共计80万元；3辆<i>A</i>型汽车、2辆<i>B</i>型汽车的进价共计95万元．</p><p>(1)求<i>A</i>、<i>B</i>两种型号的汽车每辆进价分别为多少万元？</p><p>(2)若该公司计划正好用200万元购进以上两种型号的新能源汽车（两种型号的汽车均购买），若该汽车销售公司销售1辆<i>A</i>型汽车可获利8000元，销售1辆<i>B</i>型汽车可获利5000元，问：购进<i>A</i>型、<i>B</i>型各几辆，才能获得最大利润？最大利润是多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东湛江 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 18, "referenceNum": 3, "createTime": "2025-04-23", "keyPointIds": "16435|16544", "keyPointNames": "分配问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569341801271697408", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569341801271697408", "title": "2025年广东省湛江市中考一模数学试题", "paperCategory": 1}, {"id": "547221736091066368", "title": "2025年广东省佛山市部分学校九年级中考一模数学试题", "paperCategory": 1}, {"id": "445718942876737536", "title": "2024年江苏省无锡市惠山区中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567844797177700352", "questionArticle": "<p>4．方程组 $ \\begin{cases} 2x+y=5 \\\\ x-y=1 \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南湘潭 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-23", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567844778773094400", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567844778773094400", "title": "湖南省湘潭市2024−2025学年九年级下学期初中学业水平模拟数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569705961071353856", "questionArticle": "<p>5．解方程组： $ \\begin{cases} 7x+4y=5① \\\\ 5x-2y=6② \\end{cases}  $ .</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江金华 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569705936396263424", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "569705936396263424", "title": "浙江省金华市东阳市五校联考2024−2025学年九年级下学期月考数学试卷（3月份）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568252766184841216", "questionArticle": "<p>6．阅读材料:善于思考的小军在解方程组 $ \\begin{cases}2x+5y=3,①\\\\ 4x+11y=5②\\end{cases} $ 时,采用了一种“整体代换”的解法:</p><p>解:将方程②变形:4<i>x</i>+10<i>y</i>+<i>y</i>=5,即2(2<i>x</i>+5<i>y</i>)+<i>y</i>=5.③</p><p>把方程①代入③,得2×3+<i>y</i>=5,所以<i>y</i>=−1.</p><p>把<i>y</i>=−1代入①,得<i>x</i>=4.</p><p>所以方程组的解为 $ \\begin{cases}x=4,\\\\ y=-1.\\end{cases} $ </p><p>请你根据以上方法解决下列问题:</p><p>(1)解方程组: $ \\begin{cases}3x-2y=5,\\\\ 9x-4y=19.\\end{cases} $ </p><p>(2)已知<i>x</i>,<i>y</i>满足方程组 $ \\begin{cases}4{x}^{2}-2xy=7,\\\\ 2{x}^{2}+xy=6,\\end{cases} $ 求<i>xy</i>的值.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "questionFeatureName": "阅读材料题", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时2 代入法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252765589250048", "questionArticle": "<p>7．用代入法解二元一次方程组 $ \\begin{cases}x-y=2,\\\\ 2x+y=7\\end{cases} $ 的过程可以用下面的框架图表示:</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/18/2/1/0/0/0/568252743372021760/images/img_1.png\" style=\"vertical-align:middle;\" width=\"272\" alt=\"试题资源网 https://stzy.com\"></p><p>根据以上思路,请用代入法求出方程组 $ \\begin{cases}x-y=0,\\\\ \\left|x\\right.-2y|=2\\end{cases} $ 的解(不用画框架图).</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时2 代入法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252764930744320", "questionArticle": "<p>8．已知关于<i>x</i>,<i>y</i>的二元一次方程组 $ \\begin{cases}2ax+y=5,①\\\\ x-by=2.②\\end{cases} $ </p><p>(1)若<i>a</i>=1,请写出方程①的所有正整数解;</p><p>(2)由于甲看错了方程①中的<i>a</i>得到方程组的解为 $ \\begin{cases}x=-2,\\\\ y=1,\\end{cases} $ 乙看错了方程②中的<i>b</i>得到方程组的解为 $ \\begin{cases}x=1,\\\\ y=3,\\end{cases} $ 求<i>a</i>,<i>b</i>的值及原方程组的解.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时2 代入法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252763747950592", "questionArticle": "<p>9．对于实数<i>x</i>,<i>y</i>我们定义一种新运算<i>F</i>(<i>x</i>,<i>y</i>)=<i>mx</i>+<i>ny</i>(其中<i>m</i>,<i>n</i>均为非零常数),等式右边是通常的四则运算,由这种运算得到的数我们称之为线性数,例如<i>m</i>=3,<i>n</i>=1时,<i>F</i>(2,4)=3×2+1×4=10.若<i>F</i>(1,−3)=6,<i>F</i>(2,5)=1,则<i>F</i>(3,−2)=<u>　　　　</u><i>.&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "questionFeatureName": "新定义问题", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时2 代入法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252764276432896", "questionArticle": "<p>10．关于<i>x</i>,<i>y</i>的方程组 $ \\begin{cases}x+2y=-4,\\\\ ax+by=4\\end{cases} $ 与 $ \\begin{cases}x-2y=12,\\\\ bx-ay=20\\end{cases} $ 的解相同,求(<i>a</i>-<i>b</i>)<sup>2 022</sup>的值.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424|30400", "keyPointNames": "加减消元法解二元一次方程组|有理数的乘方", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时2 代入法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 117, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 117, "timestamp": "2025-07-01T02:14:37.415Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}