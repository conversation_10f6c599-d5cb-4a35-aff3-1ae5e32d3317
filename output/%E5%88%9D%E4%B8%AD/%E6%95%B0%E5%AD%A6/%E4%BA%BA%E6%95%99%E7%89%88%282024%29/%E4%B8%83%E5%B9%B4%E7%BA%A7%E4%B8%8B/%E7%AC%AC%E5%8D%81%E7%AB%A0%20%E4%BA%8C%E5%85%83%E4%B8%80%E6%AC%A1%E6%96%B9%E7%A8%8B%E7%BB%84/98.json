{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 97, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "571886846080229376", "questionArticle": "<p>1．把方程 $ 3x-2y=-1 $ 写成用含 $ x $ 的代数式表示 $ y $ 的形式，则 $ y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南外（NFLS） · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886827847589888", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886827847589888", "title": "江苏省南京市玄武区南京外国语学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886840514387968", "questionArticle": "<p>2．下列各对数值中是二元一次方程 $ x+2y=2 $ 的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=-2 \\\\ y=0 \\end{cases}  $ B． $ \\begin{cases} x=2 \\\\ y=-2 \\end{cases}  $ C． $ \\begin{cases} x=0 \\\\ y=1 \\end{cases}  $ D． $ \\begin{cases} x=-1 \\\\ y=0 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南外（NFLS） · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886827847589888", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886827847589888", "title": "江苏省南京市玄武区南京外国语学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571885617086242816", "questionArticle": "<p>3．下列方程组中，是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2a-3b=11 \\\\ 5b-4c=6 \\end{cases}  $ B． $ \\begin{cases} x+y=4 \\\\ 2x+3y=7 \\end{cases}  $ </p><p>C． $ \\begin{cases} x{^{2}}=9 \\\\ y=2x \\end{cases}  $ D． $ \\begin{cases} x+y=8 \\\\ x{^{2}}-y=4 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆实验中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571885606487236608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571885606487236608", "title": "重庆市实验中学教育集团2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571885890831687680", "questionArticle": "<p>4．如图，将正方形 $ ABCD $ 沿 $ AE $ （点 $ E $ 在边 $ CD $ 上）所在直线折叠后，点 $ D $ 的对应点为点 $ D^{′} $ ， $ \\angle BAD^{′} $ 比 $ \\angle EAD{^{\\prime }} $ 大 $ 20{}\\degree  $ ，若设 $ \\angle BAD^{′}=x{}\\degree  $ ， $ \\angle EAD^{′}=y{}\\degree  $ ，则下列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/28/2/1/0/0/0/571885835596898314/images/img_10.jpg\" style=\"vertical-align:middle;\" width=\"111\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} x-y=20 \\\\ x+2y=90 \\end{cases}  $ B． $ \\begin{cases} x+y=20 \\\\ x+2y=90 \\end{cases}  $ C． $ \\begin{cases} x-y=20 \\\\ 2x+y=90 \\end{cases}  $ D． $ \\begin{cases} x+y=20 \\\\ 2x+y=90 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北黄冈 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16439|16704|26530", "keyPointNames": "几何问题|正方形的性质|折叠问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571885875820273664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571885875820273664", "title": "2025年湖北省黄冈市九年级中考调研考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571886949218164736", "questionArticle": "<p>5．若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 3x-2y=m \\\\ 3y-2x=5 \\end{cases}  $ 的解互为相反数，则<i>m</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南外（NFLS）等校 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16252|16424", "keyPointNames": "相反数的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886928716406784", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886928716406784", "title": "江苏省南京市玄武区外国语学校、科利华中学联考2024-−2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886956449144832", "questionArticle": "<p>6．解方程组：</p><p>(1) $ \\begin{cases} x-\\dfrac { y+2 } { 3 }=1 \\\\ 2x-y=1 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 3x-2y+z=9 \\\\ 2x+y-z=2 \\\\ x+y+z=2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏南外（NFLS）等校 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16424|16443", "keyPointNames": "加减消元法解二元一次方程组|解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886928716406784", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886928716406784", "title": "江苏省南京市玄武区外国语学校、科利华中学联考2024-−2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886941286735872", "questionArticle": "<p>7． $ 3x+2y=11 $ 的正整数解有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1组B．2组C．3组D．无数组</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南外（NFLS）等校 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886928716406784", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886928716406784", "title": "江苏省南京市玄武区外国语学校、科利华中学联考2024-−2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571886435755663360", "questionArticle": "<p>8．“满筐圆实骊珠滑，入口甘香冰玉寒”，提子是一种甘甜爽口的水果，富含维生素<i>C</i>，深受大家喜爱，某水果超市为了解两种提子市场销售情况，购进了一批数量相等的背提和红提供客户对比品尝，购买2千克红提和5千克青提用了78元，购买3千克红提和4千克青提用了75元，</p><p>(1)求每千克红提和青提进价各是多少元．</p><p>(2)若该水果商城决定再次购买同种红提和青提共40千克，且再次购买的费用不超过450元，且每种提子进价保持不变，若红提的销售单价为13元，“青提”的销售单价为18元，则该水果超市应如何进货，使得第二批的红提和青提售完后获得利润最大？最大利润是多少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886413630709760", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886413630709760", "title": "广东省深圳市福田外国语学校2024−2025学年八年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886539032010752", "questionArticle": "<p>9．规定：形如关于 $ x $ 、 $ y $ 的方程 $ mx+ky=b $ 与 $ kx+my=b $ 的两个方程互为共轭二元一次方程，其中 $ k\\mathrm{ ≠ }m $ ；由这两个方程组成的方程组 $ \\begin{cases} mx+ky=b \\\\ kx+my=b \\end{cases}  $ 叫做共轭方程组．</p><p>(1)方程 $ 6x+y=2 $ 的共轭二元一次方程是_；</p><p>(2)若关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} x+\\left ( { 1-a } \\right ) y=b+2 \\\\ \\left ( { 2a-1 } \\right ) x+y=4-b \\end{cases}  $ 为共轭方程组，则 $ a= $ _， $ b= $ _；</p><p>(3)拓展：阅读下列解共轭方程组的方法，然后解答问题：</p><p>解共轭方程组 $ \\begin{cases} 4x+5y=9① \\\\ 5x+4y=9② \\end{cases}  $ 时，可以采用下面的解法：</p><p>②+①得： $ 9x+9y=18 $ ，所以 $ x+y=2 $ ③</p><p>③ $ \\times 4 $ 得： $ 4x+4y=8 $ ④</p><p>①-④得： $ y=1 $ ，从而得 $ x=1 $ </p><p>所以原方程组的解是 $ \\begin{cases} x=1① \\\\ y=1② \\end{cases}  $ </p><p>用上述方法求共轭方程组 $ \\begin{cases} 2023x+2024y=8094 \\\\ 2024x+2023y=8094 \\end{cases}  $ 的解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南河南师大附中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886512666615808", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886512666615808", "title": "河南省新乡市河南师范大学附属中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886534762209280", "questionArticle": "<p>10．解方程组：</p><p>(1) $ \\begin{cases} 3x-2y=46 \\\\ y=3-5x \\end{cases}  $ </p><p>(2) $ \\begin{cases} 2x+3y=12 \\\\ 3x-2y=5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南河南师大附中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886512666615808", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886512666615808", "title": "河南省新乡市河南师范大学附属中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 98, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 98, "timestamp": "2025-07-01T02:12:23.296Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}