{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 108, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "571878932103864320", "questionArticle": "<p>1．中国古代数学著作《增删算法统宗》中记载的“绳索量竿”问题,大意是现有一根竿子和一条绳索,用绳索去量竿子,绳索比竿子长5尺;若将绳索对折去量竿子,绳索就比竿子短5尺,问绳索、竿子各有多长?该问题中的竿子长为<u>　　　　</u>尺<i>.&nbsp;</i></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第四部分 中考新趋势分类练", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "570265512413798400", "questionArticle": "<p>2．开封作为八朝古都，有着深厚的历史文化，也吸引着无数的游客前往观光．开封特产桶子鸡、酱牛肉深受游客的喜爱．已知2包桶子鸡和3包酱牛肉的价格为310元，3包桶子鸡和4包酱牛肉的价格为430元．</p><p>(1)分别求出桶子鸡和酱牛肉的单价；</p><p>(2)若某公司决定购买桶子鸡和酱牛肉共200包作为员工福利，且购买桶子鸡的数量不超过酱牛肉的数量，则应该如何安排购买方案，才能使购买总费用最低，并求出最低费用．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南驻马店 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 3, "createTime": "2025-04-28", "keyPointIds": "16438|16535", "keyPointNames": "和差倍分问题|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570265473310302208", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "570265473310302208", "title": "河南省驻马店市2024−2025学年九年级下学期第二次联考数学试题", "paperCategory": 1}, {"id": "567844487747117056", "title": "河南省焦作市2024−2025学年九年级下学期第二次联考数学试题试卷", "paperCategory": 1}, {"id": "567844279214710784", "title": "河南省济源市2024−2025学年九年级下学期第二次联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571878519061389312", "questionArticle": "<p>3．近年来,中国传统服饰备受大家的青睐,走上国际时装周舞台,大放异彩.某服装店直接从工厂购进长、短两款传统服饰进行销售,进货价和销售价如下表:</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 94.5pt;\"><p>价格<i>/</i>类别</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50.25pt;\"><p>短款</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50.25pt;\"><p>长款</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 94.5pt;\"><p>进货价(元<i>/</i>件)</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50.25pt;\"><p>80</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50.25pt;\"><p>90</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 94.5pt;\"><p>销售价(元<i>/</i>件)</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50.25pt;\"><p>100</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50.25pt;\"><p>120</p></td></tr></table><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>(1)该服装店第一次用4 300元购进长、短两款服装共50件,求两款服装分别购进的件数;</p><p>(2)第一次购进的两款服装售完后,该服装店计划再次购进长、短两款服装共200件(进货价和销售价都不变),且第二次进货总价不高于16 800元.服装店这次应如何设计进货方案,才能获得最大销售利润,最大销售利润是多少?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第三部分 题型3 函数的实际应用", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "571878516926488576", "questionArticle": "<p>4．学校通过劳动教育促进学生树德、增智、强体、育美全面发展,计划组织八年级学生到“开心”农场开展劳动实践活动.到达农场后分组进行劳动,若每位老师带38名学生,则还剩6名学生没老师带;若每位老师带40名学生,则有一位老师少带6名学生.劳动实践结束后,学校在租车总费用2 300元的限额内,租用汽车送师生返校,每辆车上至少要有1名老师.现有甲、乙两种大型客车,它们的载客量和租金如表所示:</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>甲型客车</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>乙型客车</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100pt;\"><p>载客量<i>/</i>(人<i>/</i>辆)</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>45</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>30</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100pt;\"><p>租金<i>/</i>(元<i>/</i>辆)</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>400</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>280</p></td></tr></table><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>(1)参加本次实践活动的老师和学生各有多少名?</p><p>(2)租车返校时,既要保证所有师生都有车坐,又要保证每辆车上至少有1名老师,则共需租车<u>　　　　</u>辆<i>.&nbsp;</i></p><p>(3)学校共有几种租车方案?最少租车费用是多少?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16441|16486|16543", "keyPointNames": "其他问题|一元一次不等式的应用|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第三部分 题型3 函数的实际应用", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "571887477473976320", "questionArticle": "<p>5．若关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} 3x+2y=2 \\\\ 2x+y=m-18 \\end{cases}  $ 的解<i>x</i>，<i>y</i>互为相反数，求<i>m</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏苏州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571887453604192256", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "571887453604192256", "title": "江苏省苏州市立达中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571887476156964864", "questionArticle": "<p>6．解方程：</p><p>(1) $ \\begin{cases} y=2x \\\\ 3y+2x=8 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 2x+3y=8 \\\\ 6x-5y=-4 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏苏州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571887453604192256", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "571887453604192256", "title": "江苏省苏州市立达中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571887471539036160", "questionArticle": "<p>7．若 $ {\\left( { a+b-1 } \\right) ^ {2}}+\\left  | { 2a-b+7 } \\right  | =0 $ ，则 $ a-2b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏苏州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16257|16424", "keyPointNames": "绝对值非负性的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571887453604192256", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "571887453604192256", "title": "江苏省苏州市立达中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571887468322004992", "questionArticle": "<p>8．对 $ x $ ， $ y $ 定义一种新运算 $ T $ ，规定： $ T\\left ( { x,y } \\right ) =axy+by-2 $ （其中 $ a $ ， $ b $ 均为非零常数），这里等式右边是通常的四则运算，例如： $ T\\left ( { 1,0 } \\right ) =a\\times 1\\times 0+b\\times 0-2=-2 $ ，若 $ T\\left ( { 2,1 } \\right ) =5，T\\left ( { -1,2 } \\right ) =0 $ ，则结论正确的个数为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p> $ ① $  $ a=2,b=3 $ ； $ ② $ 若 $ T\\left ( { m,n } \\right ) =1 $ ， $ m $ ， $ n $ 取整数，则 $ \\begin{cases} m=-1 \\\\ n=3 \\end{cases}  $ 或 $ \\begin{cases} m=-2 \\\\ n=-3 \\end{cases}  $ 或 $ \\begin{cases} m=0 \\\\ n=1 \\end{cases}  $ 或 $ \\begin{cases} m=-3 \\\\ n=-1 \\end{cases}  $ ；</p><p> $ ③ $ 若 $ T\\left ( { x,ky } \\right ) =T\\left ( { y,kx } \\right )  $ 对任意有理数 $ x,y $ 都成立（这里 $ T\\left ( { x,y } \\right )  $ 和 $ T\\left ( { y,x } \\right )  $ 均有意义），则 $ k=0 $ ．</p><p>A． $ 0 $ 个　　　　B． $ 1 $ 个　　　　C． $ 2 $ 个　　　　D． $ 3 $ 个</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏苏州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571887453604192256", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "571887453604192256", "title": "江苏省苏州市立达中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571887467202125824", "questionArticle": "<p>9．已知 $ \\begin{cases} x=1 \\\\ y=2 \\\\ z=3 \\end{cases}  $ 是方程组 $ \\begin{cases} ax+by=2 \\\\ by+cz=3 \\\\ cx+az=7 \\end{cases}  $ 的解，则 $ a+b+c $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3　　　　B．2　　　　C．1　　　　D．无法确定</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏苏州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571887453604192256", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "571887453604192256", "title": "江苏省苏州市立达中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571887463775379456", "questionArticle": "<p>10．下列各项是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 5xy+2=0 $　　　　B． $ x{^{2}}+y=1 $　　　　C． $ y+\\dfrac { 1 } { x }=2 $　　　　D． $ 2x-1=5y $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025江苏苏州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571887453604192256", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "571887453604192256", "title": "江苏省苏州市立达中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 109, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 109, "timestamp": "2025-07-01T02:13:41.025Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}