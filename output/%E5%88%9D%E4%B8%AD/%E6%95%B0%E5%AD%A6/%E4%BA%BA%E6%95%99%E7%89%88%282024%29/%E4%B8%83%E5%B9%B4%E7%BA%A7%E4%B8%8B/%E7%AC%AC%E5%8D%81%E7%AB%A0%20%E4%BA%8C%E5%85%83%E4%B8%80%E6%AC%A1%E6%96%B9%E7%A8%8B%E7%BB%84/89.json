{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 88, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "575480070548004864", "questionArticle": "<p>1．根据以下素材，探索完成任务．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">为了抓住文化艺术节的商机，某商店决定购进<i>A</i>、<i>B</i>两种艺术节纪念品</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">素材1</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>此商店若购进<i>A</i>种纪念品8件，<i>B</i>种纪念品3件，需要950元；若购进<i>A</i>种纪念品5件，<i>B</i>种纪念品6件，需要800元．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">素材2</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>若该商店决定购进这两种纪念品100件，考虑市场需求和资金周转，用于购买这100件纪念品的资金不少于7000元，但不超过7500元；</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">素材3</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>该商店销售每件<i>A</i>种纪念品可获利润20元，销售每件<i>B</i>种纪念品可获利润30元；</p></td></tr><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">任务1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">探求商品单价</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>求出购进<i>A</i>、<i>B</i>两种纪念品每件各需多少元？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">任务2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">探究购买方案</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>求出该商店有几种进货方案；</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">任务3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">确定最优方案</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>在所有的进货方案中，哪一种方案获利最大？最大利润是多少元？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东深圳 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-11", "keyPointIds": "16438|16490|16535|16544", "keyPointNames": "和差倍分问题|一元一次不等式组的应用|一次函数的图象和性质|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575480040827166720", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "575480040827166720", "title": "广东省深圳市北京师范大学南山附属学校2024−2025学年八年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574751524284309504", "questionArticle": "<p>2．阅读与思考</p><p>对于未知数是 $ x，y $ 的二元一次方程组，如果方程组的解 $ x,y $ 满足 $ \\left  | { x-y } \\right  | =1 $ ，我们就说方程组的解 $ x $ 与 $ y $ 具有“邻好关系”．</p><p>(1)方程组 $ \\begin{cases} x+y=3 \\\\ 3x-2y=4 \\end{cases}  $ 的解 $ x $ 与 $ y $ 是否具有“邻好关系”呢？说明你的理由．</p><p>(2)若方程组 $ \\begin{cases} 2x+y=7 \\\\ 4x-y=8m-1 \\end{cases}  $ 的解 $ x $ 与 $ y $ 具有“邻好关系”，求 $ m $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏连云港市新海初级中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-10", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574751497012944896", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574751497012944896", "title": "江苏省连云港市新海初级中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574751314380365824", "questionArticle": "<p>3．若 $ \\begin{cases} x=3 \\\\ y=-2 \\end{cases}  $ 是二元一次方程 $ ax+by=-2 $ 的一个解，则 $ 3a-2b+2026 $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北黄冈 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-10", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574751294113488896", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574751294113488896", "title": "湖北省黄冈市2024−2025学年八年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574751520190668800", "questionArticle": "<p>4．解二元一次方程组：</p><p>(1) $ \\begin{cases} 2x+4y=5 \\\\ x=1-y \\end{cases}  $ ；（要求：用代入消元法求解）</p><p>(2) $ \\begin{cases} 3x-4y=1 \\\\ 5x+2y=6 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏连云港市新海初级中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-05-10", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574751497012944896", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574751497012944896", "title": "江苏省连云港市新海初级中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574751311935086592", "questionArticle": "<p>5．在代数式 $ kx+b $ 中，当<i>x</i>分别取 $ -3 $ ， $ -2 $ ， $ -1 {\\rm ，1，2，3} $ 时，对应代数式的值如表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 39.25pt;\"><p><i>x</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ -3 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ -2 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 25.15pt;\"><p> $ -1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p>2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p>3</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 39.25pt;\"><p> $ kx+b $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ -5 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ -3 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 25.15pt;\"><p> $ -1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p>7</p></td></tr></table><p>则 $ 4k-2b+1 $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3B．7C． $ -5 $ D． $ -4 $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000|420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京交通大学附属中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-05-10", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578014982274588672", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578014982274588672", "title": "北京交通大学附属中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "574751294113488896", "title": "湖北省黄冈市2024−2025学年八年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574751318281068544", "questionArticle": "<p>6．解二元一次方程组：</p><p>(1) $ \\begin{cases} y=2x-3 \\\\ 2x+y=5 \\end{cases}  $ </p><p>(2) $ \\begin{cases} \\dfrac { x } { 2 }-\\dfrac { y+1 } { 3 }=3 \\\\ 3x+2y=10 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北黄冈 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-05-10", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574751294113488896", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574751294113488896", "title": "湖北省黄冈市2024−2025学年八年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574751514914234368", "questionArticle": "<p>7．已知 $ \\left ( { a+1 } \\right ) x{^{\\left  | { a } \\right  | }}+3y=0 $ 是关于 $ x，y $ 的二元一次方程，则 $ a $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏连云港市新海初级中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-10", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574751497012944896", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574751497012944896", "title": "江苏省连云港市新海初级中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574751306977419264", "questionArticle": "<p>8．关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} x+2y=k \\\\ x+3y=k+2 \\end{cases}  $ 的解 $ x $ 与 $ y $ 互为相反数，则 $ k $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．4B．3C．2D．6</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北黄冈 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-10", "keyPointIds": "16252|16423", "keyPointNames": "相反数的应用|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574751294113488896", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574751294113488896", "title": "湖北省黄冈市2024−2025学年八年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574751321443573760", "questionArticle": "<p>9．【综合与实践】如图，把两个面积均为 $ 18{ \\rm{ c } }{ \\rm{ m } }{^{2}} $ 的小正方形纸片分别沿对角线裁剪后拼成一个大的正方形纸片．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/06/2/1/0/0/0/574751265873240068/images/img_10.png\" style=\"vertical-align:middle;\" width=\"160\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)大正方形纸片的边长为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ c } }{ \\rm{ m } } $ ；</p><p>(2)若沿此大正方形纸片边的方向裁剪出一个长方形纸片，能否使裁剪出的长方形纸片的长宽之比为 $ 4:3 $ ，且面积为 $ 24{ \\rm{ c } }{ \\rm{ m } }{^{2}} $ ?若能，求剪出的长方形纸片的长和宽；若不能，试说明理由．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖北黄冈 · 期中", "showQuestionTypeCode": "42", "showQuestionTypeName": "综合题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-10", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574751294113488896", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574751294113488896", "title": "湖北省黄冈市2024−2025学年八年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574751509839126528", "questionArticle": "<p>10．方程5x+3y=54共有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）组正整数解．</p><p>A．2B．3C．4D．5</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏连云港市新海初级中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-10", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574751497012944896", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574751497012944896", "title": "江苏省连云港市新海初级中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 89, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 89, "timestamp": "2025-07-01T02:11:21.732Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}