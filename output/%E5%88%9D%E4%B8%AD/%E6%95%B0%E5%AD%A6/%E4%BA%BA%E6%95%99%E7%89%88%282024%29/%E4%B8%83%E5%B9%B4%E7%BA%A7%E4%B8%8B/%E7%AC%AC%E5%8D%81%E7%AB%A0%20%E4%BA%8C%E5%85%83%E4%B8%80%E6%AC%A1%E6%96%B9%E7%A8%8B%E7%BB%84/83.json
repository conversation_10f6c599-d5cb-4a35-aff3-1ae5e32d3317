{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 82, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "575477654805065728", "questionArticle": "<p>1．我国古代数学名著《孙子算经》中有一问题：“今五人共车，两车空；三人共车，八人步．问人与车各几何？”其大意是：现有若干人和车，若每辆车乘坐5人，则空余2辆车；若每辆车乘坐3人，则有8人步行．问人与车各多少？若设有 $ x $ 人， $ y $ 辆车，则所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} \\dfrac { x } { 5 }=y-2 \\\\ \\dfrac { x-8 } { 3 }=y \\end{cases}  $ B． $ \\begin{cases} \\dfrac { x } { 5 }=y-2 \\\\ \\dfrac { x+8 } { 3 }=y \\end{cases}  $ C． $ \\begin{cases} \\dfrac { x } { 5 }=y+2 \\\\ \\dfrac { x-8 } { 3 }=y \\end{cases}  $ D． $ \\begin{cases} \\dfrac { x } { 5 }=y+2 \\\\ \\dfrac { x+8 } { 3 }=y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁锦州 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575477639370027008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575477639370027008", "title": "辽宁省锦州市2024−2025学年下学期九年级数学一模试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "575478715192225792", "questionArticle": "<p>2．如图，某校的饮水机有温水、开水两个按钮，温水和开水共用一个出水口．温水的温度为 $ 30℃ $ ，流速为 $ 20{ \\rm{ m } }{ \\rm{ l } }/{ \\rm{ s } } $ ；开水的温度为 $ 100℃ $ ，流速为 $ 15{ \\rm{ m } }{ \\rm{ l } }/{ \\rm{ s } } $ ．整个接水的过程不计热量损失．</p><table style=\"border: solid 1px;border-collapse: collapse; width:414.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>物理常识：</p><p>开水和温水混合时会发生热传递，开水放出的热量等于温水吸收的热量，可以转化为：开水的体积 $ \\times  $ 开水降低的温度 $ = $ 温水的体积 $ \\times  $ 温水升高的温度．</p></td></tr></table><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/08/2/1/0/0/0/575478626482692097/images/img_16.png\" style=\"vertical-align:middle;\" width=\"181\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）甲同学用空杯先接了 $ 6{ \\rm{ s } } $ 温水，再接 $ 4{ \\rm{ s } } $ 开水，接完后杯中共有水<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ m } }{ \\rm{ l } } $ ；</p><p>（2）乙同学先接了一会儿温水，又接了一会儿开水，得到一杯 $ 210{ \\rm{ m } }{ \\rm{ l } } $ 温度为 $ 40℃ $ 的水（不计热损失），求乙同学分别接温水和开水的时间．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京55中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575478681105117184", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575478681105117184", "title": "北京市第五十五中学2024—2025学年下学期期中调研九年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575478258919055360", "questionArticle": "<p>3．智能快递机器人是一种能够自主完成快递分拣任务的智能设备．它可以自主感知、识别、分拣快递包裹，大大提高了物流企业的分拣速度和效率．某物流公司为提高工作效率，拟购买甲、乙两种型号智能快递机器人共10台进行快递分拣工作，1台甲型智能快递机器人和3台乙型智能快递机器人每天一共可分拣快递36万件；3台甲型智能快递机器人比2台乙型智能快递机器人每天可多分拣快递20万件．</p><p>求：</p><p>(1)甲、乙两种型号智能快递机器人每台每天分别可分拣快递多少万件？</p><p>(2)该物流公司每天快递量不超过100万件，则该公司最多可以购买甲型智能快递分拣机器人多少台？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025辽宁营口 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 8, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16435|16486", "keyPointNames": "分配问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575478236487917568", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575478236487917568", "title": "2025年辽宁省营口市中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577684041744293888", "questionArticle": "<p>4．列方程解应用题</p><p>今年春节期间，坐落于彩云湖畔的“彩云灯会”盛况空前，景区购进富贵牡丹、龙腾虎跃两种手工灯笼销售，其进价如下：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">类型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p style=\"text-align:center;\">进价（元／个）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">富贵牡丹</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p style=\"text-align:center;\">25</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">龙腾虎跃</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p style=\"text-align:center;\">35</p></td></tr></table><p>（1）灯会第一天，景区共购进富贵牡丹、龙腾虎跃两种手工灯笼共80个，进货款恰好为2300元．求这两种灯笼各购进多少个？</p><p>（2）为提升人气，第二天景区决定增加购入进价15元／个的吉祥如意灯笼，并推出促销方案：“买4个富贵牡丹灯笼送1个吉祥如意灯笼，买5个龙腾虎跃灯笼送2个吉祥如意灯笼．若进货款比第一天多了490元，第二天购进数量恰好满足促销方案，求三种灯笼各购进多少个？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市杨家坪中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16420|16437", "keyPointNames": "二元一次方程的解|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577684010760970240", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "577684010760970240", "title": "重庆市杨家坪中学教育集团2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577684035968737280", "questionArticle": "<p>5．解下列方程组．</p><p>（1） $ \\begin{cases} x=2y+5① \\\\ 3x-5y=6② \\end{cases}  $ </p><p>（2） $ \\begin{cases} 6(x+y)-5(2x+y)=-10① \\\\ \\dfrac { x+y } { 6 }-\\dfrac { x-y } { 4 }=-1② \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市杨家坪中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577684010760970240", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "577684010760970240", "title": "重庆市杨家坪中学教育集团2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577683897317629952", "questionArticle": "<p>6．小明分三次和家人、朋友一起参观某科技馆，只有一次恰逢科技馆成人票和学生票都打折，其余两次均按标准票价购买门票（无任何优惠）．三次参观科技馆时，购买成人票和学生票的数量和费用如表所示：</p><table style=\"border: solid 1px;border-collapse: collapse; width:179.3pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>购买门票的数量（张 $ ) $ </p></td><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>购买总费用（元 $ ) $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>成人票</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>学生票</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第一次购物</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>380</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第二次购物</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>340</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第三次购物</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>7</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>310</p></td></tr></table><p>（1）小明以折扣价购买门票是第<u>　　</u>次参观；</p><p>（2）求出每张成人票和每张学生票的标准票价；</p><p>（3）如果成人票和学生票的折扣相同，问：当购买成人票和学生票共15张，并且享受同样的折扣，购票总费用不超过320元时，有几种购票方案？（要求必需购买成人票）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16438|16440|16486", "keyPointNames": "和差倍分问题|表格或图示问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683866338500608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577683866338500608", "title": "重庆市万州第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577683891722428416", "questionArticle": "<p>7．甲、乙两人解同一个关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} ax+5y=15① \\\\ 4x-by=-2② \\end{cases}  $ ，甲看错了方程①中的<i>a</i>，得到方程组的解为 $ \\begin{cases} x=-3 \\\\ y=-1 \\end{cases}  $ ，乙看错了方程②中的<i>b</i>，得到方程组的解为 $ \\begin{cases} x=5 \\\\ y=4 \\end{cases}  $ ．</p><p>（1）求<i>a</i>与<i>b</i>的值；</p><p>（2）求 $ a{^{2022}}-{\\left( { -\\dfrac { 1 } { 10 }b } \\right) ^ {2023}} $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683866338500608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577683866338500608", "title": "重庆市万州第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577683893307875328", "questionArticle": "<p>8．已知关于 $ x,y $ 的方程组 $ \\begin{cases} x-4y=2m-2 \\\\ 2x+y=m+5 \\end{cases}  $ </p><p>（1）若该方程组的解满足 $ x-y=2025 $ ，求 $ m $ 的值；</p><p>（2）若该方程组的解满足 $ x,y $ 均为正数，求 $ m $ 的取值范围．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683866338500608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577683866338500608", "title": "重庆市万州第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577684027743707136", "questionArticle": "<p>9．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+y=-a+1 \\\\ x-y=3a+5 \\end{cases}  $ ，给出下列说法：①当 $ a=0 $ 时，方程组的解也是方程 $ \\dfrac { 3 } { 2 }x+y=0 $ 的一个解；②当<i>x</i>与<i>y</i>互为相反数时， $ a=-3 $ ；③不论<i>a</i>取什么实数， $ 7x+2y $ 的值始终不变；④若 $ a=1 $ ，则 $ x{^{2}}+4y=0 $ ．其中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．①②　　　　B．①③　　　　C．①②③　　　　D．①③④</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市杨家坪中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577684010760970240", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "577684010760970240", "title": "重庆市杨家坪中学教育集团2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577683890019540992", "questionArticle": "<p>10．（1）解方程： $ 3\\left ( { x-2 } \\right ) =5x+4 $ ．&nbsp;&nbsp;&nbsp;&nbsp;</p><p>（2）解方程组： $ \\begin{cases} x=2y \\\\ 3x+2y=8 \\end{cases}  $ </p><p>（3）解不等式 $ \\dfrac { 2+x } { 2 }\\geqslant  \\dfrac { 2x-1 } { 3 }+1 $ &nbsp;&nbsp;&nbsp;&nbsp;</p><p>（4）解不等式组 $ \\begin{cases} x-3\\left ( { x-2 } \\right ) \\leqslant  4 \\\\ 1+2x &gt; 3\\left ( { x-1 } \\right )  \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16402|16423|16489", "keyPointNames": "解一元一次方程|代入消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683866338500608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577683866338500608", "title": "重庆市万州第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 83, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 83, "timestamp": "2025-07-01T02:10:37.794Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}