{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 135, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "564939366050603008", "questionArticle": "<p>1．老陈醋产于清徐县，至今有几千年的醋历史文化．老陈醋色泽为红棕色，味道以“绵、酸、甜、香”为主，是我国四大名醋之首．某生鲜超市计划购进一些坛装和塑料桶装的老陈醋进行销售，据了解，2坛坛装和1桶塑料桶装的老陈醋的进价共计160元；3坛坛装和4桶塑料桶装的老陈醋的进价共计340元．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/08/2/1/0/0/0/564939326393458700/images/img_12.jpg\" style=\"vertical-align:middle;\" width=\"136\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)1坛坛装和1桶塑料桶装的老陈醋的进价分别是多少元？</p><p>(2)若该超市计划恰好用300元购进以上两种包装的老陈醋（两种包装的老陈醋均购进），求该超市有哪几种购进方案？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939346324791296", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939346324791296", "title": "山西省晋城市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939364150583296", "questionArticle": "<p>2．（1）解方程： $ 3+2x=2\\left ( { 7-x } \\right )  $ ；</p><p>（2）解方程组： $ \\begin{cases} 3x+2y=12 \\\\ 5x+4y=-2 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939346324791296", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939346324791296", "title": "山西省晋城市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939361336205312", "questionArticle": "<p>3．已知方程组 $ \\begin{cases} 5x+5y=9 \\\\ 3x+7y=5 \\end{cases}  $ ，则 $ x-y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939346324791296", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939346324791296", "title": "山西省晋城市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939353295724544", "questionArticle": "<p>4．下列方程是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 3x-y=1 $ B． $ 5x=1 $ C． $ x-2y+4 $ D． $ x{^{2}}-5=0 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939346324791296", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939346324791296", "title": "山西省晋城市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564939504471023616", "questionArticle": "<p>5．阅读下列材料，完成相应的任务．</p><p>在平面直角坐标系中，已知点 $ A\\left ( { a+1,a-2 } \\right )  $ ，当 $ a $ 的值发生改变时，点 $ \\mathrm{ A } $ 的位置也会发生改变，为了求点 $ \\mathrm{ A } $ 运动所形成的图象的解析式，令点 $ \\mathrm{ A } $ 的横坐标为 $ x $ ，纵坐标为 $ y $ ，得到了方程组 $ \\begin{cases} x=a+1 \\\\ y=a-2 \\end{cases}  $ ．消去 $ a $ ，得 $ y-x=-3 $ ，即 $ y=x-3 $ ，可以发现，点 $ \\mathrm{ A } $ 随 $ a $ 的变化而运动所形成的图象的解析式是 $ y=x-3 $ ．</p><p>(1)求点 $ A\\left ( { a-3,2a-7 } \\right )  $ 随 $ a $ 的变化而运动所形成的图象的解析式．</p><p>(2)点 $ \\left ( { 1,0 } \\right )  $ <u>&nbsp;&nbsp;&nbsp;&nbsp;</u>点 $ A\\left ( { a-3,2a-7 } \\right )  $ 随 $ a $ 的变化而运动所形成的图象上，点 $ \\left ( { 2t,4t-1 } \\right )  $ <u>&nbsp;&nbsp;&nbsp;&nbsp;</u>点 $ A\\left ( { a-3,2a-7 } \\right )  $ 随 $ a $ 的变化而运动所形成的图象上（横线上填“在”或“不在”）．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西临汾 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16424|16535", "keyPointNames": "加减消元法解二元一次方程组|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939480093728768", "questionFeatureName": "阅读材料题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939480093728768", "title": "山西省临汾市部分学校2023−2024学年八年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939632820920320", "questionArticle": "<p>6．已知关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} ax-4y=10 \\\\ 5x+by=42 \\end{cases}  $ ，甲由于看错了方程组中的<i>a</i>，得到的方程组的解为 $ \\begin{cases} x=12 \\\\ y=-3 \\end{cases}  $ ，乙由于看错了<i>b</i>，得到方程组的解为 $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ ．</p><p>(1)求<i>a</i>，<i>b</i>的值．</p><p>(2)若方程组 $ \\begin{cases} ax-4y=10 \\\\ 5x+by=42 \\end{cases}  $ 的解与方程组 $ \\begin{cases} 2mx+ny=6 \\\\ mx+2ny=-6 \\end{cases}  $ 的解相同，求 $ 2m-n $ 的值．</p><p>(3)在（2）的条件下，是否存在<i>k</i>的值，使得关于<i>x</i>的方程 $ \\dfrac { 3\\left ( { kx+2 } \\right )  } { 2 }-m=\\dfrac { -nx+10 } { 5 } $ 有无数个解？若存在，求<i>k</i>的值；若不存在，请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山西临汾 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16402|16424|16427", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组|同解方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939609844523008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939609844523008", "title": "山西省临汾市尧都区多校2023−2024学年七年级下学期期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939632040779776", "questionArticle": "<p>7．为了丰富学生的阅读内容，拓展阅读活动，某校计划从甲、乙两家书店中购买《跟我学数学》和《数学名师讲解》）两种图书.甲、乙两家书店这两本书的标价相同.已知购买5本《跟我学数学》与购买4本《数学名师讲解》的价格相同；购买3本《跟我学数学》比购买2本《数学名师讲解》多花费10元．</p><p>(1)分别求《跟我学数学》和《数学名师讲解》的单价．</p><p>(2)书店推出多买多送活动，甲、乙两家书店的优惠方案如下：</p><p>甲书店的优惠方案：购买18本《数学名师讲解》以上（不包括18本），《数学名师讲解》打六折．</p><p>乙书店的优惠方案：购买18本《数学名师讲解》以上（不包括18本），《跟我学数学）打五折．</p><p>该校计划购买两种图书共60本，设购买《数学名师讲解》 $ m\\left ( { m &gt; 18 } \\right )  $ 本．</p><p>①求当<i>m</i>为何值时，在两家书店所用的费用相同．</p><p>②当该校计划购买《数学名师讲解》28本，若只能在其中一家书店购买，请问在哪家书店购买划算？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山西临汾 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16406|16438", "keyPointNames": "销售盈亏问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939609844523008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939609844523008", "title": "山西省临汾市尧都区多校2023−2024学年七年级下学期期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939630572773376", "questionArticle": "<p>8．阅读下列材料，完成任务：</p><p>解方程组 $ \\begin{cases} \\left ( { a-2 } \\right ) +2\\left ( { b+1 } \\right ) =-5 \\\\ 2\\left ( { a-2 } \\right ) -3\\left ( { b+1 } \\right ) =4 \\end{cases}  $ ，某同学提供了如下解法：</p><p>解：设 $ a-2=x $ ， $ b+1=y $ ，则原方程可化为 $ \\begin{cases} x+2y=-5① \\\\ 2x-3y=4② \\end{cases}  $ ，解得 $ \\begin{cases} x=-1 \\\\ y=-2 \\end{cases}  $ ，</p><p>∴ $ \\begin{cases} a-2=-1 \\\\ b+1=-2 \\end{cases}  $ ，解得 $ \\begin{cases} a=1 \\\\ b=-3 \\end{cases}  $ </p><p>∴原方程组的解为 $ \\begin{cases} a=1 \\\\ b=-3 \\end{cases}  $ </p><p>任务：</p><p>(1)由上述解法可以看出，对于一些较复杂的解方程组问题，若把其中某些部分看成一个<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，用字母代替，则能使复杂的问题简单化，因而把这种解方程组的方法称为换元法．</p><p>(2)已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} mx+ny=p \\\\ rx+qy=s \\end{cases}  $ 的解为 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ ，则关于<i>a</i>，<i>b</i>的方程组 $ \\begin{cases} m\\left ( { a+5 } \\right ) +n\\left ( { b-1 } \\right ) =p \\\\ r\\left ( { a+5 } \\right ) +q\\left ( { b-1 } \\right ) =s \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>(3)利用上述方法解方程组： $ \\begin{cases} \\left ( { 3a-1 } \\right ) +2\\left ( { b-2 } \\right ) =4 \\\\ 4\\left ( { 3a-1 } \\right ) -\\left ( { b-2 } \\right ) =7 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山西临汾 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939609844523008", "questionFeatureName": "阅读材料题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939609844523008", "title": "山西省临汾市尧都区多校2023−2024学年七年级下学期期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939626743373824", "questionArticle": "<p>9．已知有理数 $ m $ 、 $ n $ ，定义一种新运算“*”，规定： $ m*n=am-bn+5 $ （ $ a $ 、 $ b $ 均不为零）．等式右边的运算是通常的四则运算，例如 $ 3*4=3a-4b+5 $ ．已知 $ 2*3=1 $ ， $ 3*\\left ( { -1 } \\right ) =10 $ ，则关于 $ x $ 的不等式 $ x∗\\left ( { 2x-3 } \\right )   &lt;  5 $ 的最小整数解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山西临汾 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939609844523008", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939609844523008", "title": "山西省临汾市尧都区多校2023−2024学年七年级下学期期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939628114911232", "questionArticle": "<p>10．解方程组： $ \\begin{cases} \\dfrac { x+y } { 2 }-y=-2① \\\\ x+y=4② \\end{cases}  $  </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西临汾 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939609844523008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939609844523008", "title": "山西省临汾市尧都区多校2023−2024学年七年级下学期期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 136, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 136, "timestamp": "2025-07-01T02:16:57.447Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}