{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 186, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "549407196821364736", "questionArticle": "<p>1．春节期间，为迎接“新春大庙会”的到来，重庆某商家推出了两款具有重庆特色的伴手礼盒，分别是重庆坝坝茶和千年非遗荣昌陶．其中，坝坝茶的售价为 $ 200 $ 元一盒，荣昌陶的售价为 $ 300 $ 元一盒．已知在 $ 1 $ 月份商家按售价销售两款商品共 $ 300 $ 件，且销售额不低于 $ 85000 $ 元．</p><p>(1)求1月份至多卖出坝坝茶多少盒？</p><p>(2)随着春节即将结束， $ 2 $ 月份商家推出了促销活动,在 $ 1 $ 月份的售价基础上，每盒坝坝茶的售价降低 $ a\\% $ ，每盒荣昌陶进行九折促销活动,现已知 $ 2 $ 月份坝坝茶的销售额为 $ 56000 $ 元，荣昌陶的销售额为 $ 67500 $ 元，而两款伴手礼盒的总销量相较 $ 1 $ 月份增长了 $ 1 $ 倍，求 $ a $ 的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市育才中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 3, "createTime": "2025-03-02", "keyPointIds": "16444|16486", "keyPointNames": "三元一次方程组的应用|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557318539314503680", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "557318539314503680", "title": "重庆育才中学教育集团2024−2025学年九年级下学期第二次定时作业数学试题", "paperCategory": 1}, {"id": "549407164235816960", "title": "重庆市第一中学校2024−2025学年九年级下学期入学考试数学试题", "paperCategory": 1}, {"id": "549407424089726976", "title": "重庆一中寄宿学校2024−2025学年九年级下学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549407855176097792", "questionArticle": "<p>2．把 $ x=1 $ 和 $ x=-2 $ 分别代入式子 $ x{^{2}}+bx+c $ 中，值分别为 $ 2 $ 和 $ 6 $ ，则 $ bc= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京师范大学附属实验中学分校 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-02", "keyPointIds": "16305|16424", "keyPointNames": "代数式求值|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549407847106256896", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549407847106256896", "title": "北京市北京师范大学附属实验中学2024−2025学年七年级下学期开学考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "550455559775887360", "questionArticle": "<p>3．计算：</p><p>(1) $ \\sqrt { 4 }+\\left  | { 2-\\sqrt { 5 } } \\right  | +{\\left( { -\\dfrac { 1 } { 3 } } \\right) ^ {-2}} $ ；</p><p>(2)解方程组： $ \\begin{cases} 3\\left ( { x+2y } \\right ) =2x+1， \\\\ x-y=2. \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市第一中学校 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-03-01", "keyPointIds": "16288|16372|16424", "keyPointNames": "算术平方根|负整数指数幂|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "550455523025395712", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "550455523025395712", "title": "重庆市第一中学2024—2025学年下学期八年级开学考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "550455421053476864", "questionArticle": "<p>4．初中生涯即将结束，同学们为友谊长存，决定互送礼物，于是去某礼品店购进了一批适合学生的毕业纪念品．已知购进3个<i>A</i>种礼品和2个<i>B</i>种礼品共需54元，购进3个<i>A</i>种礼品比购进5个<i>B</i>种礼品多花12元．问<i>A</i>，<i>B</i>两种礼品每个的进价是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东济南 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-01", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "550455361708269568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "550455361708269568", "title": "山东省济南市高新区2024-−2025学年九年级下学期数学开学测", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "550455384927936512", "questionArticle": "<p>5．解方程组 $ \\begin{cases} y=2x-1① \\\\ 4x-3y=12② \\end{cases}  $ 时，把①代入②，得（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 4\\left ( { 2x-1 } \\right ) -3y=12 $ B． $ 4x-\\left ( { 2x-1 } \\right ) =12 $ </p><p>C． $ 4x-3\\times 2x-1=12 $ D． $ 4x-3\\left ( { 2x-1 } \\right ) =12 $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东济南 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-01", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "550455361708269568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "550455361708269568", "title": "山东省济南市高新区2024-−2025学年九年级下学期数学开学测", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "550454918873653248", "questionArticle": "<p>6．（1）计算： $ \\left ( { -1 } \\right ) \\times 2{^{2}}+\\left ( { 5-7 } \\right ) \\div \\left ( { -2 } \\right )  $ ；</p><p>（2）解方程组： $ \\begin{cases} x+2y=3 \\\\ x-2y=1 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西南宁 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-01", "keyPointIds": "16278|16424", "keyPointNames": "有理数的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "550454899017818112", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "550454899017818112", "title": "广西南宁市第十中学2024−2025学年下学期九年级开学考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "550043398331211776", "questionArticle": "<p>7．若关于<i>x</i>的不等式组 $ \\begin{cases} x-(4a-2)\\leqslant  2 \\\\ \\dfrac { 3x-1 } { 2 }  <  \\dfrac { x+2 } { 3 } \\end{cases}  $ 的解集为 $ x\\leqslant  4a $ ，且关于<i>y</i>，<i>z</i>的二元一次方程组 $ \\begin{cases} y+2z=4a+5 \\\\ 2y+z=2a+4 \\end{cases}  $ 的解满足 $ y+z\\geqslant  -1 $ ，则满足条件的所有整数<i>a</i>的和为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -3 $　　　　B． $ -2 $　　　　C．0　　　　D．3</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆八中 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-28", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "550043370812383232", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "550043370812383232", "title": "重庆市第八中学校2024−2025学年八年级下学期开学数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "550043390701772800", "questionArticle": "<p>8．关于<i>x</i>,<i>y</i>的方程组 $ \\begin{cases} 3x-y=5 \\\\ 4ax+5by=-22 \\end{cases}  $ 与 $ \\begin{cases} \\; & 2x+3y=-4 \\\\ \\; & ax-by=8 \\end{cases}  $ 有相同的解，则 $ {\\left( { -a } \\right) ^ {b}}= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;</u><u>.</u></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆八中 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-28", "keyPointIds": "16424|30400", "keyPointNames": "加减消元法解二元一次方程组|有理数的乘方", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "550043370812383232", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "550043370812383232", "title": "重庆市第八中学校2024−2025学年八年级下学期开学数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549095539423354880", "questionArticle": "<p>9．某校需要购进一批消毒液，经了解，某商场供应<i>A</i>，<i>B</i>两种类型的消毒液．购买2瓶<i>A</i>类型消毒液所需费用和3瓶<i>B</i>类型消毒液所需费用相同；购头3瓶<i>A</i>类型消毒液和1瓶<i>B</i>类型消毒液共需要55元．</p><p>(1)求<i>A</i>，<i>B</i>两种类型消毒液的单价．</p><p>(2)若根据需求，需要购买<i>A</i>，<i>B</i>两种类型消毒液共300瓶，其中<i>A</i>类型消毒液的数量不少于<i>B</i>类型消毒液数量的 $ \\dfrac { 1 } { 2 } $ ，如何购买才能使得花费最少，最少花费为多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川绵阳 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-28", "keyPointIds": "16441|16547", "keyPointNames": "其他问题|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549095516086247424", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549095516086247424", "title": "四川省绵阳市江油市八校联考2024−2025学年九年级下学期开学数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549095528690130944", "questionArticle": "<p>10．（中国古代数学问题）5头牛和2只羊，共值银10两,2头牛和5只羊，共值银8两．问一头牛和一只羊各值银几两？设一头牛值银 $ x $ 两，一只羊值银 $ y $ 两，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2x+5y=10, \\\\ 5x+2y=8 \\end{cases}  $　　　　B． $ \\begin{cases} 5x+2y=8, \\\\ 2x+5y=10 \\end{cases}  $　　　　C． $ \\begin{cases} 5x+2y=10, \\\\ 2x+5y=8 \\end{cases}  $　　　　D． $ \\begin{cases} 5x+5y=10, \\\\ 2x+2y=8 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川绵阳 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-28", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549095516086247424", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549095516086247424", "title": "四川省绵阳市江油市八校联考2024−2025学年九年级下学期开学数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 187, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 187, "timestamp": "2025-07-01T02:22:58.908Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}