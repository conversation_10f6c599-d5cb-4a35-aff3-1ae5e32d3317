{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 168, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "554779794061172736", "questionArticle": "<p>1．关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2ax+3y=18 \\\\ -x+5by=17 \\end{cases}  $ （其中<i>a</i>，<i>b</i>是常数）的解为 $ \\begin{cases} x=3 \\\\ y=4 \\end{cases}  $ ，则方程组 $ \\begin{cases} 2a\\left ( { x+y } \\right ) +3\\left ( { x-y } \\right ) =18 \\\\ \\left ( { x+y } \\right ) -5b\\left ( { x-y } \\right ) =-17 \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈工大附中 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779775455240192", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554779793302003712", "questionArticle": "<p>2．已知关于<i>x</i>，<i>y</i>的二元一次方程 $ \\left ( { 3x-2y+9 } \\right ) +m\\left ( { 2x+y-1 } \\right ) =0 $ ，不论<i>m</i>取何值，方程总有一个固定不变的解，这个解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈工大附中 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779775455240192", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554779792509280256", "questionArticle": "<p>3．甲、乙两人共同解方程组 $ \\begin{cases} ax+5y=15① \\\\ 4x-by=-2② \\end{cases}  $ 由于甲看错了方程①中的<i>a</i>，得到方程组的解为 $ \\begin{cases} x=-3 \\\\ y=-1 \\end{cases}  $ ，乙看错了方程②中的<i>b</i>，得到方程组的解为 $ \\begin{cases} x=5 \\\\ y=4 \\end{cases}  $ ，则 $ 10a+b $ 的值<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈工大附中 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779775455240192", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554779790265327616", "questionArticle": "<p>4．已知 $ \\begin{cases} x=3 \\\\ y=5 \\end{cases}  $ 是关于 $ x $ ， $ y $ 的方程 $ ax-2y=2 $ 的一组解，那么 $ a $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈工大附中 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779775455240192", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554779788860235776", "questionArticle": "<p>5．甲乙两种商品原来的单价和为100元，因市场变化，甲商品降价10%，乙商品提价40%，调价后两种商品的单价和比原来的单价和提高了20%，若设甲、乙两种商品原来的单价分别为<i>x</i>元,<i>y</i>元，则下列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）．</p><p>A． $ \\begin{cases} x+y=100 \\\\ (x+10\\%)x+(1-40\\%)y=100\\times (1+20\\%) \\end{cases}  $　　　　B． $ \\begin{cases} x+y=100 \\\\ (x-10\\%)x+(1+40\\%)y=100\\times 20\\% \\end{cases}  $</p><p>C． $ \\begin{cases} x+y=100 \\\\ (1-10\\%)x+(1+40\\%)y=100\\times (1+20\\%) \\end{cases}  $　　　　D． $ \\begin{cases} x+y=100 \\\\ (1+10\\%)x+(1-40\\%)y=100\\times 20\\% \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈工大附中 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779775455240192", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "554779788050735104", "questionArticle": "<p>6．已知 $ x+y+7z=0 $ ， $ x-y-3z=0\\left ( { xyz\\ne 0 } \\right )  $ ，则 $ \\dfrac { 2x+y+z } { 2x-y+z }= $ （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 4 $</p><p>B． $ -4 $</p><p>C． $ 5 $</p><p>D． $ -5 $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈工大附中 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16371|16424", "keyPointNames": "分式化简求值|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779775455240192", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "554780118528335872", "questionArticle": "<p>7．对于任意的有理数<i>a</i>，<i>b</i>，<i>c</i>，<i>d</i>，我们规定 $ \\begin{vmatrix} a & b \\\\ c & d \\end{vmatrix} =ad-bc $ ．如 $ \\begin{vmatrix} -2 & -4 \\\\ 3 & 5 \\end{vmatrix} =\\left ( { -2 } \\right ) \\times 5-\\left ( { -4 } \\right ) \\times 3=2 $ ，根据这一规定，解答下列问题：</p><p>(1)化简 $ \\begin{vmatrix} x+3y & 2x \\\\ 3y & 2x+y \\end{vmatrix}  $ ；</p><p>(2)若<i>x</i>，<i>y</i>同时满足 $ \\begin{vmatrix} 3 & -2 \\\\ y & x \\end{vmatrix} =5 $ ， $ \\begin{vmatrix} x & 1 \\\\ y & 2 \\end{vmatrix} =8 $ ，求<i>x</i>，<i>y</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16333|16424", "keyPointNames": "整式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780097598758912", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780097598758912", "title": "湖南省永州市第十二中学2023−2024学年七年级下学期第一次月考数学模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780115562962944", "questionArticle": "<p>8．两根铁棒直立于桶底水平的木桶中，在木桶中加入水后，一根露出水面的长度是它的总长度的 $ \\dfrac { 1 } { 3 } $ ，另一根露出水面的长度是它的总长度的 $ \\dfrac { 1 } { 5 } $ ，两根铁棒长度之和为 $ 220{ \\rm{ c } }{ \\rm{ m } } $ ，此时木桶中水的深度是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ c } }{ \\rm{ m } } $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780097598758912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780097598758912", "title": "湖南省永州市第十二中学2023−2024学年七年级下学期第一次月考数学模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780117077106688", "questionArticle": "<p>9．解方程组．</p><p>(1) $ \\begin{cases} x+2y=0 \\\\ 3x+4y=6 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 2\\left ( { x+y } \\right ) +3\\left ( { x-y } \\right ) =30 \\\\ 2\\left ( { x+y } \\right ) -3\\left ( { x-y } \\right ) =6 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780097598758912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780097598758912", "title": "湖南省永州市第十二中学2023−2024学年七年级下学期第一次月考数学模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780114774433792", "questionArticle": "<p>10．对于有理数定义一种新运算： $ x{ \\rm{ ■ } }y=ax+by-5 $ ，其中<i>a</i>，<i>b</i>为常数，已知 $ 1{ \\rm{ ■ } }2=9{ \\rm{ ， } }\\left ( { \\mathrm{ - }3 } \\right ) { \\rm{ ■ } }3=-2 $ ，则 $ 2a+b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780097598758912", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780097598758912", "title": "湖南省永州市第十二中学2023−2024学年七年级下学期第一次月考数学模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 169, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 169, "timestamp": "2025-07-01T02:20:50.935Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}