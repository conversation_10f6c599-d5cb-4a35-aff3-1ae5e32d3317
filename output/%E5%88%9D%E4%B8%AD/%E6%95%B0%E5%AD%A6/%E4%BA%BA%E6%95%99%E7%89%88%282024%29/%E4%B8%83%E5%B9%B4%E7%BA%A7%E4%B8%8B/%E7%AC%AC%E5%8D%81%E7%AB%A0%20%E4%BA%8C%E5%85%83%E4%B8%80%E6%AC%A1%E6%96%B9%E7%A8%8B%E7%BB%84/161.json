{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 160, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "557696605333266432", "questionArticle": "<p>1．为改善农村居民生活环境，某村庄要修整一条长360米的街道，修整的任务由甲、乙两个工程队接力完成． 已知甲工程队每天修整16米，乙工程队每天修整24米，完成该任务两工程队共用时20天．求甲、乙两工 程队分别修整街道多少米．小明、小芳、小华三人的解题思路如下：</p><p>小明：设甲工程队修整街道<i>x</i> 米，则可列方程为 $ \\dfrac { x } { 16 }+\\dfrac { 360-x } { 24 }=20 $ ，</p><p>小芳：设甲工程队修整街道<i>x</i> 米，乙工程队修整街道<i>y</i> 米，则可列方程组为 $ \\begin{cases} x+y=360 \\\\ \\dfrac { x } { 24 }+\\dfrac { y } { 16 }=20 \\end{cases}  $  ，小华：设甲工程队工作<i>m</i> 天，乙工程队工作 <i>n</i> 天，则可列方程组为 $ \\begin{cases} m+n=20 \\\\ 16m+24n=360 \\end{cases}  $ ，其中，思路正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．小明B．小明、小芳C．小明、小华D．三人都对</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南信阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-21", "keyPointIds": "16405|16431", "keyPointNames": "工程问题|工程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557696593618575360", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "557696593618575360", "title": "河南省信阳市2024−2025学年九年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "558041208221638656", "questionArticle": "<p>2．若<i>x</i>，<i>y</i>满足方程组 $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} 2x+3y=2 \\\\ 3x+2y=3 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ ，则 $ x+y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏盐城 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "558041193952616448", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "558041193952616448", "title": "江苏省盐城市盐都区第一共同体2024−2025学年九年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "558041206963347456", "questionArticle": "<p>3．明代《算法统宗》有一首饮酒数学诗：“肆中饮客乱纷纷，薄酒名醨厚酒醇。醇酒一瓶醉三客，薄酒三瓶醉一人.共同饮了一十九，三十三客醉颜生.试问高明能算士，几多醨酒几多醇？”设有醇酒 $ x $ 瓶，薄酒 $ y $ 瓶．根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=19 \\\\ 3x+y=33 \\end{cases}  $　　　　B． $ \\begin{cases} x+y=19 \\\\ x+3y=33 \\end{cases}  $　　　　C． $ \\begin{cases} x+y=19 \\\\ \\dfrac { 1 } { 3 }x+y=33 \\end{cases}  $　　　　D． $ \\begin{cases} x+y=19 \\\\ 3x+\\dfrac { 1 } { 3 }y=33 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏盐城 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-21", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "558041193952616448", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "558041193952616448", "title": "江苏省盐城市盐都区第一共同体2024−2025学年九年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557236865822138368", "questionArticle": "<p>4．定义：关于<i>x</i>，<i>y</i>的二元一次方程 $ ax+by=c $ （其中 $ a\\ne b\\ne c $ ）中的常数项<i>c</i>与未知数系数<i>a</i>，<i>b</i>之一互换，得到的方程叫“交换系数方程”，例如： $ ax+by=c $ 的“交换系数方程”为 $ cx+by=a $ 或 $ ax+cy=b $ ．</p><p>(1)方程 $ 3x+2y=4 $ 的“交换系数方程”为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)已知关于<i>x</i>，<i>y</i>的二元一次方程 $ ax+by=c $ 的系数满足 $ a+b+c=0 $ ，且 $ ax+by=c $ 与它的“交换系数方程”组成的方程组的解恰好是关于<i>x</i>，<i>y</i>的二元一次方程 $ mx+ny=p $ 的一个解，求 $ m+p+n+2024 $ 的值；</p><p>(3)已知<i>m</i>，<i>n</i>，<i>t</i>都是整数，并且 $ \\left ( { 10m-t } \\right ) x+2023y=m+t $ 是关于<i>x</i>，<i>y</i>的二元一次方程 $ \\left ( { 1+n } \\right ) x+2023y=2m+2 $ 的“交换系数方程”，求 $ 9m-n $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南长沙 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557236839221862400", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557236839221862400", "title": "湖南省长沙市麓山外国语实验中学2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557236864769368064", "questionArticle": "<p>5．为贯彻落实党中央、国务院决策部署，陕西省推动“消费品以旧换新”行动，对购买一、二级能效绿色智能家电的消费者予以一定置换补贴．补贴标准为产品最终销售价格的 $ 15\\% $ ，对购买 $ 1 $ 级及以上能效或水校的产品，额外再给予产品最终销售价格的 $ 5\\% $ 的补贴．某学校分两次更新部分电脑和空调（二级能效），第一次购买 $ 1 $ 台电脑和 $ 2 $ 台空调，补贴前需花费 $ 10000 $ 元；第二次购买 $ 2 $ 台电脑和 $ 1 $ 台空调，补贴前需花费 $ 12200 $ 元．</p><p>(1)补贴前．学校购买一台电脑和一台空调所需的资金分别是多少元？</p><p>(2)若该校两次购买的所有电脑和空调均参加以旧换新活动，则一共能获得多少元的国家补贴？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-21", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557236839221862400", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557236839221862400", "title": "湖南省长沙市麓山外国语实验中学2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557236861195821056", "questionArticle": "<p>6．<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/18/2/1/0/0/0/557236801045307395/images/img_13.png\" style=\"vertical-align:middle;\" width=\"131\" alt=\"试题资源网 https://stzy.com\">已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x-3y=3 \\\\ ax+by=1 \\end{cases}  $ 和 $ \\begin{cases} 3x+2y=11 \\\\ ay-bx=3 \\end{cases}  $ 的解相同，求 $ 2a-b $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557236839221862400", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557236839221862400", "title": "湖南省长沙市麓山外国语实验中学2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557236860335988736", "questionArticle": "<p>7．解下列方程组：</p><p>(1) $ \\begin{cases} \\dfrac { x-1 } { 2 }+\\dfrac { y+1 } { 3 }=1 \\\\ x+y=4 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} x+2y+z=0 \\\\ 2x-y-z=1 \\\\ 3x-y-z=2 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-03-21", "keyPointIds": "16424|16443", "keyPointNames": "加减消元法解二元一次方程组|解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557236839221862400", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557236839221862400", "title": "湖南省长沙市麓山外国语实验中学2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557236858687627264", "questionArticle": "<p>8．对于三个数<i>a</i>、<i>b</i><i>、</i><i>c</i>，用 $ M\\left \\{a,b,c\\right \\}  $ 表示这三个数的平均数，用 $ {\\rm  \\min }\\left \\{a,b,c\\right \\}  $ 表示这三个数中最小的数．</p><p>（1）若 $ {\\rm  \\min }\\left \\{1,3,4-2x\\right \\} =x $ ，则<i>x</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>（2）若 $ M\\left \\{3x+y,x+2y+11,4x-y-2\\right \\} ={\\rm  \\min }\\left \\{3x+y,x+2y+11,4x-y-2\\right \\}  $ ，</p><p>则 $ x-y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南长沙 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557236839221862400", "questionFeatureName": "新定义问题", "questionMethodName": "分类讨论思想", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557236839221862400", "title": "湖南省长沙市麓山外国语实验中学2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557236856909242368", "questionArticle": "<p>9．若 $ x{^{m-1}}+y{^{n-3}}=2 $ 是关于 $ x,y $ 的二元一次方程，则 $ m-n= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-21", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557236839221862400", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557236839221862400", "title": "湖南省长沙市麓山外国语实验中学2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557236853545410560", "questionArticle": "<p>10．方程 $ |x-2y-3\\left  | { + } \\right  | x+y-1|=2 $ 的整数解的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1个B．2个C．3个D．4个</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-21", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557236839221862400", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557236839221862400", "title": "湖南省长沙市麓山外国语实验中学2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 161, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 161, "timestamp": "2025-07-01T02:19:53.658Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}