{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 39, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "585961042615775232", "questionArticle": "<p>1．平面直角坐标系内有一点 $ A(a,b) $ ，若 $ \\left  | { a+b+5 } \\right  | +\\left  | { 2a-b+1 } \\right  | =0 $ ，则 $ \\mathrm{ A } $ 点坐标为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16257|16424", "keyPointNames": "绝对值非负性的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585961045849583616", "questionArticle": "<p>2．要修一段420千米长的公路．甲工程队先干2天乙工程队加入，两队再合干2天完成任务；如果乙队先干2天，甲、乙两队再合干3天完成任务，问甲、乙两个工程队每天各能修路多少千米？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585961044201222144", "questionArticle": "<p>3．解方程组</p><p>（1） $ \\begin{cases} y=x+3 \\\\ 7x+5y=9\\, \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} 3x-y=5\\, \\\\ x+y=-1\\, \\end{cases}  $ ；</p><p>（3） $ \\begin{cases} 2x+5y=8 \\\\ 3x+2y=-10 \\end{cases}  $ ；</p><p>（4） $ \\begin{cases} \\dfrac { x } { 2 }-\\dfrac { y+1 } { 3 }=1 \\\\ 3x+2y=10 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585961043274280960", "questionArticle": "<p>4．关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} x+2y=3a-1 \\\\ 2x+y=7 \\end{cases}  $ 的解满足 $ x+y=3 $ ，则 $ a $ 的值是<u>&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585961033367334912", "questionArticle": "<p>5．已知方程 $ \\left ( { m-2 } \\right ) x+y{^{\\left  | { m-1 } \\right  | }}-3=0 $ 是关于 $ x $ 、 $ y $ 的二元一次方程，则 $ m $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2B．0或2C．1D．0</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "585961041823051776", "questionArticle": "<p>6．将一张面值100元的人民币，兑换成10元或20元的零钱，兑换方案有<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>种．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585961032645914624", "questionArticle": "<p>7．下列不是二元一次方程组的有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>① $ \\begin{cases} x=6 \\\\ y=5 \\end{cases}  $ ；② $ \\begin{cases} x+y=2 \\\\ y-z=3 \\end{cases}  $ ；③ $ \\begin{cases} xy=1 \\\\ x=2 \\end{cases}  $ ；④ $ \\begin{cases} \\dfrac { 1 } { x }=1 \\\\ \\dfrac { 1 } { y }=1 \\end{cases}  $ </p><p>A．1个B．2个C．3个D．4个</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "585961031723167744", "questionArticle": "<p>8．以下式子中，是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x+2y=2 $ B． $ x+1=-8 $ </p><p>C． $ x-\\dfrac { 1 } { y }=7 $ D． $ x{^{2}}=3y $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "587590566587707392", "questionArticle": "<p>9．已知一个二元一次方程组的解是 $ \\begin{cases}x=-1,\\\\ y=-2,\\end{cases} $ 则这个方程组可以是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A. $ \\begin{cases}x+y=-3,\\\\ 2x-y=0\\end{cases} $ &nbsp;&nbsp;&nbsp;&nbsp;\t</p><p>B. $ \\begin{cases}x+y=-3,\\\\ x-2y=1\\end{cases} $ &nbsp;&nbsp;&nbsp;&nbsp;\t</p><p>C. $ \\begin{cases}2x=y,\\\\ y-x=-3\\end{cases} $ &nbsp;&nbsp;&nbsp;&nbsp;\t</p><p>D. $ \\begin{cases}x-y=1,\\\\ 2x-y=-4\\end{cases} $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 7, "referenceNum": 8, "createTime": "2025-06-10", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519536082712567808", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "519536082712567808", "title": "第10讲　二元一次方程组的概念（练习）", "paperCategory": 10}, {"id": "587590562850582528", "title": "第11讲　二元一次方程组的概念（练习）BS.", "paperCategory": 10}, {"id": "552431899458707456", "title": "XJ第19讲　二元一次方程组的概念（练习）", "paperCategory": 10}, {"id": "552902331047649280", "title": "第5讲 二元一次方程组的概念（练习）", "paperCategory": 10}, {"id": "537320561409564672", "title": "第1讲　二元一次方程组的概念（练习）LJ七下", "paperCategory": 10}, {"id": "534030111047196672", "title": "第5讲　二元一次方程组的概念（练习）", "paperCategory": 10}, {"id": "523817379203162112", "title": "第1讲　二元一次方程组的概念（练习）", "paperCategory": 10}, {"id": "311233827719913472", "title": "重庆市北碚区朝阳中学2022-2023学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "587396324401655808", "questionArticle": "<p>10．用大小完全相同的长方形纸片在直角坐标系中摆成如图所示图案，已知 $ A\\left ( { -1,5 } \\right )  $ ，则<i>B</i>点的坐标是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/23/2/1/0/0/0/592360878151868417/images/img_1.png\" style='vertical-align:middle;' width=\"195\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\left ( { -6,4 } \\right )  $</p><p>B． $ \\left ( { -\\dfrac { 20 } { 3 },\\dfrac { 14 } { 3 } } \\right )  $</p><p>C． $ \\left ( { -6,5 } \\right )  $</p><p>D． $ \\left ( { -\\dfrac { 14 } { 3 },\\dfrac { 11 } { 3 } } \\right )  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|370000|130000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河北石家庄 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 12, "referenceNum": 3, "createTime": "2025-06-10", "keyPointIds": "16424|16439|16499", "keyPointNames": "加减消元法解二元一次方程组|几何问题|坐标确定位置", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587396310203936768", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "587396310203936768", "title": "2025年河北省石家庄市四区联考中考模拟数学试题", "paperCategory": 1}, {"id": "559853104558022656", "title": "山东省淄博市张店区第七中学2024—2025学年下学期3月月考七年级数学试题（五四学制）", "paperCategory": 1}, {"id": "208544269614751744", "title": "重庆市九龙坡区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 40, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 40, "timestamp": "2025-07-01T02:05:31.233Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}