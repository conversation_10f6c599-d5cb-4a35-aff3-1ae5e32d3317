{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 102, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "571884284555862016", "questionArticle": "<p>1．用10块大小形状完全相同的长方形木板拼成如图所示的一个长方形，如果设每块长方形木板的长和宽分别是 $ x { \\rm{ c } }{ \\rm{ m } } $ 和 $ y { \\rm{ c } }{ \\rm{ m } } $ ，下列方程组错误的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/28/2/1/0/0/0/571884185838723079/images/img_7.png\" style=\"vertical-align:middle;\" width=\"176\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} 2x=120 \\\\ x+3y=120 \\end{cases}  $ B． $ \\begin{cases} 2x=120 \\\\ x+2y=5y \\end{cases}  $ </p><p>C． $ \\begin{cases} 2x=x+3y \\\\ x+3y=120 \\end{cases}  $ D． $ \\begin{cases} 2x=x+3y \\\\ x+2y=120 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东潍坊 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-04-30", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884270358142976", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884270358142976", "title": "山东省潍坊市2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "571884380517343232", "title": "山东省潍坊市诸城市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571884771304841216", "questionArticle": "<p>2．当<i>m</i>，<i>n</i>．都是实数，且满足 $ 2m-n=6 $ 时，称 $ Q\\left ( { m,n } \\right )  $ 为巧妙点．</p><p>(1)若 $ A\\left ( { m,4 } \\right )  $ 是巧妙点，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)判断点 $ P\\left ( { 4,-2 } \\right )  $ 是否为巧妙点，并说明理由；</p><p>(3)已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x+y=a \\\\ x-y=b \\end{cases}  $ 且 $ a、b $ 为正整数，若以方程组的解为坐标的点 $ B\\left ( { x,y } \\right )  $ 是巧妙点，求 $ C\\left ( { a,b } \\right )  $ 的坐标．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884753671987200", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571884753671987200", "title": "省级重点 福建省福州第十六中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884512327540736", "questionArticle": "<p>3．随着交通安全意识的增强，某城镇居民开始积极购买头盔以保证骑行安全．某小商店购进<i>A</i>种头盔3个和<i>B</i>种头盔4个共需345元，<i>A</i>种头盔4个和<i>B</i>种头盔3个共需390元．</p><p>(1)求<i>A</i>，<i>B</i>两种头盔的单价各是多少元；</p><p>(2)若该商店计划正好用450元购进<i>A</i>，<i>B</i>两种头盔 $ (A,B $ 两种头盔均购买 $ ) $ ，销售1个<i>A</i>种头盔可获利35元，销售1个<i>B</i>种头盔可获利15元，求该商店共有几种购买方案？假如这些头盔全部售出，最大利润是多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南郑州 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-04-30", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545291179627159552", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "545291179627159552", "title": "河南省郑州市新密市、荥阳市、登封市 \r\n \r\n2024−2025学年八年级（上）期末数学试卷", "paperCategory": 1}, {"id": "571884487744724992", "title": "山东省淄博市张店区第八中学2024−2025学年七年级下学期期中考试数学试卷 【五四制】", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884772026261504", "questionArticle": "<p>4．（列二元一次方程组解应用题）2025年春节档票房冠军《哪吒之魔童闹海》受到广大青少年、小朋友的喜爱，某电影院有两个不同规格的观影厅，长行厅每张票售价40元，星瀚厅每张票售价50元．李叔叔购买了20张票，一共用了950元，请问李叔叔分别买了多少张长行厅票和星瀚厅票？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884753671987200", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571884753671987200", "title": "省级重点 福建省福州第十六中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884511492874240", "questionArticle": "<p>5．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} mx+2ny=4 \\\\ x+y=2 \\end{cases}  $ 与 $ \\begin{cases} x-y=4 \\\\ nx+\\left ( { m-1 } \\right ) y=3 \\end{cases}  $ 有相同的解．</p><p>(1)求这个相同的解；</p><p>(2)求<i>m</i>，<i>n</i>的值；</p><p>(3)若（1）中的解也是关于<i>x</i>，<i>y</i>的方程 $ \\left ( { 3-a } \\right ) x+\\left ( { 2a+1 } \\right ) y=3 $ 的解，求<i>a</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东淄博 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 8, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16424|16427", "keyPointNames": "加减消元法解二元一次方程组|同解方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884487744724992", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884487744724992", "title": "山东省淄博市张店区第八中学2024−2025学年七年级下学期期中考试数学试卷 【五四制】", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884279363313664", "questionArticle": "<p>6．下列哪一组 $ x，y $ 的值不是方程 $ x-2y=5 $ 的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=-1 \\\\ y=-3 \\end{cases}  $ B． $ \\begin{cases} x=1 \\\\ y=-2 \\end{cases}  $ C． $ \\begin{cases} x=3 \\\\ y=4 \\end{cases}  $ D． $ \\begin{cases} x=4 \\\\ y=-\\dfrac { 1 } { 2 } \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025山东潍坊 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-04-30", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884270358142976", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884270358142976", "title": "山东省潍坊市2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "571884380517343232", "title": "山东省潍坊市诸城市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571884769358684160", "questionArticle": "<p>7．如图，某校的饮水机有温水、开水两个按钮，温水和开水共用一出水口．利用图中信息解决下列问题：</p><table style=\"border: solid 1px;border-collapse: collapse; width:492.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 316.5pt;\"><p style=\"text-indent:56pt;\">物理常识开水和温水混合时会发生热传递，开水放出的热量等于温水吸收的热量，可以转化为“开水的体积 $ \\times  $ 开水降低的温度 $ = $ 温水的体积 $ \\times  $ 温水升高的温度”．</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 176.25pt;\"><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/28/2/1/0/0/0/571884722080489480/images/img_8.png\" style=\"vertical-align:middle;\" width=\"193\" alt=\"试题资源网 https://stzy.com\"></p></td></tr></table><p>(1)王老师拿空水杯先接了 $ 14{ \\rm{ s } } $ 的温水，又接了 $ 8{ \\rm{ s } } $ 的开水，刚好接满，且水杯中的水温为 $ t{ \\rm{ ° } }{ \\rm{ C } } $ ．</p><p>①王老师的水杯容量为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ m } }{ \\rm{ l } } $ ；</p><p>②求此时杯中的水温（不计热损失）；</p><p>(2)嘉琪同学拿空水杯先接了一会儿温水，又接了一会儿开水，得到一杯 $ 210{ \\rm{ m } }{ \\rm{ l } } $ 温度为 $ 40{}\\degree { \\rm{ C } } $ 的水（不计热损失），求嘉琪同学的接水时间．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884753671987200", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571884753671987200", "title": "省级重点 福建省福州第十六中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884508330369024", "questionArticle": "<p>8．解下列方程组：</p><p>（1） $ \\begin{cases} 2x-y=1 \\\\ 3x+2y=5 \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} 3(x-1)=y+5 \\\\ \\dfrac { y-1 } { 3 }=\\dfrac { x } { 5 }+1 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东淄博 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884487744724992", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884487744724992", "title": "山东省淄博市张店区第八中学2024−2025学年七年级下学期期中考试数学试卷 【五四制】", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884506682007552", "questionArticle": "<p>9．用8个一样大小的长方形，恰好可以拼成一个大的长方形如图1，也可以拼成如图2那样的正方形，中间还留了一个洞，恰好是边长为3cm的小正方形，则一个小长方形的面积是 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>cm<sup>2</sup>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/28/2/1/0/0/0/571884470510329860/images/img_8.png\" style=\"vertical-align:middle;\" width=\"238\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东淄博 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884487744724992", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884487744724992", "title": "山东省淄博市张店区第八中学2024−2025学年七年级下学期期中考试数学试卷 【五四制】", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884505822175232", "questionArticle": "<p>10．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+y=3k+2 \\\\ 4x-3y=-k+5 \\end{cases}  $ 且 $ x-2y=-3 $ ，则<i>k</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东淄博 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884487744724992", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884487744724992", "title": "山东省淄博市张店区第八中学2024−2025学年七年级下学期期中考试数学试卷 【五四制】", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 103, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 103, "timestamp": "2025-07-01T02:13:00.083Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}