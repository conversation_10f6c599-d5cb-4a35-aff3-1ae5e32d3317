{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 156, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "559115976496685056", "questionArticle": "<p>1．善于思考的小明在解方程组 $ \\begin{cases} 4x+10y=6① \\\\ 8x+22y=10② \\end{cases}  $ 时，采用了一种“整体代换”的思想.</p><p>解法如下：将方程 $ 8x+22y=10 $ 变形为： $ 2(4x+10y)+2y=10 $ ③</p><p>把方程①代入③得， $ 2\\times 6+2y=10 $ ，则 $ y=-1 $ ；把 $ y=-1 $ 代入①得， $ x=4 $ ，</p><p>所以方程组的解为： $ \\begin{cases} x=4 \\\\ y=-1 \\end{cases}  $ </p><p>请你运用“整体代换”的思想解决下列问题：</p><p>（1）解方程组 $ \\begin{cases} 2x-3y=7 \\\\ 6x-5y=25 \\end{cases}  $ ；（2）已知 $ x $ 、 $ y $ 、 $ z $ 满足 $ \\begin{cases} 3x-2z+12y=47 \\\\ x+z+4y=19 \\end{cases}  $ ，试求 $ z $ 的值.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建泉州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-26", "keyPointIds": "16425|16426", "keyPointNames": "二元一次方程组的特殊解法|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559115954417868800", "questionMethodName": "整体思想", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559115954417868800", "title": "福建省永春第二中学2024−2025学年下学期七年级3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559115973002829824", "questionArticle": "<p>2．关于<i>x,y</i>的二元一次方程组 $ \\{\\hspace{-0.5em}  \\begin{array} {} 5x+3y=23 \\\\ x+y=p \\end{array} \\hspace{-0.5em}  $ 的解是正整数，则整数<i>P</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建泉州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-26", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559115954417868800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559115954417868800", "title": "福建省永春第二中学2024−2025学年下学期七年级3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559115971220250624", "questionArticle": "<p>3．若关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} 2x+y=m \\\\ x+2y=2m+2 \\end{cases}  $ 的解满足 $ x+y=-7 $ ，则 $ m $ 为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建泉州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-26", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559115954417868800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559115954417868800", "title": "福建省永春第二中学2024−2025学年下学期七年级3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559115969110515712", "questionArticle": "<p>4．把方程 $ 3x-y=4 $ 改写成用含<i>x</i>的代数式表示<i>y</i>，则 $ y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025福建泉州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-26", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559115954417868800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559115954417868800", "title": "福建省永春第二中学2024−2025学年下学期七年级3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559115968313597952", "questionArticle": "<p>5．已知关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} ax+by=c \\\\ bx+ay=d \\end{cases}  $ 的解为 $ \\begin{cases} x=1 \\\\ y=3 \\end{cases}  $ ，若<i>m</i>，<i>n</i>满足二元一次方程组 $ \\begin{cases} a\\left ( { m+n } \\right ) +b\\left ( { m-n } \\right ) =2c \\\\ b\\left ( { m+n } \\right ) +a\\left ( { m-n } \\right ) =2d \\end{cases}  $ ，则 $ m+2n= $ （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0B．2C．4D．6</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000|330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建泉州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-03-26", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559115954417868800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559115954417868800", "title": "福建省永春第二中学2024−2025学年下学期七年级3月月考数学试题", "paperCategory": 1}, {"id": "446418849996513280", "title": "浙江省宁波市海曙区部分学校2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "559115965809598464", "questionArticle": "<p>6．小亮解方程组 $ \\begin{cases} 2x+y=● \\\\ 2x-y=12 \\end{cases}  $ 的解为 $ \\begin{cases} x=5 \\\\ y=Δ \\end{cases}  $ ，由于不小心滴上了两滴墨水，刚好遮住了两个数●和△，则两个数●与△的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} ●=8 \\\\ Δ=2 \\end{cases}  $ B． $ \\begin{cases} ●=-8 \\\\ Δ=-2 \\end{cases}  $ C． $ \\begin{cases} ●=-8 \\\\ Δ=2 \\end{cases}  $ D． $ \\begin{cases} ●=8 \\\\ Δ=-2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|410000|350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建泉州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 16, "referenceNum": 4, "createTime": "2025-03-26", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559115954417868800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559115954417868800", "title": "福建省永春第二中学2024−2025学年下学期七年级3月月考数学试题", "paperCategory": 1}, {"id": "424698918628270080", "title": "河南省洛阳市偃师市实验中学2022-2023学年七年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "206707688847220736", "title": "2022年七年级上册沪科版数学第3章3.4二元一次方程组的应用课时练习", "paperCategory": 1}, {"id": "169375729578188800", "title": "2022年七年级下册人教版数学第八章8.3实际问题与二元一次方程组课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "559115136360816640", "questionArticle": "<p>7．《九章算术》是我国古代一部著名的算书，它的出现标志着中国古代数学形成了完整的体系，其中卷八方程[七]中记载：“今有牛五，羊二，直金十两；牛二、羊五，直金八两，问牛、羊直金几何？”译文：“假设有5头牛，2只羊共值金10两；2头牛，5只羊共值金8两，问每头牛、每只羊各值金多少两？”设每头牛值金<i>x</i>两，每只羊值金<i>y</i>两，那么下面列出的方程组中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+2y=10 \\\\ 2x+5y=8 \\end{cases}  $ B． $ \\begin{cases} 5x+2y=8 \\\\ 2x+5y=10 \\end{cases}  $ C． $ \\begin{cases} x+y=10 \\\\ 2x+5y=8 \\end{cases}  $ D． $ \\begin{cases} 5x+2y=8 \\\\ x+y=10 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|120000|350000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025天津河东 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 3, "createTime": "2025-03-26", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559115121169047552", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559115121169047552", "title": "天津市河东区2024−2025学年下学期九年级结课考试数学试题（1）", "paperCategory": 1}, {"id": "414560369274822656", "title": "福建省漳州市台商区华侨中学2023-2024学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "238998507448737792", "title": "四川省成都市实验外国语学校2021-2022学年八年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "559472999478370304", "questionArticle": "<p>8．对于有理数<i>x</i>，<i>y</i>，定义新运算 $ ★: $  $ x★y=ax+by $ ，其中<i>a</i>，<i>b</i>是常数，已 $ 1★2=5 $ ， $ \\left ( { -1 } \\right ) ★1=1 $ ，则 $ 2★\\left ( { -5 } \\right )  $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆七中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-25", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559472981002461184", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "559472981002461184", "title": "重庆市第七中学校2024−2025学年七年级下学期第一次数学定时作业", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559472990901018624", "questionArticle": "<p>9．一种商品有大、小盒两种包装，3大盒、4小盒共装108瓶，2大盒、3小盒共装76瓶．大盒与小盒各装多少瓶？若设大盒每盒装<i>x</i>瓶，小盒每盒装<i>y</i>瓶，则可列方程组得（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3x+2y=76 \\\\ 3x+4y=108 \\end{cases}  $　　　　B． $ \\begin{cases} 3x+4y=76 \\\\ 2x+3y=108 \\end{cases}  $　　　　C． $ \\begin{cases} 3x+4y=108 \\\\ 2x+3y=76 \\end{cases}  $　　　　D． $ \\begin{cases} 3x+2y=76 \\\\ 2x+4y=108 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆七中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-25", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559472981002461184", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "559472981002461184", "title": "重庆市第七中学校2024−2025学年七年级下学期第一次数学定时作业", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "559472825783853056", "questionArticle": "<p>10．某商店销售10台<i>A</i>型和20台 $ B $ 型电脑的利润为4000元，销售20台<i>A</i>型和10台 $ B $ 型电脑的利润为3500元．</p><p>(1)求每台<i>A</i>型电脑和 $ B $ 型电脑的销售利润；</p><p>(2)该商店计划一次购进两种型号的电脑共100台，其中 $ B $ 型电脑的进货量不超过<i>A</i>型电脑的3倍，预期进<i>A</i>型电脑 $ x $ 台，这100台电脑的销售总利润为 $ y $ 元．</p><p>①求 $ y $ 关于 $ x $ 的函数关系式；</p><p>②该商店购进<i>A</i>型、 $ B $ 型电脑各多少台，才能使销售总利润最大？最大利润是多少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆七中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-25", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559472799045165056", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "559472799045165056", "title": "重庆市第七中学校2024−2025学年八年级下学期第一次定时作业数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 157, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 157, "timestamp": "2025-07-01T02:19:25.421Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}