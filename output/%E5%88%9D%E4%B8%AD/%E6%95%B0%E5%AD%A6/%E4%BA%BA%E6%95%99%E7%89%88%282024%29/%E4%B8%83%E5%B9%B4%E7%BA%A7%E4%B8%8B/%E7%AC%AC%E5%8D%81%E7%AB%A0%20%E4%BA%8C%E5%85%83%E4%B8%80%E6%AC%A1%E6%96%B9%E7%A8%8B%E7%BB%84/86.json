{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 85, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "576949290980909056", "questionArticle": "<p>1．某市2023年的耕地面积和林地面积共有1000万亩，2024年该市响应国家“退耕还林”号召，将一部分耕地恢复为林地后，耕地面积减少了 $ 20\\% $ ，林地面积增加了 $ 60\\% $ ．求2023年耕地面积和林地面积分别是多少万亩？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽合肥 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-14", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576949265995440128", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "576949265995440128", "title": "2025年安徽省合肥市蜀山区中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "576949634813173760", "questionArticle": "<p>2．为积极响应国家“双碳”战略，推进绿色发展，某县全力打造生态优先、绿色低碳的工业园区，经反复研讨与周密规划，决定在园区内大规模安装光伏板和风力发电机组，以此构建稳定可靠的绿色能源供应体系．</p><p>这两类设备的安装需求各有不同，具体如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse; width:256.5pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>设备类型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>每台所需技术人员</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>每台投入成本（万元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>光伏板</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 5 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 12 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>风力机组</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 8 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 18 $ </p></td></tr></table><p>园区共有技术人员 $ 92 $ 人，全部参与安装且每人只负责一种设备，总投入资金为 $ 216 $ 万元．问光伏板和风力发电机组的安装数量各是多少台？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽淮北 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-13", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576949609009815552", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "576949609009815552", "title": "2025年安徽省淮北市二中联考中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "576949952175185920", "questionArticle": "<p>3．每年的5月20日为中国学生营养日，2024年营养日的主题是“奶豆添营养，少油更健康”．某学校为每位学生定制了盒装的牛奶和豆浆，它们的营养成分表如下：</p><table style=\"border: solid 1px;border-collapse: collapse; width:228.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 83.25pt;\"><p style=\"text-align:center;\">营养成分</p><p style=\"text-align:center;\">食品种类</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72pt;\"><p style=\"text-align:center;\">一盒牛奶</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72.75pt;\"><p style=\"text-align:center;\">一盒豆浆</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 83.25pt;\"><p style=\"text-align:center;\">能量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72pt;\"><p style=\"text-align:center;\"> $ 280{ \\rm{ k } }{ \\rm{ J } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72.75pt;\"><p style=\"text-align:center;\"> $ 210{ \\rm{ k } }{ \\rm{ J } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 83.25pt;\"><p style=\"text-align:center;\">蛋白质</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72pt;\"><p style=\"text-align:center;\"> $ 3.5{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72.75pt;\"><p style=\"text-align:center;\"> $ 4.2{ \\rm{ g } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 83.25pt;\"><p style=\"text-align:center;\">脂肪</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72pt;\"><p style=\"text-align:center;\"> $ 3.5{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72.75pt;\"><p style=\"text-align:center;\"> $ 2.4{ \\rm{ g } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 83.25pt;\"><p style=\"text-align:center;\">碳水化合物</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72pt;\"><p style=\"text-align:center;\"> $ 5.6{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72.75pt;\"><p style=\"text-align:center;\"> $ 1.7{ \\rm{ g } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 83.25pt;\"><p style=\"text-align:center;\">钠</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72pt;\"><p style=\"text-align:center;\"> $ 65{ \\rm{ m } }{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72.75pt;\"><p style=\"text-align:center;\"> $ 13{ \\rm{ m } }{ \\rm{ g } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 83.25pt;\"><p style=\"text-align:center;\">钙</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72pt;\"><p style=\"text-align:center;\"> $ 130{ \\rm{ m } }{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72.75pt;\"><p style=\"text-align:center;\">&nbsp;</p></td></tr></table><p>某天，初中生小石从这两种食品中恰好摄入了 $ 770{ \\rm{ k } }{ \\rm{ J } } $ 能量和 $ 11.2{ \\rm{ g } } $ 蛋白质．</p><p>（1）小石喝了牛奶和豆浆各多少盒？</p><p>（2）初中生每日脂肪摄入量约为 $ 59\\sim 73{ \\rm{ g } } $ ．若小石这天已经从其它食品中摄入 $ 60{ \\rm{ g } } $ 脂肪，在他喝完牛奶和豆浆后，脂肪摄入量是否超标，并说明理由．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京石景山 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-13", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576949905790377984", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "576949905790377984", "title": "2025年北京市石景山区九年级统一练习（一模）数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "576950604410433536", "questionArticle": "<p>4．甘肃地震牵动着全国人民的心，某地区开展了“一方有难，八方支援”抢险救灾活动，准备组织400名志愿者参加救灾．现需租用若干辆大、小客车将志愿者送往灾区，已知租用的大、小客车满员时载客情况如表格所示：</p><table style=\"border: solid 1px;border-collapse: collapse; width:234pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">小客车（辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">大客车（辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">合计载客量（人）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">105</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">110</p></td></tr></table><p>（1）求满员载客时每辆小客车与每辆大客车分别能坐多少名志愿者？</p><p>（2）若计划租用小客车 $ m $ 辆，大客车 $ n $ 辆，大小客车都要有，一次全送完，且每辆车都坐满；</p><p>①请你设计出所有的租车方案：</p><p>②若小客车每辆租金1000元，大客车每辆租金1900元，请选出最省钱的租车方案，并求出最少租金．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广西南宁 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 12, "referenceNum": 2, "createTime": "2025-05-13", "keyPointIds": "16412|16434", "keyPointNames": "和差倍分问题|方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576950577197789184", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "576950577197789184", "title": "2025年广西壮族自治区南宁市中考二模数学试题", "paperCategory": 1}, {"id": "575321432969748480", "title": "广西南宁第二中学2024-2025学年七年级下学期期中数学试题", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "576950242911756288", "questionArticle": "<p>5．随着新能源汽车的推广，某市大力推进公共充电桩的建设．据最新资讯，目前该市有甲、乙两种型号的公共充电桩．已知安装3个甲型充电桩和2个乙型充电桩共需成本 $ 5.6 $ 万元；安装2个甲型充电桩和3个乙型充电桩共需成本 $ 5.4 $ 万元．</p><p>（1）求每个甲型充电桩和乙型充电桩的安装成本分别是多少万元；</p><p>（2）若该市计划再安装甲、乙两种型号的充电桩共50个，且总成本不超过54万元，求最多能安装多少个甲型充电桩．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东东莞 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-13", "keyPointIds": "16441|16486", "keyPointNames": "其他问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576950219360739328", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "576950219360739328", "title": "2025年广东省东莞市初中学业水平模拟联考（二）数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "576950340668399616", "questionArticle": "<p>6．滨海学校在“玩转数学”为主题的数学节活动中，将 $ x $ 份奖品分给了 $ y $ 名学生，若每人分4份，则剩余30份；若每人分5份，则还缺20份．根据题意可列方程（组）（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 4y-30=5y+20 $ B． $ 4x+20=5x-30 $ </p><p>C． $ \\begin{cases} 4y+30=x \\\\ 5y-20=x \\end{cases}  $ D． $ \\begin{cases} \\dfrac { x+30 } { 4 }=y \\\\ \\dfrac { x-20 } { 5 }=y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东宝中 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-13", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576950328030961664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "576950328030961664", "title": "2025年广东省深圳市宝安九年级中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "576950590741196800", "questionArticle": "<p>7．如图，用12块形状和大小均相同的小长方形纸片拼成一个宽是40的大长方形，若设小长方形的长为<i>x</i>，宽为<i>y</i>，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/12/2/1/0/0/0/576950550215827464/images/img_18.png\" style=\"vertical-align:middle;\" width=\"179\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} 4y=40 \\\\ y=3x \\end{cases}  $ B． $ \\begin{cases} x+y=40 \\\\ 3x=2x+3y \\end{cases}  $ C． $ \\begin{cases} x+y=40 \\\\ 3y=2y+3x \\end{cases}  $ D． $ \\begin{cases} x-y=40 \\\\ x=3y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西南宁 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-05-13", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576950577197789184", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "576950577197789184", "title": "2025年广西壮族自治区南宁市中考二模数学试题", "paperCategory": 1}, {"id": "575321432969748480", "title": "广西南宁第二中学2024-2025学年七年级下学期期中数学试题", "paperCategory": 11}], "questionTypeCode": "1"}, {"questionId": "576950465562193920", "questionArticle": "<p>8．《九章算术》是中国古代的一本重要数学著作，其中有一道方程的应用题：“五只雀、六只燕，共重16两，雀重燕轻．互换其中一只，恰好一样重．问每只雀、燕的重量各为多少？”解：设雀每只<i>x</i>两，燕每只<i>y</i>两，则可列出方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+6y=16 \\\\ 5x+y=5y+x \\end{cases}  $ B． $ \\begin{cases} 5x+6y=16 \\\\ 4x+y=5y+x \\end{cases}  $ </p><p>C． $ \\begin{cases} 6x+5y=16 \\\\ 6x+y=5y+x \\end{cases}  $ D． $ \\begin{cases} 6x+5y=16 \\\\ 5x+y=4y+x \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆清华 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-05-13", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585959679529885696", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585959679529885696", "title": "重庆市清华中学校2024−2025学年七年级下学期第二阶段定时作业数学试题", "paperCategory": 1}, {"id": "576950452278833152", "title": "2025年广东省深圳市罗湖区部分学校中考模拟数学试卷（4月）", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "576950594910334976", "questionArticle": "<p>9．已知<i>x、y</i>满足方程组 $ \\begin{cases} 2x+y=5 \\\\ x+2y=4 \\end{cases}  $ ，则<i>x</i>+<i>y</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西南宁 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-05-13", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576950577197789184", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "576950577197789184", "title": "2025年广西壮族自治区南宁市中考二模数学试题", "paperCategory": 1}, {"id": "575321432969748480", "title": "广西南宁第二中学2024-2025学年七年级下学期期中数学试题", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "576952380702371840", "questionArticle": "<p>10．（1）计算： $ 2{ \\rm{ t } }{ \\rm{ a } }{ \\rm{ n } }60{}\\degree -\\sqrt { 12 }+{\\left( { π-2 } \\right) ^ {0}} $ ；&nbsp;&nbsp;&nbsp;&nbsp;</p><p>（2）解方程组： $ \\begin{cases} 2x-y=5 \\\\ 3x+4y=2 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东聊城 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-13", "keyPointIds": "16323|16423|16834", "keyPointNames": "零指数幂|代入消元法解二元一次方程组|特殊角的三角函数值", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576952358925545472", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "576952358925545472", "title": "2025年山东省临清市中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 86, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 86, "timestamp": "2025-07-01T02:10:58.869Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}