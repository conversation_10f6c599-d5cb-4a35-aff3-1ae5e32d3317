{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 121, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "568253316599160832", "questionArticle": "<p>1．若<i>a</i>+2<i>b</i>=8,3<i>a</i>+4<i>b</i>=18,则<i>a</i>+<i>b</i>的值为<span style=\"font-family: &quot;Courier New&quot;;\"><u> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</u></span><i>. </i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 一次方程与方程组 全章综合训练《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253235007365120", "questionArticle": "<p>2．某中学学生张强到某服装商场进行社会调查,了解到该商场为了激励营业员的工作积极性,实行“月总收入=基本工资+计件奖金”的方法(即营业员月总收入由基本工资和计件奖金两部分构成),并获得如下信息:</p><p>营业员<i>A</i>:月销售件数200件,月总收入4 500元;</p><p>营业员<i>B</i>:月销售件数300件,月总收入5 000元.</p><p>假设营业员的月基本工资为x元,销售每件服装奖励y元.</p><p>(1)求x,y的值;</p><p>(2)商场为了多销售服装,向顾客推荐一种购买方式:如果购买甲服装3件,乙服装2件,丙服装1件共需1 500元;如果购买甲服装1件,乙服装2件,丙服装3件共需1 620元.某顾客想购买甲、乙、丙服装各一件,共需多少元?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.5 三元一次方程组及其解法《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253233883291648", "questionArticle": "<p>3．有甲、乙、丙三种规格的钢条,已知甲种2根,乙种1根,丙种3根,共长23米;甲种1根,乙种4根,丙种5根,共长36米.问甲种1根,乙种2根,丙种3根,共长<u>　　　　</u>米.&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.5 三元一次方程组及其解法《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253233291894784", "questionArticle": "<p>4．解三元一次方程组: $ \\begin{cases}x-4y+z=-3,①\\\\ 2x+y-z=18,②\\\\ x-y-z=7.③\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.5 三元一次方程组及其解法《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253232763412480", "questionArticle": "<p>5．若 $ \\begin{cases}x+y=27,\\\\ y+z=33,\\\\ z+x=30,\\end{cases} $ 则代数式<i>x</i>+<i>y</i>+<i>z</i>的值为<u>　　　　</u><i>.&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.5 三元一次方程组及其解法《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253316003569664", "questionArticle": "<p>6．已知 $ \\begin{cases}x=1,\\\\ y=2\\end{cases} $ 是方程<i>ax</i>+<i>by</i>=3的解,则代数式2<i>a</i>+4<i>b</i>−5的值为<u>　　　　</u><i>.&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 一次方程与方程组 全章综合训练《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253232234930176", "questionArticle": "<p>7．利用加减消元法解方程组 $ \\begin{cases}x+2y+z=8,①\\\\ 2x-y-z=-3,②\\\\ 3x+y-2z=-1,③\\end{cases} $ 下列做法正确的是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．要消去<i>z</i>,先将①+②,再将①×2+③</p><p>B．要消去<i>z</i>,先将①+②,再将①×3-③</p><p>C．要消去<i>y</i>,先将①-③×2,再将②-③</p><p>D．要消去<i>y</i>,先将①-②×2,再将②+③</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.5 三元一次方程组及其解法《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568253231446401024", "questionArticle": "<p>8．下列方程组中,是三元一次方程组的是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}x+z=2,\\\\ xy+x=4,\\\\ z-x=1\\end{cases} $ B． $ \\begin{cases}x-\\dfrac{3}{y}=4,\\\\ x+z=6,\\\\ y-2z=7\\end{cases} $ </p><p>C． $ \\begin{cases}x=9,\\\\ x-y=4,\\\\ z-y=5\\end{cases} $ D． $ \\begin{cases}x+y=8,\\\\ y-m=3,\\\\ z-x=5\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.5 三元一次方程组及其解法《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568254253124329472", "questionArticle": "<p>9．程大位是我国珠算发明家,他的著作《直指算法统宗》中记载了一个数学问题,大意是有100个和尚分100个馒头,如果大和尚1人分3个,小和尚3人分1个,正好分完.问大、小和尚各有多少人?若设大和尚有<i>x</i>人,小和尚有<i>y</i>人,则可列方程组为<u>　　　　　　</u><i>.&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时1 认识二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568254250851016704", "questionArticle": "<p>10．某商店出售大、小盒两种规格的口罩,已知2大盒、4小盒共装80个口罩;3大盒、5小盒共装110个口罩,则大盒与小盒每盒各装多少个口罩?设大盒装x个,小盒装y个,则下列方程组中正确的是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}2\\mathrm{x}+3\\mathrm{y}=80,\\\\ 5\\mathrm{x}+3\\mathrm{y}=110\\end{cases} $ B． $ \\begin{cases}2\\mathrm{x}+4\\mathrm{y}=80,\\\\ 5\\mathrm{x}+2\\mathrm{y}=110\\end{cases} $ </p><p>C． $ \\begin{cases}2\\mathrm{x}+4\\mathrm{y}=80,\\\\ 3\\mathrm{x}+5\\mathrm{y}=110\\end{cases} $ D． $ \\begin{cases}2\\mathrm{x}+3\\mathrm{y}=80,\\\\ 5\\mathrm{x}+2\\mathrm{y}=110\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时1 认识二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 122, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 122, "timestamp": "2025-07-01T02:15:15.154Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}