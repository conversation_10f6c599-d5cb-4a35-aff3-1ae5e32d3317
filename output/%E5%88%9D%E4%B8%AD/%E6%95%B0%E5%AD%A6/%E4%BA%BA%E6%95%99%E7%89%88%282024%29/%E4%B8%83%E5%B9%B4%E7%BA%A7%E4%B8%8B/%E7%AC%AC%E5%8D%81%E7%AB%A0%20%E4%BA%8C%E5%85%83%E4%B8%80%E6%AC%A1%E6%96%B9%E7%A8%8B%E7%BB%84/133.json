{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 132, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "567105174952517632", "questionArticle": "<p>1．下列方程组中，是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）．</p><p>A． $ \\begin{cases} x-y=6 \\\\ \\dfrac { 1 } { x }+\\dfrac { 1 } { y }=2 \\end{cases}  $　　　　B． $ \\begin{cases} x-y=5 \\\\ y+z=7 \\end{cases}  $</p><p>C． $ \\begin{cases} 4x=1 \\\\ 3x-2y=6 \\end{cases}  $　　　　D． $ \\begin{cases} x+y=xy \\\\ x-y=1 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105165871849472", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567105165871849472", "title": "黑龙江省哈尔滨市第十七中学2024−2025学年七年级下学期3月考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567105530788880384", "questionArticle": "<p>2．综合与实践</p><p>问题情境：小明同学在学习二元一次方程组时遇到了这样一个问题：</p><p>解方程组： $ \\begin{cases} \\dfrac { 4x+3y } { 3 }+\\dfrac { 6x-y } { 8 }=8 \\\\ \\dfrac { 4x+3y } { 6 }+\\dfrac { 6x-y } { 2 }=11 \\end{cases}  $ ．</p><p>观察发现：（1）如果用代入消元法或加减消元法求解，运算量比较大，容易出错．如果把方程组中的 $ (4x+3y) $ 看成一个整体，把 $ (6x-y) $ 看成一个整体，通过换元，可以解决问题．</p><p>设 $ 4x+3y=m $ ， $ 6x-y=n $ ，则原方程组可化为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，解关于<i>m</i>，<i>n</i>的方程组，得 $ \\begin{cases} m=18 \\\\ n=16 \\end{cases}  $ ，</p><p>所以 $ \\begin{cases} 4x+3y=18 \\\\ 6x-y=16 \\end{cases}  $ ，解方程组，得<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>探索猜想：（2）运用上述方法解下列方程组： $ \\begin{cases} 3\\left ( { 2x+y } \\right ) -2\\left ( { x-2y } \\right ) =26 \\\\ 2\\left ( { 2x+y } \\right ) +3\\left ( { x-2y } \\right ) =13 \\end{cases}  $ ．</p><p>拓展延伸：（3）已知关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ 的解为 $ \\begin{cases} x=4 \\\\ y=-3 \\end{cases}  $ ，求关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2a{{}_{ 1 } }x+3b{{}_{ 1 } }y=5c{{}_{ 1 } } \\\\ 2a{{}_{ 2 } }x+3b{{}_{ 2 } }y=5c{{}_{ 2 } } \\end{cases}  $ 的解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025吉林长春 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16424|16425|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的特殊解法|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105508978499584", "questionFeatureName": "综合与实践题", "questionMethodName": "整体思想", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567105508978499584", "title": "吉林省长春市东北师范大学附属实验学校2024−2025学年七年级下学期3月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105530008739840", "questionArticle": "<p>3．<i>A</i>、<i>B</i>两地相距36千米．甲从<i>A</i>地出发步行到<i>B</i>地，乙从<i>B</i>地出发步行到<i>A</i>地．两人同时出发，4小时后相遇；6小时后，甲所余路程为乙所余路程的2倍，求两人的速度．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105508978499584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567105508978499584", "title": "吉林省长春市东北师范大学附属实验学校2024−2025学年七年级下学期3月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105529320873984", "questionArticle": "<p>4．上数学课时，陈老师让同学们解一道关于<i>x</i>、<i>y</i>的方程组 $ \\begin{cases} ax+3y=-5① \\\\ 2x-by=14② \\end{cases}  $ ，并请小方和小龙两位同学到黑板上板演，可是小方同学看错了方程①中的<i>a</i>，得到方程组的解为 $ \\begin{cases} x=3 \\\\ y=2 \\end{cases}  $ ，小龙同学看错了方程②中的<i>b</i>，得到方程组的解为 $ \\begin{cases} x=-2 \\\\ y=-1 \\end{cases}  $ ．</p><p>(1) $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;</u>； $ b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)按照正确的<i>a</i>、<i>b</i>求出原方程组的解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025吉林长春 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105508978499584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567105508978499584", "title": "吉林省长春市东北师范大学附属实验学校2024−2025学年七年级下学期3月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105526154174464", "questionArticle": "<p>5．解下列方程组：</p><p>(1) $ \\begin{cases} x-3y=2 \\\\ y=x \\end{cases}  $ </p><p>(2) $ \\begin{cases} 3x-y=1 \\\\ 2x+y=4 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105508978499584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567105508978499584", "title": "吉林省长春市东北师范大学附属实验学校2024−2025学年七年级下学期3月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105524744888320", "questionArticle": "<p>6．已知关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} 2x+m=1 \\\\ y=m+3 \\end{cases}  $ ，若用含 $ x $ 的代数式来表示 $ y $ ，则 $ y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105508978499584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567105508978499584", "title": "吉林省长春市东北师范大学附属实验学校2024−2025学年七年级下学期3月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105524107354112", "questionArticle": "<p>7．一个两位数，个位数字与十位数字之和为12，如果交换个位数字与十位数字的位置，所得新数比原数大36，则原两位数为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105508978499584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567105508978499584", "title": "吉林省长春市东北师范大学附属实验学校2024−2025学年七年级下学期3月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105523486597120", "questionArticle": "<p>8．如果 $ x、y $ 满足 $ \\left  | { x+y-1 } \\right  | +{\\left( { x-2y-4 } \\right) ^ {2}}=0 $ ，则 $ {\\left( { x-y } \\right) ^ {2}}= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16257|16424", "keyPointNames": "绝对值非负性的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105508978499584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567105508978499584", "title": "吉林省长春市东北师范大学附属实验学校2024−2025学年七年级下学期3月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105522744205312", "questionArticle": "<p>9．已知 $ \\begin{cases} x+2y=5 \\\\ 2x+y=4 \\end{cases}  $ ，则 $ x+y= $  <u>&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105508978499584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567105508978499584", "title": "吉林省长春市东北师范大学附属实验学校2024−2025学年七年级下学期3月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105521955676160", "questionArticle": "<p>10．若关于 $ x，y $ 的方程 $ \\left ( { n-1 } \\right ) x{^{\\left  | { n } \\right  | }}+3y=0 $ 是二元一次方程，则 $ n $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105508978499584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567105508978499584", "title": "吉林省长春市东北师范大学附属实验学校2024−2025学年七年级下学期3月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 133, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 133, "timestamp": "2025-07-01T02:16:36.219Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}