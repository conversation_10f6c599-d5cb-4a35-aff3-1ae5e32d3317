{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 57, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "581952130019274752", "questionArticle": "<p>1．某企业为制作智能机器人，拟购买<i>A</i>，<i>B</i>两种型号国产芯片，制定方案如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.05pt;\"><p><i>A</i>型国产芯片（枚）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.05pt;\"><p><i>B</i>型国产芯片（枚）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>总费用（万元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>方案一</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.05pt;\"><p>100</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.05pt;\"><p>200</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>300</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>方案二</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.05pt;\"><p>200</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.05pt;\"><p>100</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>240</p></td></tr></table><p>（1）求<i>A</i>，<i>B</i>两种型号国产芯片的单价；</p><p>（2）若该企业调整方案，计划购买<i>A</i>，<i>B</i>两种型号国产芯片共400枚，且总费用不超过400万元，则至少购买<i>A</i>型号国产芯片多少枚？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南怀化 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-29", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581952097345646592", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581952097345646592", "title": "2025年湖南省怀化市初中学业水平考试模拟数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581952006383775744", "questionArticle": "<p>2．初中生正处于生长发育的重要时期，每天要保证摄入足够的能量．某学校食堂中午提供<i>A</i>，<i>B</i>两种套餐，每种套餐的热量及一些营养成分如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">套餐</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">热量</p><p style=\"text-align:center;\">（千卡）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\">蛋白质</p><p style=\"text-align:center;\">（克）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\">脂肪</p><p style=\"text-align:center;\">（克）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">碳水化合物</p><p style=\"text-align:center;\">（克）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">钠</p><p style=\"text-align:center;\">（毫克）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\"><i>A</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">1150</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\">&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\">53</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">147</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">586</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\"><i>B</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">800</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\">&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\">140</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">111</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">247</p></td></tr></table><p>（1）小涵同学发现9份<i>A</i>套餐和11份<i>B</i>套餐中的蛋白质含量相同，每份<i>A</i>套餐比<i>B</i>套餐蛋白质含量多6克，求每份<i>A</i>，<i>B</i>套餐中各含有蛋白质多少克．</p><p>（2）依据中国营养学会推荐，建议中学生午餐蛋白质摄入总量每周不低于150克．为符含该标准，小涵同学在一周内可以选择<i>A</i>，<i>B</i>两种套餐各几天？写出所有的方案（说明：一周按5天计算）</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南信阳 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-29", "keyPointIds": "16434|16486", "keyPointNames": "方案问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581951973341048832", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581951973341048832", "title": "2025年河南省信阳市中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581953082319216640", "questionArticle": "<p>3．解方程组： $ \\begin{cases} 2x+3y=6 \\\\ 5x-3y=8 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江舟山 · 临考冲刺", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-28", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581953059116326912", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581953059116326912", "title": "2025年浙江省舟山市定海区金衢山五校联考中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581952973191815168", "questionArticle": "<p>4．随着新年的到来，清明上河园景区又迎来了一年一度的旅游高峰，为了给游客更好的体验，该景区准备购进一批太阳帽和旅行包．已知购进4个太阳帽和3个旅行包共需要100元，购进6个太阳帽和4个旅行包共需要140元．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/25/2/1/0/0/0/581952915029405732/images/img_37.png\" style=\"vertical-align:middle;\" width=\"244\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）求每个太阳帽和每个旅行包的进价．</p><p>（2）该景区太阳帽的售价为15元，旅行包的售价为30元．景区计划购进太阳帽和旅行包共600个，且购进太阳帽的数量不少于旅行包数量的2倍，景区该如何设计进货方案，可使销售所获利润最大？最大利润为多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东东营 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-28", "keyPointIds": "16438|16535|16544", "keyPointNames": "和差倍分问题|一次函数的图象和性质|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581952940404940800", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581952940404940800", "title": "2025年山东省东营市中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580242463215886336", "questionArticle": "<p>5．“梅兰竹菊”是花中四君子，是中国传统文化中的象征，它们各自代表着不同的品质和精神．梅花象征着坚强，兰花象征着高洁，竹子象征着坚韧不屈，菊花象征着淡泊．某校为了落实双减政策，丰富学生的课外活动，开设了绘画社团，计划为学生购买水彩画、创意字当做教具，经过调查得知：每组水彩画比每组创意字的价格贵 $ 40 $ 元，买2组水彩画和3组创意字共用 $ 380 $ 元．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/21/2/1/0/0/0/580242394970370065/images/img_17.png\" style=\"vertical-align:middle;\" width=\"334\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;</p><p>（1）求每组水彩画、创意字的价格分别是多少？</p><p>（2）若学校需购进水彩画、创意字共 $ 12 $ 组，总费用不超过 $ 900 $ 元，并且根据学生需求，要求购进创意字的数量必须低于水彩画数量的5倍，问有几种购买方案？最低费用是多少？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京北京二中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-28", "keyPointIds": "16438|16490", "keyPointNames": "和差倍分问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580242416269041664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580242416269041664", "title": "北京二中教育集团2024—2025学年下学期七年级数学期中考试试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580242452973395968", "questionArticle": "<p>6．（1）解方程组： $ \\begin{cases} y=2x-4 \\\\ x+2y=-3 \\end{cases}  $ ；</p><p>（2）解不等式组 $ \\begin{cases} 2\\left ( { 2x+3 } \\right )  &gt; 1-x \\\\ \\dfrac { 5+x } { 3 }\\geqslant  x+\\dfrac { 1 } { 3 } \\end{cases}  $ ，并写出满足条件的非正整数解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京北京二中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-28", "keyPointIds": "16423|16489", "keyPointNames": "代入消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580242416269041664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580242416269041664", "title": "北京二中教育集团2024—2025学年下学期七年级数学期中考试试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580242449441792000", "questionArticle": "<p>7．已知关于 $ x,y $ 的二元一次方程组 $ \\begin{cases} 3x-2y=2k+1 \\\\ y-2x=4 \\end{cases}  $ 的解为 $ \\begin{cases} x=a \\\\ y=b \\end{cases}  $ ，且满足 $ a-b=3 $ ，则 $ k $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京二中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-28", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580242416269041664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580242416269041664", "title": "北京二中教育集团2024—2025学年下学期七年级数学期中考试试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580242448649068544", "questionArticle": "<p>8．在长方形 $ ABCD $ 中放入六个相同的小长方形，尺寸如图所标示．设小长方形的长、宽分别为 $ x{ \\rm{ c } }{ \\rm{ m } } $ ， $ y{ \\rm{ c } }{ \\rm{ m } } $ ，则可列方程组<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/21/2/1/0/0/0/580242394970370056/images/img_8.png\" style=\"vertical-align:middle;\" width=\"186\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京二中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-28", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580242416269041664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580242416269041664", "title": "北京二中教育集团2024—2025学年下学期七年级数学期中考试试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580242444656091136", "questionArticle": "<p>9．已知关于 $ x,y $ 的二元一次方程 $ ax+by=c $ 的解如表：</p><table style=\"border: solid 1px;border-collapse: collapse; width:343.5pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ x $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ -4 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ {-3} $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ {-2} $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ {-1} $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>0</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>…</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ y $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ \\dfrac { 14 } { 3 } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ \\dfrac { 10 } { 3 } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ \\dfrac { 8 } { 3 } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ \\dfrac { 4 } { 3 } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>…</p></td></tr></table><p>关于 $ x,y $ 的二元一次方程 $ mx-ny=k $ 的解如表：</p><table style=\"border: solid 1px;border-collapse: collapse; width:342.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ x $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ -4 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ {-3} $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ {-2} $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ {-1} $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>0</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>…</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ y $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ \\dfrac { 11 } { 2 } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ \\dfrac { 5 } { 2 } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ -\\dfrac { 1 } { 2 } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>−2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>…</p></td></tr></table><p>则关于 $ x,y $ 的二元一次方程组 $ \\begin{cases} ax-by=2c \\\\ mx+ny=2k \\end{cases}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=-6 \\\\ y=-8 \\end{cases}  $ B． $ \\begin{cases} x=-6 \\\\ y=8 \\end{cases}  $ C． $ \\begin{cases} x=-\\dfrac { 3 } { 2 } \\\\ y=-2 \\end{cases}  $ D． $ \\begin{cases} x=-\\dfrac { 3 } { 2 } \\\\ y=2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京北京二中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-28", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580242416269041664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580242416269041664", "title": "北京二中教育集团2024—2025学年下学期七年级数学期中考试试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580242918780219392", "questionArticle": "<p>10．汉服作为中国传统文化的重要组成部分，承载着深厚的历史底蕴和文化内涵．在某网店中，<i>A</i>，<i>B</i>两款汉服备受消费者青睐，某月份<i>A</i>款汉服售出200件，<i>B</i>款汉服售出400件，两款汉服销售总额为108000元．已知每件<i>A</i>款汉服的售价比每件<i>B</i>款汉服售价的2倍少100元．</p><p>（1）求<i>A</i>，<i>B</i>两款汉服每件的售价．</p><p>（2）为满足店铺的日常运营需求，该网店决定从服装厂订购<i>A</i>，<i>B</i>两款汉服共2400件，且订购<i>A</i>款汉服的数量不超过<i>B</i>款汉服数量的 $ \\dfrac { 1 } { 2 } $ ，已知<i>A</i>款汉服进价为每件120元，<i>B</i>款汉服进价为每件110元，请你设计一种订购方案，使得这批汉服全部售出后获利最大，并求出最大利润．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-05-28", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580242883480956928", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580242883480956928", "title": "2025年河南省2市十六校中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 58, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 58, "timestamp": "2025-07-01T02:07:40.409Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}