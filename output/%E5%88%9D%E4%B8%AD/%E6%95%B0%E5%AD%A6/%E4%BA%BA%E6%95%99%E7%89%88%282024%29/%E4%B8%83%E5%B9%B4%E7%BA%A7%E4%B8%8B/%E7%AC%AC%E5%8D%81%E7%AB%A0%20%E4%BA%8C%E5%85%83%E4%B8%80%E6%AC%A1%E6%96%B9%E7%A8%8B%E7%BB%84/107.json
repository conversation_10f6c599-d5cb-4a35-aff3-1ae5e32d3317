{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 106, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "572226672998522880", "questionArticle": "<p>1．已知 $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $ 是方程组 $ \\begin{cases} ax+by=5 \\\\ bx-ay=1 \\end{cases}  $ 的解，则 $ (a+b)(a-b) $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．5</p><p>B． $ -5 $</p><p>C．25</p><p>D． $ -25 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄市第四十中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-04-29", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226659476086784", "questionMethodName": "整体思想", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572226659476086784", "title": "河北省 石家庄市第四十中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}, {"id": "569703598805786624", "title": "河北省石家庄市第十七中学2024−2025学年七年级下学期4月考试数学试题", "paperCategory": 1}, {"id": "537437271684849664", "title": "陕西省西安市铁一中学2024-−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571884502722584576", "questionArticle": "<p>2．《孙子算经》是中国古代重要的数学著作，成书大约在一千五百年前．其中一道题，原文是：“今三人共车，两车空；二人共车，九人步．问人与车各几何？”意思是：现有若干人和车，若每辆车乘坐3人，则空余两辆车；若每辆车乘坐2人，则有9人步行．问人与车各多少？设有 $ x $ 人， $ y $ 辆车，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=3\\left ( { y+2 } \\right )  \\\\ x=2y-18 \\end{cases}  $ B． $ \\begin{cases} x=3\\left ( { y-2 } \\right )  \\\\ x=2y-18 \\end{cases}  $ C． $ \\begin{cases} x=3\\left ( { y+2 } \\right )  \\\\ x=2y+9 \\end{cases}  $ D． $ \\begin{cases} x=3\\left ( { y-2 } \\right )  \\\\ x=2y+9 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|370000|350000|420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东淄博 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 26, "referenceNum": 6, "createTime": "2025-04-29", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884487744724992", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884487744724992", "title": "山东省淄博市张店区第八中学2024−2025学年七年级下学期期中考试数学试卷 【五四制】", "paperCategory": 1}, {"id": "376129623602536448", "title": "重庆市沙坪坝区第一中学校2023-2024学年八年级上学期10月月考数学试题", "paperCategory": 1}, {"id": "459112907676098560", "title": "湖北省黄石市五校联考2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "349317447231512576", "title": "湖北省黄石市四区2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "209988586971635712", "title": "福建省龙岩市上杭县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "201998858347388928", "title": "福建省福州市鼓楼区福州屏东中学2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571884498335342592", "questionArticle": "<p>3．已知 $ \\begin{cases} x=2 \\\\ y=3 \\end{cases}  $ 是关于<i>x</i>，<i>y</i>的二元一次方程 $ mx+ny=7 $ 的解，则代数式 $ 4m+6n-3 $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．14</p><p>B．11</p><p>C．7</p><p>D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|370000|430000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东中山市第一中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 4, "createTime": "2025-04-29", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226368257171456", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572226368257171456", "title": "广东省中山市第一中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}, {"id": "570806839450836992", "title": "湖南省长沙市雅礼教育集团2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "559469768685690880", "title": "重庆市万州二中2024−2025学年七年级下学期尖子班内部第一次月考数学卷", "paperCategory": 1}, {"id": "571884487744724992", "title": "山东省淄博市张店区第八中学2024−2025学年七年级下学期期中考试数学试卷 【五四制】", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571884282001530880", "questionArticle": "<p>4．在明朝程大位《算法统宗》中有首住店诗：我问开店李三公，众客都来到店中，一房七客多七客，一房九客一房空．诗的大意是：一些客人到李三公的店中住宿，如果每一间客房住7人，那么有7人无房可住；如果每一间客房住9人，那么就空出一间房．设该店有客房<i>x</i>间，房客<i>y</i>人，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/28/2/1/0/0/0/571884185838723075/images/img_3.png\" style=\"vertical-align:middle;\" width=\"80\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} 7x+7=y \\\\ 9\\left ( { x-1 } \\right ) =y \\end{cases}  $ B． $ \\begin{cases} 7x+7=y \\\\ 9\\left ( { x+1 } \\right ) =y \\end{cases}  $ </p><p>C． $ \\begin{cases} 7x-7=y \\\\ 9\\left ( { x-1 } \\right ) =y \\end{cases}  $ D． $ \\begin{cases} 7x+7=y \\\\ 9\\left ( { x+1 } \\right ) =y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024广东深圳 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-04-29", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "462934527641427968", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "462934527641427968", "title": "2024年广东省深圳市中考数学试题", "paperCategory": 1}, {"id": "571884270358142976", "title": "山东省潍坊市2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571881272349335552", "questionArticle": "<p>5．A、B两种型号的吉祥物具有吉祥如意、平安幸福的美好寓意,深受大家喜欢.某超市销售A、B两种型号的吉祥物,有关信息见下表:</p><table class=\"editor-gen-table\" data-edited=\"true\" style=\"width: auto;\"><tbody><tr><td style=\"width:60pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"> </td><td style=\"width:100pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>成本(单位:元/个)</p></td><td style=\"width:120pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>销售价格(单位:元/个)</p></td></tr><tr><td style=\"width:60pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>A型号</p></td><td style=\"width:100pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>35</p></td><td style=\"width:120pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p><i>a</i></p></td></tr><tr><td style=\"width:60pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>B型号</p></td><td style=\"width:100pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>42</p></td><td style=\"width:120pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p><i>b</i></p></td></tr></tbody></table><p></p><p></p><p>若顾客在该超市购买8个A种型号吉祥物和7个B种型号吉祥物,则一共需要670元;购买4个A种型号吉祥物和5个B种型号吉祥物,则一共需要410元.</p><p>(1)求<i>a</i>,<i>b</i>的值;</p><p>(2)若某公司计划从该超市购买A、B两种型号的吉祥物共90个,且购买A种型号吉祥物的数量<i>x</i>(单位:个)不少于B种型号吉祥物数量的 $\\dfrac{4}{3}$ ,又不超过B种型号吉祥物数量的2倍.设该超市销售这90个吉祥物获得的总利润为<i>y</i>元,求<i>y</i>的最大值.</p><p>注:该超市销售每个吉祥物获得的利润等于每个吉祥物的销售价格与每个吉祥物的成本的差.</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16440|16490|16547", "keyPointNames": "表格或图示问题|一元一次不等式组的应用|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第三章 函数 分类集训7 一次函数", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "571879629583065088", "questionArticle": "<p>6．为落实“五育并举”,绿化美化环境,学校在劳动周组织学生到校园周边种植甲、乙两种树苗,已知购买甲种树苗3棵,乙种树苗2棵共需12元;购买甲种树苗1棵,乙种树苗3棵共需11元.</p><p>(1)求每棵甲、乙树苗的价格;</p><p>(2)本次活动共种植了200棵甲、乙树苗,假设所种的树苗若干年后全部长成了参天大树,并且平均每棵树的价值(含生态价值、经济价值等)均为原来树苗价的100倍,要想获得不低于5万元的价值,请问乙种树苗种植数量不得少于多少棵?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 卷2 方程(组)与不等式(组)综合检测", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "571879540080812032", "questionArticle": "<p>7．为了增强学生的体质,某学校倡导学生在大课间开展踢毽子活动,需购买甲、乙两种品牌毽子.已知购买甲种品牌毽子10个和乙种品牌毽子5个共需200元;购买甲种品牌毽子15个和乙种品牌毽子10个共需325元.</p><p>(1)购买一个甲种品牌毽子和一个乙种品牌毽子各需要多少元?</p><p>(2)若购买甲、乙两种品牌毽子共花费1 000元,甲种品牌毽子数量不低于乙种品牌毽子数量的5倍且不超过乙种品牌毽子数量的16倍,则有几种购买方案?</p><p>(3)若商家每售出一个甲种品牌毽子利润是5元,每售出一个乙种品牌毽子利润是4元,在(2)的条件下,学校如何购买毽子商家获得利润最大?最大利润是多少元?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16438|16490|16544", "keyPointNames": "和差倍分问题|一元一次不等式组的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训5 不等式(组)及其应用", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "571879538826715136", "questionArticle": "<p>8．为拓宽销售渠道,助力乡村振兴,某乡镇帮助农户将A、B两个品种的柑橘加工包装成礼盒再出售.已知每件A品种柑橘礼盒比B品种柑橘礼盒的售价少20元,且出售25件A品种柑橘礼盒和15件B品种柑橘礼盒的总价共3 500元.</p><p>(1)求A、B两种柑橘礼盒每件的售价分别为多少元.</p><p>(2)已知加工A、B两种柑橘礼盒每件的成本分别为50元、60元,乡镇计划在某农产品展销活动中售出A、B两种柑橘礼盒共1 000盒,且A品种柑橘礼盒售出的数量不超过B品种柑橘礼盒数量的1.5倍,总成本不超过54 050元,要使农户收益最大,该乡镇应怎样安排A、B两种柑橘礼盒的销售方案?并求出农户在这次农产品展销活动中的最大收益为多少元.</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16438|16490|16544", "keyPointNames": "和差倍分问题|一元一次不等式组的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训5 不等式(组)及其应用", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "571879537706835968", "questionArticle": "<p>9．刺绣是我国民间传统手工艺.湘绣作为中国四大刺绣之一,闻名中外.在巴黎奥运会倒计时50天之际,某国际旅游公司计划购买A、B两种奥运主题的湘绣作品作为纪念品.已知购买1件A种湘绣作品与2件B种湘绣作品共需要700元,购买2件A种湘绣作品与3件B种湘绣作品共需要1 200元.</p><p>(1)求A种湘绣作品和B种湘绣作品的单价分别为多少元.</p><p>(2)该国际旅游公司计划购买A种湘绣作品和B种湘绣作品共200件,总费用不超过50 000元,那么最多能购买A种湘绣作品多少件?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训5 不等式(组)及其应用", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "571879437278420992", "questionArticle": "<p>10．为促进新质生产力的发展,某企业决定投入一笔资金对现有甲、乙两类共30条生产线的设备进行更新换代.</p><p>(1)为鼓励企业进行生产线的设备更新,某市出台了相应的补贴政策.根据相关政策,更新1条甲类生产线的设备可获得3万元的补贴,更新1条乙类生产线的设备可获得2万元的补贴.这样更新完这30条生产线的设备,该企业可获得70万元的补贴.该企业甲、乙两类生产线各有多少条?</p><p>(2)经测算,购买更新1条甲类生产线的设备比购买更新1条乙类生产线的设备需多投入5万元,用200万元购买更新甲类生产线的设备数量和用180万元购买更新乙类生产线的设备数量相同,那么该企业在获得70万元的补贴后,还需投入多少资金更新生产线的设备?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16441|16476", "keyPointNames": "其他问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训4 方程(组)及其应用", "paperCategory": 2}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 107, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 107, "timestamp": "2025-07-01T02:13:27.973Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}