{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 35, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "586985168335843328", "questionArticle": "<p>1．解方程组： $ \\begin{cases} 5x+y=6 \\\\ 2x-y=8 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东广州外国语学校 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-13", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586985145422360576", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586985145422360576", "title": "2025年广东省广州市南沙区广州外国语学校中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588075939663228928", "questionArticle": "<p>2．“端午节”来临之际，各超市纷纷搞促销活动，小美发现离家不远的状元超市有蜜枣粽和肉粽两种粽子正在参加活动．购买 $ 30 $ 个肉粽和 $ 40 $ 个蜜枣粽需要 $ 410 $ 元，购买 $ 50 $ 个肉粽和 $ 20 $ 个蜜枣粽需要 $ 450 $ 元．</p><p>（1）求肉粽和蜜枣粽的单价分别为多少元？</p><p>（2）“端午节”当天，状元超市加大促销活动力度，将肉粽的单价降低了 $ 2m $ 元，蜜枣粽单价降低了 $ \\dfrac { m } { 5 } $ ，节日当天肉粽的销量是蜜枣粽销量的 $ 1.5 $ 倍，且肉粽的销售额为 $ 1800 $ 元，蜜枣粽的销售额为 $ 900 $ 元，求 $ m $ 的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆西大附中 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-13", "keyPointIds": "16437|16476", "keyPointNames": "销售利润问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588075905005694976", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588075905005694976", "title": "2025年重庆市西南大学附属中学中考三模数学", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585921593324380160", "questionArticle": "<p>3．设整式 $ Q:mx{^{4}}+nx{^{3}}+px{^{2}}+nx+m $ ，其中 $ m,n,p $ 为整数且 $ m  <  n  <  p $ ．下列说法：</p><p>① $ Q $ 可以表示为关于 $ x $ 的二次多项式；</p><p>②若 $ Q\\left ( { 1 } \\right ) =12 $ 且 $ Q\\left ( { 0 } \\right ) =2 $ ，则 $ 2n+p=10 $ ；</p><p>③满足 $ m+n+p=12 $ 且 $ m  <  n  <  p $ 的正整数解有7组．</p><p>其中正确的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0　　　　B．1　　　　C．2　　　　D．3</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆八中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-12", "keyPointIds": "16308|16420", "keyPointNames": "多项式|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585921572000538624", "questionMethodName": "分类讨论思想", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585921572000538624", "title": "重庆市第八中学校2024−2025学年九年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "585959712056713216", "questionArticle": "<p>4．2024年10月30日，神舟十九号载人飞船发射取得圆满成功，神州十九号航天员乘组顺利进驻中国空间站．某航模商店购进<i>A</i>、 $ B $ 两种航空模型进行销售，已知购进 $ \\mathrm{ A } $ 种航空模型和 $ B $ 种航空模型各1个共65元，购进<i>A</i>种航空模型2个和 $ B $ 种航空模型1个共需90元．</p><p>（1）求<i>A</i>、 $ B $ 两种航空模型进价分别多少元；</p><p>（2）某商店计划购买 $ \\mathrm{ A } $ 、 $ B $ 两种航空模型共80个，若 $ \\mathrm{ A } $ 、 $ B $ 两种航空模型的售价分别是40元和50元，要使获得的利润不低于1100元，请问至少购买 $ \\mathrm{ A } $ 种航空模型多少个？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆清华 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-12", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585959679529885696", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585959679529885696", "title": "重庆市清华中学校2024−2025学年七年级下学期第二阶段定时作业数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585959707375869952", "questionArticle": "<p>5．解方程和不等式组：</p><p>（1） $ \\begin{cases} 2x-3y=17 \\\\ 5x-2y=26 \\end{cases}  $ </p><p>（2） $ \\begin{cases} 2x+4 &gt; 0 \\\\ \\dfrac { 1-x } { 2 }  &lt;  \\dfrac { 4-2x } { 3 } \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆清华 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-12", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585959679529885696", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585959679529885696", "title": "重庆市清华中学校2024−2025学年七年级下学期第二阶段定时作业数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585959701797445632", "questionArticle": "<p>6．已知方程组 $ \\begin{cases} 2x+5y=-26 \\\\ ax-by=-4 \\end{cases}  $ 和方程组 $ \\begin{cases} 3x-5y=36 \\\\ bx+ay=-8 \\end{cases}  $ 解相同，则 $ {\\left( { 2a+b } \\right) ^ {2005}}= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆清华 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-12", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585959679529885696", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585959679529885696", "title": "重庆市清华中学校2024−2025学年七年级下学期第二阶段定时作业数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585959958157500416", "questionArticle": "<p>7．如果  $ \\left  | { x-2 } \\right  | +(x-y+4){^{2}}=0 $ ，那么  $ (x+y){^{2}}= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市渝北中学校 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-12", "keyPointIds": "16257|16423", "keyPointNames": "绝对值非负性的应用|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585959936745578496", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585959936745578496", "title": "重庆市渝北中学2024−2025学年 九年级下学期第三学月考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586985440965599232", "questionArticle": "<p>8．为庆祝国庆，某校初三（1）班开展了以“迎国庆，梦想起航”为主题的演讲比赛，计划拿出240元钱全部用于购买一等奖和二等奖两种奖品，一等奖每件15元，二等奖每件10元，则购买方案有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．6种B．7种C．8种D．9种</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江佳木斯 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-12", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586985424700088320", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586985424700088320", "title": "2025年黑龙江省佳木斯市中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "587394570570211328", "questionArticle": "<p>9．关于 $ x, y $ 的方程组 $ \\begin{cases} 3x+y=k+1 \\\\ x+3y=3 \\end{cases}  $ ，若 $ 2  <  k  <  4, t=x-y $ ，则 $ t $ 的取值范围是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -3  <  t  <  -1 $</p><p>B． $ -1  <  t  <  0 $</p><p>C． $ -1  <  t  <  1 $</p><p>D． $ 0  <  t  <  1 $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川南充 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-12", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587394551200915456", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "587394551200915456", "title": "2025年四川省南充市名校联测中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "587393844204843008", "questionArticle": "<p>10．中国古代数学著作《增删算法统宗》中记载的“绳索量竿”问题，大意是：现有一根竿子和一条绳索，用绳索去量竿子，绳索比竿子长5尺；若将绳索对折去量竿子，绳索就比竿子短5尺，问绳索、竿子各有多长？该问题中的竿子长为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>尺．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-12", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587393819626221568", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "587393819626221568", "title": "2025年湖南省永州市中考适应性考试二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 36, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 36, "timestamp": "2025-07-01T02:05:03.395Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}