{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 5, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "590998875319607296", "questionArticle": "<p>1．动画电影《哪吒 $ 2 $ 》以打破中国影史记录的票房引起国内外关注，某商家相应推出了联名款的玩偶和人物卡片．已知购买 $ 3 $ 个玩偶和2包人物卡片需花费55元，购买 $ 1 $ 个玩偶和5包人物卡片需花费65元，问联名款的玩偶和人物卡片的单价分别为多少？设玩偶单价为 $ x $ 元/个，人物卡片单价为 $ y $ 元/包，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3x+y=55 \\\\ 2x+5y=65 \\end{cases}  $ B． $ \\begin{cases} 3x+2y=65 \\\\ x+5y=55 \\end{cases}  $ C． $ \\begin{cases} 3x+5y=65 \\\\ x+2y=55 \\end{cases}  $ D． $ \\begin{cases} 3x+2y=55 \\\\ x+5y=65 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南周南 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998860165586944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590998860165586944", "title": "湖南省长沙市周南教育集团2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592870417582301184", "questionArticle": "<p>2．解方程组： $ \\begin{cases} 2x+3y=13 \\\\ 4x+5y=23 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025新疆乌鲁木齐一中 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870398695350272", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "592870398695350272", "title": "新疆维吾尔自治区乌鲁木齐市第一中学2024−2025学年七年级下学期数学期末素养调查模拟卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592870415124439040", "questionArticle": "<p>3．已知方程组 $ \\begin{cases} 5x-4y=n \\\\ 3x+5y=8 \\end{cases}  $ 中， $ x $ ， $ y $ 的值相等，则 $ n= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025新疆乌鲁木齐一中 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16420|16423", "keyPointNames": "二元一次方程的解|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870398695350272", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "592870398695350272", "title": "新疆维吾尔自治区乌鲁木齐市第一中学2024−2025学年七年级下学期数学期末素养调查模拟卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589573252353138688", "questionArticle": "<p>4．在求值问题中，我们经常遇到利用整体思想来解决问题．</p><p>例如1：已知： $ x+2y-3z=2 $ ， $ 2x+y+6z=1 $ ，求： $ x+y+z $ 的值．</p><p>解：令 $ x+2y-3z=2 $ ……①</p><p> $ 2x+y+6z=1 $ ……②</p><p>①＋②得 $ 3x+3y+3z=3 $ ，所以 $ x+y+z=1 $ ，</p><p>已知 $ \\begin{cases} x+y=-5① \\\\ 3x+4y=1② \\end{cases}  $ ，求 $ x+2y $ 的值．</p><p>解：①×2得： $ 2x+2y=-10 $ ……③</p><p>②－③得： $ x+2y=11 $ ．</p><p>利用材料中提供的方法，解决下列问题：</p><p>（1）已知：关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} x-3y=2m-3 \\\\ 4x-6y=m-1 \\end{cases}  $ 的解满足 $ x-y=6 $ ，求 $ m $ 的值；</p><p>（2）某步行街摆放有甲、乙、丙三种造型的盆景分别 $ x $ ， $ y $ ， $ z $ 盆．甲种盆景由15朵红花、8朵黄花和25朵紫花搭配而成，乙种盆景由10朵红花、6朵黄花和20朵紫花搭配而成，丙种盆景由10朵红花、7朵黄花和25朵紫花搭配而成．这些盆景一共用了2900朵红花，3750朵紫花，求黄花一共用了多少朵？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山西大同 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16426|16441", "keyPointNames": "二元一次方程组的应用|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589573228693069824", "questionMethodName": "整体思想", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589573228693069824", "title": "山西省大同六中集团校2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590998420157931520", "questionArticle": "<p>5．定义一种新运算“ $ \\otimes  $ ”：当 $ a\\geqslant  b $ 时， $ a\\otimes b=2a+b $ ；当 $ a  &lt;  b $ 时， $ a\\otimes b=a+2b $ ．</p><p>（1）计算： $ 4\\otimes (-7)= $  <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>； $ \\left ( { -\\dfrac { 1 } { 3 } } \\right ) \\otimes \\left ( { -\\dfrac { 1 } { 6 } } \\right ) = $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）解方程组： $ \\begin{cases} x\\otimes y=5 \\\\ 3x-4y=2 \\end{cases}  $ ；</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建泉州七中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16278|16424", "keyPointNames": "有理数的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998393041756160", "questionFeatureName": "新定义问题", "questionMethodName": "分类讨论思想", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590998393041756160", "title": "福建省泉州市第七中学2024−2025学年七年级下学期第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589573248905420800", "questionArticle": "<p>6．（1）解方程组： $ \\begin{cases} 3x+4y=1 \\\\ 5x+2y=11 \\end{cases}  $ ．</p><p>（2）解不等式： $ \\dfrac { 2x-1 } { 3 }  &lt;  \\dfrac { 3x-2 } { 2 }-1 $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西大同 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589573228693069824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589573228693069824", "title": "山西省大同六中集团校2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589573248225943552", "questionArticle": "<p>7．小明在拼图时发现，用8个一样大的长方形恰好可以拼成一个大的长方形，如图（1）所示；而小红七拼八凑，拼成了一个如图（2）所示的正方形，但中间留下了一个洞，恰好是边长为2的小正方形，则每个小长方形的面积是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/15/2/1/0/0/0/589573212473700360/images/img_8.png\" style=\"vertical-align:middle;\" width=\"326\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西大同 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589573228693069824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589573228693069824", "title": "山西省大同六中集团校2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589573245428342784", "questionArticle": "<p>8．写一个解为 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ 的二元一次方程<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025山西大同 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589573228693069824", "questionFeatureName": "开放性试题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589573228693069824", "title": "山西省大同六中集团校2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589573244698533888", "questionArticle": "<p>9．如图，老师利用两块大小一样的长方体木块测量一张桌子的高度，首先按照图①方式放置，再交换两木块的位置，按照图②方式放置，测量的数据如图，则桌子的高度是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/15/2/1/0/0/0/589573212473700359/images/img_7.png\" style=\"vertical-align:middle;\" width=\"277\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ 77{ \\rm{ c } }{ \\rm{ m } } $ B． $ 78{ \\rm{ c } }{ \\rm{ m } } $ C． $ 79{ \\rm{ c } }{ \\rm{ m } } $ D． $ 80{ \\rm{ c } }{ \\rm{ m } } $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西大同 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589573228693069824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589573228693069824", "title": "山西省大同六中集团校2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590998416928317440", "questionArticle": "<p>10．（1）解方程组： $ \\begin{cases} 2\\left ( { x+2y } \\right ) -5y=-1 \\\\ 3\\left ( { x-y } \\right ) +y=2 \\end{cases}  $ ．</p><p>（2）解不等式组 $ \\begin{cases} 2\\left ( { x-1 } \\right ) -x\\leqslant  2 \\\\ x＞\\dfrac { x } { 2 }-1 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建泉州七中 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998393041756160", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590998393041756160", "title": "福建省泉州市第七中学2024−2025学年七年级下学期第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 6, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 6, "timestamp": "2025-07-01T02:01:28.654Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}