{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 141, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "564578990393958400", "questionArticle": "<p>1．中国的《九章算术》是世界现代数学的两大源泉之一，其中有一问题：“今有牛五、羊二，直金十两，牛二、羊五，直金八两问牛、羊各直金几何?”译文：今有牛5头，羊2头，共值金10两．牛2头，羊5头，共值金8两．问牛、羊每头各值金多少?设牛、羊每各值金<i>x</i>两、<i>y</i>两，依题意，可列出方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+2y=10 \\\\ 2y+5x=8 \\end{cases}  $ B． $ \\begin{cases} 5x+2y=10 \\\\ 2x+5y=8 \\end{cases}  $ C． $ \\begin{cases} 2x+5y=10 \\\\ 2y+5x=8 \\end{cases}  $ D． $ \\begin{cases} 2x+5y=10 \\\\ 2x+5y=8 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-04-11", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564578973704822784", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564578973704822784", "title": "河北省石家庄市第三十八中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "311233085751730176", "title": "湖南省长沙市南雅中学2022-2023学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "565670679007240192", "questionArticle": "<p>2．为打造三墩五里塘河河道风光带，现有一段长为180米的河道整治任务，由<i>A</i>，<i>B</i>两个工程小组先后接力完成，<i>A</i>工程小组每天整治12米，<i>B</i>工程小组每天整治8米，共用时20天，设<i>A</i>工程小组整治河道<i>x</i>米，<i>B</i>工程小组整治河道<i>y</i>米，依题意可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=180 \\\\ \\dfrac { x } { 12 }+\\dfrac { y } { 8 }=20 \\end{cases}  $</p><p>B． $ \\begin{cases} x+y=20 \\\\ 12x+8y=180 \\end{cases}  $</p><p>C． $ \\begin{cases} x+y=20 \\\\ \\dfrac { x } { 12 }+\\dfrac { y } { 8 }=180 \\end{cases}  $</p><p>D． $ \\begin{cases} x+y=180 \\\\ \\dfrac { 12 } { x }+\\dfrac { 8 } { y }=20 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|-1|420000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙一中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 24, "referenceNum": 6, "createTime": "2025-04-11", "keyPointIds": "16431", "keyPointNames": "工程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565670664939544576", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "565670664939544576", "title": "湖南省长沙市一中集团2024—2025学年九年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "209318748859179008", "title": "浙教版七年级下册第2章二元一次方程组单元测试", "paperCategory": 1}, {"id": "173444343637778432", "title": "湘教版七年级下册第1章二元一次方程组单元测试", "paperCategory": 1}, {"id": "206707688847220736", "title": "2022年七年级上册沪科版数学第3章3.4二元一次方程组的应用课时练习", "paperCategory": 1}, {"id": "164306350578638848", "title": "湖北省武汉市2020年中考评价检测数学试题（三）", "paperCategory": 1}, {"id": "138993261188784128", "title": "山东省东营市东营区2020年中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564578865676328960", "questionArticle": "<p>3．植树节这天有35名同学共种了85棵树苗，其中男生每人种树3棵，女生每人种树2棵．设男生有<i>x</i>人，女生有<i>y</i>人，根据题意，下列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=85 \\\\ 2x+3y=35 \\end{cases}  $ B． $ \\begin{cases} x+y=85 \\\\ 3x+2y=35 \\end{cases}  $ C． $ \\begin{cases} x+y=35 \\\\ 2x+3y=85 \\end{cases}  $ D． $ \\begin{cases} x+y=35 \\\\ 3x+2y=85 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|-1|420000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 9, "createTime": "2025-04-11", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519569833647710208", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "519569833647710208", "title": "第12讲　实际问题与二元一次方程组（诊断）", "paperCategory": 10}, {"id": "536617049658793984", "title": "广东省深圳实验学校初中部2024−2025学年上学期八年级期末数学试卷", "paperCategory": 1}, {"id": "552902683469848576", "title": "第7讲 实际问题与二元一次方程组（诊断）", "paperCategory": 10}, {"id": "552437279148515328", "title": "XJ第21讲　实际问题与二元一次方程组（诊断）", "paperCategory": 10}, {"id": "537300180430790656", "title": "第3讲　实际问题与二元一次方程组（诊断）LJ七下", "paperCategory": 10}, {"id": "534033121366286336", "title": "第7讲　实际问题与二元一次方程组（诊断）", "paperCategory": 10}, {"id": "564578858013335552", "title": "河北省石家庄市第二十五中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "523817712629358592", "title": "第3讲　实际问题与二元一次方程组（诊断）", "paperCategory": 10}, {"id": "199848616080482304", "title": "湖北省襄樊市2022年九年级四月调研数学模拟试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564578864019578880", "questionArticle": "<p>4．有下列方程：①<i>xy</i>＝1；②2<i>x</i>＝3<i>y</i>；③ $ x-\\dfrac { 1 } { y }=2 $ ；④<i>x</i><i><sup>2</sup></i>＋<i>y</i>＝3； ⑤ $ \\dfrac { x } { 4 }=3y-1 $ ；⑥<i>ax</i><i><sup>2</sup></i>＋2<i>x</i>＋3<i>y</i>＝0  $ {\\rm （\\mathit{a}＝0）} $ ，其中，二元一次方程有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1个B．2个C．3个D．4个</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-04-11", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564578858013335552", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564578858013335552", "title": "河北省石家庄市第二十五中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": null, "title": "第六章6.2二元一次方程组的解法 综合训练【初中必刷题】七年级下册冀教版数学", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "564578782201290752", "questionArticle": "<p>5．解方程组： $ \\begin{cases} 2x+3y=16① \\\\ x{ { + } }{ { 4 } }y=1{ { 3 } }② \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|-1|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 3, "createTime": "2025-04-11", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564578764316778496", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564578764316778496", "title": "河北省石家庄市第二十八中学2023−2024学年下学期七年级期中数学试卷", "paperCategory": 1}, {"id": "460576052508663808", "title": "北师大版教材例题 八年级上册 第五章 第2节 求解二元一次方程组", "paperCategory": 1}, {"id": "158689752790638592", "title": "广东省河源市和平县2021-2022学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564221490171781120", "questionArticle": "<p>6．火龙果是一种花青素、维生素<i>E</i>含量较为丰富的水果，有延缓衰老、调节免疫的作用．现有“白心火龙果”和“红心火龙果”两个品种，某水果店试销这两种火龙果，已知每箱的售价“红心火龙果”比“白心火龙果”贵10元，销售6箱“白心火龙果”的总价比销售5箱“红心火龙果”的总价多30元．</p><p>(1)问“白心火龙果”与“红心火龙果”每箱的售价各是多少元？</p><p>(2)若“白心火龙果”每箱的进价为65元，“红心火龙果”每箱的进价为70元．现水果店购进两种火龙果共38箱，计划所花资金不高于2600元，设购进“白心火龙果” $ a $ 箱，销售这两种火龙果的利润为 $ w $ 元，则该水果店应如何设计购进方案才能使得利润 $ w $ 最大，最大利润是多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东济南 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-10", "keyPointIds": "16438|16544", "keyPointNames": "和差倍分问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564221464838184960", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564221464838184960", "title": "2025年山东省济南市钢城区九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564221889003954176", "questionArticle": "<p>7．为了提高同学们学习数学的兴趣，某中学开展主题为“感受数学魅力，享受数学乐趣”的数学活动．并计划购买 $ \\mathrm{ A } $ 、 $ B $ 两种奖品奖励在数学活动中表现突出的学生，购买 $ 1 $ 件 $ \\mathrm{ A } $ 种奖品和 $ 2 $ 件 $ B $ 种奖品共需 $ 64 $ 元，购买 $ 2 $ 件 $ \\mathrm{ A } $ 种奖品和 $ 1 $ 件 $ B $ 种奖品共需 $ 56 $ 元．</p><p>(1)每件 $ \\mathrm{ A } $ 、 $ B $ 奖品的价格各是多少元？</p><p>(2)根据需要，该学校准备购买 $ \\mathrm{ A } $ 、 $ B $ 两种奖品共 $ 80 $ 件，其中购买的 $ \\mathrm{ A } $ 种奖品的数量不超过 $ B $ 种奖品数量的 $ 3 $ 倍，所需总费用为 $ w $ 元，求所需总费用的最小值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川泸州 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-10", "keyPointIds": "16438|16535", "keyPointNames": "和差倍分问题|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564221863053795328", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564221863053795328", "title": "2025年四川省泸州市龙马潭区五校联考中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "565309974764625920", "questionArticle": "<p>8．已知关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} x+y=3 \\\\ x+ay=b \\end{cases}  $ 的解是 $ \\begin{cases} x=1 \\\\ y=a \\end{cases}  $ ，则<i>b</i>的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江衢州等地 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-10", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565309948730580992", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "565309948730580992", "title": "2025年浙江省衢州市柯城区、龙游县、江山市中考一模数学卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "565309116593250304", "questionArticle": "<p>9．开封作为八朝古都，有着深厚的历史文化，也吸引者无数的游客前往观光，开封，特产桶子鸡、酱牛肉深受游客的喜爱．已知2包桶子鸡和3包酱牛肉的价格为 $ 310 $ 元，3包桶子鸡和4包酱牛肉的价格为 $ 430 $ 元，分别求出桶子鸡和酱牛肉的单价．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林吉林 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-10", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565309100323545088", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "565309100323545088", "title": "2025年吉林省吉林市部分学校九年级中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "565308832378822656", "questionArticle": "<p>10．我国明代珠算家程大位的名著《直指算法统宗》里有一道著名算题：一百馒头一百僧，大僧三个更无争，小僧三人分一个，大小和尚各几丁？译成白话文，其意思是：有100个和尚分100个馒头，正好分完．如果大和尚一人分3个，小和尚3人分一个，试问大小和尚各有几人？设大和尚 $ x $ 人，小和尚 $ y $ 人，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=100 \\\\ 3x+\\dfrac { 1 } { 3 }y=100 \\end{cases}  $ B． $ \\begin{cases} x+y=100 \\\\ \\dfrac { 1 } { 3 }x+y=100 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=100 \\\\ 3x+y=100 \\end{cases}  $ D． $ \\begin{cases} x+y=100 \\\\ x+\\dfrac { 1 } { 3 }y=100 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北孝感 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-10", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565308819846242304", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "565308819846242304", "title": "2025年湖北省孝感市孝南区中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 142, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 142, "timestamp": "2025-07-01T02:17:40.386Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}