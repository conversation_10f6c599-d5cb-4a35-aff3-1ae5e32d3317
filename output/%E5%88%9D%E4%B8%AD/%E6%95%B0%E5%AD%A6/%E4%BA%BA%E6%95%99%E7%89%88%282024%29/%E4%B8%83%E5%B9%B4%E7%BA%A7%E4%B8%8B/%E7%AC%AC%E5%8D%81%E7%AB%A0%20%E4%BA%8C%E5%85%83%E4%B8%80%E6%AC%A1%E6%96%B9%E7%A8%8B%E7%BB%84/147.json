{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 146, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "564581022177730560", "questionArticle": "<p>1．已知关于<i>x</i>、<i>y</i>的二元一次方程组 $ \\begin{cases} x-y=k+2 \\\\ x+3y=k \\end{cases}  $ ，且 $ x+y=2 $ ，则<i>k</i>值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2B． $ -2 $ C．1D． $ -\\dfrac { 1 } { 2 } $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581012056875008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564581012056875008", "title": "吉林省吉林市第九中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564580929483612160", "questionArticle": "<p>2．健康营养师用甲、乙两种原料为运动员的康复训练配制营养品，每克甲原料含 $ 0.6 $ 单位蛋白质和 $ 0.8 $ 单位铁质，每克乙原料含 $ 1 $ 单位蛋白质和 $ 0.5 $ 单位铁质．</p><table style=\"border: solid 1px;border-collapse: collapse; width:351.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 133.5pt;\"><p>项目</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 69pt;\"><p>甲原料 $ x $ 克</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 69pt;\"><p>乙原料 $ y $ 克</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 80.25pt;\"><p>所配制营养品</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 133.5pt;\"><p>其中所含蛋白质（单位）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 69pt;\"><p><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 69pt;\"><p><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 80.25pt;\"><p><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 133.5pt;\"><p>其中所含铁质（单位）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 69pt;\"><p><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 69pt;\"><p><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 80.25pt;\"><p><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td></tr></table><p>(1)依据题意，填写上表：</p><p>(2)如果运动员每餐需要 $ 35 $ 单位蛋白质和 $ 40 $ 单位铁质，那么每餐甲、乙两种原料各多少克恰好满足运动员的需要？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564580905399918592", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564580905399918592", "title": "吉林省吉林市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564580922701422592", "questionArticle": "<p>3．解方程组： $ \\begin{cases} 2x-y=5 \\\\ 11x-3y=20 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564580905399918592", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564580905399918592", "title": "吉林省吉林市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564580921698983936", "questionArticle": "<p>4．《九章算术》中记载了一道数学问题，其译文为有大小两种盛酒的桶，已知5个大桶加上1个小桶可以盛酒3斛（斛，音hú，是古代一种容量单位），1个大桶加上5个小桶可以盛酒2斛．1个大桶、1个小桶分别可以盛酒多少斛？设1个大桶可以盛酒 $ x $ 斛、1个小桶可以盛酒 $ y $ 斛．根据题意，可列方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564580905399918592", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564580905399918592", "title": "吉林省吉林市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564581019245912064", "questionArticle": "<p>5．下列方程是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x+y=2 $ B． $ x+2y $ C． $ \\dfrac { 1 } { x }+y=0 $ D． $ x{^{2}}+2y=1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581012056875008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564581012056875008", "title": "吉林省吉林市第九中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564580919413088256", "questionArticle": "<p>6．已知二元一次方程组 $ \\begin{cases} x+2y=4 \\\\ 2x+y=5 \\end{cases}  $ ，则 $ x-y $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564580905399918592", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564580905399918592", "title": "吉林省吉林市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564580925255753728", "questionArticle": "<p>7．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x-y=13 \\\\ x+ay=-7 \\end{cases}  $ 的解是 $ \\begin{cases} x=b \\\\ y=4 \\end{cases}  $ ，求<i>a</i>，<i>b</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000|350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-04-08", "keyPointIds": "16420|16423", "keyPointNames": "二元一次方程的解|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564580905399918592", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564580905399918592", "title": "吉林省吉林市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "208512377473507328", "title": "福建省莆田市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564580923796135936", "questionArticle": "<p>8．2022年12月28日查干湖冬捕活动后，某商家销售<i>A</i>，<i>B</i>两种查干湖野生鱼，如果购买1箱<i>A</i>种鱼和2箱<i>B</i>种鱼需花费1300元：如果购买2箱<i>A</i>种鱼和3箱<i>B</i>种鱼需花费2300元．分别求每箱<i>A</i>种鱼和每箱<i>B</i>种鱼的价格．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2023吉林 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 10, "referenceNum": 2, "createTime": "2025-04-08", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "331159289619324928", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "331159289619324928", "title": "2023年吉林省中考数学真题", "paperCategory": 1}, {"id": "564580905399918592", "title": "吉林省吉林市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564581137563033600", "questionArticle": "<p>9．解方程组： $ \\begin{cases} 2x-y=3 \\\\ 3x+2y=8 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|110000|220000|340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽淮北 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 5, "createTime": "2025-04-08", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "537018634234798080", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "537018634234798080", "title": "安徽省淮北市五校联考2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}, {"id": "564581119414280192", "title": "吉林省吉林市吉化第九中学校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "466842911486812160", "title": "北京市朝阳区2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "213632306023538688", "title": "安徽省黄山市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "203142997000626176", "title": "江苏省南京市八区联考2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564581250154930176", "questionArticle": "<p>10．某中学计划购买<i>A</i>型和<i>B</i>型课桌凳共200套，经招标，购买一套<i>A</i>型课桌凳比购买一套<i>B</i>型课桌凳少用40元，且购买4套<i>A</i>型和5套<i>B</i>型课桌凳共需1820元．</p><p>（1）求购买一套<i>A</i>型课桌凳和一套<i>B</i>型课桌凳各需多少元？</p><p>（2）学校根据实际情况，要求购买这两种课桌凳总费用不能超过40880元，并且购买<i>A</i>型课桌凳的数量不能超过<i>B</i>型课桌凳的 $ \\dfrac { 2 } { 3 } $ ，求该校本次购买<i>A</i>型和<i>B</i>型课桌凳共有几种方案？哪种方案的总费用最低？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|210000|220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林长春外国语学校 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 9, "referenceNum": 5, "createTime": "2025-04-08", "keyPointIds": "16438|16490", "keyPointNames": "和差倍分问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581225806995456", "questionFeatureName": "生活背景问题", "questionMethodName": "函数与方程思想", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564581225806995456", "title": "吉林省长春市朝阳区长春外国语学校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "129992170803601408", "title": "辽宁省阜新市2018-2019学年九年级下学期模拟测试数学试题", "paperCategory": 1}, {"id": "1008436820971520", "title": "山东省济南市平阴县2019年中考二模数学试卷", "paperCategory": 1}, {"id": "130001145158737920", "title": "山东省邹城市第八中学2020年九年级中考一模数学试题", "paperCategory": 1}, {"id": "129994587049861120", "title": "山东省东明县菜园集中学2020年九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 147, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 147, "timestamp": "2025-07-01T02:18:16.189Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}