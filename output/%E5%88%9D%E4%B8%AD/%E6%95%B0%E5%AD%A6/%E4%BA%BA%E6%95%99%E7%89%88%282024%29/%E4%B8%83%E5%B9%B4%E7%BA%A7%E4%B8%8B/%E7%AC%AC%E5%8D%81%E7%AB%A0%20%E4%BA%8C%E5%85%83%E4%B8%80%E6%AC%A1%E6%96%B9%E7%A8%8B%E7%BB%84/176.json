{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 175, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "555078182052864000", "questionArticle": "<p>1．若 $ \\begin{cases}a=2,\\\\ b=1\\end{cases} $ 是二元一次方程组 $ \\begin{cases}\\dfrac{3}{2}ax+by=5,\\\\ ax-by=2\\end{cases} $ 的解,则<i>x</i>+2<i>y</i>的算术平方根为&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3&nbsp;&nbsp;B．3,−3&nbsp;&nbsp;C． $ \\sqrt{3} $ &nbsp;&nbsp;D． $ \\sqrt{3} {\\rm ,-} $  $ \\sqrt{3} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 3, "createTime": "2025-03-13", "keyPointIds": "16288|16420|16424", "keyPointNames": "算术平方根|二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555078178819055616", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555078178819055616", "title": "10.2消元-解二元一次方程组（练习）", "paperCategory": 10}, {"id": "204613314541821952", "title": "黑龙江省牡丹江、鸡西地区朝鲜族学校2020年中考数学试题", "paperCategory": 1}, {"id": "198412778029228032", "title": "2022年八年级上册华师版数学第11章11.1平方根与立方根课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555076391844225024", "questionArticle": "<p>2．如图,2个塑料凳子叠放在一起时的高度为60 cm,4个塑料凳子叠放在一起时的高度为80 cm,塑料凳子相同且忽略叠放时的缝隙,则11个塑料凳子叠放在一起时的高度为&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/12/2/1/0/0/0/555076325087682563/images/img_4.png\" style=\"vertical-align:middle;\" width=\"144\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/12/2/1/0/0/0/555076325087682564/images/img_5.png\" style=\"vertical-align:middle;\" width=\"144\" alt=\"试题资源网 https://stzy.com\"></p><p>A．120 cm&nbsp;&nbsp;B．130 cm&nbsp;&nbsp;C．140 cm&nbsp;&nbsp;D．150 cm</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-13", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555076384667770880", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555076384667770880", "title": "10.2消元-解二元一次方程组（诊断）", "paperCategory": 10}, {"id": "458780296344805376", "title": "河南省新乡市2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555076388824326144", "questionArticle": "<p>3．已知关于<i>x</i>,<i>y</i>的二元一次方程组 $ \\begin{cases}2ax+by=3,\\\\ ax-by=1\\end{cases} $ 的解为 $ \\begin{cases}x=1,\\\\ y=-1,\\end{cases} $ 则 $ a-2b $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．−2　　　　B．2　　　　C．3　　　　D．−3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|150000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-13", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555076384667770880", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555076384667770880", "title": "10.2消元-解二元一次方程组（诊断）", "paperCategory": 10}, {"id": "209628114594340864", "title": "内蒙古自治区巴彦淖尔市临河区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555076387452788736", "questionArticle": "<p>4．解下列方程组:(1) $ \\begin{cases}x-y=13,\\\\ x=6y-7;\\end{cases} $ &nbsp;&nbsp;&nbsp;&nbsp;\t(2) $ \\begin{cases}2x+5y=8,\\\\ 3x+2y=5.\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-03-13", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555076384667770880", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555076384667770880", "title": "10.2消元-解二元一次方程组（诊断）", "paperCategory": 10}, {"id": "478351376667418624", "title": "重庆市綦江区古南中学2023−2024学年八年级上学期入学数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555073908870782976", "questionArticle": "<p>5．若方程 $ ax+3y=2+4x $ 是关于 $ x,y $ 的二元一次方程,则 $ a $ 满足&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ a\\ne 1 $　　　　B． $ a\\ne 2 $　　　　C． $ a\\ne 3 $　　　　D． $ a\\ne 4 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|350000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-13", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555073907033677824", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555073907033677824", "title": "10.1二元一次方程组的概念（练习）", "paperCategory": 10}, {"id": "450067090822701056", "title": "福建省厦门市松柏中学2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555177147549130752", "questionArticle": "<p>6．某超市销售每台进价分别为200元、150元的甲、乙两种型号的电器，下表是近两周的销售情况：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售时段</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售数量</p></td><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售收入</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>甲种型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>乙种型号</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第一周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>3台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>5台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1900元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第二周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>4台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>10台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>3200元</p></td></tr></table><p>(进价、售价均保持不变，利润=销售收入-进货成本)</p><p>⑴求<i>A</i>、<i>B</i>两种型号的电风扇的销售单价；</p><p>⑵若超市准备用不多于5000元的金额再采购这两种型号的电风扇共30台，且按(1)中的销售单价全部售完利润不少于1850元，则有几种购货方案？</p><p>⑶在⑵的条件下，超市销售完这30台电风扇哪种方案利润最大？最大利润是多少？请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024江苏宿迁 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-03-13", "keyPointIds": "16437|16490|16544", "keyPointNames": "销售利润问题|一元一次不等式组的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555177121989042176", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "555177121989042176", "title": "江苏省宿迁市2023−2024学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555177141043765248", "questionArticle": "<p>7．解下列方程组：</p><p>(1) $ \\begin{cases} x+y+z=6 \\\\ x+z=4 \\\\ x=3 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 5x+3y=2 \\\\ 3x+2y=1 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏宿迁 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-13", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555177121989042176", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "555177121989042176", "title": "江苏省宿迁市2023−2024学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555177135754747904", "questionArticle": "<p>8．方程组 $ \\begin{cases} x+y=-3 \\\\ x-y=5 \\end{cases}  $ 的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏宿迁 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-13", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555177121989042176", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "555177121989042176", "title": "江苏省宿迁市2023−2024学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555175894249152512", "questionArticle": "<p>9．解一元一次方程及二元一次方程组</p><p>(1) $ 5\\left ( { x-5 } \\right ) -2\\left ( { x+1 } \\right ) =3 $ ；</p><p>(2) $ \\begin{cases} x+y=4 \\\\ 3x-y=-9 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈尔滨 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-13", "keyPointIds": "16402|16423", "keyPointNames": "解一元一次方程|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555175874812747776", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555175874812747776", "title": "黑龙江省哈尔滨市松雷中学2024—2025学年七年级下学期数学开学考试试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553363733487067136", "questionArticle": "<p>10．我国古代数学著作《九章算术》中记载了一个关于“方程”的问题：“今有牛五、羊二，直金十两．牛二、羊五，直金八两．问牛羊各直金几何？”译文：“今有牛5头，羊2头，共值金10两．牛2头，羊5头，共值金8两．问牛、羊每头各值金多少？”若设牛每头值金<i>x</i>两，羊每头值金<i>y</i>两，则可列方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+2y=10 \\\\ 2x+5y=8 \\end{cases}  $ B． $ \\begin{cases} 2x+5y=10 \\\\ 5x+2y=8 \\end{cases}  $ </p><p>C． $ \\begin{cases} 5x+5y=10 \\\\ 2x+5y=8 \\end{cases}  $ D． $ \\begin{cases} 5x+2y=10 \\\\ 2x+2y=8 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025江苏宿迁 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-13", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553363720593776640", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "553363720593776640", "title": "江苏省宿迁市南京师范大学附属中学宿迁分校2024−2025学年九年级下学期2月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 176, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 176, "timestamp": "2025-07-01T02:21:41.476Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}