{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 63, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "579473671267332096", "questionArticle": "<p>1．若关于 $ x $ 、 $ y $ 的二元一次方程组 $ \\begin{cases} 2x+3y=3 \\\\ ax-by=-5 \\end{cases}  $ 和 $ \\begin{cases} 3x-2y=11 \\\\ bx-ay=1 \\end{cases}  $ 有相同的解，则 $ a-b $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 1 $ B． $ 3 $ C． $ -1 $ D． $ -3 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆朝高 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579473654632722432", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579473654632722432", "title": "重庆朝阳中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579473670281670656", "questionArticle": "<p>2．如图，在长方形 $ ABCD $ 中，放入六个形状、大小相同的小长方形，经测量， $ BC=18{ \\rm{ c } }{ \\rm{ m } } $ ， $ BE=10{ \\rm{ c } }{ \\rm{ m } } $ ．图中阴影部分的总面积为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/19/2/1/0/0/0/579473628409929729/images/img_8.png\" style=\"vertical-align:middle;\" width=\"203\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ 24{ \\rm{ c } }{ \\rm{ m } }{^{2}} $ B． $ 108{ \\rm{ c } }{ \\rm{ m } }{^{2}} $ C． $ 112{ \\rm{ c } }{ \\rm{ m } }{^{2}} $ D． $ 144{ \\rm{ c } }{ \\rm{ m } }{^{2}} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆朝高 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579473654632722432", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579473654632722432", "title": "重庆朝阳中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579473379062755328", "questionArticle": "<p>3．解下列方程组：</p><p>（1） $ \\begin{cases} x+y=4 \\\\ 2x-y=5 \\end{cases}  $ </p><p>（2） $ \\begin{cases} 4x+y=5 \\\\ \\dfrac { x-1 } { 2 }+\\dfrac { y } { 3 }=2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津一中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579473351652978688", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579473351652978688", "title": "天津市第一中学2024—2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579857369959538688", "questionArticle": "<p>4．某数学兴趣小组在一次探究性学习中，研究了“寻找无数组整数<i>x</i>，<i>y</i>，使得 $ 5x+6y=1 $ ”的问题，指导教师将学生的发现进行整理，设计了如下数表，部分信息如下：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 16.7pt;\"><p><i>x</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 25.15pt;\"><p> $ -1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p>11</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 69.75pt;\"><p>（<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>…</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 16.7pt;\"><p><i>y</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 25.15pt;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ -4 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ -9 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 69.75pt;\"><p>（<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>…</p></td></tr></table><p>（1）观察表格，根据规律请在表格的横线上填空；</p><p>（2）由上面的规律可知，若表中某一列的两个整数依次是<i>m</i>和<i>n</i>，这表中相邻的下一列的两个数分别是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>和<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>（分别用<i>m</i>和<i>n</i>表示）；</p><p>（3）有同学根据上面的探究得出结论“对于任何正整数<i>k</i>，都存在无数组整数<i>m</i>，<i>n</i>，使得 $ 5m+3n=k $ 成立”．请对该结论判断正误并简述理由．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽马鞍山 · 临考冲刺", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16311|16420", "keyPointNames": "规律型：数与式的变化类|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579857335708852224", "questionFeatureName": "规律探究题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579857335708852224", "title": "2025安徽省马鞍山市第七中学中考三调数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580247000785924096", "questionArticle": "<p>5．2025年春节，随着《哪吒2》电彩的爆火，某玩具公司生产了“哪吒”和“敖丙”两款手办．已知每个“哪吒”手办的售价比每个“敖丙”手办的售价便宜20元，按售价购买3个“哪吒”手办和2个“敖丙”手办共需540元．</p><p>（1）每个“哪吒”和“敖丙”手办的售价分别是多少元？</p><p>（2）由于电影角色深受大家喜爱，所以玩具公司决定对两款手办进行降价促销，若降价后每个“敖丙”手办的售价是每个“哪吒”手办售价的 $ 1.3 $ 倍，且用800元购买“哪吒”手办的数量比用520元购买“敖丙”手办的数量多5个，求降价后每个“哪吒”手办的售价为多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市长寿川维中学校 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-22", "keyPointIds": "16437|16476", "keyPointNames": "销售利润问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580246968196182016", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580246968196182016", "title": "重庆市长寿川维中学校2024−2025学年九年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580246543296409600", "questionArticle": "<p>6．已知整式 $ M=a{{}_{ m } }x{^{m}}+a{{}_{ m-1 } }x{^{m-1}}+\\cdots +a{{}_{ 1 } }x+a{{}_{ 0 } } $ ， $ N=b{{}_{ n } }x{^{n}}+b{{}_{ n-1 } }x{^{n-1}}+\\cdots +b{{}_{ 1 } }x+b{{}_{ 0 } } $ ，其中 $ a{{}_{ m-1 } } $ ， $ a{{}_{ m-2 } } $ ，…， $ a{{}_{ 0 } } $ ， $ b{{}_{ n-1 } } $ ， $ b{{}_{ n-2 } } $ ，…， $ b{{}_{ 0 } } $ 为自然数， $ m $ ， $ a{{}_{ m } } $ ， $ n $ ， $ b{{}_{ n } } $ 为正整数，且满足： $ a{{}_{ m } }+a{{}_{ m-1 } }+\\cdots +a{{}_{ 1 } }+a{{}_{ 0 } }=m $ ， $ b{{}_{ n } }+b{{}_{ n-1 } }+\\cdots +b{{}_{ 1 } }+b{{}_{ 0 } }=n $ ，记 $ S=M+N $ ， $ T=M-N $ ．则下列说法：①当 $ x=1 $ 时，若 $ \\begin{cases} S=5 \\\\ T=1 \\end{cases}  $ ，则 $ \\begin{cases} m=3 \\\\ n=2 \\end{cases}  $ ；②当 $ m=3 $ 时，满足条件的整式 $ M $ 共有8个；③不存在任何一个 $ m=n $ ，使得 $ S=4x{^{4}}+2x{^{3}}+x $ ；其中正确的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0B．1C．2D．3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025重庆重庆18中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-22", "keyPointIds": "16424|28551", "keyPointNames": "加减消元法解二元一次方程组|代数推理", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580246524669505536", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580246524669505536", "title": "重庆市第十八中学2024—2025学年下学期期中考试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580246551936675840", "questionArticle": "<p>7．解答下列各题</p><p>（1） $ \\dfrac { x+0.4 } { 0.2 }=1-\\dfrac { 1.2-x } { 0.3 } $ ；</p><p>（2） $ \\begin{cases} \\dfrac { 3\\left ( { x+y } \\right )  } { 5 }-\\dfrac { 2x-y } { 4 }=2 \\\\ x-2y=-1 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆18中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-22", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580246524669505536", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580246524669505536", "title": "重庆市第十八中学2024—2025学年下学期期中考试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580246539995492352", "questionArticle": "<p>8．已知 $ \\begin{cases} x=-2 \\\\ y=m \\end{cases}  $ 是方程 $ x+3y=10 $ 的一个解，则 $ m $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．16B．6C． $ \\dfrac { 3 } { 8 } $ D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆18中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-22", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580246524669505536", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580246524669505536", "title": "重庆市第十八中学2024—2025学年下学期期中考试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580246054357999616", "questionArticle": "<p>9．在解方程组 $ \\begin{cases} ax+5y=15 \\\\ 4x-by=-2 \\end{cases}  $ 时，由于粗心，甲看错了方程组中的 $ a $ ，而得解为 $ \\begin{cases} x=-3 \\\\ y=-1 \\end{cases}  $ ，乙看错了方程组中的 $ b $ ，而得解为 $ \\begin{cases} x=5 \\\\ y=4 \\end{cases}  $ </p><p>（1）甲把 $ a $ 看成了什么，乙把 $ b $ 看成了什么；</p><p>（2）求出原方程组的正确解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川宜宾 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-22", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580246025236946944", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580246025236946944", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580246053418475520", "questionArticle": "<p>10．若关于 $ x、y $ 的二元一次方程组 $ \\begin{cases} x-y=m-5 \\\\ x+y=3m+3 \\end{cases}  $ 中，<i>x</i>的值为负数，<i>y</i>的值为正数．</p><p>（1）用含<i>m</i>的代数式表示 $ x、y $ ；</p><p>（2）求<i>m</i>的取值范围．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川宜宾 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-05-22", "keyPointIds": "16426|16489", "keyPointNames": "二元一次方程组的应用|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580246025236946944", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580246025236946944", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 64, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 64, "timestamp": "2025-07-01T02:08:22.133Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}