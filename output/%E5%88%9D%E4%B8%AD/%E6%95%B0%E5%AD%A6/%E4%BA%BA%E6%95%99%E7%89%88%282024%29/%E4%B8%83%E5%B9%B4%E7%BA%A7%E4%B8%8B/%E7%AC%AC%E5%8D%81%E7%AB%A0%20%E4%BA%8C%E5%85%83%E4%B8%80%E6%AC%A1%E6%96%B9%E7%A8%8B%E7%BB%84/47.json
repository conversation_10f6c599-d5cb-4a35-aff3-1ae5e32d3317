{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 46, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "584431320681459712", "questionArticle": "<p>1．《九章算术》是中国古代数学著作之一，书中有这样一个问题：四只雀、六只燕共重一斤：雀重燕轻，互换其中一只，恰好一样重，问：每只雀、燕的重量各为多少斤？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏淮安 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584431293653364736", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "584431293653364736", "title": "江苏省淮安市开明中学2024−2025学年九年级下学期一模数学卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585255585769107456", "questionArticle": "<p>2．袁隆平爷爷多次说：“中国人要把饭碗牢牢地端在自己的手里！”为扩大粮食生产规模，稻田公园生产基地计划投入一笔资金购进甲、乙两种农机，已知购进1件甲种农机和1件乙种农机共需2万元，购进2件甲种农机和3件乙种农机共需5.5万元．</p><p>（1）求购进1件甲种农机和1件乙种农机各需多少万元？</p><p>（2）若该粮食生产基地计划购进甲、乙两种农机共10件，且投入资金不少于9.5万元且不超过12万元，则有哪几种购买方案？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南娄底 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-05", "keyPointIds": "16441|16490", "keyPointNames": "其他问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585255557491109888", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "585255557491109888", "title": "2025年湖南省娄底市涟源市中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585614062790156288", "questionArticle": "<p>3．我国古代《算法统宗》里有这样一首诗“我问开店李三公，众客都来到店中．一房七客多七客，一房九客一房空．”诗中后面两句的意思是：如果每一间客房住7人，那么有7人无房可住；如果每一间客房住9人，那么就空出一间客房．设有客房 $ x $ 间，客人 $ y $ 人，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 7x-7=y \\\\ 9(x+1)=y \\end{cases}  $ B． $ \\begin{cases} 7x-7=y \\\\ 9(x-1)=y \\end{cases}  $ C． $ \\begin{cases} 7x+7=y \\\\ 9(x+1)=y \\end{cases}  $ D． $ \\begin{cases} 7x+7=y \\\\ 9(x-1)=y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|130000|320000|510000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 7, "referenceNum": 6, "createTime": "2025-06-05", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577297793284354048", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "577297793284354048", "title": "2025年河北省石家庄市九年级数学中考一模试题", "paperCategory": 1}, {"id": "567961685262114816", "title": "2025年四川省成都市石室天府中学九年级下学期一诊模拟数学", "paperCategory": 1}, {"id": "551913341523894272", "title": "湖南省长沙市第十五中学2024−2025学年九年级下学期入学考试数学试题", "paperCategory": 1}, {"id": "585614042024157184", "title": "2025年河北省唐山市丰润区九年级中考二模数学试题", "paperCategory": 1}, {"id": "571884380517343232", "title": "山东省潍坊市诸城市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}, {"id": "558038756739031040", "title": "2025年江苏省镇江市丹徒区中考数学一模试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "585254127585767424", "questionArticle": "<p>4．每年的5月20日为中国学生营养日，2024年营养日的主题是“奶豆添营养，少油更健康”．某学校为每位学生定制了盒装的牛奶和豆浆，它们的营养成分表如下：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p>营养成分食品种类</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>一盒牛奶</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>一盒豆浆</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p>能量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p> $ { { 2 } }{ { 8 } }{ { 0 } }{ \\rm{ k } }{ \\rm{ J } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p> $ { { 2 } }{ { 1 } }{ { 0 } }{ \\rm{ k } }{ \\rm{ J } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p>蛋白质</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p> $ { { 3 } }{ { . } }{ { 5 } }{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p> $ { { 4 } }{ { . } }{ { 2 } }{ \\rm{ g } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p>脂肪</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p> $ { { 3 } }{ { . } }{ { 5 } }{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p> $ { { 2 } }{ { . } }{ { 4 } }{ \\rm{ g } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p>碳水化合物</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p> $ { { 5 } }{ { . } }{ { 6 } }{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p> $ { { 1 } }{ { . } }{ { 7 } }{ \\rm{ g } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p>钠</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p> $ { { 6 } }{ { 5 } }{ \\rm{ m } }{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p> $ { { 1 } }{ { 3 } }{ \\rm{ m } }{ \\rm{ g } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p>钙</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p> $ { { 1 } }{ { 3 } }{ { 0 } }{ \\rm{ m } }{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>&nbsp;</p></td></tr></table><p>某天，初中生小志从这两种食品中恰好摄入了 $ { { 7 } }{ { 7 } }{ { 0 } }{ \\rm{ k } }{ \\rm{ J } } $ 能量和 $ { { 1 } }{ { 1 } }{ { . } }{ { 2 } }{ \\rm{ g } } $ 蛋白质．</p><p>（1）小志喝了牛奶和豆浆各多少盒？</p><p>（2）初中生每日脂肪摄入量标准约为 $ 59\\sim 73{ \\rm{ g } } $ ．若小志这天已经从其它食品中摄入 $ 60{ \\rm{ g } } $ 脂肪，在他喝完牛奶和豆浆后，脂肪摄入量是否超标，并说明理由．</p><p>（3）老师又统计了小石所在班级的三名学生这天的脂肪摄入量，见下表．老师从这三名学生中随机选两位，则她们的脂肪摄入量均达标的概率为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p>学生</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>小静</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>小华</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>小畅</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p>脂肪摄入量（克）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>54</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>66</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>70</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京人大附中 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-05", "keyPointIds": "16440|16904", "keyPointNames": "表格或图示问题|列表法或树状图法求概率", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585254077530943488", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "585254077530943488", "title": "2025年北京市中国人民大学附属中学中考数学二模模拟试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585255192154644480", "questionArticle": "<p>5．我国明代数学读本《算法统宗》中有一道题，其题意为：客人一起分银子，若每人7两，还剩4两；若每人9两，则差8两，若客人为 $ x $ 人，银子为 $ y $ 两，可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 7x+4=y \\\\ 9x-8=y \\end{cases}  $ B． $ \\begin{cases} 7x+4=y \\\\ 9x+8=y \\end{cases}  $ </p><p>C． $ \\begin{cases} 7y+4=x \\\\ 9y-8=x \\end{cases}  $ D． $ \\begin{cases} 7y-4=x \\\\ 9y+8=x \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北孝感 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-05", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585255176988041216", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "585255176988041216", "title": "2025年湖北省孝感市孝南区中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "585253698357473280", "questionArticle": "<p>6．北京市中小学课间延长为15分钟后，某校为丰富学生的课间活动，准备购买一批课外读物．一班买4本《三国演义》与3本《红楼梦》共用190元，二班买3本《三国演义》与6本《红楼梦》共用255元．求《三国演义》和《红楼梦》每本多少元．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京昌平 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-05", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585253662265487360", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585253662265487360", "title": "2025年北京市昌平区九年级二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585254257168789504", "questionArticle": "<p>7．《算法统宗》中有一道题为“隔沟计算”，其原文是：甲乙隔沟放牧，二人暗里参详，甲云得乙九只羊，多你一倍之上；乙说得甲九只羊，二家之数相当，两人都在暗思对方有多少只羊，甲对乙说：“我若得你9只羊，我的羊多你一倍．”乙对甲说：“我若得你9只羊，我们两家的羊数就一样多．”设甲有<i>x</i>只羊，乙有<i>y</i>只羊，根据题意列出二元一次方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-9=2\\left ( { y+9 } \\right )  \\\\ y+9=x-9 \\end{cases}  $ B． $ \\begin{cases} x+9=2(y-9) \\\\ y+9=x-9 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+9=2y \\\\ y+9=x \\end{cases}  $ D． $ \\begin{cases} x-9=2y \\\\ y+9=x-9 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北恩施土家族苗族自治州 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-05", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585254245164691456", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "585254245164691456", "title": "2025年湖北省恩施州中考二模九年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "585253556866818048", "questionArticle": "<p>8．乡村振兴，科技助农．某农户用甲、乙两种原料配制植物生长肥料，已知每克甲原料含0.5单位氮和0.4单位磷，每克乙原料含1单位氮和0.6单位磷．若一种植物每天需要40单位氮和26单位磷，则每天配制的植物生长肥料中含甲、乙两种原料各多少克恰好能满足需要？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽宣城 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-05", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585253531386421248", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "585253531386421248", "title": "2025年安徽省宣城市九年级数学二模试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581951874720374784", "questionArticle": "<p>9．某学校计划购买甲、乙两种科技类科普读物作为科技节活动奖品，甲类科普读物的单价比乙类科普读物的单价高5元，若购买1本甲类科普读物，2本乙类科普读物共需80元．</p><p>（1）甲类和乙类科普读物单价分别是多少?</p><p>（2）该校计划共购进100本科普读物，总费用不超过2800元，甲类科普读物最多可以买多少本?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-05", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581951855204278272", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "581951855204278272", "title": "2025年广东省深圳市龙华区中考二模数学测试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583053698173018112", "questionArticle": "<p>10．（1）写出一个解为 $ \\begin{cases} x=7 \\\\ y=4 \\end{cases}  $ 的二元一次方程组；</p><p>（2）以（1）中所写的二元一次方程组，编一道生活中的实际问题，并设出未知数．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南许昌 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-05", "keyPointIds": "16421|16426", "keyPointNames": "二元一次方程组的定义|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583053675624439808", "questionFeatureName": "开放性试题", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583053675624439808", "title": "河南省许昌市2024—2025学年下学期期中测试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 47, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 47, "timestamp": "2025-07-01T02:06:22.584Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}