{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 1, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "593317300222078976", "questionArticle": "<p>1．2025年春晚《秧 $ { \\rm{ B } }{ \\rm{ O } }{ \\rm{ T } } $ 》的精彩呈现，是一系列关键技术的突破与创新．机器人采用了先进的 $ { \\rm{ A } }{ \\rm{ I } } $ 驱动全身运动控制技术，某科技公司计生产 $ { \\rm{ A } }{ \\rm{ l } }{ \\rm{ p } }{ \\rm{ h } }{ \\rm{ a } } $ 和 $ { \\rm{ B } }{ \\rm{ e } }{ \\rm{ t } }{ \\rm{ a } } $ 两款 $ { \\rm{ A } }{ \\rm{ I } } $ 机器人，每款机器人主要控制芯片和传感器两种核心零件 $ {\\rm ．} 5 $ 月 $ 20 $ 日，公司采购部门调研市场后得知，花费 $ 320 $ 元购买的主控芯片比花 $ 200 $ 元购买的传感器模块数量少8片，主控芯片的单价是传感器模块的 $ 2 $ 倍．另一部分人对机器人进行研究后发现：用 $ 6 $ 个主控芯片、 $ 48 $ 个传感器模块恰好能制作 $ 1 $ 个 $ { \\rm{ A } }{ \\rm{ l } }{ \\rm{ p } }{ \\rm{ h } }{ \\rm{ a } } $ 机器人和 $ 1 $ 个 $ { \\rm{ B } }{ \\rm{ e } }{ \\rm{ t } }{ \\rm{ a } } $ 机器人，制作 $ 1 $ 个 $ { \\rm{ A } }{ \\rm{ l } }{ \\rm{ p } }{ \\rm{ h } }{ \\rm{ a } } $ 机器人所需主控芯片、传感器模块数量之比是 $ 1:7 $ ，制作 $ 1 $ 个 $ { \\rm{ B } }{ \\rm{ e } }{ \\rm{ t } }{ \\rm{ a } } $ 机器人需要的主控芯片、传感器模块数量之比是 $ 1:9 $ ．</p><p>（1）求主控芯片、传感器模块每个单价分别多少元？</p><p>（2）求制作一个 $ { \\rm{ A } }{ \\rm{ l } }{ \\rm{ p } }{ \\rm{ h } }{ \\rm{ a } } $ 机器人和一个 $ { \\rm{ B } }{ \\rm{ e } }{ \\rm{ t } }{ \\rm{ a } } $ 机器人分别需要主控芯片、传感器模块多少个？</p><p>（3）市场优惠促销，购买 $ 3 $ 个主控芯片赠送 $ 1 $ 个传感器模块．该公司发放活动经费 $ 25000 $ 元，采购部门向市场采购主控芯片、传感器模块采用来制作 $ { \\rm{ A } }{ \\rm{ l } }{ \\rm{ p } }{ \\rm{ h } }{ \\rm{ a } } $ 、 $ { \\rm{ B } }{ \\rm{ e } }{ \\rm{ t } }{ \\rm{ a } } $ 机器人，由于市场库存数量有限，主控芯片仅剩 $ 515 $ 个．如果一个 $ { \\rm{ A } }{ \\rm{ l } }{ \\rm{ p } }{ \\rm{ h } }{ \\rm{ a } } $ 和一个 $ { \\rm{ B } }{ \\rm{ e } }{ \\rm{ t } }{ \\rm{ a } } $ 机器人配成一套，请问最多可以生产多少套机器人？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东华南师大附中 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16432|16476", "keyPointNames": "配套问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593317270631264256", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "593317270631264256", "title": "2025年广东省广州市天河区华南师范大学附属中学中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "594844209258995712", "questionArticle": "<p>2．自2025年5月9日起至2025年12月31日，周末自驾游广西的外省籍小客车，可享受高速公路车辆通行费（以下简称高速费）优惠．小悦一家5月中旬从湖南自驾到广西探亲游玩，此次全程所产生的高速费享受的优惠如下：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">湖南境内路段</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">广西境内特定路段</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">广西境内其他路段</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">周一至周四</p></td><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">9． 5折</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">周五至周日</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">9． 5折</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">全免</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">5折</p></td></tr></table><p>（1）周六小悦一家从湖南Z市到广西A市，所经湖南境内路段、广西境内特定路段和其他路段的高速费原价分别为<i>a</i>元、<i>b</i>元和<i>c</i>元．求此行程的高速费实付多少元？</p><p>（2）周日他们从A市到K市（全程在广西境内），高速费实付27.55元；周一从K市原路返回到A市，高速费实付95.95元．求此行程中A市与K市间广西境内特定路段和其他路段的单程高速费原价分别是多少元．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广西 · 中考真题", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16304|16438", "keyPointNames": "列代数式|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "594844182822297600", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "594844182822297600", "title": "2025年广西中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593317622323650560", "questionArticle": "<p>3．我国明代数学读本《算法统宗》有一道题，其题意为：客人一起分银子，若每人7两，还剩4两；若每人9两，则差8两．若客人为<i>x</i>人，银子为<i>y</i>两，可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 7x+4=y \\\\ 9x-8=y \\end{cases}  $ B． $ \\begin{cases} 7x-4=y \\\\ 9x+8=y \\end{cases}  $ </p><p>C． $ \\begin{cases} 7y+4=x \\\\ 9y-8=x \\end{cases}  $ D． $ \\begin{cases} 7y-4=x \\\\ 9y+8=x \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西桂林十八中 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593317606947332096", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "593317606947332096", "title": "2025年广西壮族自治区 桂林市第十八中学中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "594843237115801600", "questionArticle": "<p>4．2024年8月6日，第十二届世界运动会口号“运动无限，气象万千”在京发布，吉祥物“蜀宝”和“锦仔”亮相．第一中学为鼓励学生积极参加体育活动，准备购买“蜀宝”和“锦仔”奖励在活动中表现优秀的学生．已知购买3个“蜀宝”和1个“锦仔”共需花费332元，购买2个“蜀宝”和3个“锦仔”共需380元．</p><p>（1）购买一个“蜀宝”和一个“锦仔”分别需要多少元？</p><p>（2）若学校计划购买这两种吉祥物共30个，投入资金不少于2160元又不多于2200元，有哪几种购买方案？</p><p>（3）设学校投入资金<i>W</i>元，在（2）的条件下，哪种购买方案需要的资金最少？最少资金是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江佳木斯等地 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16437|16490|16546", "keyPointNames": "销售利润问题|一元一次不等式组的应用|几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "594843199434174464", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "594843199434174464", "title": "2025年黑龙江省龙东地区中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "594843580499275776", "questionArticle": "<p>5．神舟二十号发射窗口时间恰逢第十个“中国航天日”．为激发青少年探索浩瀚宇宙的兴趣，学校组织900名师生乘车前往航空科技馆参观，计划租用45座和60座两种客车（两种客车都要租），若每名学生都有座位且每辆客车都没有空座位，则租车方案有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3种B．4种C．5种D．6种</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江齐齐哈尔 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "594843564909047808", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "594843564909047808", "title": "2025年黑龙江省齐齐哈尔市中考数学真题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "594843214944710656", "questionArticle": "<p>6．为促进学生德智体美劳全面发展，某校计划用1200元购买足球和篮球用于课外活动，其中足球80元/个，篮球120元/个，共有多少种购买方案（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．6B．7C．4D．5</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江佳木斯等地 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "594843199434174464", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "594843199434174464", "title": "2025年黑龙江省龙东地区中考数学真题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "594704227349340160", "questionArticle": "<p>7．解方程组和不等式组：</p><p>（1） $ \\begin{cases} 2x-4y=3 \\\\ x=5y-1 \\end{cases}  $ </p><p>（2） $ \\begin{cases} -3\\left ( { x-2 } \\right ) \\leqslant  4-x \\\\ \\dfrac { 1+4x } { 5 } &gt; 2x-3 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16423|16489", "keyPointNames": "代入消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "594704201038471168", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "594704201038471168", "title": "黑龙江省哈尔滨市第十七中学校2024−2025学年七年级下学期6月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "594704330868961280", "questionArticle": "<p>8．在学习了《二元一次方程》后，数学兴趣小组的同学们探究了多元一次方程正整数解问题．如：方程 $ x+y=2 $ 的正整数解有 $ 1 $ 个，方程 $ x+y=3 $ 的正整数解有 $ 2 $ 个，方程 $ x+y=4 $ 的正整数解有 $ 3 $ 个， $ \\cdots  $ ， 那么方程 $ x+y+z=8 $ 的正整数解的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 7 $ B． $ 21 $ C． $ 22 $ D． $ 23 $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖北武汉 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "594704313177387008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "594704313177387008", "title": "湖北省武汉一初慧泉中学2024−2025学年九年级下学期6月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "594704221963853824", "questionArticle": "<p>9．用大小形状完全相同的长方形纸片在直角坐标系中摆成如图所示的图案，已知 $ A\\left ( { -1,5 } \\right )  $ ，则点 $ B $ 的坐标为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/30/2/1/0/0/0/594704168637476873/images/img_9.png\" style=\"vertical-align:middle;\" width=\"153\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16439|16497", "keyPointNames": "几何问题|点的坐标", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "594704201038471168", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "594704201038471168", "title": "黑龙江省哈尔滨市第十七中学校2024−2025学年七年级下学期6月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "594704230360850432", "questionArticle": "<p>10．某商场化妆品专柜计划购进<i>A</i>，<i>B</i>两种化妆品，已知购进<i>A</i>种化妆品5件，<i>B</i>种化妆品4件需200元；购进<i>A</i>种化妆品10件，<i>B</i>种化妆品5件需310元．</p><p>（1）求<i>A</i>，<i>B</i>两种化妆品每件的进价；</p><p>（2）若该化妆品专柜<i>A</i>种化妆品每件售价21元，<i>B</i>种化妆品每件售价38元，准备购进<i>A</i>，<i>B</i>两种化妆品共100件，且这两种化妆品全部售出后，总获利高于588元，则最多购进<i>A</i>种化妆品多少件？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-06-30", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "594704201038471168", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "594704201038471168", "title": "黑龙江省哈尔滨市第十七中学校2024−2025学年七年级下学期6月月考数学试卷", "paperCategory": 1}, {"id": "493234788003782656", "title": "黑龙江省哈尔滨市松雷中学校2024−2025学年八年级上学期开学测试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 2, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 2, "timestamp": "2025-07-01T02:01:01.556Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}