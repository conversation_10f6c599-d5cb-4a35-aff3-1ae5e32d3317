{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 26, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "589575822446473216", "questionArticle": "<p>1．阅读材料：</p><p>已知 $ m $ 、 $ n $ 都是实数，且满足 $ m-n=17 $ ，就称点 $ T\\left ( { m-1,2n+1 } \\right )  $ 为“和谐点”．例如：点 $ P\\left ( { 4,2 } \\right )  $ ，令 $ \\begin{cases} m-1=4 \\\\ 2n+1=2 \\end{cases}  $ ，解得 $ \\begin{cases} m=5 \\\\ n=\\dfrac { 1 } { 2 } \\end{cases}  $ ，其中 $ m-n=\\dfrac { 9 } { 2 }\\ne 17 $ ，所以 $ P\\left ( { 4,2 } \\right )  $ 不是“和谐点”；点 $ Q\\left ( { 20,9 } \\right )  $ ，令 $ \\begin{cases} m-1=20 \\\\ 2n+1=9 \\end{cases}  $ ，解得 $ \\begin{cases} m=21 \\\\ n=4 \\end{cases}  $ 其中 $ m-n=21-4=17 $ ，所以 $ Q\\left ( { 20,9 } \\right )  $ 是“和谐点”．</p><p>（1）请判断点 $ A\\left ( { 21,13 } \\right )  $ ， $ B\\left ( { 6,-19 } \\right )  $ 是否为“和谐点”，并说明理由；</p><p>（2）若以关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} x-y=3 \\\\ 2x+y=a \\end{cases}  $ 的解为坐标的点 $ F\\left ( { x,y } \\right )  $ 是“和谐点”，求 $ a $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽阜阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575796445982720", "questionFeatureName": "阅读材料题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575796445982720", "title": "安徽省阜阳市2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589575818092785664", "questionArticle": "<p>2．《九章算术》中记载：“今有共买金，人出四百，盈三千四百；人出三百，盈一百．问人数、金价各几何？”意思是：今有人合伙买金，每人出钱400，会多出3400钱；每人出钱300，会多出100钱，问合伙人数、金价各是多少？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽阜阳 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575796445982720", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575796445982720", "title": "安徽省阜阳市2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589575815177744384", "questionArticle": "<p>3．对有理数 $ x $ ， $ y $ 定义一种新运算“ $ { \\rm{ * } } $ ”： $ x{ \\rm{ * } }y=ax+by $ ，其中 $ a $ ， $ b $ 为常数，等式右边是通常的加法和乘法运算．若 $ 3{ \\rm{ * } }5=15 $ ， $ 5{ \\rm{ * } }3=25 $ ，则 $ a-b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽阜阳 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575796445982720", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575796445982720", "title": "安徽省阜阳市2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589575814322106368", "questionArticle": "<p>4．若 $ \\begin{cases} x=3, \\\\ y=-2 \\end{cases}  $ 是二元一次方程 $ ax+by=-2 $ 的一个解，则 $ 3a-2b+2027 $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽阜阳 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575796445982720", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575796445982720", "title": "安徽省阜阳市2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589575810622730240", "questionArticle": "<p>5．已知<i>x</i>，<i>y</i>满足方程组 $ \\begin{cases} x+2y=12 \\\\ 2x+y=-15 \\end{cases}  $ ，则 $ {\\left( { x+y } \\right) ^ {2025}} $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2025B．﹣1C．1D．﹣2025</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽阜阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16424|30400", "keyPointNames": "加减消元法解二元一次方程组|有理数的乘方", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575796445982720", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575796445982720", "title": "安徽省阜阳市2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590829116338778112", "questionArticle": "<p>6．明代数学家吴敬的《九章算法比类大全》中有一个“哪吒夜叉”问题，大意是有3个头6只手的哪吒若干，有1个头8只手的夜叉若干，两方交战，共有36个头，108只手．问哪吒、夜叉各有多少？设哪吒有 $ x $ 个，夜叉有 $ y $ 个，则根据条件所列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+3y=36 \\\\ 8x+6y=108 \\end{cases}  $　　　　B． $ \\begin{cases} x+3y=36 \\\\ 6x+8y=108 \\end{cases}  $</p><p>C． $ \\begin{cases} 3x+y=36 \\\\ 8x+6y=108 \\end{cases}  $　　　　D． $ \\begin{cases} 3x+y=36 \\\\ 6x+8y=108 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590829098928222208", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590829098928222208", "title": "2025年山东省中考数学真题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590467885572595712", "questionArticle": "<p>7．请你根据下列素材，完成有关任务．</p><table style=\"border: solid 1px;border-collapse: collapse; width:414pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>背景</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>某校计划购买篮球和排球，供更多学生参加体育锻炼，增强身体素质．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材一</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>购买 $ 2 $ 个篮球与购买 $ 3 $ 个排球需要的费用相等．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材二</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>购买 $ 2 $ 个篮球和 $ 5 $ 个排球共需 $ 800 $ 元．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材三</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>该校计划购买篮球和排球共 $ 60 $ 个，篮球和排球均需购买，且购买排球的个数不超过购买篮球个数的 $ 2 $ 倍．</p></td></tr><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>请完成下列任务：</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务一</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>每个篮球，每个排球的价格分别是多少元？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务二</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>给出最节省费用的购买方案．</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "530000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025云南 · 中考真题", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16438|16490|16547", "keyPointNames": "和差倍分问题|一元一次不等式组的应用|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590467854123704320", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "590467854123704320", "title": "2025年云南省中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590467882158432256", "questionArticle": "<p>8．如图，在 $ \\vartriangle ABC $ 中， $ \\angle ABC=90{}\\degree  $ ， $ O $ 是 $ AC $ 的中点．延长 $ BO $ 至点 $ D $ ，使 $ OD=OB $ ,连接 $ AD, CD $ ，记 $ AB=a, BC=b $ ， $ \\mathrm{ △ }AOB $ 的周长为 $ l{{}_{ 1 } } $ ， $ \\mathrm{ △ }BOC $ 的周长为 $ l{{}_{ 2 } } $ ，四边形 $ ABCD $ 的周长为 $ l{{}_{ 3 } } $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/19/2/1/0/0/0/590917876040638465/images/img_1.png\" style='vertical-align:middle;' width=\"156\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）求证：四边形 $ ABCD $ 是矩形；</p><p>（2）若 $ l{{}_{ 2 } }-l{{}_{ 1 } }=2, l{{}_{ 3 } }=28 $ ，求 $ AC $ 的长．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "530000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025云南 · 中考真题", "showQuestionTypeCode": "297", "showQuestionTypeName": "证明题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16424|16672|16703", "keyPointNames": "加减消元法解二元一次方程组|勾股定理|矩形的判定与性质综合", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590467854123704320", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "590467854123704320", "title": "2025年云南省中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590580846286909440", "questionArticle": "<p>9． 下列表格中给出的几组数都是关于 $ x $ ， $ y $ 的二元一次方程 $ ax-by=3 $ 的解，表格中 $ m $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 8.1pt;\"><p> $ x $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 7.85pt;\"><p> $ 0 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 7.85pt;\"><p> $ 1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 15.7pt;\"><p> $ 2 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 11pt;\"><p> $ 5 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 8.1pt;\"><p> $ y $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 7.85pt;\"><p> $ 3 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 7.85pt;\"><p> $ 1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 15.7pt;\"><p> $ -1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 11pt;\"><p> $ m $ </p></td></tr></table><p>&nbsp;</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "520000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025贵州贵阳 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-18", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590580820521299968", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590580820521299968", "title": "2025年贵州省贵阳市中考数学三模试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "589575821523726336", "questionArticle": "<p>10．某中学七年级（1）班去体育用品商店买一些篮球和排球，供班上同学进行体育锻炼时使用，共买了2个篮球和6个排球，花 $ 570 $ 元，并且每个排球比篮球便宜 $ 25 $ 元．</p><p>（1）求篮球和排球的单价各是多少；</p><p>（2）商店里搞活动，有两种套餐，①套餐打折：五个篮球和五个排球为一套餐，套餐打八折；②满减活动：满 $ 999 $ 减 $ 100 $ ，满 $ 1999 $ 减 $ 200 $ ；两种活动不重复参与，学校打算购买 $ 14 $ 个篮球， $ 12 $ 个排球，请问如何安排更划算？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽阜阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 11, "referenceNum": 2, "createTime": "2025-06-18", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575796445982720", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "589575796445982720", "title": "安徽省阜阳市2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}, {"id": "311232515770982400", "title": "湖南省长沙市湖南师大附中教育集团2022-2023学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 27, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 27, "timestamp": "2025-07-01T02:03:59.899Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}