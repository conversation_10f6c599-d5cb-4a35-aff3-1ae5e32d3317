{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 9, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "592870822588493824", "questionArticle": "<p>1．已知二元一次方程 $ x-2y=1 $ ，用含<i>y</i>的代数式表示<i>x</i>的形式是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆七中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-06-27", "keyPointIds": "16400|16419", "keyPointNames": "等式的性质|二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559472981002461184", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559472981002461184", "title": "重庆市第七中学校2024−2025学年七年级下学期第一次数学定时作业", "paperCategory": 1}, {"id": "557959326305918976", "title": "重庆市第七中学校2024−2025年度七年级下学期月考数学试题", "paperCategory": 11}, {"id": "592870802313228288", "title": "天津市建华中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592870819417600000", "questionArticle": "<p>2．踩高跷又称为“扎高脚”“缚柴脚”，如图是一位演员踩着长度为身高一半的高跷，脚踏处距高跷顶端 $ 28{ \\rm{ c } }{ \\rm{ m } } $ ，演员踩在高跷上时，“身高”为 $ 224{ \\rm{ c } }{ \\rm{ m } } $ ．设演员的身高为 $ x{ \\rm{ c } }{ \\rm{ m } } $ ，高跷的长度为 $ y{ \\rm{ c } }{ \\rm{ m } } $ ，则下列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/25/2/1/0/0/0/592870623006732290/images/img_3.png\" style=\"vertical-align:middle;\" width=\"147\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} y=\\dfrac { x } { 2 } \\\\ \\left ( { x-28 } \\right ) +y=224 \\end{cases}  $ B． $ \\begin{cases} y=2x \\\\ x+y=224 \\end{cases}  $ </p><p>C． $ \\begin{cases} y=\\dfrac { x } { 2 } \\\\ x+y=224 \\end{cases}  $ D． $ \\begin{cases} y=2x \\\\ \\left ( { x-28 } \\right ) +y=224 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|120000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024天津 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-06-27", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870802313228288", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592870802313228288", "title": "天津市建华中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "181402557041909760", "title": "重庆市重点中学十校联考2020-2021学年七年级下学期第二阶段数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592870818494853120", "questionArticle": "<p>3．解方程组 $ \\begin{cases} ax+by=2 \\\\ cx-7y=8 \\end{cases}  $ 时，甲同学正确解得 $ \\begin{cases} x=3 \\\\ y=2 \\end{cases}  $ ，乙同学因把 $ c $ 写错而得到 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ ，则 $ 7a+7b+3c{ \\rm{ = } } $ （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -22 $ B． $ \\dfrac { 22 } { 3 } $ C．22D．29</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|420000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024湖北武汉 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-06-27", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "458214490573676544", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "458214490573676544", "title": "湖北省武汉市部分学校2023-2024学年七年级下学期月考数学试题", "paperCategory": 1}, {"id": "592870802313228288", "title": "天津市建华中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592869691514724352", "questionArticle": "<p>4．现代办公纸张通常以 $ A{{}_{ 0 } },A{{}_{ 1 } },A{{}_{ 2 } },A{{}_{ 3 } },A{{}_{ 4 } } $ 等标记来表示纸张的幅面规格，一张 $ A{{}_{ 2 } } $ 纸可截成2张 $ A{{}_{ 3 } } $ 纸或4张 $ A{{}_{ 4 } } $ 纸，现计划将100张 $ A{{}_{ 2 } } $ 纸裁成 $ A{{}_{ 3 } } $ 纸和 $ A{{}_{ 4 } } $ 纸，两者共计300张，设可裁成 $ A{{}_{ 3 } } $ 纸 $ x $ 张， $ A{{}_{ 4 } } $ 纸 $ y $ 张，根据题意，可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=100 \\\\ 2x+4y=300 \\end{cases}  $ B． $ \\begin{cases} x+y=300 \\\\ 2x+4y=100 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=100 \\\\ \\dfrac { 1 } { 2 }x+\\dfrac { 1 } { 4 }y=300 \\end{cases}  $ D． $ \\begin{cases} x+y=300 \\\\ \\dfrac { 1 } { 2 }x+\\dfrac { 1 } { 4 }y=100 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江蛟川书院 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 3, "createTime": "2025-06-27", "keyPointIds": "16424|16438", "keyPointNames": "加减消元法解二元一次方程组|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592869675421179904", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592869675421179904", "title": "浙江省宁波市镇海区蛟川书院2023−2024学年下学期七年级期末数学试卷", "paperCategory": 1}, {"id": "430702892451930112", "title": "2024年福建省泉州市中考一模数学试题", "paperCategory": 1}, {"id": "430702685534330880", "title": "2024年福建省泉州市中考模拟数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592869133697458176", "questionArticle": "<p>5．解二元一次方程组： $ \\begin{cases} x+y=4 \\\\ 2x-y=5 \\end{cases}  $ .</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东执信中学 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 3, "createTime": "2025-06-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593321950644056064", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "593321950644056064", "title": "广东省广州市执信中学2024−2025学年中考数学二模试卷", "paperCategory": 1}, {"id": "592869098838597632", "title": "2025年广东省广州市广州中学中考二模数学试卷", "paperCategory": 1}, {"id": "587395481459793920", "title": "2025年广东省广州市花都区中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592868976306200576", "questionArticle": "<p>6．小明在学习了勾股定理的证明后，尝试制作了四个全等三角形纸板，并拼出一个新图形，如图所示，若 $ EF=1,GH=7 $ ，则正方形 $ ABCD $ 的周长为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/25/2/1/0/0/0/592868937349509132/images/img_12.png\" style=\"vertical-align:middle;\" width=\"150\" alt=\"试题资源网 https://stzy.com\"></p><p>A．14B．17C．20D．24</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-06-27", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592868958807564288", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592868958807564288", "title": "2025年黑龙江省龙东地区中考四模数学试题", "paperCategory": 1}, {"id": "565310370648203264", "title": "2025年浙江温州鹿城区温州外国语学校九年级中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592868974284546048", "questionArticle": "<p>7．某果农将采摘的荔枝分装为大箱和小箱销售，其中每个大箱装4千克荔枝，每个小箱装3千克荔枝．该果农现采摘有32千克荔枝，根据市场销售需求，大小箱都要装满，则所装的箱数最多为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．8箱B．9箱C．10箱D．11箱</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|230000|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川宜宾 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 3, "createTime": "2025-06-27", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "457847279958925312", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "457847279958925312", "title": "2024年四川省宜宾市中考数学试题", "paperCategory": 1}, {"id": "592868958807564288", "title": "2025年黑龙江省龙东地区中考四模数学试题", "paperCategory": 1}, {"id": "465537589874827264", "title": "河南省驻马店市部分学校联考2023-2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592868888871743488", "questionArticle": "<p>8．近年来教育部要求学校积极开展素质教育，落实“双减”政策，深圳市某中学把足球和篮球列为该校的特色项目．学校准备从体育用品商店一次性购买若干个篮球和足球，若购买3个篮球和2个足球共490元，购买2个篮球和3个足球共460元．</p><p>（1）篮球、足球的单价各是多少元?</p><p>（2）根据学校实际需要，需一次性购买篮球和足球共100个．购买篮球的数量不少于足球数量的一半，为使购买的总费用最小，那么应购买篮球、足球各多少个?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025浙江舟山 · 临考冲刺", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-06-27", "keyPointIds": "16424|16438|16535", "keyPointNames": "加减消元法解二元一次方程组|和差倍分问题|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592868858500788224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592868858500788224", "title": "2025年浙江省舟山市定海区第五中学中考三模数学试题", "paperCategory": 1}, {"id": "460574803612704768", "title": "2024年广东省深圳市35校中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592868772333006848", "questionArticle": "<p>9．某电器超市销售每台进价分别为 $ 160 $ 元、 $ 120 $ 元的<i>A</i>、<i>B</i>两种型号的电风扇，下表是近两周的销售情况：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售时段</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售数量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售收入</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>A</i>种型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>B</i>种型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第一周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>3台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>4台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 1200 $ 元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第二周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>5台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>6台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 1900 $ 元</p></td></tr></table><p>（进价、售价均保持不变，利润=销售收入-进货成本）</p><p>（1）求<i>A</i>、<i>B</i>两种型号的电风扇的销售单价；</p><p>（2）若超市准备用不多于 $ 7500 $ 元的金额再采购这两种型号的电风扇共 $ 50 $ 台，求<i>A</i>种型号的电风扇最多能采购多少台？若超市销售完这 $ 50 $ 台电风扇能实现利润超过 $ 1850 $ 元的目标，请直接给出相应的采购方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山西十中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-06-27", "keyPointIds": "16434|16437|16486", "keyPointNames": "方案问题|销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "464294142706753536", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "464294142706753536", "title": "山西省太原市实验中学校2023-2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "592868735515406337", "title": "安徽省阜阳实验中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592868750950445056", "questionArticle": "<p>10．已知 $ \\{\\hspace{-0.5em}  \\begin{array} {l} x{ \\rm{ = } }{ { 2 } } \\\\ y{ \\rm{ = } }{ { 1 } } \\end{array} \\hspace{-0.5em}  $ 是二元一次方程组 $ \\{\\hspace{-0.5em}  \\begin{array} {l} mx{ { + } }ny{ \\rm{ = } }{ { 8 } } \\\\ nx-&nbsp;my{ \\rm{ = } }{ { 1 } } \\end{array} \\hspace{-0.5em}  $ 的解，则 $ { { 2 } }m-n $ 的算术平方根为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．±2B． $ \\sqrt { 2 } $ C．2D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|520000|150000|340000|420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024安徽阜阳 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 8, "referenceNum": 5, "createTime": "2025-06-27", "keyPointIds": "16288|16426", "keyPointNames": "算术平方根|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592868735515406337", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592868735515406337", "title": "安徽省阜阳实验中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "446100224987144192", "title": "内蒙古自治区包头市九原区2023-2024学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "209999312197033984", "title": "湖北省恩施土家族苗族自治州宣恩县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "209694529632378880", "title": "贵州省安顺市2020-2021学年八年级下学期期末数学试题", "paperCategory": 1}, {"id": "139026030405132288", "title": "山东省东营市实验中学2020年九年级中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 10, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 10, "timestamp": "2025-07-01T02:01:57.682Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}