{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 99, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "572224297365708800", "questionArticle": "<p>1．解方程组</p><p>(1) $ \\begin{cases} 2x-3y=-5 \\\\ 3x+2y=12 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x+y } { 2 }+\\dfrac { x-y } { 3 }=6 \\\\ 4\\left ( { x+y } \\right ) -5\\left ( { x-y } \\right ) =2 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津耀华 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572224272225050624", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572224272225050624", "title": "天津市和平区天津市耀华中学2024-2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572224288658333696", "questionArticle": "<p>2．以方程组 $ \\begin{cases} 2x+y=1 \\\\ y=2\\left ( { x+1 } \\right )  \\end{cases}  $ 的解为坐标的点 $ \\left ( { x,y } \\right )  $ 在第（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）象限</p><p>A．第一象限B．第二象限C．第三象限D．第四象限</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津耀华 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16423|16497", "keyPointNames": "代入消元法解二元一次方程组|点的坐标", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572224272225050624", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572224272225050624", "title": "天津市和平区天津市耀华中学2024-2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "572224287135801344", "questionArticle": "<p>3．刘老师班里共有学生46人，研学当天一男生因病请假，出勤男生数恰为女生数的一半．设该班男生总人数为<i>x</i>人，女生总人数为<i>y</i>人，下列方程组中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\{\\hspace{-0.5em}  \\begin{array} {} x-y=46 \\\\ y=2\\left ( { x+1 } \\right )  \\end{array} \\hspace{-0.5em}  $ B． $ \\{\\hspace{-0.5em}  \\begin{array} {} x+y=46 \\\\ y=2\\left ( { x+1 } \\right )  \\end{array} \\hspace{-0.5em}  $ </p><p>C． $ \\{\\hspace{-0.5em}  \\begin{array} {} x-y=46 \\\\ y=2\\left ( { x-1 } \\right )  \\end{array} \\hspace{-0.5em}  $ D． $ \\{\\hspace{-0.5em}  \\begin{array} {} x+y=46 \\\\ y=2\\left ( { x-1 } \\right )  \\end{array} \\hspace{-0.5em}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津耀华 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572224272225050624", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572224272225050624", "title": "天津市和平区天津市耀华中学2024-2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "572224284094930944", "questionArticle": "<p>4．在下列方程中，是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ &nbsp;\\dfrac { 2 } { x }+y=5 $ B．  $ 3\\left ( { x-2y } \\right ) =1-2\\left ( { 3y+x } \\right )  $ </p><p>C．  $ x{^{2}}+y{^{2}}=1 $ D． $ x=y $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025天津耀华 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572224272225050624", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572224272225050624", "title": "天津市和平区天津市耀华中学2024-2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "572224766850932736", "questionArticle": "<p>5．若关于<i>x</i>和<i>y</i>的二元一次方程组 $ \\begin{cases} x+2y=2 \\\\ 2x+y=3a+1 \\end{cases}  $ 的解满足 $ x &gt; 0 $ ， $ y\\leqslant  0 $ ．</p><p>(1)求<i>a</i>的取值范围；</p><p>(2)是否存在一个整数<i>a</i>使不等式 $ 3ax-3a  &lt;  7x-7 $ 的解集为 $ x &gt; 1 $ ．若存在，请求出<i>a</i>的值；若不存在，请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽安庆 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16424|16485|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572224744025530368", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572224744025530368", "title": "安徽省安庆市外国语学校2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572594237289570304", "questionArticle": "<p>6．某中学为落实长沙市教育办公厅《关于进一步加强中小学生体质管理的通知》文件要求，决定增设篮球、足球两门选修课程，为此需要购进一批篮球和足球．已知购买2个篮球和3个足球需要510元；购买3个篮球和5个足球需要810元．根据以上信息解答：</p><p>(1)购买1个篮球和1个足球各需要多少钱？</p><p>(2)若学校计划采购篮球、足球共30个，并要求购买篮球不少于19个，又不超过足球个数的2倍，怎样购买才能使总费用最少？并求出最少费用．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南长沙一中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 9, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16438|16490|16535", "keyPointNames": "和差倍分问题|一元一次不等式组的应用|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572594202766254080", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572594202766254080", "title": "湖南省长沙市一中教育集团2024−2025学年八年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572594075339104256", "questionArticle": "<p>7．我们知道：关于 $ x{ \\rm{ , } }y $ 的二元一次方程 $ ax+by=c $ 有无数个解，每个解记为点 $ P\\left ( { x,y } \\right )  $ ，称点 $ P\\left ( { x,y } \\right )  $ 为“中国结”，这些“中国结”在同一条直线上，称这条直线是所有“中国结”的“复兴线”，记作“复兴线 $ ax+by=c $ ”．特别的，我们把横坐标与纵坐标相等的“中国结”称为“超级中国结”，把横坐标与纵坐标均为整数的“中国结”称为“奇妙中国结”．回答下列问题：</p><p>(1)已知 $ A\\left ( { 2{ \\rm{ , } }-1 } \\right ) { \\rm{ , } }B\\left ( { -5{ \\rm{ , } }1 } \\right ) { \\rm{ , } }C\\left ( { -1{ \\rm{ , } }3 } \\right )  $ ，则是“复兴线” $ x+6y=-4 $ 的“中国结”的是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)“复兴线” $ 2mx-y+n-3=0 $ （ $ m{ \\rm{ , } }n $ 是常数且 $ m\\ne 0 $ ），是否存在“超级中国结”？若存在，请求出“超级中国结”的坐标；若不存在，请说明理由；</p><p>(3)平面直角坐标系中， $ D\\left ( { -6{ \\rm{ , } }-1 } \\right ) { \\rm{ , } }E\\left ( { 8{ \\rm{ , } }-1 } \\right )  $ ，若“复兴线” $ ax-3x+5y=2a-4 $ 与线段 $ DE $ 的交点为“奇妙中国结”，求整数<i>a</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南明德 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16420|16423|16501", "keyPointNames": "二元一次方程的解|代入消元法解二元一次方程组|坐标与图形性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572594042975854592", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572594042975854592", "title": "湖南省长沙市明德教育集团2024−2025年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572594068296867840", "questionArticle": "<p>8．解方程组：</p><p>(1) $ \\begin{cases} x+y=4 \\\\ x=2+y \\end{cases}  $ </p><p>(2) $ \\begin{cases} 3s-2t=2 \\\\ 2s+3t=10 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南明德 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572594042975854592", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572594042975854592", "title": "湖南省长沙市明德教育集团2024−2025年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572594064131923968", "questionArticle": "<p>9．已知 $ x $ ， $ y $ 满足方程组 $ \\begin{cases} y=5-x \\\\ 2x-y=4 \\end{cases}  $ ，则 $ xy $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南明德 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572594042975854592", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572594042975854592", "title": "湖南省长沙市明德教育集团2024−2025年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572594062370316288", "questionArticle": "<p>10．若 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是方程 $ ax+y=6 $ 的解，则<i>a</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南明德 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572594042975854592", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572594042975854592", "title": "湖南省长沙市明德教育集团2024−2025年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 100, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 100, "timestamp": "2025-07-01T02:12:36.795Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}