{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 41, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "584850701999714304", "questionArticle": "<p>1．已知二元一次方程组 $ \\begin{cases} x+2y=5 \\\\ 2x+y=7 \\end{cases}  $ ，则 $ x+y $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 2 $　　　　B． $ 4 $　　　　C． $ 5 $　　　　D． $ 6 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东莞中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-09", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584850683985178624", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584850683985178624", "title": "广东省东莞市东城区东莞中学2024—2025学年下学期七年级数学期中考试试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "584850697318871040", "questionArticle": "<p>2．用代入法解方程组 $ \\begin{cases} x+y=6① \\\\ y=2x② \\end{cases}  $ 时，将②代入①正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x-2x=6 $</p><p>B． $ 2y+y=6 $</p><p>C． $ x+2x=6 $</p><p>D． $ y+y=6 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025广东莞中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-09", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584850683985178624", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584850683985178624", "title": "广东省东莞市东城区东莞中学2024—2025学年下学期七年级数学期中考试试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "584435044376158208", "questionArticle": "<p>3．《九章算术》第八章“方程”篇中记载了这样一道题：“今有甲乙二人持钱不知其数，甲得乙半而钱八十，乙得甲大半而钱亦八十．问甲、乙持钱各几何？”题目大意是甲、乙两人各带了若干钱,如果甲得到乙所有钱的一半，那么甲共有钱80;如果乙得到甲所有钱的 $ \\dfrac { 2 } { 3 } $ ，那么乙也共有钱80．若设甲、乙原本各持钱<i>x</i>,<i>y</i>，则根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=80 \\\\ y+\\dfrac { 2 } { 3 }x=80 \\end{cases}  $　　　　B． $ \\begin{cases} \\dfrac { 1 } { 2 }x+y=80 \\\\ y+\\dfrac { 2 } { 3 }x=80 \\end{cases}  $　　　　C． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=80 \\\\ \\dfrac { 2 } { 3 }y+x=80 \\end{cases}  $　　　　D． $ \\begin{cases} \\dfrac { 1 } { 2 }x+y=80 \\\\ \\dfrac { 2 } { 3 }y+x=80 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津河北 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-09", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584435026911076352", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584435026911076352", "title": "2025年天津市河北区九年级二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "584435238094282752", "questionArticle": "<p>4．解方程组 $ \\begin{cases} 3x-4y=-6 \\\\ x+2y=8 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江台州 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-09", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584435213872177152", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "584435213872177152", "title": "2025年浙江省台州市温岭市中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584850410751438848", "questionArticle": "<p>5．中国古代数学著作《九章算术》中记载了这样一个题目：今有共买琎，人出半，盈四；人出少半，不足三．问人数，琎价各几何？其大意是：今有人合伙买琎石，每人出 $ \\dfrac { 1 } { 2 } $ 钱，会多出4钱；每人出 $ \\dfrac { 1 } { 3 } $ 钱，又差了3钱．问人数，琎价各是多少？设人数为 $ x $ ，琎价为 $ y $ ，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y=\\dfrac { 1 } { 2 }x+4 \\\\ y=\\dfrac { 1 } { 3 }x+3 \\end{cases}  $ B． $ \\begin{cases} y=\\dfrac { 1 } { 2 }x-4 \\\\ y=\\dfrac { 1 } { 3 }x+3 \\end{cases}  $ C． $ \\begin{cases} y=\\dfrac { 1 } { 2 }x-4 \\\\ y=\\dfrac { 1 } { 3 }x-3 \\end{cases}  $ D． $ \\begin{cases} y=\\dfrac { 1 } { 2 }x+4 \\\\ y=\\dfrac { 1 } { 3 }x-3 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|500000|210000|510000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川成都 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 13, "referenceNum": 9, "createTime": "2025-06-09", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "457847809808572416", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "457847809808572416", "title": "2024年四川省成都市中考数学试题", "paperCategory": 1}, {"id": "588012200557584384", "title": "2025年宁夏银川市中考数学第三次模拟测试试卷", "paperCategory": 11}, {"id": "584850394745974784", "title": "2025年山东省聊城市东昌府区、茌平区等部分学校5月中考数学联考模拟试题", "paperCategory": 1}, {"id": "570064015033933824", "title": "2025年山东省日照市新营中学中考4月模拟测试数学试卷", "paperCategory": 1}, {"id": "564221163582300160", "title": "2025年山东省东营市胜利第一初级中学中考一模数学试题", "paperCategory": 1}, {"id": "565669415334748160", "title": "2025年浙江省舟山市定海区金衢山五校联盟九年级中考一模数学试题", "paperCategory": 1}, {"id": "565545612927606784", "title": "2025年浙江省舟山市定海区金衢山五校联盟中考数学一模试卷", "paperCategory": 11}, {"id": "554781399661715456", "title": "辽宁省沈阳市东北育才学校2024−2025学年九年级下学期开学考试数学试题", "paperCategory": 1}, {"id": "544976294900965376", "title": "辽宁省锦州市2024−2025学年 八年级上学期期末数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "584850595355336704", "questionArticle": "<p>6．已知 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是方程<i>ax</i>+<i>by</i>＝3的解，则代数式2<i>a</i>+4<i>b</i>﹣5的值为 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京35中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-09", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584850574408982528", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "584850574408982528", "title": "北京市第三十五中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585613136893353984", "questionArticle": "<p>7．已知 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是二元一次方程 $ ax+by=5 $ 的一个解，则 $ a{^{2}}+4ab+4b{^{2}} $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏无锡市天一实验中学 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-09", "keyPointIds": "16345|16420", "keyPointNames": "运用完全平方公式分解因式|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585613112755134464", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585613112755134464", "title": "2025年江苏省无锡市天一实验学校中考数学二模试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585253113050734592", "questionArticle": "<p>8．某数学学习小组在综合实践《猜想、证明、拓广》中探究了矩形的“减半”问题，课后对其他问题进行探究，发现当已知矩形的相邻两边分别为 $ 2 $ 和 $ 1 $ ， $ 3 $ 和 $ 1 $ ， $ 4 $ 和 $ 1 $ ， $ 5 $ 和 $ 1 $ ， $ 6 $ 和 $ 1 $ ， $ 7 $ 和 $ 1 $ ， $ 8 $ 和 $ 1 $ ， $ 9 $ 和 $ 1 $ 时，都不存在这样的矩形，它的周长和面积分别为已知矩形的周长和面积的 $ \\dfrac { 1 } { 3 } $ ；当已知矩形的相邻两边分别为 $ 10 $ 和 $ 1 $ 时，他们发现存在一个矩形使它的周长和面积分别为已知矩形的周长和面积的 $ \\dfrac { 1 } { 3 } $ ，请你帮助他们写出这个矩形较短边的长为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；当已知矩形的长和宽分别为 $ a $ 和 $ b $ 时，若存在一个矩形使它的周长和面积分别为已知矩形的 $ \\dfrac { 1 } { 3 } $ ，则 $ a $ 和 $ b $ 应满足的关系式为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东青岛市青岛大学附属中学 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-08", "keyPointIds": "16439|16454", "keyPointNames": "几何问题|一元二次方程根的判别式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585253091043221504", "questionFeatureName": "综合与实践题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585253091043221504", "title": "山东省青岛大学附属中学2025年中考第二次模拟数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585253105937195008", "questionArticle": "<p>9．《九章算术》中记载：今有玉方一寸，重七两；石方一寸，重六两，今有石立方三寸，中有玉，并重十一斤、问玉、石重各几何？大意是：若有玉1立方寸，重7两；石1立方寸，重6两．今有石为棱长3寸的正方体（体积为27立方寸），其中含有玉，总重11斤（注：1斤=16两）．问玉、石各重多少？若设玉重 $ x $ 两，石重 $ y $ 两，则可列方程为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=11 \\\\ \\dfrac { x } { 7 }+\\dfrac { y } { 6 }=27 \\end{cases}  $</p><p>B． $ \\begin{cases} x+y=176 \\\\ \\dfrac { x } { 7 }+\\dfrac { y } { 6 }=27 \\end{cases}  $</p><p>C． $ \\begin{cases} x+y=11 \\\\ \\dfrac { 7 } { x }+\\dfrac { 6 } { y }=27 \\end{cases}  $</p><p>D． $ \\begin{cases} x+y=176 \\\\ \\dfrac { 7 } { x }+\\dfrac { 6 } { y }=27 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东青岛市青岛大学附属中学 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-08", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585253091043221504", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585253091043221504", "title": "山东省青岛大学附属中学2025年中考第二次模拟数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "585252264303960064", "questionArticle": "<p>10．如图，款式相同的4个碗叠放在一起总高度为 $ 11.5{ \\rm{ c } }{ \\rm{ m } } $ ，若同款的7个碗叠放在一起总高度为 $ 16{ \\rm{ c } }{ \\rm{ m } } $ ，则一个碗的高度为<u>&nbsp;&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ c } }{ \\rm{ m } } $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/03/2/1/0/0/0/585252219462656000/images/img_15.png\" style=\"vertical-align:middle;\" width=\"120\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-08", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585252238144086016", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "585252238144086016", "title": "2025年浙江省杭州市西湖区九年级中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 42, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 42, "timestamp": "2025-07-01T02:05:46.894Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}