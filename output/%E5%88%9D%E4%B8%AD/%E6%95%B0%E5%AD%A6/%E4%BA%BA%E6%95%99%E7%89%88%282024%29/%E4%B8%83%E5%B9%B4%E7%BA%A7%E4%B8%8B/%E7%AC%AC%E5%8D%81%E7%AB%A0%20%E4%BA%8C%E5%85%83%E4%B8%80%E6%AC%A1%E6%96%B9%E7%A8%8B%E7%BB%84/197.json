{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 196, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "544317287689396224", "questionArticle": "<p>1．用代入消元法解方程组 $ \\begin{cases}3x+4y=2,\\mathrm{①}\\\\ 2x-y=5,\\mathrm{②}\\end{cases} $ 使得代入后化简比较容易的变形是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．由①得 $ x=\\dfrac{2-4y}{3} $ B．由①得 $ y=\\dfrac{2-3x}{4} $ </p><p>C．由②得 $ x=\\dfrac{y+5}{2} $ D．由②得 $ y=2x-5 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317286896672768", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317286896672768", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.1 代入消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317288343707648", "questionArticle": "<p>2．老师设计了一个解方程组的接力游戏，学习小组的四个成员每人做一步，每人只能看到前一人给的步骤，并进行下一步计算，再将结果传递给下一个人，用合作的方式完成该方程组的解题过程，过程如图所示，合作中出现错误的同学是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/11/2/1/0/0/0/544317267917447168/images/img_1.jpg\" style=\"vertical-align:middle;\" width=\"219\" alt=\"试题资源网 https://stzy.com\"></p><p>A．甲B．丙C．乙和丁D．甲和丙</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317286896672768", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317286896672768", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.1 代入消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317289006407680", "questionArticle": "<p>3．已知 $ \\begin{cases}x=0,\\\\ y=-10\\end{cases} $ 和 $ \\begin{cases}x=4,\\\\ y=-8\\end{cases} $ 都是方程 $ ax+by=10 $ 的解，则这个方程是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16420|16423", "keyPointNames": "二元一次方程的解|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317286896672768", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317286896672768", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.1 代入消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317288742166528", "questionArticle": "<p>4．已知方程组 $ \\begin{cases}x=y+5,\\\\ x+y+m=0\\end{cases} $ 和方程组 $ \\begin{cases}2x-y=5,\\\\ x+y+m=0\\end{cases} $ 有相同的解，则 $ m $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317286896672768", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317286896672768", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.1 代入消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317288545034240", "questionArticle": "<p>5．用代入消元法解二元一次方程组 $ \\begin{cases}y=x-3,\\mathrm{①}\\\\ 2x+3y=6,\\mathrm{②}\\end{cases} $ 应先消去<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，具体做法是将<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>代入<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317286896672768", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317286896672768", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.1 代入消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317288016551936", "questionArticle": "<p>6．已知 $ |3x+2y-4| $ 与 $ (5x+7y-3)^{2} $ 互为相反数，则 $ x $ , $ y $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}x=1,\\\\ y=-1\\end{cases} $ B． $ \\begin{cases}x=2,\\\\ y=-1\\end{cases} $ C． $ \\begin{cases}x=-1,\\\\ y=2\\end{cases} $ D．无法确定</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317286896672768", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317286896672768", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.1 代入消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317289203539968", "questionArticle": "<p>7．用代入消元法解下列方程组：</p><p>（1）  $ \\begin{cases}x=3-y,\\mathrm{①}\\\\ 2x-3y=1；\\mathrm{②}\\end{cases} $ </p><p>（2）  $ \\begin{cases}3x+2y=3,\\mathrm{①}\\\\ 2y=x-5.\\mathrm{②}\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317286896672768", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317286896672768", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.1 代入消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317486985945088", "questionArticle": "<p>8．判断 $ \\begin{cases}x=3,\\\\ y=-5\\end{cases} $  是不是二元一次方程组 $ \\begin{cases}4x+2y=2,\\\\ x+y=-1\\end{cases} $  的解.以下是小华对本题的解答过程，请判断是否正确，如果不正确，请写出正确的解答过程.</p><p>解：把 $ \\begin{cases}x=3,\\\\ y=-5\\end{cases} $ 代入 $ 4x+2y=2 $ ，左边 $ =4×3+2×(-5)=2= $ 右边， $ \\therefore \\begin{cases}x=3,\\\\ y=-5\\end{cases} $ 是二元一次方程组 $ \\begin{cases}4x+2y=2,\\\\ x+y=-1\\end{cases} $ 的解.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317484809101312", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317484809101312", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.1 二元一次方程组的概念 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317663884910592", "questionArticle": "<p>9．我国清代算书《御制数理精蕴》中有这样一题：“设如砚七方比笔三支价多四百八十文，又砚三方比笔九支价少一百八十文，问笔砚价各若干？”其大意为假设七方砚台的价格比三支笔的价格多出四百八十文钱，而三方砚台的价格则比九支笔的价格少了一百八十文钱，请问笔和砚台的单价分别是多少？</p><p>(1)求笔和砚台的单价．</p><p>(2)为落实立德树人的根本任务，某校开设了书法课程，需购买砚台和笔若干，已知笔的数量是砚台数量的2倍，学校共花费3420元．问该校可以购买砚台和笔各多少？（1文约等于1.2元）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317661406076928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317661406076928", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时1 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317663675195392", "questionArticle": "<p>10．本学期学校开展以“感受中华传统美德”为主题的研学活动，组织150名学生参观历史博物馆和民俗展览馆，每一名学生只能参加其中一项活动，共支付票款2700元，票价信息如下：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 69.5pt;\"><p>地点</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 52.8pt;\"><p>票价</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 69.5pt;\"><p>历史博物馆</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 52.8pt;\"><p>10元/人</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 69.5pt;\"><p>民俗展览馆</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 52.8pt;\"><p>20元/人</p></td></tr></table><p>(1)请问参观历史博物馆和民俗展览馆的人数各是多少人？</p><p>(2)若学生都去参观历史博物馆，则能节省票款多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317661406076928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317661406076928", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时1 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 197, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 197, "timestamp": "2025-07-01T02:24:08.774Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}