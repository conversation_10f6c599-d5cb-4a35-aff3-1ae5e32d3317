{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 163, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "557696924498829312", "questionArticle": "<p>1．我国古代数学著作《九章算术》卷七有下列问题：“今有共买物，人出八，盈三：人出七，不足四，问人数、物价几何？”意思是：现在有几个人共同出钱去买件物品，如果每人出8钱，则剩余3钱：如果每人出7钱，则差4钱．问有多少人，物品的价格是多少？设有<i>x</i>人，物品的价格为<i>y</i>元，可列方程（组）为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 8x-3=y \\\\ 7x+4=y \\end{cases}  $ B． $ \\begin{cases} 8x+3=y \\\\ 7x-4=y \\end{cases}  $ C． $ \\dfrac { x+3 } { 8 }=\\dfrac { x-4 } { 7 } $ D． $ \\dfrac { y-3 } { 8 }=\\dfrac { y+4 } { 7 } $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000|350000|420000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南雅礼 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 16, "referenceNum": 9, "createTime": "2025-03-20", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557696910842175488", "questionFeatureName": "数学文化题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557696910842175488", "title": "湖南省长沙市雅礼集团2024−2025学年九年级下学期第一次月考数学试题", "paperCategory": 1}, {"id": "374652726246940672", "title": "湖北省十堰市实验中学2022-2023学年九年级上学期期中数学试题", "paperCategory": 1}, {"id": "361569206310051840", "title": "第3部分·限时训练 选择、填空限时训练 刷速度3 【中考必刷题】 河南版 数学", "paperCategory": 1}, {"id": "161413737349423104", "title": "福建省厦门同安区2021年初中毕业班适应性综合练习数学试题", "paperCategory": 1}, {"id": "161415110925262848", "title": "福建厦门同安区2021年初中中考三模数学试题", "paperCategory": 1}, {"id": "141116031959867392", "title": "河南省平顶山市2020年九年级中招二模数学试题", "paperCategory": 1}, {"id": "128574482155872256", "title": "福建省莆田市2020年初中毕业班质检数学卷", "paperCategory": 1}, {"id": "128572029138149376", "title": "福建省福州市长乐区2020年6月九年级综合练习数学试题", "paperCategory": 1}, {"id": "129985381592768512", "title": "河南省南阳市新野县2019年中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557236193160634368", "questionArticle": "<p>2．解方程组： $ \\{\\hspace{-0.5em}  \\begin{array} {l} x-y=1, \\\\ 3x+y=7． \\end{array} \\hspace{-0.5em}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000|330000|440000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2020浙江台州 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 8, "referenceNum": 4, "createTime": "2025-03-20", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "202069161245188096", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "202069161245188096", "title": "浙江省台州市2020年中考数学试题", "paperCategory": 1}, {"id": "557236172591767552", "title": "福建省漳州市第三中学2024−2025学年九年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "159548614896820224", "title": "福建省福州市2021-2022学年中考数学模拟试卷（一）试题", "paperCategory": 1}, {"id": "996526581878784", "title": "广东省广州市增城区2021年中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557698537418432512", "questionArticle": "<p>3．甲仓库与乙仓库共存粮450吨、现从甲仓库运出存粮的60%．从乙仓库运出存粮的40%．结果乙仓库所余的粮食比甲仓库所余的粮食多30吨．若设甲仓库原来存粮<i>x</i>吨．乙仓库原来存粮<i>y</i>吨，则有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\{\\hspace{-0.5em}  \\begin{array} {l} x+y=450 \\\\ (1-60\\%)x-(1-40\\%)y=30 \\end{array} \\hspace{-0.5em}  $ B． $ \\{\\hspace{-0.5em}  \\begin{array} {l} x+y=450 \\\\ 60\\%x-40\\%y=30 \\end{array} \\hspace{-0.5em}  $ </p><p>C． $ \\{\\hspace{-0.5em}  \\begin{array} {l} x+y=450 \\\\ (1-40\\%)y-(1-60\\%)x=30 \\end{array} \\hspace{-0.5em}  $ D． $ \\{\\hspace{-0.5em}  \\begin{array} {l} x+y=450 \\\\ 40\\%y-60\\%x=30 \\end{array} \\hspace{-0.5em}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-03-20", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}, {"id": "197042091737784320", "title": "河北省邢台市信都区第五中学2021-2022学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557698534067183616", "questionArticle": "<p>4．哥哥与弟弟的年龄和是18岁，弟弟对哥哥说：“当我的年龄是你现在年龄的时候，你就是18岁”．如果现在弟弟的年龄是x岁，哥哥的年龄是y岁，下列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=y-18 \\\\ y-x=18-y \\end{cases}  $ B． $ \\begin{cases} y-x=18 \\\\ x-y=y+18 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=18 \\\\ y-x=18+y \\end{cases}  $ D． $ \\begin{cases} y=18-x \\\\ 18-y=y-x \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 9, "referenceNum": 2, "createTime": "2025-03-20", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}, {"id": "158940303591776256", "title": "北师大版八年级上册第5章二元一次方程组单元测", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557235068638371840", "questionArticle": "<p>5．“广安盐皮蛋”是小平故里的名优特产，某超市销售 $ A、B $ 两种品牌的盐皮蛋，若购买9箱 $ \\mathrm{ A } $ 种盐皮蛋和6箱 $ B $ 种盐皮蛋共需390元；若购买5箱 $ \\mathrm{ A } $ 种盐皮蛋和8箱 $ B $ 种盐皮蛋共需310元．</p><p>(1) $ \\mathrm{ A } $ 种盐皮蛋、 $ B $ 种盐皮蛋每箱价格分别是多少元？</p><p>(2)若某公司购买 $ A、B $ 两种盐皮蛋共30箱，且 $ \\mathrm{ A } $ 种的数量至少比 $ B $ 种的数量多5箱，又不超过 $ B $ 种的2倍，怎样购买才能使总费用最少？并求出最少费用．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2023四川广安 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 8, "referenceNum": 3, "createTime": "2025-03-20", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "326389346336022528", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "326389346336022528", "title": "2023年四川省广安市中考数学真题", "paperCategory": 1}, {"id": "372802973481607168", "title": "2023年四川省广安市中考数学真题", "paperCategory": 1}, {"id": "557235042239422464", "title": "2025年湖南省长沙市一中教育集团中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557318134446727168", "questionArticle": "<p>6．2025年春节，随着《哪吒2》电彩的爆火，某玩具公司生产了“哪吒”和“敖丙”两款手办．已知每个“哪吒”手办的售价比每个“敖丙”手办的售价便宜20元，按售价购买3个“哪吒”手办和2个“敖丙”手办共需540元．</p><p>(1)每个“哪吒”和“敖丙”手办的售价分别是多少元？</p><p>(2)由于电影角色深受大家喜爱，所以玩具公司决定对两款手办进行降价促销，若降价后每个“敖丙”手办的售价是每个“哪吒”手办售价的 $ 1.3 $ 倍，且用800元购买“哪吒”手办的数量比用520元购买“敖丙”手办的数量多5个，求降价后每个“哪吒”手办的售价为多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市第一中学校 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-20", "keyPointIds": "16438|16476", "keyPointNames": "和差倍分问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557318101915705344", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "557318101915705344", "title": "重庆市第一中学校2024-2025学年九年级下学期第一次月考数学试题", "paperCategory": 1}, {"id": "566996393266552832", "title": "2025年辽宁省朝阳市第四中学中考数学零模试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "557317971695149056", "questionArticle": "<p>7．解方程组： $ \\begin{cases} x+3y=2 \\\\ 2x-y=-3 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江温州 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557317947879890944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557317947879890944", "title": "浙江省温州市洞头区2024−2025学年九年级下学期第一次适应性检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557317749246042112", "questionArticle": "<p>8．北京时间2024年4月26日5时04分，神舟十八号航天员乘组顺利进驻中国空间站与神舟十七号航天员乘组太空会师，载人飞船发射取得了圆满成功！小星和小红都是航天爱好者，他们计划购买甲、乙两种飞船模型收藏．下面是两位同学的对话：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/18/2/1/0/0/0/557317688474771458/images/img_27.png\" style=\"vertical-align:middle;\" width=\"475\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)求甲、乙两种飞船模型每件的售价分别为多少元？</p><p>(2)若小星计划正好用200元零花钱购买以上两种飞船模型，且每种都有购买，请通过计算说明有多少种购买方案．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西忻州 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-19", "keyPointIds": "16420|16437", "keyPointNames": "二元一次方程的解|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557317725552418816", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557317725552418816", "title": "2025年山西省忻州市部分学校中考一模数学试卷", "paperCategory": 1}, {"id": "555433483289534464", "title": "2025年山西省忻州市部分学校中考一模数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "557317632690528256", "questionArticle": "<p>9．某中学为落实教育部出台的《关于全面加强和改进新时代学校卫生与健康教育工作的意见》，保障学生每天在校内、校外各有1个小时的体育活动时间，决定购买一定数量的篮球和足球供学生使用．已知购买1个篮球和2个足球需花费260元，购买3个篮球和5个足球需花费700元．</p><p>(1)购买一个篮球和一个足球各需花费多少元？</p><p>(2)如果学校购买篮球和足球的总费用不超过2000元，且购买足球15个，那么最多可以购买多少个篮球？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025辽宁沈阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 9, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557317606375464960", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "557317606375464960", "title": "辽宁省沈阳市第一二六中学2024−2025学年八年级下学期3月考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557317626797531136", "questionArticle": "<p>10．解方程组： $ \\begin{cases} 3x+2y=6 \\\\ y=x-2 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁沈阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557317606375464960", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "557317606375464960", "title": "辽宁省沈阳市第一二六中学2024−2025学年八年级下学期3月考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 164, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 164, "timestamp": "2025-07-01T02:20:16.613Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}