{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 30, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "589935126903042048", "questionArticle": "<p>1．下列每对 $ x $ ， $ y $ 的值不是方程 $ x+y=6 $ 的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x=-1 $ ， $ y=7 $　　　　B． $ x=0 $ ， $ y=6 $</p><p>C． $ x=1 $ ， $ y=-6 $　　　　D． $ x=2 $ ， $ y=4 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025辽宁营口等地 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589935117071593472", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589935117071593472", "title": "辽宁省营口市、鞍山市部分学校2024−2025学年七年级下学期5月联考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "588076902692200448", "questionArticle": "<p>2．今年的春节动画电影“哪吒2”火爆影院，成为全民话题，片中各角色的经历和所做所为共同构成了一部生动的教育启示录，“哪吒2”的成功上映，不仅意味着国漫崛起，也是一场教育哲学的胜利，它告诉我们：真正的教育不是矫正与规训，而是唤醒与赋能．“哪吒2”的教育意义深远，吸引了大量市民踊跃观影，各大影院积极推送．金字塔电影院最初上映时准备了成人票和儿童票，发现购买3张成人票和5张儿童票共需350元；若购买6张成人票和3张儿童票共需420元．</p><p>（1）求每张成人票和每张儿童票分别需要多少元？</p><p>（2）金字塔电影院预估正月初一到正月初六处于观看高峰阶段，不再分类购票，实行票价统一．据统计正月初一该影院票房收入费用为40000元，正月初二该影院票房收入费用为43200元，但正月初二的电影票单价在正月初一的票价上涨了 $ 20\\% $ ，且正月初二售出的电影票张数比正月初一售出的张数少了100张，那么正月初一该影院的电影票的单价是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都九中 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16441|16476", "keyPointNames": "其他问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588076854101188608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588076854101188608", "title": "四川省成都市树德中学2024−2025学年下学期二诊校考九年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588076874728775680", "questionArticle": "<p>3．如图，在 $ ΔABC $ 中， $ AB=AC $ ， $ AB $ 的垂直平分线 $ AB $ 交于点 $ D $ ，交 $ AC $ 于点 $ E $ .已知 $ ΔBCE $ 的周长为 $ 8 $ ， $ AC-BC=2 $ ，则 $ AB $ 的长是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/11/2/1/0/0/0/588076835373621251/images/img_4.png\" style=\"vertical-align:middle;\" width=\"146\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都九中 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16424|30383", "keyPointNames": "加减消元法解二元一次方程组|线段垂直平分线的性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588076854101188608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588076854101188608", "title": "四川省成都市树德中学2024−2025学年下学期二诊校考九年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588079423028506624", "questionArticle": "<p>4． $ \\mathrm{ A } $ ， $ B $ 两块试验田去年共收获小麦 $ 500{ \\rm{ k } }{ \\rm{ g } }． $ 今年采用新技术实现了增产，共收获小麦 $ 562{ \\rm{ k } }{ \\rm{ g } }． $ 已知 $ \\mathrm{ A } $ 试验田今年比去年增产 $ 16{ \\rm{ \\% } } $ ， $ B $ 试验田今年比去年增产 $ 10{ \\rm{ \\% } }． $ 去年 $ \\mathrm{ A } $ ， $ B $ 两块试验田分别收获小麦多少 $ { \\rm{ k } }{ \\rm{ g } } $ ？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588079394347855872", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588079394347855872", "title": "2025年江苏省南京市联合体中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588077057592045568", "questionArticle": "<p>5．某快递公司需将一批总重为 $ 25 $ 吨的物品从仓库运往配送中心．现有下表所示两种类型货车可供调配：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>类型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>甲型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>乙型</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>满载（吨）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p> $ 4 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p> $ 3 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>价格（元）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p> $ 500 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p> $ 400 $ </p></td></tr></table><p>（1）若公司一次性派出两种货车共 $ 8 $ 辆，恰好运完所有物品，且公司要求每辆货车必须满载运输，求甲、乙两种货车各派出多少辆？</p><p>（2）若快递公司派出甲型、乙型货车共 $ 7 $ 辆，其中甲型货车不少于 $ 2 $ 辆，要求预算运输费用不超过 $ 3600 $ 元，请设计一种运输方案使总费用最低，并计算最低费用．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川绵阳 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16434|16490", "keyPointNames": "方案问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588077024079556608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588077024079556608", "title": "2025年四川省绵阳市九年级下学期第三次模拟考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588079140038815744", "questionArticle": "<p>6．为奖励在手工制作“动植物细胞”模型活动中获奖的同学，初二（八）班生物付老师计划购买巧克力和酸奶两种零食，已知一块巧克力3.5元，一盒酸奶4元．付老师准备将140元钱全部用于购买这两种零食（两种零食都买），则购买方案共有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．6种B．5种C．4种D．3种</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江齐齐哈尔 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588079114831048704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588079114831048704", "title": "2025年黑龙江省齐齐哈尔市部分学校中考三模联考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589823541450678272", "questionArticle": "<p>7．某小区人行道地砖铺设图案如图所示．用10块相同的小平行四边形地砖拼成一个大平行四边形．若大平行四边形短边长 $ 40{ \\rm{ c } }{ \\rm{ m } } $ ．则小地砖短边长（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/20/2/1/0/0/0/591271319842365441/images/img_1.png\" style='vertical-align:middle;' width=\"167\" alt=\"试题资源网 https://stzy.com\"></p><p>A．7cm　　　　B． $ {\\rm 8} { \\rm{ c } }{ \\rm{ m } } $　　　　C． $ {\\rm 9} { \\rm{ c } }{ \\rm{ m } } $　　　　D． $ 10{ \\rm{ c } }{ \\rm{ m } } $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川自贡 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589823520198139904", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "589823520198139904", "title": "2025年四川省自贡市中考数学真题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589813564799496192", "questionArticle": "<p>8．中国古代数学著作《九章算术》中记载了这样一个题目：今有善田一亩，价三百；恶田七亩，价五百;今并买一顷，价钱一万;问善、恶田各几何？其大意是今有良田1亩价值300钱；劣田7亩价值500钱;今合买良、劣田1顷（100亩），价值10000钱．问良田、劣田各有多少亩？设良田为<i>x</i>亩，劣田为<i>y</i>亩，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=100 \\\\ 300x+\\dfrac { 500 } { 7 }y=10000 \\end{cases}  $　　　　B． $ \\begin{cases} x+y=100 \\\\ 300y+\\dfrac { 500 } { 7 }x=10000 \\end{cases}  $</p><p>C． $ \\begin{cases} x+y=100 \\\\ 300x+500y=10000 \\end{cases}  $　　　　D． $ \\begin{cases} x+y=100 \\\\ 300y+500x=10000 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589813550207512576", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589813550207512576", "title": "2025年四川省成都市中考数学真题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "588078456409202688", "questionArticle": "<p>9．某校为了丰富“阳光大课间”，准备开展花样跳绳活动．某班经过同学投票，决定选购 $ A、B $ 两种型号的跳绳．已知购买 $ 1 $ 根 $ \\mathrm{ A } $ 型跳绳和 $ 2 $ 根 $ B $ 型跳绳共需 $ 42 $ 元；购买 $ 2 $ 根 $ \\mathrm{ A } $ 型跳绳和 $ 1 $ 根 $ B $ 型跳绳共需 $ 39 $ 元．</p><p>（1）购买 $ 1 $ 根 $ \\mathrm{ A } $ 型跳绳和 $ 1 $ 根 $ B $ 型跳绳各需多少元？</p><p>（2）若班级计划购买 $ A、B $ 两种型号的跳绳共 $ 55 $ 根，总费用不超过 $ 700 $ 元，则最多购买多少根 $ B $ 型跳绳？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南河南师大附中 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588078417880326144", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588078417880326144", "title": "2025年河南省新乡市河南师范大学附属中学中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589823907374342144", "questionArticle": "<p>10．《九章算术》是中国古代一部重要的数学著作，在“方程”章中记载了求不定方程（组）解的问题．例如方程 $ x+2y=3 $ 恰有一个正整数解 $ x=1, y=1 $ ．类似地，方程 $ 2x+3y=21 $ 的正整数解的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1　　　　B．2　　　　C．3　　　　D．4</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川泸州 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589823888013434880", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589823888013434880", "title": "2025年四川省泸州市中考数学真题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 31, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 31, "timestamp": "2025-07-01T02:04:27.176Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}