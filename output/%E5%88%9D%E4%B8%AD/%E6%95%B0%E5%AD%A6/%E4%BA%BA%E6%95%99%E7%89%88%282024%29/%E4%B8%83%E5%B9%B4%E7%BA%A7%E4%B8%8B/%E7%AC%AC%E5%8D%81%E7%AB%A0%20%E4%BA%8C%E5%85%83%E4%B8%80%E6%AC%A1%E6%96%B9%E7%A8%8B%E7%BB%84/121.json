{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 120, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "568252966739681280", "questionArticle": "<p>1．甲、乙两人从A,B两地同时出发,甲骑自行车,乙骑摩托车,沿同一条直线公路匀速相向行驶,出发3小时后两人相遇.已知在相遇时乙比甲多行驶了90千米,相遇后经1小时乙到达A地.</p><p>(1)问甲、乙行驶的速度分别是多少?</p><p>(2)甲、乙行驶多长时间时,两车相距30千米?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时1 列二元一次方程组解决积分和行程问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252966144090112", "questionArticle": "<p>2．小虎、大壮和明明三人玩飞镖游戏,各投5支镖,规定在同一环内得分相同,中靶和得分情况如图,则大壮的得分是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/18/2/1/0/0/0/568252941070540800/images/img_2.png\" style=\"vertical-align:middle;\" width=\"231\" alt=\"试题资源网 https://stzy.com\"></p><p>小虎19分　　大壮<u>　　</u>分　　明明21分&nbsp;</p><p>A．20B．22C．23D．25</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424|16438", "keyPointNames": "加减消元法解二元一次方程组|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时1 列二元一次方程组解决积分和行程问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568253162353631232", "questionArticle": "<p>3．某物流公司计划用两种车型运输物资,用2辆A型车和1辆B型车装满物资一次可运10吨;用1辆A型车和2辆B型车一次可运11吨.某物流公司现有31吨物资,计划同时租用A型车<i>a</i>辆,B型车<i>b</i>辆,一次运完,且恰好每辆车都装满.</p><p>(1)1辆A型车和1辆B型车都装满物资一次可分别运多少吨?</p><p>(2)请你帮该物流公司设计租车方案,并把符合要求的租车方案都列出来;</p><p>(3)若A型车每辆需租金每次100元,B型车每辆需租金每次120元,请从(2)中的方案里选出最省钱的租车方案,并求出最少租车费.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16435|16438", "keyPointNames": "分配问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时3 列二元一次方程组解决分段计费和方案问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252965548498944", "questionArticle": "<p>4．某型号动车由一节车头和若干节车厢组成,每节车厢的长度都相等.已知该型号动车挂8节车厢以38米<i>/</i>秒的速度通过某观测点用时6秒,挂12节车厢以41米<i>/</i>秒的速度通过该观测点用时8秒.</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/18/2/1/0/0/0/568252941066346496/images/img_1.png\" style=\"vertical-align:middle;\" width=\"189\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)车头及每节车厢的长度分别是多少米?</p><p>(2)小明乘坐该型号动车匀速通过某隧道时,如果车头进隧道5秒后他也进入了隧道,此时车内屏幕显示速度为180千米<i>/</i>时,请问他乘坐的是几号车厢?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424|16430", "keyPointNames": "加减消元法解二元一次方程组|行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时1 列二元一次方程组解决积分和行程问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253318641786880", "questionArticle": "<p>5．已知方程组 $ \\begin{cases}27x+63y=59,\\\\ 63x+27y=-13\\end{cases} $ 的解满足<i>x</i>-<i>y</i>=3<i>m</i>+1,则<i>m</i>的值为&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2　　　　B．−2　　　　C．1　　　　D．−1</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 一次方程与方程组 全章综合训练《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568252964428619776", "questionArticle": "<p>6．请欣赏下列描述中孙悟空追妖精的数学诗:悟空顺风探妖踪,千里只行四分钟.归时四分行六百,风速多少才称雄?</p><p>解释:孙悟空顺风去查妖精的行踪,4分钟就飞跃1 000里,逆风返回时4分钟飞跃600里,问风速是多少?(1里=500米)</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时1 列二元一次方程组解决积分和行程问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253161758040064", "questionArticle": "<p>7．为响应国家节能减排的号召,鼓励居民节约用电,各省市先后出台了“阶梯价格”制度,下表中是某市的电价标准.</p><p>(1)已知小明家5月份用电252千瓦时,缴纳电费158.4元,6月份用电340千瓦时,缴纳电费220元,请你根据以上数据,求出表格中<i>a</i>,<i>b</i>的值.</p><p>(2)小明家7月份缴纳电费285.5元,求小明家7月份的用电量.</p><table style=\"border: solid 1px;border-collapse: collapse; margin: auto;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 40pt;\"><p>阶梯</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 110pt;\"><p>用电量<i>x</i>(单位:千瓦时)</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>价格</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 40pt;\"><p>一档</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 110pt;\"><p>0&lt;<i>x</i>⩽180</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p><i>a</i>元<i>/</i>千瓦时</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 40pt;\"><p>二档</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 110pt;\"><p>180&lt;<i>x</i>⩽350</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p><i>b</i>元<i>/</i>千瓦时</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 40pt;\"><p>三档</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 110pt;\"><p><i>x</i>&gt;350</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>0． 9元<i>/</i>千瓦时</p></td></tr></table>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时3 列二元一次方程组解决分段计费和方案问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252963904331776", "questionArticle": "<p>8．全国足球甲A联赛共22轮,即每个队需参赛22场,胜一场得3分,平一场得1分,负一场得0分.若冠军队积43分,并知道他们胜的场数比负的场数多10场,试问该冠军队胜、平、负各多少场?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时1 列二元一次方程组解决积分和行程问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253159119822848", "questionArticle": "<p>9．本地某快递公司规定:寄件不超过1千克的部分按起步价计费;寄件超过1千克的部分按千克计费,小文分别寄快递到上海和北京,收费标准及实际收费如表:</p><p>收费标准:</p><table style=\"border: solid 1px;border-collapse: collapse; margin: auto;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 36.85pt;\"><p>目的地</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 56.7pt;\"><p>起步价(元)</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 163.05pt;\"><p>超过1千克的部分(元<i>/</i>千克)</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 36.85pt;\"><p>上海</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 56.7pt;\"><p>7</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 163.05pt;\"><p><i>b</i></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 36.85pt;\"><p>北京</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 56.7pt;\"><p>10</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 163.05pt;\"><p><i>b</i>+4</p></td></tr></table><p>实际收费:</p><table style=\"border: solid 1px;border-collapse: collapse; margin: auto;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.6pt;\"><p>目的地</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.6pt;\"><p>质量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 101.35pt;\"><p>费用(元)</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.6pt;\"><p>上海</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.6pt;\"><p>2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 101.35pt;\"><p><i>a</i>−6</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.6pt;\"><p>北京</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.6pt;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 101.35pt;\"><p><i>a</i>+7</p></td></tr></table><p>(1)求<i>a</i>,<i>b</i>的值.</p><p>(2)小文要寄5千克的东西到上海,7千克的东西到北京,需花多少运费?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时3 列二元一次方程组解决分段计费和方案问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253234411773952", "questionArticle": "<p>10．甲、乙、丙三人做100道数学题,每人都解出其中的60道题,将其中只有1人解出的题叫做难题,3人都解出的题叫做容易题,试问:难题多还是容易题多?(多的比少的)多几道题?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.5 三元一次方程组及其解法《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 121, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 121, "timestamp": "2025-07-01T02:15:07.441Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}