{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 17, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "591002478331600896", "questionArticle": "<p>1．解方程组： $ \\begin{cases} 4x-y=5① \\\\ 3x+2y=12② \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽阜阳 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591002458463182848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "591002458463182848", "title": "安徽省阜阳市第十八中学2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "591002473441042432", "questionArticle": "<p>2．某中学计划租用<i>x</i>辆汽车运送七年级<i>y</i>名学生到南安市中小学生社会实践基地进行社会实践活动，若全租用45座客车，则有35名学生没有座位；若全租用60座客车，则其中有一辆车只坐了35人，并且还空出一辆车．根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 45x-35=y \\\\ 60\\left ( { x-2 } \\right ) =y-35 \\end{cases}  $ B． $ \\begin{cases} 45x=y-35 \\\\ 60\\left ( { x-2 } \\right ) +35=y \\end{cases}  $ </p><p>C． $ \\begin{cases} 45x+35=y \\\\ 60\\left ( { x-1 } \\right ) +35=y \\end{cases}  $ D． $ \\begin{cases} 45x=y+35 \\\\ y-60\\left ( { x-2 } \\right ) =35 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽阜阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591002458463182848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "591002458463182848", "title": "安徽省阜阳市第十八中学2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "591002467468353536", "questionArticle": "<p>3．下列方程中，属于二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 2x{^{2}}-1=3 $ B． $ 2x-y=z $ </p><p>C． $ x+\\dfrac { 1 } { y }=0 $ D． $ 3x-y=1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽阜阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591002458463182848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "591002458463182848", "title": "安徽省阜阳市第十八中学2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589573242987257856", "questionArticle": "<p>4．我国古代数学名著《张邱建算经》中记载：“今有清酒一斗直粟十斗，醑酒一斗直粟三斗．今持粟三斛，得酒五斗，问清、醑酒各几何？”意思是：现在一斗清酒价值10斗谷子，一斗醑酒价值3斗谷子，现在拿30斗谷子，共换了5斗酒，问清酒、醑酒各几斗？如果设清酒<i>x</i>斗，醑酒<i>y</i>斗，那么可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=5 \\\\ 10x+3y=30 \\end{cases}  $ B． $ \\begin{cases} x+y=5 \\\\ 3x+10y=30 \\end{cases}  $ C． $ \\begin{cases} x+y=30 \\\\ \\dfrac { x } { 10 }+\\dfrac { y } { 3 }=5 \\end{cases}  $ D． $ \\begin{cases} x+y=30 \\\\ \\dfrac { x } { 3 }+\\dfrac { y } { 10 }=5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西大同 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 7, "referenceNum": 2, "createTime": "2025-06-23", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589573228693069824", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "589573228693069824", "title": "山西省大同六中集团校2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}, {"id": "586985620267900928", "title": "2025年湖北省丹江口市中考适应性训练数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "591001949840908288", "questionArticle": "<p>5．数学典籍《孙子算经》中记载了许多著名的数学问题，其中就有“韩信点兵”问题．相传汉朝大将韩信在点兵时，为了快速统计士兵人数，韩信让士兵们5人一组列阵，结果多出2人：他又让士兵们7人一组列阵，结果多出3人．已知士兵总数不超过100人，那么士兵总数可能为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>人．（写出一个符合条件的值即可）</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西光中学 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591001928659673088", "questionFeatureName": "开放性试题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "591001928659673088", "title": "2025年陕西省西安市新城区西光中学教育集团多校协作模拟预测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "591000794956079104", "questionArticle": "<p>6．某新能源汽车经销商购进紧凑和中级两种型号的新能源汽车，据了解3辆中级型汽车、2辆紧凑型汽车的进价共计104万元；2辆紧凑型汽车比3辆中级型汽车的进价少40万元．</p><p>（1）求中级型和紧凑型汽车两种型号汽车的进货单价；</p><p>（2）该店准备购进中级型和紧凑型汽车两种型号的新能源汽车100辆，已知中级型汽车的售价为27万元/辆，紧凑型汽车的售价为20万元/辆．根据销售经验，购中级型汽车的数量不低于25辆，设购进 $ a $ 辆中级型汽车，100辆车全部售完获利 $ W $ 万元，该经销商应购进中级型和紧凑型汽车各多少辆．才能使 $ W $ 最大？ $ W $ 最大为多少万元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南长沙青竹湖湘一外国语学校 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16438|16535", "keyPointNames": "和差倍分问题|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591000768204808192", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "591000768204808192", "title": "2025年湖南省长沙市开福区青竹湖湘一外国语学校中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590998409529565184", "questionArticle": "<p>7．我们规定： $ \\left [ m \\right ]  $ 表示不超过 $ m $ 的最大整数，例如： $ \\left [ 3.1 \\right ] =3 $ ， $ \\left [ 0 \\right ] =0 $ ， $ \\left [ -3.1 \\right ] =-4 $ ，则关于 $ x $ 和 $ y $ 的二元一次方程组 $ \\begin{cases} \\left [ x \\right ] +y=3.2 \\\\ x-\\left [ y \\right ] =\\left [ 3.2 \\right ]  \\end{cases}  $ 的解为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=3 \\\\ y=0.2 \\end{cases}  $ B． $ \\begin{cases} x=2 \\\\ y=1.2 \\end{cases}  $ C． $ \\begin{cases} x=3.3 \\\\ y=0.2 \\end{cases}  $ D． $ \\begin{cases} x=3.4 \\\\ y=0.2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建泉州七中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-06-23", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998393041756160", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590998393041756160", "title": "福建省泉州市第七中学2024−2025学年七年级下学期第二次月考数学试题", "paperCategory": 1}, {"id": "267637040505724928", "title": "重庆市西南大学附属中学校2022-2023学年七年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "591001248700080128", "questionArticle": "<p>8．在长为 $ 1 $ 米的书架上按图示方式摆放数学书和语文书，已知每本数学书厚 $ 0.8 $ 厘米，每本语文书厚 $ 1.2 $ 厘米．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/19/2/1/0/0/0/591001200268455958/images/img_22.png\" style=\"vertical-align:middle;\" width=\"186\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）若数学书和语文书共 $ 100 $ 本恰好摆满该书架，问数学书和语文书各有多少本？</p><p>（2）若书架上已摆放了 $ 60 $ 本数学书，那么最多还可以摆多少本语文书？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁本溪 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16441|16486", "keyPointNames": "其他问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591001225711099904", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "591001225711099904", "title": "2025年辽宁省本溪市第十二中学教育集团中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "591000585115054080", "questionArticle": "<p>9．在数学知识竞赛中，为奖励成绩突出的学生，班级计划用100元钱购买甲，乙，丙三种奖品，三种奖品都要购买，甲种奖品每个5元，乙种奖品每个10元，丙种奖品每个15元，在丙种奖品不超过两个且钱全部用完的情况下，购买方案有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．12种B．15种C．16种D．14种</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江齐齐哈尔 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591000568417529856", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "591000568417529856", "title": "2025年黑龙江省齐齐哈尔市部分学校中考四模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "591000203697627136", "questionArticle": "<p>10．《九章算术》是中国古代第一部数学专著，是当时世界上最简练有效的应用数学，它的出现标志中国古代数学形成了完整的体系．在卷八方程篇中，记录了如图的数学问题：“今有五雀、六燕，集称之衡，雀俱重，燕俱轻．一雀一燕交而处，衡适平．并燕雀重一斤．问燕雀一枚各重几何？”其大意是：“现在有5只雀，6只燕，分别集中放在天平上称重，聚在一起的雀重燕轻．将一只雀一只燕交换位置而放，重量相等．5只雀、6只燕重量共一斤，问雀和燕各重多少？”古代记1斤为16两，则设1只雀<i>x</i>两，一只燕<i>y</i>两，则下列正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/19/2/1/0/0/0/591000136001564679/images/img_7.jpg\" style=\"vertical-align:middle;\" width=\"133\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ 4x+y=5y+x $ B． $ 5x+6y=1 $ C．雀重一两D．燕重一两</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北邯郸 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591000178028486656", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "591000178028486656", "title": "2025年河北省邯郸市部分学校中考模拟数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 18, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 18, "timestamp": "2025-07-01T02:02:56.894Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}