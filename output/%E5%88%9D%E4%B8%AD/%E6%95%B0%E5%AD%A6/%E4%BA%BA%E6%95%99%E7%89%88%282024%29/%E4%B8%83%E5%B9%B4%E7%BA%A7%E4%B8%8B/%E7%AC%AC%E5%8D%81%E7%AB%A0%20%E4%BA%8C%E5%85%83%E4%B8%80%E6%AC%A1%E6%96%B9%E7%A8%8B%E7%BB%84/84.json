{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 83, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "577684026816765952", "questionArticle": "<p>1．中国传统数学重要著作《九章算术》中记载：“今有甲乙二人持钱不知其数．甲得乙半而钱五十，乙得甲太半而钱亦五十．问甲、乙持钱各几何？”译为“假设有甲乙二人，不知其钱包里有多少钱．若乙把自己一半的钱给甲，则甲的钱数为50；而甲把自己 $ \\dfrac { 2 } { 3 } $ 的钱给乙，则乙的钱数也能为50．问甲、乙各有多少钱？”设甲的钱数为<i>x</i>，乙的钱数为<i>y</i>，根据题意，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=50 \\\\ y+\\dfrac { 2 } { 3 }x=50 \\end{cases}  $　　　　B． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=50 \\\\ x+\\dfrac { 2 } { 3 }y=50 \\end{cases}  $　　　　C． $ \\begin{cases} x-\\dfrac { 1 } { 2 }y=50 \\\\ y-\\dfrac { 2 } { 3 }x=50 \\end{cases}  $　　　　D． $ \\begin{cases} x-\\dfrac { 1 } { 2 }y=50 \\\\ x-\\dfrac { 2 } { 3 }y=50 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市杨家坪中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577684010760970240", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "577684010760970240", "title": "重庆市杨家坪中学教育集团2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577684025126461440", "questionArticle": "<p>2．对有理数<i>x</i>，<i>y</i>定义一种新运算“ $ * $ ” $ :x*y=ax+by-1 $ ，其中<i>a</i>，<i>b</i>为常数，等式右边是通常的加法和乘法运算，已知 $ 3*2=21 $ ， $ 3*(-2)=4 $ ，那么 $ a+b $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\dfrac { 35 } { 4 } $　　　　B． $ -3 $　　　　C． $ \\dfrac { 9 } { 2 } $　　　　D． $ \\dfrac { 17 } { 4 } $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市杨家坪中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577684010760970240", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "577684010760970240", "title": "重庆市杨家坪中学教育集团2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577683888308264960", "questionArticle": "<p>3．如图所示，8个相同的长方形地砖拼成一个大长方形，则每块小长方形地砖的面积是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>cm<sup>2</sup>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/14/2/1/0/0/0/577683846923071494/images/img_8.png\" style=\"vertical-align:middle;\" width=\"180\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683866338500608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577683866338500608", "title": "重庆市万州第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577683882822115328", "questionArticle": "<p>4．如果方程组 $ \\begin{cases} ax-by=13 \\\\ 4x-5y=41 \\end{cases}  $ 与 $ \\begin{cases} ax+by=3 \\\\ 2x+3y=-7 \\end{cases}  $ 有相同的解，则<i>a</i>，<i>b</i>的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} a=2 \\\\ b=1 \\end{cases}  $ B． $ \\begin{cases} a=2 \\\\ b=-3 \\end{cases}  $ C． $ \\begin{cases} a=\\dfrac { 5 } { 2 } \\\\ b=1 \\end{cases}  $ D． $ \\begin{cases} a=4 \\\\ b=-5 \\end{cases}  $  </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683866338500608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577683866338500608", "title": "重庆市万州第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577683878611034112", "questionArticle": "<p>5．下列图组数值是二元一次方程2<i>x</i>﹣<i>y</i>＝6的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=1 \\\\ y=4 \\end{cases}  $ B． $ \\begin{cases} x=4 \\\\ y=2 \\end{cases}  $ C． $ \\begin{cases} x=2 \\\\ y=4 \\end{cases}  $ D． $ \\begin{cases} x=2 \\\\ y=3 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683866338500608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577683866338500608", "title": "重庆市万州第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577683790706814976", "questionArticle": "<p>6．阅读下列一段材料，运用相关知识解决问题．</p><p>换元法是数学中一个非常重要而且应用十分广泛的解题方法，我们通常把未知数或变数称为元，所谓换元法，就是解数学题时，把某个式子看成一个整体，用一个变量去代替它，从而使得复杂问题简单化．换元的实质是转化，关键是构造元和设元．</p><p>例如解方程组 $ \\begin{cases} \\dfrac { 1 } { x }+\\dfrac { 1 } { y }=12 \\\\ \\dfrac { 2 } { x }+\\dfrac { 1 } { y }=20 \\end{cases}  $ ，设 $ {\\rm \\mathit{m}} =\\dfrac { 1 } { x } {\\rm ，\\mathit{n}} $  $ =\\dfrac { 1 } { y } $ ，则原方程组可化为 $ \\begin{cases} m+n=12 \\\\ 2m+n=20 \\end{cases}  $ ，解化简之后的方程组得 $ \\begin{cases} m=8 \\\\ n=4 \\end{cases}  $ ，即 $ \\begin{cases} \\dfrac { 1 } { x }=8 \\\\ \\dfrac { 1 } { y }=4 \\end{cases}  $ ，所以原方程组的解为 $ \\begin{cases} x=\\dfrac { 1 } { 8 } \\\\ y=\\dfrac { 1 } { 4 } \\end{cases}  $ ．</p><p>运用以上知识解决下列问题：</p><p>（1）求方程组 $ \\begin{cases} \\dfrac { 1 } { x }+\\dfrac { 2 } { y }=2 \\\\ \\dfrac { 3 } { x }+\\dfrac { 2 } { y }=4 \\end{cases}  $ 的解．</p><p>（2）关于<i>x</i>，<i>y</i>二元一次方程组 $ \\begin{cases} 3x+5y=11 \\\\ ax+11y=12 \\end{cases}  $ 的解为 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ ，则方程组 $ \\begin{cases} 3\\left ( { x-2 } \\right ) +5\\left ( { y+1 } \\right ) =11 \\\\ a\\left ( { x-2 } \\right ) +11\\left ( { y+1 } \\right ) =12 \\end{cases}  $ 的解为_．</p><p>（3）举一反三：方程组 $ \\begin{cases} 3\\cdot 2{^{x+2}}-3{^{y+1}}=111 \\\\ 2{^{x+1}}+2\\cdot 3{^{y}}=86 \\end{cases}  $ 的解为_．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025浙江台州市书生中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683765515825152", "questionFeatureName": "阅读材料题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577683765515825152", "title": "浙江省台州市书生中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577683785732370432", "questionArticle": "<p>7．解方程组：</p><p>（1） $ \\begin{cases} y=2x \\\\ 2x+3y=8 \\end{cases}  $ </p><p>（2） $ \\begin{cases} 2x-3y=1 \\\\ 5x-3y=7 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江台州市书生中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683765515825152", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577683765515825152", "title": "浙江省台州市书生中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577683781366099968", "questionArticle": "<p>8．已知 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ 是方程 $ kx-y=3 $ 的解，则<i>k</i>的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江台州市书生中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683765515825152", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577683765515825152", "title": "浙江省台州市书生中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577683778333618176", "questionArticle": "<p>9．七年级某班学生组织去研学旅行，男生戴蓝色帽，女生戴红色帽，每位男生看到蓝色和红色帽一样多，每位女生看到蓝色帽是红色的两倍，若假设男女生人数分别是 $ x、y $ ，则可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=y \\\\ x=2y \\end{cases}  $ B． $ \\begin{cases} x-1=y \\\\ x=2(y-1) \\end{cases}  $ C． $ \\begin{cases} x=y \\\\ x=2(y-1) \\end{cases}  $ D． $ \\begin{cases} x-1=y \\\\ x=2y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江台州市书生中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683765515825152", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577683765515825152", "title": "浙江省台州市书生中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577683773321424896", "questionArticle": "<p>10．下列各式是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 3y+\\dfrac { 1 } { 2 }x $ B． $ \\dfrac { x+y } { 3 }-2y=0 $ C． $ y=\\dfrac { 2 } { x }+1 $ D． $ x{^{2}}+y=0 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025浙江台州市书生中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683765515825152", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577683765515825152", "title": "浙江省台州市书生中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 84, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 84, "timestamp": "2025-07-01T02:10:44.181Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}