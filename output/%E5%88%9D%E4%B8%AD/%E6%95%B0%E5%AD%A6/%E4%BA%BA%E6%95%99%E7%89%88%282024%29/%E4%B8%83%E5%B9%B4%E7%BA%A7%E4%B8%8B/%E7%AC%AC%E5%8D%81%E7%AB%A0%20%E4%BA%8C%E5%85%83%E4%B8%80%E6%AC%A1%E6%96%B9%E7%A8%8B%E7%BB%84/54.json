{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 53, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "581951231003762688", "questionArticle": "<p>1．下列各方程中，是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．  $ \\dfrac { 1 } { 3 }x+y=2 $　　　　B． $ \\dfrac { x } { 3 }-\\dfrac { 2 } { y }=y+5x $　　　　C． $ x{^{2}}=y{^{2}}+1 $　　　　D． $ 3x+1=2xy $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581951220270538752", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "581951220270538752", "title": "重庆市万州中学教育集团2024—2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580620711527489536", "questionArticle": "<p>2．二次函数 $ y=ax{^{2}}+bx+c\\left ( { a\\ne 0 } \\right )  $ 的图象经过 $ A\\left ( { 0,m } \\right ) ,B\\left ( { 1,-m } \\right ) ,C\\left ( { 2,n } \\right ) ,D\\left ( { 3,-m } \\right )  $ ，其中<i>m</i>、<i>n</i>为常数，则 $ \\dfrac { n } { m } $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\dfrac { 3 } { 5 } $ B． $ -\\dfrac { 3 } { 5 } $ C． $ \\dfrac { 5 } { 3 } $ D． $ -\\dfrac { 5 } { 3 } $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西实验中学 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16443|16553", "keyPointNames": "解三元一次方程组|y=ax²+bx+c的图象与性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580620693970132992", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580620693970132992", "title": "2025年陕西省咸阳市实验中学中考第二次模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580621113350205440", "questionArticle": "<p>3．每年的4月23日是世界读书日，某校为响应“全民阅读”的号召，计划购入<i>A</i>，<i>B</i>两种规格的书柜用于放置图书．经市场调查发现：若购买<i>A</i>种书柜3个，<i>B</i>种书柜4个，共需资金1700元；若购买<i>A</i>种书柜4个，<i>B</i>种书柜3个，共需资金1800元．</p><p>（1）<i>A</i>，<i>B</i>两种规格书柜的单价分别是多少元？</p><p>（2）若该校准备用2000元购买两种书柜（要求既有购买<i>A</i>种书柜，又有购买<i>B</i>种书柜，且资金2000元须全部用完），请求出所有可能的购买方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京北京八十中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16420|16438", "keyPointNames": "二元一次方程的解|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580621084388536320", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580621084388536320", "title": "北京市第八十中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580621107079720960", "questionArticle": "<p>4．《九章算术》中有这样一个题：“今有醇酒一斗，直钱五十；行酒一斗，直钱一十．今将钱三十，得酒二斗．问醇、行酒各得几何？”其译文是：今有醇酒（优质酒）1斗，价值50钱；行酒（劣质酒）1斗，价值10钱．现有30钱，买得2斗酒．问醇酒、行酒各能买得多少？设醇酒为 $ x $ 斗，行酒为 $ y $ 斗，则可列二元一次方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京八十中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580621084388536320", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580621084388536320", "title": "北京市第八十中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580621105368444928", "questionArticle": "<p>5．若 $ \\begin{cases} x=a \\\\ y=b \\end{cases}  $ 是方程 $ x-2y=7 $ 的一个解，则代数式 $ a-2b-1 $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京八十中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580621084388536320", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580621084388536320", "title": "北京市第八十中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580621109197844480", "questionArticle": "<p>6．解方程组： $ \\begin{cases} 3x-4y=14 \\\\ 2x+3y=-2 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京八十中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580621084388536320", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580621084388536320", "title": "北京市第八十中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580620981661638656", "questionArticle": "<p>7．若关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} 2x+y=3 \\\\ x+2y=m \\end{cases}  $ 的解满足 $ x+y=2 $ ，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京八一学校 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580620953886957568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580620953886957568", "title": "北京市八一学校2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580621334356467712", "questionArticle": "<p>8．我们在学习二次根式的时候会发现：有时候两个含有二次根式的代数式相乘，积不含有二次根式，如 $ \\sqrt { a }\\centerdot \\sqrt { a }=a $ ， $ \\left ( { \\sqrt { 5 }+\\sqrt { 2 } } \\right ) \\left ( { \\sqrt { 5 }-\\sqrt { 2 } } \\right ) =(\\sqrt { 5 }){^{2}}-(\\sqrt { 2 }){^{2}}=3 $ ．</p><p>两个含有二次根式的非零代数式相乘，如果它们的积不是二次根式，那么我们称这两个代数式互为有理化因式．</p><p>请运用有理化因式的知识，解决下列问题：</p><p>（1）化简： $ \\dfrac { 1 } { \\sqrt { 17 }-4 }= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）比较大小： $ \\sqrt { 2024 }-\\sqrt { 2023 } $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ \\sqrt { 2025 }-\\sqrt { 2024 } $ ；（用“＞”、“=”或“＜”填空）</p><p>（3）设有理数 $ a $ 、 $ b $ 满足： $ \\dfrac { a } { \\sqrt { 2 }+1 }+\\dfrac { b } { \\sqrt { 2 }-1 }=3\\sqrt { 2 }-1 $ ，则 $ a+b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（4）已知 $ \\sqrt { 12-x }-\\sqrt { 4-x }=2 $ ，求 $ \\sqrt { 12-x }+\\sqrt { 4-x } $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京四中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16331|16424|30375", "keyPointNames": "平方差公式|加减消元法解二元一次方程组|分母有理化", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580621298847490048", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580621298847490048", "title": "北京市第四中学2024−2025学年八年级数学下学期期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580620984769617920", "questionArticle": "<p>9．解方程组： $ \\begin{cases} 2x+3y=1① \\\\ x-2y=4② \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京八一学校 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580620953886957568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580620953886957568", "title": "北京市八一学校2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580620966717333504", "questionArticle": "<p>10．若 $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ 是关于 $ x $ ， $ y $ 的二元一次方程 $ mx+y=3 $ 的解，则 $ m $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B． $ -1 $ C． $ -2 $ D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京八一学校 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580620953886957568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580620953886957568", "title": "北京市八一学校2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 54, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 54, "timestamp": "2025-07-01T02:07:11.594Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}