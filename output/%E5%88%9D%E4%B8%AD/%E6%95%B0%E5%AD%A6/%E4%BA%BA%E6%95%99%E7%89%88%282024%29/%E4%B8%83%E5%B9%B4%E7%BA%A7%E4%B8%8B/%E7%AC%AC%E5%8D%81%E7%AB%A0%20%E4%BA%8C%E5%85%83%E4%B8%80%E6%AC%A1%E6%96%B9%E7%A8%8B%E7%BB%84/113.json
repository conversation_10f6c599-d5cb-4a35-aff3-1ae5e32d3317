{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 112, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "569703623187275776", "questionArticle": "<p>1．在解方程组 $ \\begin{cases} ax+5y=15 \\\\ 4x-by=-2 \\end{cases}  $ ，甲看错了方程组中的 $ a $ ，得到的解为 $ \\begin{cases} x=-3 \\\\ y=-1 \\end{cases}  $ ，乙看错了方程组中的 $ b $ ，得到的解是 $ \\begin{cases} x=5 \\\\ y=4 \\end{cases}  $ ．</p><p>(1)求原方程组中 $ a $ 、 $ b $ 的值各是多少？</p><p>(2)求出原方程组中的正确解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆朝高 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 2, "createTime": "2025-04-25", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579473654632722432", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579473654632722432", "title": "重庆朝阳中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}, {"id": "569703598805786624", "title": "河北省石家庄市第十七中学2024−2025学年七年级下学期4月考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569703619890552832", "questionArticle": "<p>2．把一根长为 $ { { 1 } }{ { 6 } }{ \\rm{ m } } $ 的钢管截成 $ { { 2 } }{ \\rm{ m } } $ 长或 $ { { 3 } }{ \\rm{ m } } $ 长的两种规格．在不造成浪费的情况下，不同的截法有<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>种．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-25", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703598805786624", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "569703598805786624", "title": "河北省石家庄市第十七中学2024−2025学年七年级下学期4月考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569703827638624256", "questionArticle": "<p>3．某电器商场销售<i>A</i>，<i>B</i>两种型号计算器，两种计算器的进货价格分别为每台30元，40元，商场销售5台<i>A</i>型号和1台<i>B</i>型号计算器，可获利润76元；销售6台<i>A</i>型号和3台<i>B</i>型号计算器，可获利120元．</p><p>（1）求商场销售<i>A</i>，<i>B</i>两种型号计算器的销售价格分别是多少元？（利润=销售价格﹣进货价格）</p><p>（2）商场准备用不多于2500元的资金购进<i>A</i>，<i>B</i>两种型号计算器共70台，问最少需要购进<i>A</i>型号的计算器多少台？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈尔滨 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-25", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703800295956480", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "569703800295956480", "title": "黑龙江省哈尔滨市萧红中学2024−2025学年九年级下学期中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569703612386942976", "questionArticle": "<p>4．小丽在用“加减消元法”解二元一次方程组 $ \\begin{cases} 5x-2y=4① \\\\ 2x+3y=9② \\end{cases}  $ 时，利用 $ ①\\times a+②\\times b $ 消去 $ x $ ，则 $ a $ 、 $ b $ 的值可能是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ a=2 $ ， $ b=5 $ B． $ a=3 $ ， $ b=2 $ C． $ a=-3 $ ， $ b=2 $ D． $ a=2 $ ， $ b=-5 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-25", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703598805786624", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "569703598805786624", "title": "河北省石家庄市第十七中学2024−2025学年七年级下学期4月考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "569704059168399360", "questionArticle": "<p>5．为了进一步抓好“三农”工作，助力乡村振兴，某经销商计划从建档贫困户家购进<i>A</i>，<i>B</i>两种农产品．已知购进<i>A</i>种农产品3件，<i>B</i>种农产品2件，共需660元；购进<i>A</i>种农产品4件，<i>B</i>种农产品1件，共需630元．</p><p>(1)<i>A</i>，<i>B</i>两种农产品每件的价格分别是多少元？</p><p>(2)该经销商计划用不超过5400元购进<i>A</i>，<i>B</i>两种农产品共40件，且<i>A</i>种农产品的件数不超过<i>B</i>种农产品件数的3倍．如果该经销商将购进的农产品按照<i>A</i>种每件160元，<i>B</i>种每件200元的价格全部售出，那么购进<i>A</i>，<i>B</i>两种农产品各多少件时获利最多？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南长沙 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-25", "keyPointIds": "16438|16490|16544", "keyPointNames": "和差倍分问题|一元一次不等式组的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569704036246528000", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569704036246528000", "title": "湖南省长沙市宁乡市2025年九年级第一次中考模拟数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569704429538025472", "questionArticle": "<p>6．今年的“三八节”商战火爆，各大商家积极促销．某社区准备采购文化墙贴和小书柜来更新社区设施，发现购买5张文化墙贴和4个小书柜共需1450元；若购买6张文化墙贴和3个小书柜共需1200元．求采购1张文化墙贴和1个小书柜各需要多少钱．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林四平 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-25", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569704412387516416", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "569704412387516416", "title": "吉林省四平市2024~2025学年 下学期九年级第一次月考试卷 数学", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569703539548659712", "questionArticle": "<p>7．解方程组：</p><p>(1) $ \\begin{cases} 2x-y=2 \\\\ 3x+2y=-11 \\end{cases}  $ </p><p>(2) $ \\begin{cases} x+3y=8 \\\\ 5x-4y=2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-25", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703514873569280", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "569703514873569280", "title": "河北省 石家庄市第六中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569703536855916544", "questionArticle": "<p>8．已知4辆小卡车和5辆大卡车一次能运货52吨，10辆小卡车和3辆大卡车一次能运货54吨．设每辆小卡车每次运货<i>x</i>吨，每辆大卡车每次运货<i>y</i>吨，列二元一次方程组是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-25", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703514873569280", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "569703514873569280", "title": "河北省 石家庄市第六中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569703535585042432", "questionArticle": "<p>9．已知 $ \\begin{cases} x=0 \\\\ y=2 \\end{cases}  $ 和 $ \\begin{cases} x=4 \\\\ y=1 \\end{cases}  $ 都是方程 $ mx+ny=8 $ 的解，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ n= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-25", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703514873569280", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "569703514873569280", "title": "河北省 石家庄市第六中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569703529570410496", "questionArticle": "<p>10．利用加减消元法解方程组 $ \\begin{cases} 2x+5y=-10① \\\\ 5x-3y=6② \\end{cases}  $ ，下列做法正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．要消去<i>y</i>，可以将 $ ①\\times 5+②\\times 2 $ B．要消去<i>x</i>，可以将 $ ①\\times 3+②\\times \\left ( { -5 } \\right )  $ </p><p>C．要消去<i>y</i>，可以将 $ ①\\times 5+②\\times 3 $ D．要消去<i>x</i>，可以将 $ ①\\times \\left ( { -5 } \\right ) +②\\times 2 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-25", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703514873569280", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "569703514873569280", "title": "河北省 石家庄市第六中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 113, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 113, "timestamp": "2025-07-01T02:14:08.863Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}