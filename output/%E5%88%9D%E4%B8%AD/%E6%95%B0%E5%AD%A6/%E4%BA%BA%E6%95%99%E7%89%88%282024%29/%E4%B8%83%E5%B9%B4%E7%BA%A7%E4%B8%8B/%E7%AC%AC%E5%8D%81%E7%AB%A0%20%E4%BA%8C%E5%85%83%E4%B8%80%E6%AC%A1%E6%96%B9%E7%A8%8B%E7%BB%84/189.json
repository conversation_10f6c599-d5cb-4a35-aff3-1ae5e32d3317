{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 188, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "547594736598556672", "questionArticle": "<p>1．方程 $ 2x-y=5 $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=3 \\\\ y=1 \\end{cases}  $ B． $ \\begin{cases} x=-2 \\\\ y=1 \\end{cases}  $ C． $ \\begin{cases} x=1 \\\\ y=3 \\end{cases}  $ D． $ \\begin{cases} x=0 \\\\ y=5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024吉林长春 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-25", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "547594726871965696", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "547594726871965696", "title": "吉林省长春市新解放学校初中部2023−2024学年下学期七年级大班期初测试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "547594735805833216", "questionArticle": "<p>2．下列方程组中，是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=6 \\\\ y+z=2 \\end{cases}  $ B． $ \\begin{cases} 2x-y=1 \\\\ y=0 \\end{cases}  $ </p><p>C． $ \\begin{cases} x{^{2}}-y{^{2}}=1 \\\\ x+y=3 \\end{cases}  $ D． $ \\begin{cases} x+\\dfrac { 1 } { y }=10 \\\\ x-2y=5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024吉林长春 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-25", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "547594726871965696", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "547594726871965696", "title": "吉林省长春市新解放学校初中部2023−2024学年下学期七年级大班期初测试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "549205672157552640", "questionArticle": "<p>3．端午节是中国传统节日，人们有吃粽子的习俗．某商店售卖某品牌瘦肉粽和五花肉粽．请依据以下对话，求促销活动前每个瘦肉粽、五花肉粽的售价．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/24/2/1/0/0/0/549205631237922826/images/img_12.png\" style=\"vertical-align:middle;\" width=\"396\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏淮安 · 模拟", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-25", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549205652519821312", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "549205652519821312", "title": "2025年江苏省淮安市中考数学模拟测试题", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "549236414208057344", "questionArticle": "<p>4．若二元一次方程组 $ \\begin{cases}9x+4y=-1，\\\\ x+6y=11\\end{cases} $ 的解满足2<i>x</i>+<i>ky</i>＝﹣1，则<i>k</i>＝<u>　</u><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u><u>　</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏宿迁 · 模拟", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-02-24", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549236397065936896", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "549236397065936896", "title": "2025年江苏省宿迁市中考数学模拟测试题", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "549096331412807680", "questionArticle": "<p>5．方程组 $ \\begin{cases} x+y=3 \\\\ 2x-y=6 \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京市育才学校 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-24", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549096319207383040", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549096319207383040", "title": "北京市育才学校2024−2025学年九年级下学期开学数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "546878102191775744", "questionArticle": "<p>6．某文具店购进了一批<i>A</i>，<i>B</i>两种型号的笔袋，16个<i>A</i>型笔袋8个 $ B $ 型笔袋需要花费640元，10个<i>A</i>型笔袋20个 $ B $ 型笔袋需要花费700元．</p><p>(1)求 $ A, B $ 两种型号的笔袋进价各是多少元？</p><p>(2)在销售过程中，为了尽可能多的减少 $ A $ 型笔袋的库存，文具店老板决定对<i>A</i>型笔袋进行降价销售，当销售单价为40元时，每天可以售出20个，每降价1元，每天将多售出5个，问将每个<i>A</i>型笔袋降价多少元时，每天售出 $ A $ 型笔袋的利润为240元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆合川 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-23", "keyPointIds": "16437|16463", "keyPointNames": "销售利润问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "546878094147100672", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "546878094147100672", "title": "重庆市合川区2024−2025学年九年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "546878548381835264", "questionArticle": "<p>7．2024年暑期，因连续高温和干旱，我区一居民小区的部分绿化树枯死，小区物业管理公司决定补种绿化树，计划找苗圃公司购买桂花树和香樟树共150棵进行补栽，其中桂花树每棵20元，香樟树每棵30元，一共需要4000元．</p><p>(1)计划购买桂花树、香樟树各多少棵？</p><p>(2)实际购买时，物业管理公司与苗圃公司协商，给与一定优惠：每棵挂花树的价格降低2元；如果每棵香樟树的价格每降低1元（香樟树的价格不能低于桂花树的价格），则物业管理公司要多购买10棵香樟树，最后，物业管理公司购买的桂花树数量不变，香樟树的棵树增加了，实际付款金额比计划多260元．求物业管理公司实际购买香樟树多少棵？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆万州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-23", "keyPointIds": "16437|16463", "keyPointNames": "销售利润问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "546878536629395456", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "546878536629395456", "title": "重庆市万州区2024—2025学年上学期九年级期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "547220252884508672", "questionArticle": "<p>8．如图，赣南脐橙是赣州的一大特色，它被称为“橙中之王”，还荣获“中华名果”称号．现有一脐橙开发商，他从果园购进一斤小脐橙需要2元，一斤大脐橙需3元，现他将大脐橙、小脐橙分别以<i>m</i>元，<i>n</i>元售出，设一天的盈利额为<i>y</i>元，且一天能卖出<i>x</i>斤大脐橙，能卖出 $ \\left ( { 2x+1 } \\right )  $ 斤小脐橙．若购买3斤大脐橙和4斤小脐橙需要34元，购买2斤大脐橙和5斤小脐橙需要32元．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/19/2/1/0/0/0/547220177240236036/images/img_24.jpg\" style=\"vertical-align:middle;\" width=\"163\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)求<i>m</i>，<i>n</i>的值．</p><p>(2)求<i>y</i>与<i>x</i>的函数表达式，并求出当 $ x=20 $ 时当天的盈利额．</p><p>(3)该开发商为博得顾客眼球，推出了如下的活动：他选出1个大棕橙子，2个大黄橙子，3个小黄橙子放入盒子中，让顾客随机摸取两个（不放回），摸到大棕橙子6折，摸到一个大黄橙子8折，一个小黄橙子免1元（至少购买5斤脐橙才能参与活动）．</p><p> $ ① $ 若一名顾客买了5斤大脐橙，3斤小脐橙，若他参与该活动，用树状图法求该顾客支付32.6元的概率（考生<i>A</i>作答）．</p><p> $ ② $ 若一名顾客购买了6斤脐橙（小脐橙购买斤数多于大脐橙且数量为整数），若该名顾客参与活动，请直接写出所有他可能支付的钱数（考生<i>B</i>作答）．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025江西上饶 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-23", "keyPointIds": "16437|16544|16904", "keyPointNames": "销售利润问题|最大利润问题|列表法或树状图法求概率", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "547220242621046784", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "547220242621046784", "title": "江西省上饶市部分学校2024——2025学年上学期八年级1月份期末联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "547962148229521408", "questionArticle": "<p>9．（列方程解应用题）兔年迎春到，正值冬去春来的时节，某商场用10600元分别以每件100元和80元的价格购进衬衫和长袖<i>T</i>恤共120件．</p><p>(1)商场购进衬衫和长袖<i>T</i>恤各多少件？</p><p>(2)1月底，该商场以每件180元的价格销售了衬衫进货量的 $ 30\\% $ ，长袖<i>T</i>恤在进价的基础上提价 $ 50\\% $ 销售，因款式火爆开售当天一抢而空．2月初，正值迎春大酬宾，商场发现有5件衬衫因损坏无法销售，于是免费赠送给员工，该商场准备将剩下的衬衫在原售价的基础上降价销售，要使商场销售完这批购进的衬衫和长袖<i>T</i>恤正好达到盈利 $ 35\\% $ 的预期目标，每件衬衫降价后的售价应是多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024重庆重庆十一中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-22", "keyPointIds": "16406|16441", "keyPointNames": "销售盈亏问题|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "547962123483127808", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "547962123483127808", "title": "重庆市第十一中学校2023−2024学年下学期七年级开学测试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "547963248571621376", "questionArticle": "<p>10．在襄阳市创建“经济品牌特色品牌”政策的影响下．每到傍晚，市内某网红烧烤店就食客如云，这家烧烤店的海鲜串和肉串非常畅销，店主从食品加工厂批发以上两种产品进行加工销售，其中海鲜串的成本为<i>m</i>元/支，肉串的成本为<i>n</i>元/支,两次购进并加工海鲜串和肉串的数量与成本如下表所示（成本包括进价和其他费用）,</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">次数</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">数量（支）</p></td><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">总成本（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">海鲜串</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">肉串</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">第一次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">3000</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">4000</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">17000</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">第二次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">4000</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">3000</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">18000</p></td></tr></table><p>针对团队消费，店主决定每次消费海鲜串不超过200支时，每支售价5元；超过200支时，不超过200支的部分按原价，超过200支的部分打八折．每支肉串的售价为3.5元．</p><p>(1)求<i>m</i>，<i>n</i>的值；</p><p>(2)五一当天，一个旅游团去此店吃烧烤，一次性消费海鲜串和肉串共1000支，且海鲜串不超过400支．在本次消费中，设该旅游团消费海鲜串<i>x</i>支，店主获得海鲜串的总利润为<i>y</i>元，求<i>y</i>与<i>x</i>的函数关系式，并写出自变量<i>x</i>的取值范围；</p><p>(3)在（2）的条件下，该旅游团消费的海鲜串超过了200支，店主决定给该旅游团更多优惠，对每支肉串降价<i>a</i>（ $ 0  <  a  <  1 $ ）元，但要确保本次消费获得肉串的总利润始终不低于海鲜串的总利润，求<i>a</i>的最大值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东江门市第二中学 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-22", "keyPointIds": "16437|16536|16544", "keyPointNames": "销售利润问题|列一次函数解析式|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "547963224320155648", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "547963224320155648", "title": "广东省江门市第二中学2024−2025学年九年级下学期开学考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 189, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 189, "timestamp": "2025-07-01T02:23:12.379Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}