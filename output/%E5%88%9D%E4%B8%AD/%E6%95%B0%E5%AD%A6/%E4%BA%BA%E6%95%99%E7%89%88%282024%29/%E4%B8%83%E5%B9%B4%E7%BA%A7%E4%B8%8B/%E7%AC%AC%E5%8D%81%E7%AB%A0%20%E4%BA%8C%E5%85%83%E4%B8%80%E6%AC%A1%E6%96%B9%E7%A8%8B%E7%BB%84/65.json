{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 64, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "580246050838978560", "questionArticle": "<p>1．解下列方程（组）</p><p>（1） $ 4x+3=2\\left ( { x-1 } \\right ) +1 $ ；</p><p>（2） $ \\begin{cases} x+y=7① \\\\ 3x+y=17② \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川宜宾 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-22", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580246025236946944", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580246025236946944", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580246167549681664", "questionArticle": "<p>2．解方程组：</p><p>（1） $ \\begin{cases} 2x+3y=22 \\\\ x=y+1 \\end{cases}  $ </p><p>（2） $ \\begin{cases} 3x+2y=7 \\\\ 2x-4y=4 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津大学附属中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-22", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580246141922484224", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580246141922484224", "title": "天津市南开区天津大学附属中学2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580246048418865152", "questionArticle": "<p>3．关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} x-y=5 \\\\ x+2y=3m-1 \\end{cases}  $ 的解满足 $ 2x+y=13 $ ，则 $ m $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川宜宾 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-22", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580246025236946944", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580246025236946944", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580246154060800000", "questionArticle": "<p>4．下列方程中，属于二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x{^{2}}+2y-1=0 $ B． $ x-y=2 $ C． $ 2xy-x=10 $ D． $ x-\\dfrac { 1 } { y }=-1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-05-22", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584852359609622528", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "584852359609622528", "title": "浙江省杭州市十三中教育集团2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}, {"id": "580246141922484224", "title": "天津市南开区天津大学附属中学2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580246041007529984", "questionArticle": "<p>5．<i>A</i>、<i>B</i>两地相距3千米，甲从<i>A</i>地出发步行到<i>B</i>地，同时乙从<i>B</i>地出发步行到<i>A</i>地，20分钟两人相遇，又经过10分钟，甲所余路程为乙所余路程的2倍，求甲、乙二人的速度．设甲的速度为 $ x $ 千米/小时，乙的速度为 $ y $ 千米/小时，下列方程组中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 20x+20y=3 \\\\ 3-30x=2\\left ( { 3-30y } \\right )  \\end{cases}  $ B． $ \\begin{cases} 20x+20y=3 \\\\ 2\\left ( { 3-\\dfrac { 30 } { 60 }x } \\right ) =3-\\dfrac { 30 } { 60 }y \\end{cases}  $ </p><p>C． $ \\begin{cases} \\dfrac { 20 } { 60 }x+\\dfrac { 20 } { 60 }y=3 \\\\ 3-\\dfrac { 30 } { 60 }x=2\\left ( { 3-\\dfrac { 30 } { 60 }y } \\right )  \\end{cases}  $ D． $ \\begin{cases} \\dfrac { 20 } { 60 }\\left ( { x+y } \\right ) =3 \\\\ \\dfrac { 30 } { 60 }x=2\\times \\dfrac { 30 } { 60 }y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川宜宾 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-22", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580246025236946944", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580246025236946944", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580246036553179136", "questionArticle": "<p>6．如果方程组 $ \\begin{cases} x+3y=5 \\\\ 2x-y=3 \\end{cases}  $ 的解是方程 $ 3x+my=8 $ 的一个解，则 $ m= $ （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．2C．3D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川宜宾 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-22", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580246025236946944", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580246025236946944", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578361541143080960", "questionArticle": "<p>7．某中学组织师生共 $ 480 $ 人去参观博物院，阅读下列对话：</p><p>李老师：“客运公司有 $ 60 $ 座和 $ 45 $ 座两种型号的客车可供租用，且租用 $ 1 $ 辆 $ 60 $ 座客车和 $ 1 $ 辆 $ 45 $ 座客车到河南省博物院，一天的租金共计 $ 1800 $ 元．”</p><p>小明说：“我们学校八年级师生昨天在这个客运公司租了 $ 4 $ 辆 $ 60 $ 座和 $ 3 $ 辆 $ 45 $ 座的客车到河南省博物院，一天的租金共计 $ 6400 $ 元．”</p><p>（1）客运公司 $ 60 $ 座和 $ 45 $ 座的客车每辆每天的租金分别是多少元？</p><p>（2）若同时租用两种或一种客车，要使每位师生都有座位；且每辆客车恰好坐满，共有哪几种租车方式？其中最省钱的租车方式，租车费用为多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东济南 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-05-22", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578361510524661760", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "578361510524661760", "title": "山东省济南市莱芜区2024−2025学年下学期二模考试九年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579864318679752704", "questionArticle": "<p>8．下表是牡丹蛋糕店几种蛋糕的售价表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 80.85pt;\"><p>蛋糕种类</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ A $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.4pt;\"><p> $ B $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24.3pt;\"><p> $ C $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24.3pt;\"><p> $ D $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.55pt;\"><p> $ E $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ F $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 80.85pt;\"><p>价格/（元/块）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ 25 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.4pt;\"><p> $ m $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24.3pt;\"><p> $ 15 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24.3pt;\"><p> $ 18 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.55pt;\"><p> $ n $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ 28 $ </p></td></tr></table><p>（1）小华发现，买 $ 2 $ 块 $ B $ 种蛋糕、 $ 1 $ 块 $ E $ 种蛋糕共需 $ 50 $ 元，而每块 $ B $ 种蛋糕比每块 $ E $ 种蛋糕便宜 $ 5 $ 元，则表中 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ n= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>（2）小华原本拿了4块蛋糕去结账，他发现店里正举办优惠活动：若买5块蛋糕，则免去一个最便宜的蛋糕的钱．因此，小华就多买了一块<i>D</i>种蛋糕．设小华原本 $ 4 $ 块蛋糕的结账金额为 $ x $ 元，多买一块后的结账金额为 $ y $ 元．</p><p>①试写出 $ y $ 与 $ x $ 的函数关系式；</p><p>②直接写出 $ y $ 的取值范围．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南安阳 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-05-21", "keyPointIds": "16440|16536|16543", "keyPointNames": "表格或图示问题|列一次函数解析式|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584851269178662912", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584851269178662912", "title": "河南省安阳市2024−2025学年九年级下学期第三次联考数学试题试卷", "paperCategory": 1}, {"id": "579864274308210688", "title": "河南省周口市2024−2025学年九年级下学期第三次联考数学试题试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579864759102648320", "questionArticle": "<p>9．（1）计算： $ \\left ( { -4+8 } \\right ) +{\\left( { -1 } \\right) ^ {2}}\\times 5\\div \\left ( { -\\dfrac { 1 } { 3 } } \\right )  $ ；</p><p>（2）解方程组： $ \\begin{cases} 3x-y=4① \\\\ 3x+2y=16② \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西 · 一模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16278|16424", "keyPointNames": "有理数的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579864735270612992", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579864735270612992", "title": "2025年山西省中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580246056564203520", "questionArticle": "<p>10．随着新能源汽车的发展，某公交公司将用新能源公交车淘汰某一条线路上“冒黑烟”较严重的燃油公交车，计划购买<i>A</i>型和<i>B</i>型新能源公交车共10辆，若购买<i>A</i>型公交车1辆，<i>B</i>型公交车2辆，共需300万元；若购买<i>A</i>型公交车2辆，<i>B</i>型公交车1辆，共需270万元，</p><p>（1）求购买<i>A</i>型和<i>B</i>型公交车每辆各需多少万元？</p><p>（2）预计在该条线路上<i>A</i>型和<i>B</i>型公交车每辆年均载客量分别为80万人次和100万人次．若该公司购买<i>A</i>型和<i>B</i>型公交车的总费用不超过1000万元，且确保这10辆公交车在该线路的年均载客量总和不少于900万人次，则该公司有哪几种购车方案？哪种购车方案总费用最少？最少总费用是多少？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|420000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川宜宾 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 12, "referenceNum": 2, "createTime": "2025-05-21", "keyPointIds": "16424|16432|16490", "keyPointNames": "加减消元法解二元一次方程组|配套问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580246025236946944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580246025236946944", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "151687465685786624", "title": "湖北省恩施土家族苗族自治州咸丰县2021-2022学年七年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 65, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 65, "timestamp": "2025-07-01T02:08:28.723Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}