{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 69, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "579474655683063808", "questionArticle": "<p>1．列方程（组）解应用题：</p><p>某超市用9600元购进甲、乙两种商品共200件，这两种商品的进价，标价如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">价格</p><p style=\"text-align:center;\">类型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">甲种</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">乙种</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">进价（元/件）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">30</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">60</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">标价（元/件）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">50</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">90</p></td></tr></table><p>（1）求甲、乙两种商品各购进多少件？</p><p>（2）若甲种商品按标价下降<i>a</i>元出售，乙种商品按标价八折出售，那么这批商品全部售出后，超市共获利2640元，求<i>a</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市长寿中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579474623105904640", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579474623105904640", "title": "重庆市长寿区重庆市长寿中学校2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579474650419212288", "questionArticle": "<p>2．解方程组：</p><p>（1） $ \\begin{cases} 5x-2y=17 \\\\ 3x+4y=5 \\end{cases}  $ </p><p>（2） $ \\begin{cases} x+3y=7 \\\\ x=y-9 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市长寿中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579474623105904640", "questionFeatureName": "开放性试题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579474623105904640", "title": "重庆市长寿区重庆市长寿中学校2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579474646531092480", "questionArticle": "<p>3．为了丰富学生课外小组活动，培养学生动手操作能力，王老师让学生把 $ { { 6 } }{ \\rm{ m } } $ 长的彩绳截成 $ 2{ \\rm{ m } } $ 或 $ 1{ \\rm{ m } } $ 的彩绳，用来做手工编织，在不造成浪费的前提下，你有<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>种不同的截法．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市长寿中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579474623105904640", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579474623105904640", "title": "重庆市长寿区重庆市长寿中学校2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579474645675454464", "questionArticle": "<p>4．已知关于<i>x</i>、<i>y</i>的方程组 $ \\begin{cases} x+2y=6-3a \\\\ x-y=6a \\end{cases}  $ 的解满足 $ 2x+y=3 $ ，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市长寿中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579474623105904640", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579474623105904640", "title": "重庆市长寿区重庆市长寿中学校2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579474640361271296", "questionArticle": "<p>5．若 $ \\begin{cases} x=-2 \\\\ y=m \\end{cases}  $ 是方程<i>nx</i>+6<i>y</i>＝4的一个解，则代数式3<i>m</i>﹣<i>n</i>+1的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3B．2C．1D．﹣1</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市长寿中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579474623105904640", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579474623105904640", "title": "重庆市长寿区重庆市长寿中学校2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579858352852742144", "questionArticle": "<p>6．寒梅中学为了丰富学生的课余生活，计划购买围棋和中国象棋供棋类兴趣小组活动使用，若购买3副围棋和5副中国象棋需用98元；若购买8副围棋和3副中国象棋需用158元；（1）求每副围棋和每副中国象棋各多少元；（2）寒梅中学决定购买围棋和中国象棋共40副，总费用不超过550元，那么寒梅中学最多可以购买多少副围棋?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|230000|410000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈尔滨 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 10, "referenceNum": 6, "createTime": "2025-05-20", "keyPointIds": "16434|16486", "keyPointNames": "方案问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579858322410483712", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "579858322410483712", "title": "2025年黑龙江省哈尔滨市萧红中学中考二模数学试卷", "paperCategory": 1}, {"id": "424698918628270080", "title": "河南省洛阳市偃师市实验中学2022-2023学年七年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "269555350541475840", "title": "黑龙江省哈尔滨市第一一三中学校2021-2022学年九年级下学期开学考试数学试题", "paperCategory": 1}, {"id": "182066061101015040", "title": "黑龙江省哈尔滨市第一一三中学校2021-2022学年九年级下学期开学考试数学试题", "paperCategory": 1}, {"id": "160700925060882432", "title": "湖南省长沙市天心区明德教育集团2020年6月中考仿真数学试卷", "paperCategory": 1}, {"id": "140458513717829632", "title": "山东省泰安市东平县2020年中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579482847725006848", "questionArticle": "<p>7．某医药器材经销商计划同时购进一批甲、乙两种型号的口罩，若购进2箱甲型口罩和1箱乙型口罩，共需要资金2800元；若购进3箱甲型口罩和2箱乙型口罩，共需要资金4600元．</p><p>（1）求甲、乙型号口罩每箱的进价为多少元？</p><p>（2）该医药器材经销商计划购进甲、乙两种型号的口罩用于销售，预计用不多于 $ 1.8 $ 万元且不少于 $ 1.74 $ 万元的资金购进这两种型号口罩共20箱，请问有几种进货方案？并写出具体的进货方案；</p><p>（3）若销售一箱甲型口罩，利润率为 $ 40\\% $ ，乙型口罩的售价为每箱1280元，为了促销，公司决定每售出一箱乙型口罩，返还顾客现金<i>m</i>元，而甲型口罩售价不变，要使（2）中所有方案获利相同，求<i>m</i>的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东青岛市青岛大学附属中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16434|16490|16544", "keyPointNames": "方案问题|一元一次不等式组的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579482812681596928", "questionFeatureName": "生活背景问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579482812681596928", "title": "山东省青岛大学附属中学2024−2025学年八年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579473669426032640", "questionArticle": "<p>8．某车间有 $ 60 $ 名工人生产太阳镜， $ 1 $ 名工人每天可生产镜片 $ 200 $ 片或镜架 $ 50 $ 个．两个镜片和一个镜架配套，应如何分配工人生产镜片和镜架，才能使产品配套？设安排 $ x $ 名工人生产镜片， $ y $ 名工人生产镜架，则可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=60 \\\\ 200x=2\\times 50y \\end{cases}  $ B． $ \\begin{cases} x+y=60 \\\\ 2\\times 200x=50y \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=60 \\\\ 200x=50y \\end{cases}  $ D． $ \\begin{cases} x+y=60 \\\\ 200x=\\dfrac { 1 } { 2 }\\times 50y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|500000|650000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆朝高 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 7, "referenceNum": 3, "createTime": "2025-05-19", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579473654632722432", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "579473654632722432", "title": "重庆朝阳中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}, {"id": "569703598805786624", "title": "河北省石家庄市第十七中学2024−2025学年七年级下学期4月考试数学试题", "paperCategory": 1}, {"id": "474318009248555008", "title": "新疆维吾尔自治区乌鲁木齐市实验学校2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579482967673712640", "questionArticle": "<p>9．2025年春节凸显了我国在机器人领域的强大实力，随着人工智能与物联网等技术的快速发展，人形机器人的应用场景不断拓展，某快递企业为提高工作效率，拟购买 $ A、B $ 两种型号智能机器人进行快递分拣，相关信息如下：</p><p>信息一</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\"><i>A</i>型机器人台数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\"><i>B</i>型机器人台数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 117pt;\"><p style=\"text-align:center;\">总费用（单位：万元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 117pt;\"><p style=\"text-align:center;\">260</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 117pt;\"><p style=\"text-align:center;\">360</p></td></tr></table><p>信息二</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 204.8pt;\"><p><i>A</i>型机器人每台每天可分拣快递22万件；</p><p><i>B</i>型机器人每台每天可分拣快递18万件．</p></td></tr></table><p>（1）求 $ A、B $ 两种型号智能机器人的单价；</p><p>（2）现该企业准备购买 $ A、B $ 两种型号智能机器人共10台，需要每天分拣快递不少于200万件，则该企业最少需要购买几台<i>A</i>种型号智能机器人？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西安市曲江第一中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16438|16440|16486", "keyPointNames": "和差倍分问题|表格或图示问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579482939253108736", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579482939253108736", "title": "陕西省西安市曲江第一中学2024−2025学年下学期八年级数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579474465400074240", "questionArticle": "<p>10．夏天到了，水果陆续上市．某水果店看好有机水果蓝莓和樱桃的市场价值．若购进40千克蓝莓和30千克樱桃需要1250元；若购进60千克蓝莓和20千克樱桃需要150元，两次购进同种水果的价格一样．</p><p>（1）求有机水果蓝莓和樱桃每千克的购进价格各是多少元？</p><p>（2）该水果店决定每天购进有机水果蓝莓和樱桃共500千克进行销售，但投入资金不超过9000元，假定该水果店将蓝莓和樱桃的售价分别定为每千克35元和每千克25元，设购进蓝莓<i>x</i>千克，请问当<i>x</i>为何值时，该超市总店将获得最大利润？最大利润是多少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆等地重庆18中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-05-19", "keyPointIds": "16424|16438|16486|16544", "keyPointNames": "加减消元法解二元一次方程组|和差倍分问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579474426384658432", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "579474426384658432", "title": "重庆市第十八中学2024−2025学年八年级下学期半期模拟检测数学试题", "paperCategory": 1}, {"id": "212166619824431104", "title": "重庆市南川区2021-2022学年八年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 70, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 70, "timestamp": "2025-07-01T02:09:03.107Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}