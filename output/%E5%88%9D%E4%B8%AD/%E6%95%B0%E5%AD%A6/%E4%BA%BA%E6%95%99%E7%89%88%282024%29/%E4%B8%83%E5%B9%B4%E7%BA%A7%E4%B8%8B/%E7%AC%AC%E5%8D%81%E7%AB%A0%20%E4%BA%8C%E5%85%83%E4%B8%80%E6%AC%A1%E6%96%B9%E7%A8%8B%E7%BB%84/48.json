{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 47, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "583053696684040192", "questionArticle": "<p>1．解下列方程组：</p><p>（1） $ \\begin{cases} x+2y=3 \\\\ 3x-2y=5 \\end{cases}  $ </p><p>（2） $ \\begin{cases} x+2\\left ( { y+1 } \\right ) =11 \\\\ x-\\left ( { y+1 } \\right ) =5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南许昌 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-05", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583053675624439808", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583053675624439808", "title": "河南省许昌市2024—2025学年下学期期中测试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583054013437878272", "questionArticle": "<p>2．某汽车销售公司经销某品牌<i>A</i>，<i>B</i>两款汽车，今年一、二月份销售情况如下表所示：（<i>A</i>，<i>B</i>两款汽车的销售单价保持不变）</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售数量（辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售额（万元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>A </i>款</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>B </i>款</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>一月份</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>35</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>二月份</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>33</p></td></tr></table><p>（1）求<i>A</i>，<i>B</i>两款汽车每辆售价分别多少万元？</p><p>（2）若<i>A</i>款汽车每辆进价为8万元，<i>B</i>款汽车每辆进价为6万元，公司预计用不多于 105万元且不少于99万元的资金购进这两款汽车共15辆，有几种进货方案？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南郴州 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-05", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583053983238889472", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583053983238889472", "title": "湖南省郴州市2024−2025学年七年级下学期5月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583053689805381632", "questionArticle": "<p>3．嫦娥六号于2024年6月2日成功着陆在月球背面南极一艾特肯盆地预选着陆区，开启人类探测器首次在月球背面实施的样品采集任务．嫦娥六号采用了钻取和表取两种方式共采集样品1935克，表取是钻取的4倍还多310克．若设钻取样品<i>x</i>克，表取样品<i>y</i>克，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=1935 \\\\ 4x-310=y \\end{cases}  $ B． $ \\begin{cases} x+y=1935 \\\\ 4x+310=y \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=1935 \\\\ 4x-y=310 \\end{cases}  $ D． $ \\begin{cases} x+4y=1935 \\\\ y-4x=310 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南许昌 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-05", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583053675624439808", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583053675624439808", "title": "河南省许昌市2024—2025学年下学期期中测试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "583054775970738176", "questionArticle": "<p>4．某地区在 $ 2023 $ 年的经济活动中表现出色，其进出口总额达到了 $ 400 $ 亿元．随着经济的持续发展， $ 2024 $ 年的进出口总额相比 $ 2023 $ 年增加了 $ 100 $ 亿元,其中，进口额增长了 $ 30\\% $ ，出口额则增长了 $ 20\\% $ ，求 $ 2024 $ 年进口额和出口额分别是多少亿元？注：进出口总额 $ = $ 进口额 $ + $ 出口额.</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽池州 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-05", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583054746518335488", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "583054746518335488", "title": "2025年安徽省池州市中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584855464279584768", "questionArticle": "<p>5．如图，在长为 $ 1{ { 0 } }{ \\rm{ m } } $ ，宽为 $ { { 8 } }{ \\rm{ m } } $ 的矩形空地上，沿平行于矩形各边的方向分割出三个全等的小矩形花圃，求其中一个小矩形花圃的长与宽．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/02/2/1/0/0/0/584855416498069511/images/img_21.png\" style=\"vertical-align:middle;\" width=\"131\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西高新一中 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-06-04", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584855311497867264", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "584855311497867264", "title": "2025年陕西省西安市西安高新第一中学中考六模数学试题", "paperCategory": 1}, {"id": "584855437415067648", "title": "2025年陕西省西安市雁塔区高新一中中考数学六模试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580622715964399616", "questionArticle": "<p>6．解方程或方程组：</p><p>（1） $ 2(x-1){^{2}}=98 $ ；</p><p>（2） $ \\begin{cases} y=2x+1 \\\\ 3x-y-1=0 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东惠州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-04", "keyPointIds": "16287|16423", "keyPointNames": "平方根|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622697090031616", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622697090031616", "title": "广东省惠阳市高级中学2024−2025学年下学期七年级数学期中质量监测试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580622715280728064", "questionArticle": "<p>7．若关于<i>x</i>、<i>y</i>的方程 $ \\left ( { m+2 } \\right ) x{^{|m|-1}}+8y=7 $ 是二元一次方程，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东惠州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-04", "keyPointIds": "16258|16419", "keyPointNames": "绝对值方程|二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622697090031616", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622697090031616", "title": "广东省惠阳市高级中学2024−2025学年下学期七年级数学期中质量监测试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580621459145404416", "questionArticle": "<p>8．某汽车销售公司为提升业绩，计划购进一批新能源汽车进行销售，据了解1辆 $ \\mathrm{ A } $ 型汽车，3辆 $ B $ 型汽车的进价共计70万元；3辆 $ \\mathrm{ A } $ 型汽车，2辆 $ B $ 型汽车的进价共计105万元．</p><p>（1）求 $ A,B $ 两种型号的汽车每辆进价分别为多少万元？</p><p>（2）若该公司计划正好用250万元购进以上两种型号的新能源汽车（两种型号的汽车均有购买），请你通过计算写出所有购买方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-04", "keyPointIds": "16420|16438", "keyPointNames": "二元一次方程的解|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580621427650375680", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580621427650375680", "title": "北京市鲁迅中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580621451318833152", "questionArticle": "<p>9．已知<i>x</i>、<i>y</i>满足方程组 $ \\begin{cases} x+2y=2 \\\\ 2x+y=7 \\end{cases}  $ ，则 $ x-y $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-04", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580621427650375680", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580621427650375680", "title": "北京市鲁迅中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580622839054643200", "questionArticle": "<p>10．根据图中给出的信息，解答下列问题：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/22/2/1/0/0/0/580622798302781443/images/img_15.png\" style=\"vertical-align:middle;\" width=\"468\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）放入一个小球水面升高<u>　　</u> $ { \\rm{ c } }{ \\rm{ m } } $ ，放入一个大球水面升高<u>　　</u> $ { \\rm{ c } }{ \\rm{ m } } $ ；</p><p>（2）如果放入10个球，使水面上升到 $ { { 5 } }{ { 0 } }{ \\rm{ c } }{ \\rm{ m } } $ ，应放入大球、小球各多少个？</p><p>（3）现放入若干个球，使水面升高 $ { { 2 } }{ { 1 } }{ \\rm{ c } }{ \\rm{ m } } $ ，且小球个数为偶数个，问有几种可能，请一一列出（写出结果即可）．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-04", "keyPointIds": "16420|16439", "keyPointNames": "二元一次方程的解|几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622816157937664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622816157937664", "title": "河北省石家庄第九中学2024−2025学年级七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 48, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 48, "timestamp": "2025-07-01T02:06:29.672Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}