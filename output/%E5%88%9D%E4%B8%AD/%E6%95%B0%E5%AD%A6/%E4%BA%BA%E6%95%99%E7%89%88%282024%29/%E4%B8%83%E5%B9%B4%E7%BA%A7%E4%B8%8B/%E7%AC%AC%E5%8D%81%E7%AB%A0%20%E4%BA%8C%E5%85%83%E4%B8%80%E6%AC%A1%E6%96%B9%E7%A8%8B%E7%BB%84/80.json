{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 79, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "577296098265440256", "questionArticle": "<p>1．如图，七个相同的小长方形组成一个大长方形 $ ABCD $ ．若 $ CD=21{ \\rm{ c } }{ \\rm{ m } } $ ，求长方形 $ ABCD $ 的面积．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/13/2/1/0/0/0/577296042057576460/images/img_12.png\" style=\"vertical-align:middle;\" width=\"147\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东聊城 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577296077239394304", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577296077239394304", "title": "山东省聊城市2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577296089671311360", "questionArticle": "<p>2．甲乙两个小组共同生产某种产品．若甲组先生产1天，然后甲、乙合作生产5天，则两个小组产量一样多；若甲组先生产300个产品，然后两组合作生产4天，则乙比甲多生产100个产品．若设甲每天生产<i>x</i>个产品，乙每天生产<i>y</i>个产品，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 6x=5y \\\\ 300+4x=4y+100 \\end{cases}  $ B． $ \\begin{cases} 5x=6y \\\\ 200+4x=4y \\end{cases}  $ </p><p>C． $ \\begin{cases} 6x-5y=0 \\\\ 4x+400=4y \\end{cases}  $ D． $ \\begin{cases} 5x-6y=0 \\\\ 4x+400=4y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东聊城 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577296077239394304", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577296077239394304", "title": "山东省聊城市2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577296096013099008", "questionArticle": "<p>3．解方程组</p><p>（1） $ \\begin{cases} 2x-3y=5 \\\\ 3x-y=-3 \\end{cases}  $ </p><p>（2） $ \\begin{cases} x+2y=-2 \\\\ 3x-4y=14 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东聊城 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577296077239394304", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577296077239394304", "title": "山东省聊城市2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577296603834265600", "questionArticle": "<p>4．“智慧农业，科技助农”．某现代化农场计划引进智能灌溉系统，需一次性采购两种不同的设备：循环灌溉系统（<i>A</i>型）和远程灌溉系统（<i>B</i>型）．经市场调研发现：购买5台<i>A</i>型和3台<i>B</i>型设备共需 $ 7.6 $ 万元，且2台<i>A</i>型设备比1台<i>B</i>型设备贵 $ 0.4 $ 万元．</p><p>（1）求<i>A</i>、<i>B</i>两种型号设备的单价分别是多少元？</p><p>（2）某农场准备购买这两种型号设备共30台，要求<i>A</i>型设备的数量不超过<i>B</i>型设备数量的3倍，请设计最省钱的购买方案．</p><p>（3）在（2）的设计方案下，现购买时恰好赶上优惠政策，<i>A</i>的价格下降 $ 15\\% {\\rm ，\\mathit{B}} $ 价格下降 $ 5\\% $ ，那么在购买时节省的钱最多能再买几台<i>A</i>型设备？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西安交通大学附属中学分校 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16438|16486|16535|16543", "keyPointNames": "和差倍分问题|一元一次不等式的应用|一次函数的图象和性质|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577296573069045760", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577296573069045760", "title": "陕西省西安交通大学附属中学分校2024−2025学年下学期期中考试八年级数学度理", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577296444081610752", "questionArticle": "<p>5．《哪吒之魔童闹海》连续三个月获得全国月度票房冠军，某小组12名同学相约一起观看该电影，其中8人购买了 $ 2D $ 电影票，4人购买了 $ 3D $ 电影票，共花费560元．已知每张 $ 3D $ 电影票的售价比 $ 2D $ 电影票的售价多5元，求每张 $ 2D $ 电影票、 $ 3D $ 电影票的售价分别为多少元．设每张 $ 2D $ 电影票的售价为 $ x $ 元，每张 $ 3D $ 电影票的售价为 $ y $ 元，根据题意，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-5=y \\\\ 8x+4y=560 \\end{cases}  $ B． $ \\begin{cases} x+5=y \\\\ 8x+4y=560 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+5=y \\\\ 4x+8y=560 \\end{cases}  $ D． $ \\begin{cases} x-5=y \\\\ 4x+8y=560 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西忻州 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577296429695148032", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577296429695148032", "title": "山西省忻州市部分学校2025年中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577296808478552064", "questionArticle": "<p>6．七年级（1）班的同学去参加科技体验活动，第一组有2人选择“九天揽月”活动，3人选择“深海探幽”活动，共花费130元；第二组有4人选择“九天揽月”活动，2人选择“深海探幽”活动，共花费140元．每张“九天揽月”和“深海探幽”活动的票价各为多少元</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市育贤中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577296783820238848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577296783820238848", "title": "天津市南开区育贤中学2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577296806897299456", "questionArticle": "<p>7．（1）计算： $ \\sqrt[3] { -8 }+∣2-\\sqrt { 5 }∣+\\sqrt { {\\left( { -3 } \\right) ^ {2}} }-\\left ( { -\\sqrt { 5 } } \\right )  $ &nbsp;&nbsp;&nbsp;&nbsp;</p><p>（2）计算： $ \\sqrt { 2{^{2}} }-\\sqrt { 2\\dfrac { 1 } { 4 } }+\\sqrt[3] { \\dfrac { 7 } { 8 }-1 }-\\sqrt[3] { -1 } $ </p><p>（3）解方程组： $ \\begin{cases} 3x+2y=19 \\\\ 2x-y=1 \\end{cases}  $ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p><p>（4）解方程组： $ \\begin{cases} 2x-y=5 \\\\ 4x+3y=-10 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市育贤中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16288|16290|16424", "keyPointNames": "算术平方根|立方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577296783820238848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577296783820238848", "title": "天津市南开区育贤中学2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577296801071411200", "questionArticle": "<p>8．养牛场有30头大牛和15头小牛，1天用饲料675kg，一周后又购进12头大牛和5头小牛，这时1天用饲料940kg. 饲养员李大叔估计每头大牛需饲料18至21 kg，每头小牛需6至8 kg. 关于李大叔的估计，下列结论正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．大牛每天所需饲料在估计的范围内，小牛每天所需饲料也在估计的范围内</p><p>B．大牛每天所需饲料在估计的范围内，小牛每天所需饲料在估计的范围外</p><p>C．大牛每天所需饲料在估计的范围外，小牛每天所需饲料在估计的范围内</p><p>D．大牛每天所需饲料在估计的范围外，小牛每天所需饲料也在估计的范围外</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市育贤中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577296783820238848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577296783820238848", "title": "天津市南开区育贤中学2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577679596461535232", "questionArticle": "<p>9．如图，在平面直角坐标系中，点 $ A\\left ( { a,0 } \\right )  $ ， $ B\\left ( { b,-4 } \\right )  $ ，且 $ a $ ， $ b $ 满足 $ {\\left( { a+b+1 } \\right) ^ {2}}+\\sqrt { a-b-5 }=0 $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/14/2/1/0/0/0/577679497471762435/images/img_10.png\" style=\"vertical-align:middle;\" width=\"364\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）请你直接写出 $ \\mathrm{ A } $ 、 $ B $ 两点的坐标；</p><p>（2）点 $ C $ 在 $ y $ 轴正半轴上，连接 $ BC $ 交 $ x $ 轴的负半轴于点 $ D $ ，若 $ S{{}_{ \\vartriangle ABD } }=S{{}_{ \\vartriangle ACD } } $ ，求 $ S{{}_{ \\vartriangle ABC } } $ ；</p><p>（3）在第（2）小题的条件下，线段 $ AB $ 交 $ y $ 轴于点 $ M $ ，将线段 $ AB $ 平移得到 $ CE $ （点 $ \\mathrm{ A } $ 与点 $ C $ 对应，点 $ B $ 与点 $ E $ 对应），点 $ F $ 在线段 $ AB $ 上，且在第三象限，点 $ G $ 在线段 $ CE $ 上，且在第二象限，点 $ H $ 在线段 $ AB $ ， $ CE $ 之间， $ \\angle HGO=2\\angle HGE $ ， $ \\angle HFO=2\\angle HFB $ ， $ \\angle FOG+\\angle FHG=200{}\\degree  $ ， $ \\angle FOM+\\angle BAO=60{}\\degree  $ ．请将图形补充完整，并求证： $ GH\\mathrm{ /\\mskip-4mu/ }OF $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16424|16501|16632", "keyPointNames": "加减消元法解二元一次方程组|坐标与图形性质|平行线的判定与性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679561179049984", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577679561179049984", "title": "福建省福州延安中学2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577679595136135168", "questionArticle": "<p>10．某文具店经销甲、乙两款品牌的笔记本，今年二、三月份销售情况如下表所示：（甲、乙款种笔记本的销售单价保持不变）</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>月份</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>销售数量（本）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>销售数量（本）</p></td><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>销售额（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>甲款</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>乙款</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>二月份</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>880</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>三月份</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>800</p></td></tr></table><p>（1）求甲、乙两款笔记本的销售单价分别是多少元；</p><p>（2）若甲款笔记本每本进价为10元，乙款笔记本每本进价为8元，文具店预计用不多于624元且不少于620元的资金购进这两款笔记本共70本，有几种进货方案；</p><p>（3）为了促销甲款笔记本，文具店决定每售出一本甲款笔记本，返还顾客现金 $ m $ 元，要使（2）中所有的方案获利相同，求 $ m $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679561179049984", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577679561179049984", "title": "福建省福州延安中学2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 80, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 80, "timestamp": "2025-07-01T02:10:16.081Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}