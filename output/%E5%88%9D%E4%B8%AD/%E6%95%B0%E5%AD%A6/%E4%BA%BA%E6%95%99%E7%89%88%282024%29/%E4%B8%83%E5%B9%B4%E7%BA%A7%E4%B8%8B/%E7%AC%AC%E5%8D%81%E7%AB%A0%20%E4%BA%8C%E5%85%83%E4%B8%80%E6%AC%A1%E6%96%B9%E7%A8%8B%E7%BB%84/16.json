{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 15, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "592539274546814976", "questionArticle": "<p>1．手工课上，同学们用图 $ 1 $ 中的彩色和白色正方形纸片拼成如图 $ 2 $ 中的甲、乙两种图案．现有 $ 50 $ 个彩色正方形纸片和 $ 130 $ 个白色正方形纸片，若拼成两种图案（两种图案都要拼）若干个，恰好将所有正方形纸片用完，设拼成了 $ x $ 个甲图案， $ y $ 个乙图案，则所列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/24/2/1/0/0/0/592539179034128393/images/img_9.png\" style=\"vertical-align:middle;\" width=\"259\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} 4x+5y=50 \\\\ x+8y=130 \\end{cases}  $ B． $ \\begin{cases} 4x+y=50 \\\\ 5x+8y=130 \\end{cases}  $ C． $ \\begin{cases} x+4y=50 \\\\ 5x+y=130 \\end{cases}  $ D． $ \\begin{cases} 4x+y=50 \\\\ 8x+5y=130 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北邯郸 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592539258491019264", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592539258491019264", "title": "河北省邯郸市2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592539269668839424", "questionArticle": "<p>2．用代入消元法解方程组 $ \\begin{cases} x=3y-1 \\\\ x-2y=4 \\end{cases}  $ 时，代入正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 3y+1-2y=4 $ B． $ 3y-1-2y=4 $ </p><p>C． $ y-2\\left ( { 3y-1 } \\right ) =4 $ D． $ 3y+1+2y=4 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北邯郸 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592539258491019264", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592539258491019264", "title": "河北省邯郸市2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "588454710534774784", "questionArticle": "<p>3．某校“综合与实践”小组的同学利用课余时间开展了一项关于“低碳生活”的课题活动，具体是对“新能源汽车充电难”问题进行调查，并写出相关活动报告．请你帮他们完成下面的活动报告．</p><table style=\"border: solid 1px;border-collapse: collapse;\">\r\n  <tbody>\r\n    <tr>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 39.75pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">活动课题</p>\r\n      </td>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 387.6pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">了解“新能源汽车充电难”问题</p>\r\n      </td>\r\n    </tr>\r\n    <tr>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 39.75pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">活动目的</p>\r\n      </td>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 387.6pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">运用一元一次不等式解决新能源汽车充电问题，提倡“低碳生活，绿色出行”</p>\r\n      </td>\r\n    </tr>\r\n    <tr>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 39.75pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">活动素材</p>\r\n      </td>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 387.6pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">某小区计划新建地上和地下两类充电桩，每个充电桩的占地面积如下：</p>\r\n      <table style=\"border: solid 1px;border-collapse: collapse;\">\r\n        <tbody>\r\n          <tr>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 125.85pt;\">\r\n            <p>&nbsp;</p>\r\n            </td>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">地上充电桩</p>\r\n            </td>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">地下充电桩</p>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 125.85pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">每个充电桩占地面积 $ /{ \\rm{ m } }{^{2}} $ </p>\r\n            </td>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">3</p>\r\n            </td>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">1</p>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <p style=\" font-family:'Times New Roman',' ';\">已知新建1个地上充电桩和2个地下充电桩需要0.8万元；</p>\r\n      <p style=\" font-family:'Times New Roman',' ';\">新建2个地上充电桩和1个地下充电桩需要0.7万元．</p>\r\n      </td>\r\n    </tr>\r\n    <tr>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 39.75pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">问题一</p>\r\n      </td>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 387.6pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">该小区新建一个地上充电桩和一个地下充电桩各需要多少万元？</p>\r\n      </td>\r\n    </tr>\r\n    <tr>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 39.75pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">问题二</p>\r\n      </td>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 387.6pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">若该小区计划用不超过16.3万元的资金新建60个充电桩，且地下充电桩的数量不少于40个，则共有几种建造方案？请列出所有方案．</p>\r\n      </td>\r\n    </tr>\r\n    <tr>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 39.75pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">问题三</p>\r\n      </td>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 387.6pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">考虑到充电设备对小区居住环境的影响，在问题二的条件下，哪种方案占地面积最小？</p>\r\n      </td>\r\n    </tr>\r\n  </tbody>\r\n</table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南信阳 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16440|16490", "keyPointNames": "表格或图示问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588454685201178624", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "588454685201178624", "title": "河南省信阳市第七中学2024−2025学年七年级下学期5月学业水平检测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588454706843787264", "questionArticle": "<p>4．（1）计算： $ \\sqrt[3] { -8 }-{\\left( { \\sqrt { 3 } } \\right) ^ {2}}-\\sqrt { 64 } $ ；</p><p>（2）解方程组： $ \\begin{cases} x-y=2 \\\\ 3x+2y=-19 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南信阳 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16290|16379|16424", "keyPointNames": "立方根|二次根式的性质和化简|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588454685201178624", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "588454685201178624", "title": "河南省信阳市第七中学2024−2025学年七年级下学期5月学业水平检测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588454703584813056", "questionArticle": "<p>5．若 $ \\begin{cases} x=1 \\\\ y=-2 \\end{cases}  $ 是二元一次方程组 $ \\begin{cases} ax+2y=0 \\\\ 2bx+ay=2 \\end{cases}  $ 的解，则 $ a+b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南信阳 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588454685201178624", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "588454685201178624", "title": "河南省信阳市第七中学2024−2025学年七年级下学期5月学业水平检测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588454701034676224", "questionArticle": "<p>6．已知关于 $ x、y $ 的方程组 $ \\begin{cases} 4x-y=3-a \\\\ x-4y=7 \\end{cases}  $ 的解满足 $ 1  &lt;  x-y  &lt;  2 $ ，则 $ a $ 的取值范围是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 1  &lt;  a  &lt;  2 $ B． $ -5  &lt;  a  &lt;  0 $ C． $ 5  &lt;  a  &lt;  10 $ D． $ 0  &lt;  a  &lt;  5 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南信阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588454685201178624", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "588454685201178624", "title": "河南省信阳市第七中学2024−2025学年七年级下学期5月学业水平检测数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "588454696701960192", "questionArticle": "<p>7．若方程 $ \\left ( { m{^{2}}-4 } \\right ) x{^{2}}+x-\\left ( { m-2 } \\right ) y=3 $ 是关于<i>x</i>，<i>y</i>的二元一次方程，则<i>m</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -2 $ B．2C． $ \\pm 2 $ D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南信阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16288|16419", "keyPointNames": "算术平方根|二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588454685201178624", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "588454685201178624", "title": "河南省信阳市第七中学2024−2025学年七年级下学期5月学业水平检测数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590999448139898880", "questionArticle": "<p>8．若关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} x-y=k+5 \\\\ 3x+2y=k \\end{cases}  $ 的解满足 $ x+y=-2 $ ，则 $ k $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东日照 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590999429286502400", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590999429286502400", "title": "山东省日照市北京路中学2024−2025学年七年级下学期6月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590999449645654016", "questionArticle": "<p>9．如图，平面直角坐标系中的大长方形是由8块完全相同的小长方形拼成的，其中点<i>A</i>的坐标为 $ \\left ( { 12,8 } \\right )  $ ，则点<i>B</i>的坐标为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/27/2/1/0/0/0/593787213877387265/images/img_1.png\" style='vertical-align:middle;' width=\"154\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东日照 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16439|16501", "keyPointNames": "几何问题|坐标与图形性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590999429286502400", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590999429286502400", "title": "山东省日照市北京路中学2024−2025学年七年级下学期6月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588451755777634304", "questionArticle": "<p>10．我国明代数学家程大位编撰的《算法统宗》记载了“绳索量竿”问题：“一条竿子一条索，索比竿子长一托，折回索子来量竿，却比竿子短一托，问索、竿各长几何？”译文为：“有一根竿和一条绳，若用绳去量竿，则绳比竿长5尺；若将绳对折后再去量竿，则绳比竿短5尺，问绳和竿各有多长？”设绳长<i>x</i>尺，竿长<i>y</i>尺，根据题意得（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）（注：“托”和“尺”为古代的长度单位，1托 $ =5 $ 尺）</p><p>A． $ \\begin{cases} x-y=5 \\\\ y-\\dfrac { 1 } { 2 }x=5 \\end{cases}  $ B． $ \\begin{cases} y-x=5 \\\\ \\dfrac { 1 } { 2 }x-y=5 \\end{cases}  $ C． $ \\begin{cases} x-y=5 \\\\ 2x=y+5 \\end{cases}  $ D． $ \\begin{cases} x-y=5 \\\\ y-2x=5 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|650000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025新疆吐鲁番 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-06-24", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588451738148974592", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "588451738148974592", "title": "2025年新疆维吾尔自治区吐鲁番市九年级中考三模数学试题", "paperCategory": 1}, {"id": "580245770042908672", "title": "四川省峨眉山市初中2024- 2025学年九年级下学期第二次调研监测数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 16, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 16, "timestamp": "2025-07-01T02:02:42.115Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}