{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 42, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "585252620115161088", "questionArticle": "<p>1．为响应“全民植树增绿，共建美丽中国”的号召，学校组织学生到郊外参加义务植树活动，并准备了 $ A，B $ 两种食品作为午餐．这两种食品每包质量均为 $ 50{ \\rm{ g } } $ ，营养成分表如下．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/29/2/1/0/0/0/594506043884875777/images/img_1.png\" style='vertical-align:middle;' width=\"384\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）若要从这两种食品中摄入 $ 5000 { \\rm{ k } }{ \\rm{ J } } $ 热量和 $ 80 { \\rm{ g } } $ 蛋白质，应选用 $ A， B $ 两种食品各多少包？</p><p>（2）运动量大的人或青少年对蛋白质的摄入量应更多．若每份午餐选用这两种食品共8包，要使每份午餐中的蛋白质含量不低于 $ { { 9 } }{ { 0 } } { \\rm{ g } } $ ，且热量最低，应如何选用这两种食品？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东济宁 · 临考冲刺", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-06-08", "keyPointIds": "16440|16486|16547", "keyPointNames": "表格或图示问题|一元一次不等式的应用|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585252584522297344", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585252584522297344", "title": "2025学年山东省济宁市济宁学院附属中学教育集团中考第三次模拟数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583055171694931968", "questionArticle": "<p>2．解方程组： $ \\begin{cases} 2x-y=7① \\\\ x+y=5② \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建漳州 · 临考冲刺", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-08", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583055148513013760", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "583055148513013760", "title": "2025年福建省漳州市5月初中毕业班适应性练习数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583054129397800960", "questionArticle": "<p>3．已知关于 $ x、y $ 的方程满足方程组 $ \\begin{cases} 3x+2y=m+1 \\\\ 2x+y=m-1 \\end{cases}  $ ．</p><p>（1）若 $ 5x+3y=-6 $ ，求 $ m $ 的值；</p><p>（2）若 $ x、y $ 均为非负数，求 $ m $ 的取值范围；</p><p>（3）在（2）的条件下，求 $ S=2x-3y+m $ 的最大值和最小值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-08", "keyPointIds": "16424|16489|16547", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583054101178523648", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "583054101178523648", "title": "湖南省永州市冷水滩区李达中学2024−2025学年七年级下学期期中考试数字试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583054128470859776", "questionArticle": "<p>4．接种新冠病毒疫苗，建立全民免疫屏障，是战胜病毒的重要手段．北京科兴中维需运输一批疫苗到我市疾控中心，据调查得知，2辆<i>A</i>型冷链运输车与3辆<i>B</i>型冷链运输车一次可以运输600盒；5辆<i>A</i>型冷链运输车与6辆<i>B</i>型冷链运输车一次可以运输1350盒．</p><p>（1）求每辆<i>A</i>型车和每辆<i>B</i>型车一次可以分别运输多少盒疫苗．</p><p>（2）计划用两种冷链运输车共12辆运输这批疫苗，<i>A</i>型车一次需费用5000元，<i>B</i>型车一次需费用3000元．若运输物资不少于1500盒，且总费用小于54000元．请你列出所有运输方案，并指出哪种方案所需费用最少，最少费用是多少？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-08", "keyPointIds": "16435|16490", "keyPointNames": "分配问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583054101178523648", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "583054101178523648", "title": "湖南省永州市冷水滩区李达中学2024−2025学年七年级下学期期中考试数字试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584430176349495296", "questionArticle": "<p>5．根据以下信息，探索完成任务：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>选择招聘方案？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>为庆祝中华人民共和国成立75周年，某工艺品厂设计出一款国庆纪念工艺品，计划在一个月（按22个工作日计算）内生产2024件限量工艺品．由于抽调不出足够的熟练工来完成工艺品的生产，为顺利完成任务，工厂决定招聘一些新工人，经过培训上岗可以独立进行生产．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>调研部门发现：2名熟练工和3名新工人每天共加工28件产品；3名熟练工和2名新工人每天共加工32件产品．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>工厂给的每名熟练工每天发300元工资，每名新工人每天发160元工资．</p></td></tr><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务一</p><p>分析数量关系</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>（1）每名熟练工和新工人每天分别可以生产多少件工艺品？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务二</p><p>确定可行方案</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>（2）如果工厂新招聘工人至少2人且不得超过抽调熟练工的人数，那么工厂有哪几种工人招聘方案，使得招聘的新工人和抽调的熟练工刚好能完成一个月（按22个工作日计算）的生产任务．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务三</p><p>选取最优方案</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>（3）在上述方案中，为了节省成本，应该招聘新工人多少名？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市第九十中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-06-08", "keyPointIds": "16420|16431", "keyPointNames": "二元一次方程的解|工程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584430146343444480", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584430146343444480", "title": "天津市第九十中学2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584430171412799488", "questionArticle": "<p>6．解方程组：</p><p>（1） $ \\begin{cases} 2x+3y=12 \\\\ 2x-y=4 \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} 3x-2y=7 \\\\ \\dfrac { x-2 } { 3 }-\\dfrac { 2y-1 } { 2 }=1 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市第九十中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-08", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584430146343444480", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584430146343444480", "title": "天津市第九十中学2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584430169101737984", "questionArticle": "<p>7．用四张形状、大小完全相同的小长方形纸片在平面直角坐标系中摆成如图所示图案，若点 $ A\\left ( { 1.8,4.2 } \\right )  $ ，则点<i>B</i>的坐标是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/28/2/1/0/0/0/594156793325985793/images/img_1.png\" style='vertical-align:middle;' width=\"155\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市第九十中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-08", "keyPointIds": "16439|16501", "keyPointNames": "几何问题|坐标与图形性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584430146343444480", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584430146343444480", "title": "天津市第九十中学2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584430516507549696", "questionArticle": "<p>8．习近平总书记说：“读书可以让人保持思想活力，让人得到智慧启发，让人滋养浩然之气．”光明中学为提升学生的阅读品味，决定购买第十届茅盾文学奖的获奖篇目《北上》（徐则臣著）和《牵风记》（徐怀中著）两种书共 $ 50 $ 本．已知购买 $ 2 $ 本《北上》和 $ 1 $ 本《牵风记》需 $ 100 $ 元；购买 $ 6 $ 本《北上》与购买 $ 7 $ 本《牵风记》的价格相同．</p><p>（1）《北上》和《牵风记》每本的价格分别为多少元？</p><p>（2）若学校购买《北上》的数量多于 $ 17 $ 本，且购买两种书的总价不超过 $ 1600 $ 元，请问有几种购买方案？最低费用为多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西安市第三中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-06-08", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584430489080995840", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "584430489080995840", "title": "陕西省西安市西安市第三中学2024−2025学年八年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584854619274129408", "questionArticle": "<p>9．阅读与思考</p><p>阅读下列材料，完成下面的任务．</p><table style=\"border: solid 1px;border-collapse: collapse; width:272.25pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 272.25pt;\"><p>关于“三角形的内切圆”的研究报告</p><p>【研究内容】如图 $ 1 $ ，在 $ \\vartriangle ABC $ 中，三边 $ AB=c $ ， $ BC=a $ ， $ AC=b $ ， $ \\odot I $ 是它的内切圆，切点分别为 $ D $ ， $ E $ ， $ F $ ，如何求 $ AD $ 、 $ BD $ 、 $ CE $ 的长呢？</p><p>【解法】 $ \\because \\odot I $ 是 $ \\vartriangle ABC $ 的内切圆，切点为 $ D $ ， $ E $ ， $ F $ ， $ \\therefore AD=AF $ ， $ BD=BE $ ， $ CE=CF $ ．设 $ AD=AF=x $ ， $ BD=BE=y $ ， $ CE=CF=z $ ，则有 $ \\begin{cases} x+y=c \\\\ y+z=a \\\\ x+z=b \\end{cases}  $ ， $ \\therefore x+y+z=▲ $ ，如果设 $ p=▲ $ ，那么有 $ \\begin{cases} x=p-a \\\\ y=p-b \\\\ z=p-c \\end{cases}  $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/02/2/1/0/0/0/584854563120783362/images/img_22.png\" style=\"vertical-align:middle;\" width=\"143\" alt=\"试题资源网 https://stzy.com\"></p></td></tr></table><p>任务：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/02/2/1/0/0/0/584854563120783363/images/img_23.png\" style=\"vertical-align:middle;\" width=\"365\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）直接写出研究报告中“▲”处空缺的内容：<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>（2）如图 $ 2 $ ，这是一张三角形纸片 $ ABC $ ， $ \\odot O $ 为它的内切圆，小悦沿着与 $ \\odot O $ 相切的 $ DE $ 剪下了一个三角形纸片 $ BDE $ ，已知 $ AC=4{ \\rm{ c } }{ \\rm{ m } } $ ， $ AB=6{ \\rm{ c } }{ \\rm{ m } } $ ， $ BC=5{ \\rm{ c } }{ \\rm{ m } } $ ，求三角形纸片 $ BDE $ 的周长．</p><p>（3）如图 $ 3 $ ， $ \\vartriangle ABC $ 的内切圆 $ O $ 与 $ BC $ ， $ AB $ ， $ AC $ 分别相切于点 $ D $ ， $ E $ ， $ F $ ， $ \\angle A=90{^{\\circ }} $ ， $ BD=3 $ ， $ CD=2 $ ，求 $ S{{}_{ \\vartriangle ABC } } $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山西运城 · 临考冲刺", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-07", "keyPointIds": "16444|16672|16732", "keyPointNames": "三元一次方程组的应用|勾股定理|切线长定理", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584854584780173312", "questionFeatureName": "阅读材料题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "584854584780173312", "title": "2025年山西省运城市运康中学校中考三模数学模拟卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584854616442974208", "questionArticle": "<p>10．绿茵场！闪电突破！篮筐下！精准投射！热血在奔跑中沸腾！团队在配合中闪光！从2025年春季学期起，云南省义务教育学校课间休息时间全面调整为15分钟，为给学生们丰富课间活动资源，某校计划购买一批足球和篮球．若购买5个足球和8个篮球，需1350元，购买10个足球和4个篮球，需1200元．</p><p>（1）求每个足球、篮球的价格？</p><p>（2）若该校计划购买足球和篮球共120个，购买足球的数量不超过篮球数量的 $ \\dfrac { 4 } { 5 } $ 且不低于篮球数量的 $ \\dfrac { 3 } { 5 } $ ，为使购买的总费用<i>W</i>最低，应购买足球和篮球各多少个？最低总费用为多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山西运城 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-07", "keyPointIds": "16437|16490|16544", "keyPointNames": "销售利润问题|一元一次不等式组的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584854584780173312", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "584854584780173312", "title": "2025年山西省运城市运康中学校中考三模数学模拟卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 43, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 43, "timestamp": "2025-07-01T02:05:53.254Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}