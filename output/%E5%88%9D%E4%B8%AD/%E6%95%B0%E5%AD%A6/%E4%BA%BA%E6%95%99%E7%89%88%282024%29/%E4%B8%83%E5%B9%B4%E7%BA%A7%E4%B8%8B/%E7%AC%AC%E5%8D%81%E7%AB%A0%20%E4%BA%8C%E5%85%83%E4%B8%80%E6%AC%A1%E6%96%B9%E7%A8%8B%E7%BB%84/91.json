{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 90, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "575778666497482752", "questionArticle": "<p style=\"text-align: justify;\">1．在2025年3月14日下午，成都外国语学校初中部举办了第二届数学“ ${ \\rm{ π } }$ ”节活动——七年级“智趣数学，欢乐游园”游园会．筹备组教师从淘宝网购进魔方和踩雷对战棋共25个活动道具，其中魔方单价12元，踩雷对战棋单价15元．</p><p style=\"text-align: justify;\">（1）若合计采购费用为330元，求购买的魔方和踩雷对战棋各有多少个；</p><p style=\"text-align: justify;\">（2）若筹备组在购买魔方时遇到淘宝平台开展限时优惠活动：一次性购买魔方超过8个，超过部分可享受8折优惠．若筹备组希望保持总数量25个不变，总费用不超过330元，且魔方数量不超过踩雷对战棋数量的 $1.5$ 倍，共有几种购买方案，并列举所有可能的方案．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成外（CFLS） · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575778633274400768", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "575778633274400768", "title": "四川省成都外国语学校2024−2025学年下学期期中考试八年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575778809837826048", "questionArticle": "<p>2．解方程组：</p><p>（1） $ \\begin{cases} 2x+3y=8 \\\\ 3x-y=1 \\end{cases}  $ </p><p>（2） $ \\begin{cases} 3x-2y=7 \\\\ 5x-3y=8 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575778782834896896", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "575778782834896896", "title": "天津市第一中学滨海学校2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575778807459655680", "questionArticle": "<p>3．定义新运算：对于任意实数<i>a</i>，<i>b</i>都有 $ a\\mathrm{ ※ }b=am-bn $ ，等式右边是通常的减法和乘法运算，规定，若 $ 3※2=5,1※\\left ( { -2 } \\right ) =-1 $ ，则 $ \\left ( { -3 } \\right ) ※2 $ 的值为 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16278|16424", "keyPointNames": "有理数的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575778782834896896", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "575778782834896896", "title": "天津市第一中学滨海学校2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575778653381894144", "questionArticle": "<p>4．已知关于 $ x,y $ 的方程组 $ \\begin{cases} 2x+y=2k-1 \\\\ x+2y=-4 \\end{cases}  $ 的解满足 $ x+y > 1 $ ，则 $ k $ 的取值范围是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成外（CFLS） · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575778633274400768", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "575778633274400768", "title": "四川省成都外国语学校2024−2025学年下学期期中考试八年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574356359350624256", "questionArticle": "<p>5．如图1，有边长为 $ 20{ \\rm{ c } }{ \\rm{ m } } $ 的甲型正方形板材和长 $ 20{ \\rm{ c } }{ \\rm{ m } } $ ，宽 $ 60{ \\rm{ c } }{ \\rm{ m } } $ 的乙型长方形板材，可用于制作成如图2所示的无盖的横式（需两张甲型和三张乙型）和无盖的竖式（需一张甲型和四张乙型）两种箱子，制作过程中不计损耗．已知购买任何型号板材单价均为每平方厘米0.02元．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/04/2/1/0/0/0/574356299481128969/images/img_9.png\" style=\"vertical-align:middle;\" width=\"405\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)购买一张甲型正方形板材需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元，制作一个无盖的<point-tag>横式</point-tag>箱子需要花费<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元；</p><p>(2)若有甲型板材70张，乙型板材182张，用这批板材制作两种类型的箱子共50个，请问可有哪几种制作方案？</p><p>(3)若有甲型板材100张，乙型板材<i>m</i>张，做成上述两种箱子，板材恰好用完．已知 $ 220  &lt;  m  &lt;  232 $ ．请求出<i>m</i>所有可能的取值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽合肥 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16432|16489|16490", "keyPointNames": "配套问题|解一元一次不等式组|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574356332813262848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574356332813262848", "title": "安徽省合肥市包河区中国科学技术大学附属中学2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574352880292896768", "questionArticle": "<p>6．已知方程组 $ \\begin{cases} x+y=-5-2m \\\\ x-y=1+4m \\end{cases}  $ 的解满足<i>x</i>为非正数，<i>y</i>为负数．</p><p>(1)求<i>m</i>的取值范围；</p><p>(2)化简 $ |m+1|-|m-2| $ ；</p><p>(3)在<i>m</i>的取值范围内，且<i>m</i>为整数，当 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>时，不等式 $ mx-x  &lt;  m-1 $ 的解为 $ x  &lt;  1 $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南驻马店市第二初级中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16256|16424|16489", "keyPointNames": "化简绝对值|加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574352854812499968", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574352854812499968", "title": "河南省驻马店市第二初级中学2024−2025学年八年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575779892618375168", "questionArticle": "<p>7．为推动传统农业向智慧农业转型，某农场决定配备 $ A，B $ 两款施肥无人机共 $ 28 $ 架．每架 $ A $ 款施肥无人机需要 $ 2 $ 人协同操控，每架 $ B $ 款施肥无人机需要 $ 3 $ 人协同操控，农场负责施肥的操控人员共有 $ 68 $ 人．</p><p>（1）求 $ A $ 款施肥无人机和 $ B $ 款施肥无人机分别有多少架？</p><p>（2）该农场共有 $ 1440 $ 亩农田需要施肥， $ A，B $ 两款施肥无人机负责施肥亩数相同，已知每架 $ B $ 款施肥无人机每小时施肥亩数是每架 $ A $ 款施肥无人机每小时施肥亩数的 $ \\dfrac { 5 } { 3 } $ 倍，所有 $ B $ 款施肥无人机同时施肥比所有 $ A $ 款施肥无人机同时施肥提前 $ 1 $ 小时完成施肥，求每架 $ B $ 款施肥无人机每小时施肥多少亩？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-05-09", "keyPointIds": "16438|16476", "keyPointNames": "和差倍分问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585921710492262400", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "585921710492262400", "title": "重庆市垫江实验中学发展共同体2024−2025学年九年级下学期定时作业数学试题", "paperCategory": 1}, {"id": "575779847135342592", "title": "重庆市南开中学校2024−2025学年九年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574752590518329344", "questionArticle": "<p>8．《算法统宗》也是我国古代非常重要的数学名著，其中记载了一道题，原文：隔墙听得客分银，不知人数不知银，七两分之多四两，九两分之少半斤，几多客人几两银？大意为：有若干客人分银若干两，若每人分7两，则还多4两；若每人分9两，则不足8两．客人有多少？银有多少两？（题中斤、两是旧制质量单位，1斤 $ =16 $ 两），设客人有<i>x</i>人，银有<i>y</i>两，根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 7x-y=4 \\\\ 9x-y=8 \\end{cases}  $ B． $ \\begin{cases} y-7x=4 \\\\ 9x-y=8 \\end{cases}  $ C． $ \\begin{cases} 7x-y=4 \\\\ y-9x=8 \\end{cases}  $ D． $ \\begin{cases} y-7x=4 \\\\ y-9x=8 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-05-09", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574752576211558400", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574752576211558400", "title": "四川省成都市七中育才学校2024−2025学年九年级下学期4月模拟数学试题", "paperCategory": 1}, {"id": "532700550288154624", "title": "四川省达州市渠县中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574750177086447616", "questionArticle": "<p>9．《九章算术》中记载了一个问题，原文如下：“今有人共买物，人出八，盈三；人出七，不足四．问人数，物价各几何？”大意是：有几个人一起去买一件物品，每人出8文，多3文；每人出7文，少4文，求人数及该物品的价格，小明用二元一次方程组解决此问题，若已经列出一个方程 $ 8x-3=y $ ，则符合题意的另一个方程是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 7x-4=y $ B． $ 7x+4=y $ C． $ \\dfrac { x } { y }+4=y $ D． $ \\dfrac { x } { y }-4=y $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574750160636387328", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574750160636387328", "title": "北京市第五十七中学2024−2025学年七年级下学期数学期中试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574752470057918465", "questionArticle": "<p>10．近年来光伏建筑一体化广受关注．某社区拟修建<i>A</i>，<i>B</i>两种光伏车棚．已知修建2个<i>A</i>种光伏车棚和1个<i>B</i>种光伏车棚共需投资8万元，修建5个<i>A</i>种光伏车棚和3个<i>B</i>种光伏车棚共需投资21万元．</p><p>(1)求修建每个<i>A</i>种，<i>B</i>种光伏车棚分别需投资多少万元？</p><p>(2)若修建<i>A</i>，<i>B</i>两种光伏车棚共20个，要求修建的<i>A</i>种光伏车棚的数量不少于修建的<i>B</i>种光伏车棚数量的2倍，问修建多少个<i>A</i>种光伏车棚时，可使投资总额最少？最少投资总额为多少万元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|610000|410000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山东济南 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 24, "referenceNum": 4, "createTime": "2025-05-09", "keyPointIds": "16441|16486|16544", "keyPointNames": "其他问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "478612479330787328", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "478612479330787328", "title": "2024年山东省济南市中考数学试卷", "paperCategory": 1}, {"id": "574752443579277312", "title": "陕西省咸阳市实验中学2024−2025学年八年级下学期第二次质量检测数学试卷A", "paperCategory": 1}, {"id": "553370064310804480", "title": "2025年河南省郑州市高新区郑州中学九年级下学期第一次模拟数学试题", "paperCategory": 1}, {"id": "489166782076329984", "title": "湖南省长沙市湖南师大附中高新实验中学2024−2025学年九年级上学期入学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 91, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 91, "timestamp": "2025-07-01T02:11:35.499Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}