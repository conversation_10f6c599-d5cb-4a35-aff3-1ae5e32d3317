{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 110, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "570267030022037504", "questionArticle": "<p>1．方程 $ 2x{^{m-1}}+3y{^{2n-1}}=1 $ 是关于 $ x $ ， $ y $ 的二元一次方程，则 $ m-2n $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长郡 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267012338851840", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267012338851840", "title": "湖南省长沙市长郡教育集团2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570267026658205696", "questionArticle": "<p>2．我国明代数学家程大位 $ \\left ( { 1533\\mathrm{ — }1606 } \\right )  $ 所著《算法统宗》中记录了“二果问价”问题：九百九十九文钱，甜果苦果买一千．甜果九个十一文，苦果七个四文钱．试问甜苦果各几个？其大意是，用九百九十九文钱共买了一千个苦果和甜果，已知十一文钱可以买九个甜果，四文钱可以买七个苦果，那么苦果、甜果各买了多少个？设苦果有 $ x $ 个，甜果有 $ y $ 个，则可列二元一次方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=1000 \\\\ 7x+9y=999 \\end{cases}  $</p><p>B． $ \\begin{cases} x+y=1000 \\\\ \\dfrac { 4 } { 7 }x+\\dfrac { 11 } { 9 }y=999 \\end{cases}  $</p><p>C． $ \\begin{cases} x+y=1000 \\\\ \\dfrac { 7 } { 4 }x+\\dfrac { 9 } { 11 }y=999 \\end{cases}  $</p><p>D． $ \\begin{cases} x+y=1000 \\\\ 4x+11y=999 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长郡 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267012338851840", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267012338851840", "title": "湖南省长沙市长郡教育集团2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570267023025938432", "questionArticle": "<p>3．下面是二元一次方程 $ 2x-y=5 $ 的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=4 \\\\ y=3 \\end{cases}  $</p><p>B． $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $</p><p>C． $ \\begin{cases} x=1 \\\\ y=3 \\end{cases}  $</p><p>D． $ \\begin{cases} x=5 \\\\ y=4 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025湖南长郡 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267012338851840", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267012338851840", "title": "湖南省长沙市长郡教育集团2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570806742650494976", "questionArticle": "<p>4．“书香中国，读领未来”．4月23日是世界读书日，我市某书店计划同时购进 $ A $ ， $ B $ 两类图书．已知购进2本 $ A $ 类图书和3本 $ B $ 类图书共需88元；购进3本 $ A $ 类图书和2本 $ B $ 类图书共需92元．</p><p>(1)  $ A $ ， $ B $ 两类图书每本的进价各是多少元？</p><p>(2)该书店计划购进 $ A $ ， $ B $ 两类图书共100本，其中购进 $ A $ 类图书 $ m $ 本．已知书店计划将 $ A $ 类图书每本的售价定为33元， $ B $ 类图书每本的售价定为30元．</p><p>（ⅰ）假设所采购的 $ A $ ， $ B $ 两类图书能够全部销售完，该书店所获利润为 $ w $ 元．请你写出利润 $ w $ 与 $ m $ 之间的函数关系式；</p><p>（ⅱ）若 $ A $ 类图书的购进数量不少于40本，在（ⅰ）的条件下，该书店如何进货才能使本次销售所获的利润最大，最大利润是多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南长郡 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16441|16544", "keyPointNames": "其他问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570806717396590592", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570806717396590592", "title": "湖南省长郡教育集团2024−2025学年八年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570806866533457920", "questionArticle": "<p>5．综合与探究</p><p>【课本再现】</p><p>七年级下册教材 $ P115 $ 页中我们曾探究过“以方程 $ x-y=0 $ 的解为坐标的点的特性”，了解了二元一次方程的解与其图象上点的坐标的关系．我们知道，二元一次方程有无数个解，在平面坐标系中，我们标出以这个方程的解为坐标（ $ x $ 的值为横坐标， $ y $ 的值为纵坐标）的点，就会发现这些点在同一条直线上．例如： $ \\begin{cases} x=-1 \\\\ y=-1 \\end{cases}  $ ， $ \\begin{cases} x=2 \\\\ y=2 \\end{cases}  $ 是方程 $ x-y=0 $ 的解，对应点 $ A\\left ( { -1,-1 } \\right )  $ ， $ B\\left ( { 2,2 } \\right )  $ ．如图所示，我们在平面坐标系中将其标出，另外，方程的解还对应点 $ \\left ( { 3,3 } \\right )  $ ， $ \\left ( { 4,4 } \\right )  $ ……将这些点连起来正好是一条直线，反过来，在这条直线上任取一点，这个点的坐标也对应方程 $ x-y=0 $ 的解，所以我们把这条直线就叫做方程 $ x-y=0 $ 的图象．</p><p>结论：一般的，以一个二元一次方程的解为坐标的点的全体叫做这个方程的图象，任意一个二元一次方程的图象都是一条直线．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/28/2/1/0/0/0/572063876235173888/images/img_1.png\" style='vertical-align:middle;' width=\"242\" alt=\"试题资源网 https://stzy.com\"></p><p>【解决问题】</p><p>（1）已知 $ A\\left ( { 1,2 } \\right )  $ ， $ B\\left ( { -2,0 } \\right )  $ ， $ C\\left ( { -1,2 } \\right )  $ ，则点<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>（填“ $ A $ 或 $ B $ 或 $ C $ ”）在方程 $ 2x+3y=4 $ 的图象上．</p><p>（2）已知无论 $ a $ 为何值，关于 $ x $ ， $ y $ 的二元一次方程 $ ax+3\\left ( { y-a } \\right ) =1 $ 的图象都经过某一定点，且这个定点在方程 $ 2x+by=5 $ 的图象上，求 $ b $ 的值．</p><p>【拓展延伸】</p><p>（3）已知 $ m $ 为实数， $ k $ 为正整数，关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} \\dfrac { 4 } { 3 }\\left ( { x-\\dfrac { 3 } { 2 }m-4 } \\right ) +6y=0 \\\\ \\dfrac { kx-y+m } { 2 }=3y-m+10 \\end{cases}  $ 的解也为正整数，且以此方程组的解为坐标的点在方程 $ 2x+y=9 $ 的图象上，求 $ m $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025湖南长沙市雅礼教育集团 · 期中", "showQuestionTypeCode": "42", "showQuestionTypeName": "综合题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16420|16424|16540", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组|一次函数与二元一次方程（组）", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570806839450836992", "questionFeatureName": "类比探究题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570806839450836992", "title": "湖南省长沙市雅礼教育集团2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570806865258389504", "questionArticle": "<p>6．随着长沙中考政策的调整和课间时间的延长，为了丰富孩子们的课余生活，提高孩子们体育锻炼的积极性，吕老师准备给每班发一些乒乓球和跳绳．已知 $ 1 $ 盒乒乓球和 $ 1 $ 根跳绳 $ 40 $ 元， $ 2 $ 盒乒乓球和 $ 3 $ 根跳绳共计 $ 95 $ 元．</p><p>(1)求 $ 1 $ 盒乒乓球和 $ 1 $ 根跳绳的售价分别为多少元？</p><p>(2)若吕老师计划正好用 $ 200 $ 元零花钱购买以上两种体育器材，且每种都有购买，请通过计算说明有多少种购买方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南长沙市雅礼教育集团 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16434|16438", "keyPointNames": "方案问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570806839450836992", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570806839450836992", "title": "湖南省长沙市雅礼教育集团2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570806859277312000", "questionArticle": "<p>7．解方程组 $ \\begin{cases} ax+by=6 \\\\ cx-4y=-2 \\end{cases}  $ 时，小强正确解得 $ \\begin{cases} x=2 \\\\ y=2 \\end{cases}  $ ，而小刚只看错了 $ c $ ，解得 $ \\begin{cases} x=-2 \\\\ y=4 \\end{cases}  $ ，则 $ a-b+c $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南长沙市雅礼教育集团 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570806839450836992", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570806839450836992", "title": "湖南省长沙市雅礼教育集团2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570806860741124096", "questionArticle": "<p>8．解下列方程组：</p><p>(1) $ \\begin{cases} 3x+2y=120 \\\\ y=3x+6 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 3x+y=22 \\\\ 4(x+y)-5(x-y)=2 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南长沙市雅礼教育集团 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570806839450836992", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570806839450836992", "title": "湖南省长沙市雅礼教育集团2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570806980081655808", "questionArticle": "<p>9．莱西仙足山田园采摘节致力打造“桑葚采摘基地”，吸引了众多游客前来观货、采摘．为了扩大基地规模，计划购买甲、乙两种桑葚树苗共800株，甲种桑葚树苗每株24元，乙种桑葚树苗每株30元．相关资料表明：甲、乙两种桑葚树苗的成活率分别为85%，90%．</p><p>(1)若购买这两种桑葚树苗共用去21000元，则甲、乙两种桑葚树苗各购买了多少株？</p><p>(2)若要使这批桑葚树苗的总成活率不低于88%，则甲种桑葚树苗至多购买多少株？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江西赣州 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16441|16486", "keyPointNames": "其他问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570806958673928192", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "570806958673928192", "title": "江西省赣州市2024−2025学年九年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570807091322986496", "questionArticle": "<p>10．已知<i>x</i>，<i>y</i>满足 $ 2x+y=3 $ ，且 $ x > -2 $ ， $ y > 2 $ ．若 $ k=x-y $ ，则<i>k</i>的取值范围是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏淮安 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16424|16426|16489", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570807072847077376", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570807072847077376", "title": "江苏省淮安市开明中学八校联考2024−2025学年下学期九年级4月期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 111, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 111, "timestamp": "2025-07-01T02:13:54.996Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}