{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 13, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "593050955270103040", "questionArticle": "<p>1．（1）计算： $ \\left  | { -\\dfrac { 1 } { 2 } } \\right  | \\times 6-3{^{2}}+(-8+4) $     ;</p><p>（2）解方程组： $ \\begin{cases} 3x-2y=11① \\\\ x+2y=1② \\end{cases}  $ .</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西 · 中考真题", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16278|16424", "keyPointNames": "有理数的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593050926140661760", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "593050926140661760", "title": "2025年山西省中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592873595832934400", "questionArticle": "<p>2．“保护好环境，拒绝冒黑烟”，某市公交公司将淘汰某一条线路上“冒黑烟”较严重的公交车，计划购买 $ \\mathrm{ A } $ 型和 $ B $ 型两种环保节能公交车共 $ 10 $ 辆，若购买 $ \\mathrm{ A } $ 型公交车 $ 1 $ 辆， $ B $ 型公交车 $ 2 $ 辆，共需 $ 400 $ 万元：若购买 $ \\mathrm{ A } $ 型公交车 $ 2 $ 辆， $ B $ 型公交车 $ 1 $ 辆，共需 $ 350 $ 万元．</p><p>（1）求购买 $ \\mathrm{ A } $ 型和 $ B $ 型公交车每辆各需多少万元？</p><p>（2）预计在该线路上 $ \\mathrm{ A } $ 型和 $ B $ 型公交车每辆年均载客量分别为 $ 60 $ 万人次和 $ 100 $ 万人次，若该公司购买 $ \\mathrm{ A } $ 型和 $ B $ 型公交车的总费用不超过 $ 1200 $ 万元，且确保这 $ 10 $ 辆公交车在该线路的年均载客总和不少于 $ 680 $ 万人次，则该公司有哪几种购车方案？哪种购车方案总费用最少？最少总费用是多少？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024湖北随州 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16434|16490", "keyPointNames": "方案问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592873568771284992", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592873568771284992", "title": "湖北省随州市2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592873579194130432", "questionArticle": "<p>3．贝贝解二元一次方程组 $ \\begin{cases} x+py=2, \\\\ x+y=1, \\end{cases}  $ 得到的解是 $ \\begin{cases} x=\\dfrac { 1 } { 2 } \\\\ y=Δ \\end{cases}  $ ，其中<i>y</i>的值被墨水盖住了，不过她通过验算求出了<i>y</i>的值，进而解得<i>p</i>的值为&nbsp;&nbsp;&nbsp;（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\dfrac { 1 } { 2 } $ B．1C．2D．3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024湖北随州 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592873568771284992", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592873568771284992", "title": "湖北省随州市2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592873368778485760", "questionArticle": "<p>4．淮安香肠历史悠久，是闻名全国的香肠品种之一．某超市分别以18元/袋、30元/袋的价格购进<i>A</i>，<i>B</i>两种规格的淮安香肠销售，近两天的销售情况如表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售时段</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售数量</p></td><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售收入</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>A</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>B</i></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第一天</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>10袋</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>6袋</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>570元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第二天</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>5袋</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>8袋</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>510元</p></td></tr></table><p>（说明：本题中，<i>A</i>，<i>B</i>两种规格淮安香肠的进价、售价均保持不变）</p><p>（1）求<i>A</i>，<i>B</i>两种规格香肠的销售单价；</p><p>（2）若该超市准备用不超过1800元再购进这两种规格香肠共80袋，求<i>B</i>规格香肠最多能采购多少袋？</p><p>（3）在（2）的条件下，销售完这80袋香肠，能否实现利润为1065元的目标？若能，直接写出相应的采购方案；若不能，请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024江苏淮安 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16406|16438|16440|16486", "keyPointNames": "销售盈亏问题|和差倍分问题|表格或图示问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592873339451912192", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592873339451912192", "title": "江苏省淮安市外国语学校2023−2024学年七年级下学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592873234434924544", "questionArticle": "<p>5．某学校计划购进一批电脑和电子白板，经过市场考察得知，购买1台电脑和2台电子白板需要3.5万元，购买2台电脑和1台电子白板需要2.5万元．</p><p>（1）求购买一台电脑和一台电子白板各需多少万元？</p><p>（2）根据学校实际，需购进电脑和电子白板共30台，总费用不高于30万元，但电脑的数量低于20台，请你通过计算求出有几种购买方案，哪种方案费用最低？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16434|16486", "keyPointNames": "方案问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592873207272611840", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "592873207272611840", "title": "江苏省南京联合体 2024~2025学年下学期七年级数学期末练习卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592873230420975616", "questionArticle": "<p>6．解方程组：</p><p>（1） $ \\begin{cases} 2x+3y=3 \\\\ x=2-y \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} 3x-4y=5 \\\\ 5x-2y=6 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592873207272611840", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "592873207272611840", "title": "江苏省南京联合体 2024~2025学年下学期七年级数学期末练习卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592873362113736704", "questionArticle": "<p>7．（1）解方程组 $ \\begin{cases} 2x-y=5 \\\\ 3x+4y=2 \\end{cases}  $ ；</p><p>（2）解不等式组 $ \\begin{cases} x-3\\left ( { x-2 } \\right ) \\geqslant  4 \\\\ \\dfrac { 1+2x } { 3 } &gt; x-1 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏淮安 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592873339451912192", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592873339451912192", "title": "江苏省淮安市外国语学校2023−2024学年七年级下学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592873226495107072", "questionArticle": "<p>8．已知关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} 2x+3y=a \\\\ 3x+4y=b \\end{cases}  $ 的解是 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ ，则关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2\\left ( { x+2025 } \\right ) +3\\left ( { y-2025 } \\right ) =a \\\\ 3\\left ( { x+2025 } \\right ) +4\\left ( { y-2025 } \\right ) =b \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏南京 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592873207272611840", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "592873207272611840", "title": "江苏省南京联合体 2024~2025学年下学期七年级数学期末练习卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592873220711161856", "questionArticle": "<p>9．《九章算术》是中国古代最重要的数学经典之一，其中记载了这样一个问题：“良马日行二百四十里，驽马日行一百五十里，驽马先行十二日，问良马几何日追及之？”其大意为：良马每天行240里，驽马每天行150里，如果驽马先出发12天，那么良马几天能够追上驽马？若设良马需 $ x $ 天追上，追上时驽马共行 $ y $ 天，根据题意，则可列出关于 $ x，y $ 的二元一次方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 240y=150x \\\\ y=x-12 \\end{cases}  $　　　　B． $ \\begin{cases} 240\\left ( { y-x } \\right ) =150y \\\\ y=x+12 \\end{cases}  $</p><p>C． $ \\begin{cases} 240x=150y \\\\ y=x+12 \\end{cases}  $　　　　D． $ \\begin{cases} 240y=150\\left ( { y-x } \\right )  \\\\ y=x-12 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592873207272611840", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "592873207272611840", "title": "江苏省南京联合体 2024~2025学年下学期七年级数学期末练习卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590999318930169856", "questionArticle": "<p>10．为迎接校园科技节的到来，学校科技社团欲购买甲、乙两种模型进行组装，已知3套甲模型的总价与2套乙模型的总价相等，若购买1套甲模型和2套乙模型共需80元．</p><p>（1）求甲、乙两种模型的单价各是多少元？</p><p>（2）现计划用1220元资金，在不超过预算的情况下，购买这两种模型共50套，且乙种模型的数量不少于甲种模型数量的 $ \\dfrac { 2 } { 3 } $ ，求两种模型共有多少种选购方案，写出具体方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏扬州 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16438|16490", "keyPointNames": "和差倍分问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590999288739569664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590999288739569664", "title": "江苏高邮市南海中学2024−2025学年七年级下学期数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 14, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 14, "timestamp": "2025-07-01T02:02:26.372Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}