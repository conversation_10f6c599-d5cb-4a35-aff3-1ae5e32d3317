{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 50, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "580244080497569792", "questionArticle": "<p>1．下列方程中是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 6x-y-z=7 $ B． $ x+4y=6 $ C． $ 4x+9=0 $ D． $ x-\\dfrac { 2 } { y }=1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025福建厦门 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-03", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580244070536097792", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580244070536097792", "title": "福建省厦门市松柏中学2024−2025学年下学期七年级数学期中考卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "584852549942947840", "questionArticle": "<p>2．嘉嘉坚持每天做运动．已知某两组运动都由波比跳和深蹲组成，每个波比跳耗时5秒，每个深蹲也耗时5秒．运动软件显示，完成第一组运动，嘉嘉做了20个波比跳和40个深蹲，共消耗热量132大卡；完成第二组运动，嘉嘉做了20个波比跳和70个深蹲，共消耗热量156大卡．每个动作之间的衔接时间忽略不计．</p><p>（1）每个波比跳和每个深蹲各消耗热量多少大卡？</p><p>（2）若嘉嘉只做波比跳和深蹲两个动作，花10分钟，消耗至少200大卡，嘉嘉至少要做多少个波比跳？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东东莞 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-06-03", "keyPointIds": "16435|16486", "keyPointNames": "分配问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584852515058921472", "questionMethodName": "函数与方程思想", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584852515058921472", "title": "2025年广东省东莞市中考二模数学试题", "paperCategory": 1}, {"id": "220495970462638080", "title": "河南省许昌市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584852639126433792", "questionArticle": "<p>3．《孙子算经》是中国古代重要的数学著作，纸书大约在一千五百年前，其中一道题，原文是：“今三人共车，两车空；二人共车，九人步．问人与车各几何？”意思是：现有若干人和车，若每辆车乘坐3人，则空余两辆车：若每辆车乘坐2人，则有9人步行，问人与车各多少？设有x人，y辆车，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} \\dfrac { x } { 3 }=y+2 \\\\ \\dfrac { x } { 2 }+9=y \\end{cases}  $ B． $ \\begin{cases} \\dfrac { x } { 3 }=y-2 \\\\ \\dfrac { x-9 } { 2 }=y \\end{cases}  $ C． $ \\begin{cases} \\dfrac { x } { 3 }=y+2 \\\\ \\dfrac { x-9 } { 2 }=y \\end{cases}  $ D． $ \\begin{cases} \\dfrac { x } { 3 }=y-2 \\\\ \\dfrac { x } { 2 }-9=y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|500000|610000|360000|530000|410000|520000|340000|450000|420000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2020山东临沂 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 14, "createTime": "2025-06-03", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "207138087083745280", "questionFeatureName": "数学文化题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "207138087083745280", "title": "山东省临沂市2020年中考数学试题", "paperCategory": 1}, {"id": "584852622353412096", "title": "2025年贵州省毕节市中考二模数学试题", "paperCategory": 1}, {"id": "331912124996820992", "title": "广西壮族自治区南宁市兴宁区第三中学2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "344279184531824640", "title": "江西省南昌市东湖区南昌市第三中学2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "208544269614751744", "title": "重庆市九龙坡区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "208523417179430912", "title": "河南省洛阳市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "202446505990463488", "title": "河南省河南师范大学附属中学2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "210007100197478400", "title": "山东省临沂市兰陵县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "201758161077837824", "title": "湖南省娄底市娄星区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "164746236808962048", "title": "湖南省长沙市雨花区南雅中学2021-2022学年九年级上学期第一次月考数学试题", "paperCategory": 1}, {"id": "161967034363846656", "title": "湖北省枣阳市2021年中考模拟性考试数学试题", "paperCategory": 1}, {"id": "202461030915547136", "title": "陕西省安康市汉阴县2020-2021学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "181379021292216320", "title": "云南省云南师范大学附属润城学校2020-2021学年七年级下学期4月月考数学试题", "paperCategory": 1}, {"id": "161403646499921920", "title": "安徽省合肥市肥东县2021年九年级中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "581951774828834816", "questionArticle": "<p>4．根据以下素材，完成项目任务：</p><table style=\"border: solid 1px;border-collapse: collapse; width:415.5pt;\"><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">有关教辅图书的素材</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 35.25pt;\"><p style=\"text-align:center;\">素材1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 380.25pt;\"><p>因材施教、分层作业是实施国家“双减”政策的重要手段．新华书店为该校九年级学生提供了 $ \\mathrm{ A } $ 、 $ B $ 两种难易程度不同的数学复习资料，已知每本 $ \\mathrm{ A } $ ， $ B $ 种资料的定价和为105元．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 35.25pt;\"><p style=\"text-align:center;\">素材2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 380.25pt;\"><p>小明按定价计算发现3本 $ \\mathrm{ A } $ 种资料的总价与4本 $ B $ 种资料的总价相同．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 35.25pt;\"><p style=\"text-align:center;\">素材3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 380.25pt;\"><p>新华书店规定： $ \\mathrm{ A } $ 种资料按定价的7折出售， $ B $ 种资料按定价的8折出售．九二班共40人，购买 $ \\mathrm{ A } $ 种资料的有30人，其余人购买 $ B $ 种资料．</p></td></tr><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 35.25pt;\"><p style=\"text-align:center;\">任务1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 380.25pt;\"><p>设 $ \\mathrm{ A } $ 种资料每本的定价为 $ x $ 元， $ B $ 种资料每本的定价为 $ y $ 元，请根据素材1，则 $ y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>（用含 $ x $ 的代数式）．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 35.25pt;\"><p style=\"text-align:center;\">任务2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 380.25pt;\"><p>基于素材1和素材2的信息，求 $ \\mathrm{ A } $ 、 $ B $ 两种资料每本的定价各是多少元．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 35.25pt;\"><p style=\"text-align:center;\">任务3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 380.25pt;\"><p>请你计算九二班这次购买资料共付新华书店<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽淮南 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-02", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581951729480019968", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581951729480019968", "title": "2025年安徽省淮南市部分学校中考三模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583056892492034048", "questionArticle": "<p>5．定义：在平面直角坐标系 $ xOy $ 中，若点 $ M\\left ( { a,b } \\right )  $ 与 $ N\\left ( { a^{′},b^{′} } \\right )  $ 的坐标满足 $ a^{′}=a+kb $ ， $ b^{′}=b+ka $ （ $ k $ 为常数， $ k\\ne 0 $ ），则称点 $ M $ 的“ $ k $ 系友好点”是点 $ N $ ．例如，点 $ \\left ( { 2,0 } \\right )  $ 的“1系友好点”是点 $ \\left ( { 2,2 } \\right )  $ ．</p><p>（1）点 $ \\left ( { 0,0 } \\right )  $ 的“1系友好点”的坐标是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，若一个点的“2系友好点”的坐标是 $ \\left ( { -6,0 } \\right )  $ ，则这个点的坐标是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）点 $ Q{{}_{ 1 } } $ 的坐标为 $ \\left ( { 1,3 } \\right )  $ ，点 $ Q{{}_{ 2 } } $ 是点 $ Q{{}_{ 1 } } $ 的“ $ -2 $ 系友好点”，点 $ Q{{}_{ 3 } } $ 是点 $ Q{{}_{ 2 } } $ 的“ $ -2 $ 系友好点”，以此类推，点 $ Q{{}_{ n+1 } } $ 是点 $ Q{{}_{ n } } $ 的“ $ -2 $ 系友好点”．若 $ m $ 为正整数，则点 $ Q{{}_{ 2m } } $ 的横坐标与纵坐标之和为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（3）已知点 $ A\\left ( { x,y } \\right )  $ 在第二象限，且满足 $ xy=-1 $ ，点 $ A $ 是点 $ B\\left ( { m,n } \\right )  $ 的“ $ -1 $ 系友好点”，求点 $ A $ 的坐标；</p><p>（4）点 $ P\\left ( { 0,t } \\right )  $ 在 $ y $ 轴正半轴上，点 $ P $ 的“ $ k $ 系友好点”为点 $ P^{′} $ ，若无论 $ t $ 为何值， $ OP+k\\cdot PP^{′} $ 的值为一个定值，求实数 $ k $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025广东执信中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-06-02", "keyPointIds": "16424|16497", "keyPointNames": "加减消元法解二元一次方程组|点的坐标", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583056859315089408", "questionFeatureName": "新定义问题", "questionMethodName": "分类讨论思想", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "583056859315089408", "title": "广东省广州市执信中学2024-2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583056889853816832", "questionArticle": "<p>6．科技节期间，小智负责记录班级购买实验耗材 $ \\mathrm{ A } $ 和 $ B $ 的情况（两次采购单价相同，且按整件购买），第一天购买7件 $ \\mathrm{ A } $ 和4件 $ B $ ，小智记为189元；第二天购买5件 $ \\mathrm{ A } $ 和2件 $ B $ ，小智记为84元．</p><p>（1）学习委员检查后指出小智记录矛盾，请通过计算说明错误原因；</p><p>（2）修正数据后，根据正确数据算得 $ \\mathrm{ A } $ 的价格为每件15元， $ B $ 的价格为每件21元．另一班级用300元以同样价格购买这两种实验耗材（要求两种实验耗材均需购买）．请求出所有满足条件的购买方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东执信中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-02", "keyPointIds": "16420|16438", "keyPointNames": "二元一次方程的解|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583056859315089408", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583056859315089408", "title": "广东省广州市执信中学2024-2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583056884380250112", "questionArticle": "<p>7．对于两个整数 $ a $ 和 $ b $ ，定义一种新运算“ $ { \\rm{ Δ } } $ ”，若 $ a+b $ 为偶数，则 $ a\\Delta b=3a-b $ ；若 $ a+b $ 为奇数，则 $ a{ \\rm{ Δ } }b=a+2b $ ．若对整数 $ m $ 和 $ n $ ，有 $ \\left ( { 4n-2 } \\right ) { \\rm{ Δ } }\\left [ \\left ( { m-1 } \\right ) { \\rm{ Δ } }m \\right ] =-2 $ ，且 $ m\\Delta \\left ( { 2n+1 } \\right ) =6 $ ，则 $ m $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东执信中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-02", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583056859315089408", "questionFeatureName": "新定义问题", "questionMethodName": "分类讨论思想", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583056859315089408", "title": "广东省广州市执信中学2024-2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583056883260370944", "questionArticle": "<p>8．关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} 4x-y=7m+1 \\\\ x-4y=3m-6 \\end{cases}  $ 的解满足 $ x-y=-1 $ ，则 $ m $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津二十五中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-06-02", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934467113857024", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "589934467113857024", "title": "天津市南开区第二十五中学2024-2025学下学期5月月考七年级数学试卷", "paperCategory": 1}, {"id": "583056859315089408", "title": "广东省广州市执信中学2024-2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583056885961502720", "questionArticle": "<p>9．解二元一次方程组： $ \\begin{cases} 2x-y=9 \\\\ 3x+2y=10 \\end{cases}  $  </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东执信中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-02", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583056859315089408", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583056859315089408", "title": "广东省广州市执信中学2024-2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583056876729839616", "questionArticle": "<p>10．某服装厂用某种布料生产玩偶 $ \\mathrm{ A } $ 与玩偶 $ B $ 组合成一批玩具礼盒，一个玩具礼盒搭配1个玩偶 $ \\mathrm{ A } $ 和2个玩偶 $ B $ ，已知每米布料可做2个玩偶 $ \\mathrm{ A } $ 或3个玩偶 $ B $ ，现计划用136米这种布料生产这批盲盒（不考虑布料的损耗），设用 $ x $ 米布料做玩偶 $ \\mathrm{ A } $ ，用 $ y $ 米布料做玩偶 $ B $ ，使得恰好配套，则下列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2x+3y=136 \\\\ x=2y \\end{cases}  $ B． $ \\begin{cases} x+y=136 \\\\ 3x=2y \\end{cases}  $ </p><p>C． $ \\begin{cases} 2x+3y=136 \\\\ 2x=3y \\end{cases}  $ D． $ \\begin{cases} x+y=136 \\\\ 4x=3y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东执信中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-02", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583056859315089408", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583056859315089408", "title": "广东省广州市执信中学2024-2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 51, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 51, "timestamp": "2025-07-01T02:06:51.484Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}