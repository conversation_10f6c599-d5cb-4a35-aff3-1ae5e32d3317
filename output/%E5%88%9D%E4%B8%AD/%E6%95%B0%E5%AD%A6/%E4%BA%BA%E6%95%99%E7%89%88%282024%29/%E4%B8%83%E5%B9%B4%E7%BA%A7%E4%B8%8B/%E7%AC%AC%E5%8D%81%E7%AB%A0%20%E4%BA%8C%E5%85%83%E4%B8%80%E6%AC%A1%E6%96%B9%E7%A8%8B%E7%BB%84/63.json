{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 62, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "579472845585035264", "questionArticle": "<p>1．用大小、形状完全相同的长方形纸片在平面直角坐标系中摆成如图所示图案，已知点 $ A\\left ( { -1,3 } \\right )  $ ，则点 $ B $ 的坐标为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/19/2/1/0/0/0/579472792300593157/images/img_8.png\" style=\"vertical-align:middle;\" width=\"145\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川德阳 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16439|16497", "keyPointNames": "几何问题|点的坐标", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579472824403800064", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "579472824403800064", "title": "四川省德阳市第二中学校2024-2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580244544949624832", "questionArticle": "<p>2．为响应“全民植树增绿，共建美丽中国”的号召，学校组织学生到郊外参加义务植树活动，并准备了 $ \\mathrm{ A } $ ， $ B $ 两种食品作为午餐．这两种食品每包质量均为 $ 50{ \\rm{ g } } $ ，营养成分表如下．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ \\mathrm{ A } $ 营养成分表</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ B $ 营养成分表</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>项目</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>每 $ 50{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>项目</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>每 $ 50{ \\rm{ g } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>热量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 700{ \\rm{ K } }{ \\rm{ J } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>热量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 900{ \\rm{ K } }{ \\rm{ J } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>蛋白质</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 10{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>蛋白质</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 15{ \\rm{ g } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>脂肪</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 5.3{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>脂肪</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 18.2{ \\rm{ g } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>碳水化合物</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 28.7{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>碳水化合物</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 6.3{ \\rm{ g } } $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>钠</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 205{ \\rm{ m } }{ \\rm{ g } } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>钠</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 236{ \\rm{ m } }{ \\rm{ g } } $ </p></td></tr></table><p>（1）若要从这两种食品中摄入 $ 4600{ \\rm{ k } }{ \\rm{ J } } $ 热量和 $ 70{ \\rm{ g } } $ 蛋白质，应选用 $ \\mathrm{ A } $ ， $ B $ 两种食品各多少包？</p><p>（2）运动量大的人或青少年对蛋白质的摄入量应更多．若每份午餐选用这两种食品共 $ 7 $ 包，要使每份午餐中的蛋白质含量不低于 $ 90{ \\rm{ g } } $ ，且热量最低，应如何选用这两种食品？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16440|16486|16547", "keyPointNames": "表格或图示问题|一元一次不等式的应用|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580244518345154560", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580244518345154560", "title": "广东省深圳市65校联考2024—2025学年下学期八年级期中质量检测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579472844729397248", "questionArticle": "<p>3．在解关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} ax-2by=8① \\\\ 2x=by+2② \\end{cases}  $ 时，小明由于将方程①的“ $ - $ ”看成了“ $ + $ ”，因而得到的解为 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ ，则原方程组的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川德阳 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579472824403800064", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "579472824403800064", "title": "四川省德阳市第二中学校2024-2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579472848432967680", "questionArticle": "<p>4．计算、解二元一次方程组：</p><p>（1） $ \\sqrt { 9 }+\\left  | { -\\sqrt { 2 } } \\right  | +\\sqrt[3] { -8 }-{\\left( { -1 } \\right) ^ {2024}}-4\\div \\left ( { -2 } \\right )  $ ；</p><p>（2） $ -1{^{3}}+\\sqrt { {\\left( { -2 } \\right) ^ {2}} }-\\sqrt[3] { 27 }+\\left  | { \\sqrt { 3 }-2 } \\right  |  $ ；</p><p>（3） $ \\begin{cases} y=x-7 \\\\ 2x-5y=11 \\end{cases}  $ ；</p><p>（4） $ \\begin{cases} 2x+3y=-1 \\\\ 4x-9y=8 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川德阳 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16290|16299|16423|16424", "keyPointNames": "立方根|实数的运算|代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579472824403800064", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "579472824403800064", "title": "四川省德阳市第二中学校2024-2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579472839171944448", "questionArticle": "<p>5．下列命题中是真命题的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>①实数与数轴上的点一一对应；</p><p>②二元一次方程 $ 2x+y=3 $ 的正整数解只有一个；</p><p>③从直线外一点作这条直线的垂线段，叫作这点到这条直线的距离；</p><p>④对顶角相等；</p><p>⑤在同一平面内，过一点有且只有一条直线与已知直线平行．</p><p>A．①②④B．①③④C．②③⑤D．③④⑤</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川德阳 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16296|16420|16615|16617", "keyPointNames": "实数与数轴|二元一次方程的解|点到直线的距离|对顶角、邻补角", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579472824403800064", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579472824403800064", "title": "四川省德阳市第二中学校2024-2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579472835745198080", "questionArticle": "<p>6．列方程组解古算题：“今有甲、乙二人持钱不知其数．甲得乙半而钱五十，乙得甲太半而亦钱五十．甲、乙持钱各几何？”题目大意是：甲、乙两人各带了若干钱．如果甲得到乙所有钱的一半，那么甲共有钱50．如果乙得到甲所有钱的 $ \\dfrac { 2 } { 3 } $ ，那么乙也共有钱50．甲、乙两人各带了多少钱？设甲带钱的数量为 $ x $ ，乙带钱的数量为 $ y $ ，则可列方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-\\dfrac { 1 } { 2 }y=50 \\\\ y-\\dfrac { 2 } { 3 }x=50 \\end{cases}  $ B． $ \\begin{cases} 2x+y=50 \\\\ x+\\dfrac { 2 } { 3 }y=50 \\end{cases}  $ </p><p>C． $ \\begin{cases} 2x-y=50 \\\\ x-\\dfrac { 2 } { 3 }y=50 \\end{cases}  $ D． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=50 \\\\ y+\\dfrac { 2 } { 3 }x=50 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川德阳 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579472824403800064", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "579472824403800064", "title": "四川省德阳市第二中学校2024-2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578372222101790720", "questionArticle": "<p>7．为了丰富学生的阅读资源，某校图书馆准备采购文学名著和人物传记两类图书．所采购的文学名著价格都一样，所采购的人物传记价格都一样．经了解，30本文学名著和20本人物传记共需1150元，10本文学名著比10本人物传记多50元．</p><p>（1）求每本文学名著和人物传记各多少元？</p><p>（2）根据学校图书馆的采购计划，拟用1500元预算购买文学名著和人物传记各40本，请通过计算判断此次采购总费用是否在预算内？若经费不足，还需追加多少资金？</p><p>（3）图书馆存书不足，学校要求再次购进两种图书，购买的文学名著比人物传记多20本，总费用不超过2000元，请求出人物传记至多买多少本？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东济南 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 11, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578372177709277184", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "578372177709277184", "title": "2025年山东省济南天桥区九年级二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579472985217609728", "questionArticle": "<p>8． $ a、b $  为常数，且对任何实数 $ x $ 都有 $ \\dfrac { x{^{2}}+3 } { \\left ( { x{^{2}}+1 } \\right ) \\left ( { x{^{2}}+2 } \\right )  }=\\dfrac { a } { x{^{2}}+1 }+\\dfrac { b } { x{^{2}}+2 } $ 成立，则 $ b{^{a}} $  = </p><p><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川内江 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16369|16424", "keyPointNames": "分式的加减|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579472958621528064", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579472958621528064", "title": "四川省内江市第一中学2024−2025学年八年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579473681518211072", "questionArticle": "<p>9．兄弟二人骑车同时从甲地到乙地，弟弟在前一半路程每小时行4千米，后一半路程每小时行6千米．哥哥按时间分段行驶，前 $ \\dfrac { 1 } { 3 } $ 时间每小时行4千米，中间 $ \\dfrac { 1 } { 3 } $ 时间每小时行5千米，后 $ \\dfrac { 1 } { 3 } $ 时间每小时行6千米，结果哥哥比弟弟早到20分钟．甲乙两地相距多少千米？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆朝高 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579473654632722432", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579473654632722432", "title": "重庆朝阳中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579473678347317248", "questionArticle": "<p>10．解下列方程（组）：</p><p>（1） $ \\dfrac { x+1 } { 2 }-1=2x-\\dfrac { 5-x } { 4 } $ </p><p>（2） $ \\begin{cases} 2x+y=4 \\\\ 7x+3y=15 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆朝高 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579473654632722432", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579473654632722432", "title": "重庆朝阳中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 63, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 63, "timestamp": "2025-07-01T02:08:15.167Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}