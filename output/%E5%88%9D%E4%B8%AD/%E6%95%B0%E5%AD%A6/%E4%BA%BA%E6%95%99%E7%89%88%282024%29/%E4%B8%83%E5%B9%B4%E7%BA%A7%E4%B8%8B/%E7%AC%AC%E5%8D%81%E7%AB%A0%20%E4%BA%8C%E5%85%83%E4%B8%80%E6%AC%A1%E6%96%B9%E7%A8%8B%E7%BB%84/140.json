{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 139, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "564938369630773248", "questionArticle": "<p>1．若 $ \\left ( { m-3 } \\right ) x+2y{^{\\left  | { m-2 } \\right  | }}+8=0 $ 是关于 $ x $ ， $ y $ 的二元一次方程，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏扬州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16258|16419", "keyPointNames": "绝对值方程|二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564938353419788288", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564938353419788288", "title": "江苏省扬州市梅岭中学2023−2024学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564938741258690560", "questionArticle": "<p>2．为确保信息安全，信息需加密传输，发送方由明文→密文（加密）；接收方由密文→明文（解密）.已知加密规则为：明文a，b，c，d对应密文， $ { \\rm{ a } }+2{ \\rm{ b } } $ ， $ 2{ \\rm{ b } }+{ \\rm{ c } } $ ， $ 2{ \\rm{ c } }+3{ \\rm{ d } } $ ， $ 4{ \\rm{ d } } $ .例如：明文1，2，3，4对应的密文5，7，18，16.当接收方收到密文14，9，23，28时，则解密得到的明文为【 】</p><p>A．4，6，1，7B．4，1，6，7C．6，4，1，7D．1，6，4，7</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁铁岭 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564938727044194304", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564938727044194304", "title": "辽宁省铁岭市2023−2024学年七年级下学期期中数学考试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564938740436606976", "questionArticle": "<p>3．若方程组 $ \\begin{cases} 2a-3b=13 \\\\ 3a+5b=30.9 \\end{cases}  $ 的解是 $ \\begin{cases} a=8.3 \\\\ b=1.2 \\end{cases}  $ ，则方程组 $ \\begin{cases} 2(x+2)-3(y-1)=13 \\\\ 3(x+2)+5(y-1)=30.9 \\end{cases}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=8.3 \\\\ y=1.2 \\end{cases}  $ B． $ \\begin{cases} x=10.3 \\\\ y=2.2 \\end{cases}  $ C． $ \\begin{cases} x=10.3 \\\\ y=0.2 \\end{cases}  $ D． $ \\begin{cases} x=6.3 \\\\ y=2.2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁铁岭 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564938727044194304", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564938727044194304", "title": "辽宁省铁岭市2023−2024学年七年级下学期期中数学考试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "565946273406164992", "questionArticle": "<p>4． 为倡导健康环保，自带水杯已成为一种好习惯，某超市销售甲，乙两种型号水杯，进价和售价均保持不变，其中甲种型号水杯进价为25元/个，乙种型号水杯进价为45元/个，下表是前两月两种型号水杯的销售情况：</p><table style=\"border: solid 1px;border-collapse: collapse; width:400.5pt;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 45pt;\"><p>时间</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 132pt;\"><p>销售数量（个）</p></td><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 223.5pt;\"><p>销售收入（元）（销售收入＝售价×销售数量）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>甲种型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p>乙种型号</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 45pt;\"><p>第一月</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>22</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p>8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 223.5pt;\"><p>1100</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 45pt;\"><p>第二月</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>38</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p>24</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 223.5pt;\"><p>2460</p></td></tr></table><p>（1）求甲、乙两种型号水杯的售价；</p><p>（2）第三月超市计划再购进甲、乙两种型号水杯共80个，这批水杯进货的预算成本不超过2600元，且甲种型号水杯最多购进55个，在80个水杯全部售完的情况下设购进甲种号水杯<i>a</i>个，利润为<i>w</i>元，写出<i>w</i>与<i>a</i>的函数关系式，并求出第三月的最大利润．</p><p>25<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/11/2/1/0/0/0/565946227977658368/images/img_25.png\" style=\"vertical-align:middle;\" width=\"3\" alt=\"试题资源网 https://stzy.com\"> 已知抛物线 $ y=-x{^{2}}+mx+3 $ 与<i>x</i>轴交于 $ A,B $ 两点，与<i>y</i>轴交于点<i>C</i>，若 $ OA=OC $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/11/2/1/0/0/0/565946227977658369/images/img_26.png\" style=\"vertical-align:middle;\" width=\"260\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）求抛物线的函数表达式和点<i>B</i>的坐标；</p><p>（2）如图1，点<i>D</i>是直线 $ AC $ 上方抛物线上一点，连接 $ BD $ 交 $ AC $ 于点<i>E</i>，记 $ \\vartriangle ADE $ 的面积与 $ \\vartriangle ABE $ 面积之比为<i>S</i>，求<i>S</i>的最大值；</p><p>（3）如图2，将抛物线 $ y=-x{^{2}}+mx+3 $ 向左平移1个单位，直线 $ l:y=6 $ 上有一动点<i>P</i>，过点<i>P</i>作两条直线，分别与抛物线有唯一的公共点 $ D,E $ （直线 $ PD,PE $ 不与<i>y</i>轴平行），则直线 $ DE $ 是否过定点？如果是，求出这个定点；如果不是，请说明理由．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川成都七中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16438|16535|16544", "keyPointNames": "和差倍分问题|一次函数的图象和性质|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565946249158893568", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "565946249158893568", "title": "四川省成都市第七中学2024−2025学年九年级下学期3月考数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "564578574784569344", "questionArticle": "<p>5．某水果经营户从水果批发市场批发水果进行零售，部分水果批发价格与零售价格如下表：</p><table style=\"border: solid 1px;border-collapse: collapse; width:327pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 99.75pt;\"><p style=\"text-align:center;\">水果品种</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 56.25pt;\"><p style=\"text-align:center;\">梨子</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\">菠萝</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\">苹果</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\">车厘子</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 99.75pt;\"><p style=\"text-align:center;\">批发价格（元/<i>kg</i>）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 56.25pt;\"><p style=\"text-align:center;\">4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\">5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\">6</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\">40</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 99.75pt;\"><p style=\"text-align:center;\">零售价格（元/<i>kg</i>）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 56.25pt;\"><p style=\"text-align:center;\">5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\">6</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\">8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\">50</p></td></tr></table><p>请解答下列问题：</p><p>(1)第一天，该经营户用1700元批发了菠萝和苹果共300<i>kg</i>，当日全部售出，求这两种水果获得的总利润？</p><p>(2)第二天，该经营户依然用1700元批发了菠萝和苹果，当日销售结束清点盘存时发现进货单丢失，只记得这两种水果的批发量均为正整数且菠萝的进货量不低于88<i>kg</i>，这两种水果已全部售出且总利润高于第一天这两种水果的总利润，请通过计算说明该经营户第二天批发这两种水果可能的方案有哪些？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024北京清华附中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-11", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564578548419174400", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564578548419174400", "title": "北京市清华大学附属中学2023−2024学年八年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564578879878242304", "questionArticle": "<p>6．计算</p><p>(1)解方程组： $ \\begin{cases} x-2y=5, \\\\ 2x+3y=-4. \\end{cases}  $ </p><p>(2) $ \\begin{cases} 6x-2y=22 \\\\ 5x+y=13 \\end{cases}  $ </p><p>(3) $ {\\left( { 2x{^{2}} } \\right) ^ {4}}-x\\cdot x{^{3}}\\cdot x{^{4}} $ </p><p>(4) $ {\\left( { m{^{4}} } \\right) ^ {2}}+m{^{5}}\\cdot m{^{3}}+(-m){^{4}}\\cdot m{^{4}} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-11", "keyPointIds": "16324|16424", "keyPointNames": "幂的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564578858013335552", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564578858013335552", "title": "河北省石家庄市第二十五中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564578785401544704", "questionArticle": "<p>7．某校欲购置甲、乙两种消毒液共200瓶，设甲、乙两种消毒液各购买 $ x $ 瓶， $ y $ 瓶．</p><p>(1)嘉嘉添加了一个条件，条件为“购买的甲种消毒液数量是乙种消毒液数量的2倍．”淇淇根据嘉嘉添加的条件列出了方程组 $ \\begin{cases} x+y=200 \\\\ x=2y \\end{cases}  $ 请用淇淇所列方程组通过计算判断嘉嘉所添加的条件是否正确；</p><p>(2)若甲消毒液15元/瓶，乙消毒液20元/瓶，购买这两种消毒液共花费3750元，求甲、乙两种消毒液分别购买多少瓶？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 8, "referenceNum": 1, "createTime": "2025-04-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564578764316778496", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564578764316778496", "title": "河北省石家庄市第二十八中学2023−2024学年下学期七年级期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564578877374242816", "questionArticle": "<p>8．若 $ (x+y-5){^{2}}+|x-3y-17|=0 $ ，则 $ x-y= $ <u>&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-04-11", "keyPointIds": "16257|16424", "keyPointNames": "绝对值非负性的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564578858013335552", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564578858013335552", "title": "河北省石家庄市第二十五中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564578770939584512", "questionArticle": "<p>9．已知 $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $ 是方程 $ 2x+ay=3 $ 的一组解，那么<i>a</i>的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B． $ -1 $ C．3D． $ -3 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-11", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564578764316778496", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564578764316778496", "title": "河北省石家庄市第二十八中学2023−2024学年下学期七年级期中数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564578779688902656", "questionArticle": "<p>10．已知方程组 $ \\begin{cases} x+2y=4-m \\\\ x-y=m \\end{cases}  $ ，则 $ 2x+y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-11", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564578764316778496", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564578764316778496", "title": "河北省石家庄市第二十八中学2023−2024学年下学期七年级期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 140, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 140, "timestamp": "2025-07-01T02:17:25.444Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}