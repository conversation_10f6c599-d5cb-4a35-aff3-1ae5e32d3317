{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 151, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "562407535954665472", "questionArticle": "<p>1．某医药超市销售 $ A,B $ 两种品牌的消毒液，购买 $ 2 $ 瓶 $ \\mathrm{ A } $ 品牌和 $ 3 $ 瓶 $ B $ 品牌的消毒液共需 $ 160 $ 元；购买 $ 3 $ 瓶 $ \\mathrm{ A } $ 品牌和 $ 1 $ 瓶 $ B $ 品牌的消毒液共需 $ 135 $ 元．</p><p>(1)求这两种品牌消毒液的单价；</p><p>(2)某学校为了给教室进行充分消杀，准备花 $ 1050 $ 元购进 $ A,B $ 两种品牌的消毒液，且要求 $ \\mathrm{ A } $ 品牌的消毒液的数量比 $ B $ 品牌多，请你给出有哪几种购买方案？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏盐城 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-02", "keyPointIds": "16426|16438", "keyPointNames": "二元一次方程组的应用|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562407510797230080", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "562407510797230080", "title": "2025年江苏省盐城市盐都区中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562407049503481856", "questionArticle": "<p>2．2024年11月15日，郑州市热力公司开启了全市供暖，但由于供暖后室内干燥，因此大多数市民们选择使用室内空气加湿器．某商场根据民众需要，代理销售每台进价分别为220元、180元的<i>A</i>，<i>B</i>两种型号的空气加湿器，如表是近两周的销售情况：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">销售时段</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">销售数量</p></td><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">销售收入</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><i>A</i>种型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><i>B</i>种型号</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">第一周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">3台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">4台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">2400元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">第二周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">6台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">9台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">5100元</p></td></tr></table><p>（进价、售价均保持不变，利润 $ = $ 销售收入 $ - $ 进货成本）</p><p>(1)求<i>A</i>，<i>B</i>两种型号的空气加湿器每台的售价．</p><p>(2)若商场准备用不超过5880元的金额再采购这两种型号的空气加湿器共30台，如何购买才可以使商场销售完这30台空气加湿器后获得最大利润？请给出相应的采购方案，并求出最大利润．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南郑州 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-04-02", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562407019287715840", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "562407019287715840", "title": "2025年河南省郑州市中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562407190323044352", "questionArticle": "<p>3．社区王阿姨准备花90元钱购买酒精或消毒液，酒精每瓶10元，消毒液每瓶6元，则王阿姨的购买方案有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）．</p><p>A．2种B．3种C．4种D．5种</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江齐齐哈尔 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-02", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562407173352890368", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562407173352890368", "title": "2025年黑龙江省齐齐哈尔市部分学校中考一模联考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562406662683795456", "questionArticle": "<p>4．有一列货运火车装运一批货物，如果每节车厢装65吨，则还剩20吨装不下；如果每节车厢装66吨，则还可多装40吨．设这列火车共有 $ x $ 节车厢，这批货物共有 $ y $ 吨，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y=65x-20 \\\\ y=66x+40 \\end{cases}  $　　　　B． $ \\begin{cases} y=65x+20 \\\\ y=66x+40 \\end{cases}  $</p><p>C． $ \\begin{cases} y=65x+20 \\\\ y=66x-40 \\end{cases}  $　　　　D． $ \\begin{cases} y=65x-20 \\\\ y=66x-40 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西钦州 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-02", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562406649085861888", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "562406649085861888", "title": "2025年广西壮族自治区钦州市中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "559868793251471360", "questionArticle": "<p>5．为了迎接“亚东会”的到来及提高学生的身体素质，哈美佳外校准备从某体育用品商店一次性购买若干个雪圈儿和雪地足球（每个雪圈儿的价格相同，每个雪地足球的价格相同），若购买2个雪圈儿和3个雪地足球共需310元，购买5个雪圈儿和2个雪地足球共需500元．</p><p>(1)每个雪圈儿和雪地足球各需多少元？</p><p>(2)根据学校的实际情况，需从该商店一次性购买雪圈儿和雪地足球共60个，要求购买雪圈儿和雪地足球的总费用不超过4020元，那么最多可以购买多少个雪圈儿？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025辽宁大连 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-02", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559868773311750144", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "559868773311750144", "title": "辽宁省大连市西岗区第三十四中学2024−2025学年九年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559856267000324096", "questionArticle": "<p>6．现有3张扑克牌，它们所标数字分别为正整数<i>a</i>、<i>b</i>、<i>c</i>，且 $ 1\\leqslant  a  &lt;  b  &lt;  c\\leqslant  9 $ ．甲、乙、丙三个同学同时从这3张扑克牌中随机各拿一张，获得与扑克牌所标数字相同数量的糖果后，完成一次游戏．已知甲、乙、丙3次游戏获得糖果之和分别为20颗、10颗、9颗，则正整数<i>a</i>、<i>b</i>、<i>c</i>分别为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025江苏常州 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-02", "keyPointIds": "16444|16486", "keyPointNames": "三元一次方程组的应用|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559856242518171648", "questionMethodName": "分类讨论思想", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "559856242518171648", "title": "江苏省常州市中天、外国语、湖塘实验2024−2025学年下学期数学九年级新课结束学业水平调研试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559868631946928128", "questionArticle": "<p>7．小何到早餐店买早点，“阿姨，我买 $ 8 $ 个肉包和 $ 5 $ 个菜包．”阿姨说：“一共 $ 17 $ 元．”付款后，小何说：“阿姨，少买 $ 2 $ 个菜包，换 $ 3 $ 个肉包吧．”阿姨说：“可以，但还需补交 $ 2.5 $ 元钱．”</p><p>(1)请从他们的对话中求出肉包和菜包的单价；</p><p>(2)如果小何一共有 $ 25 $ 元，需要买 $ 20 $ 个包子，他最多可以买几个肉包呢？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西临川二中 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-02", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559868612665712640", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "559868612665712640", "title": "江西省抚州市临川第二中学等校2024−2025学年九年级下学期第一次模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559856708035584000", "questionArticle": "<p>8．一辆汽车从 $ \\mathrm{ A } $ 地驶往 $ B $ 地，前 $ \\dfrac { 1 } { 3 } $ 路段为普通公路，其余路段为高速公路，已知汽车在普通公路上行驶的速度为 $ 60{ \\rm{ k } }{ \\rm{ m } }{ \\rm{ / } }{ \\rm{ h } } $ ，在高速公路上行驶的速度为 $ 100{ \\rm{ k } }{ \\rm{ m } }{ \\rm{ / } }{ \\rm{ h } } $ ，汽车从 $ \\mathrm{ A } $ 地到 $ B $ 地一共行驶了 $ { { 2 } }{ { . } }{ { 2 } }{ \\rm{ h } } $ ．设普通公路长、高速公路长分别为 $ x{ \\rm{ k } }{ \\rm{ m } }、y{ \\rm{ k } }{ \\rm{ m } } $ ，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=2y \\\\ \\dfrac { x } { 100 }+\\dfrac { y } { 60 }=2.2 \\end{cases}  $ B． $ \\begin{cases} x=2y \\\\ \\dfrac { x } { 60 }+\\dfrac { y } { 100 }=2.2 \\end{cases}  $ C． $ \\begin{cases} 2x=y \\\\ \\dfrac { x } { 60 }+\\dfrac { y } { 100 }=2.2 \\end{cases}  $ D． $ \\begin{cases} 2x=y \\\\ \\dfrac { x } { 100 }+\\dfrac { y } { 60 }=2.2 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东SFLS · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 3, "createTime": "2025-04-02", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549854212840005632", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "549854212840005632", "title": "广东省深圳市深圳外国语学校2024−2025学年九年级下学期开学考试数学试题", "paperCategory": 1}, {"id": "559856696824209408", "title": "江苏省南京市树人学校2024−2025学年九年级下学期数学3月月考试卷", "paperCategory": 1}, {"id": "183998239485501440", "title": "江苏省南京市2022年中考一模模拟提优数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562035251490365440", "questionArticle": "<p>9．已知方程组 $ \\begin{cases} x+y=-7-m \\\\ x-y=1+3m \\end{cases}  $ 的解满足 $ x $ ， $ y $ 均为负数．</p><p>(1)求 $ m $ 的取值范围；</p><p>(2)在(1)的条件下，若不等式 $ \\left ( { 2m+1 } \\right ) x-2m  <  1 $ 的解集为 $ x > 1 $ ，求 $ m $ 的整数值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市长寿中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-01", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562035227977097216", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "562035227977097216", "title": "重庆市长寿中学校2024−2025学年八年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562035253897895936", "questionArticle": "<p>10．为降低空气污染，公交公司决定全部更换节能环保的燃气公交车，计划购买<i>A</i>型和<i>B</i>型两种公交车共10辆，其中每台的价格，年载客量如表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 109.45pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p><i>A</i>型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p><i>B</i>型</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 109.45pt;\"><p>价格（万元/台）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p> $ a $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p> $ b $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 109.45pt;\"><p>年载客量（万人/年）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p>60</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p>100</p></td></tr></table><p>若购买<i>A</i>型公交车1辆，<i>B</i>型公交车2辆，共需400万元；若购买<i>A</i>型公交车2辆，<i>B</i>型公交车1辆，共需350万元．</p><p>(1)求 $ a $ ， $ b $ 的值；</p><p>(2)如果该公司购买<i>A</i>型和<i>B</i>型公交车的总费用不超过1200万元，且确保这10辆公交车在该线路的年均载客总和不少于680万人次．请你利用方程组或不等式组设计一个总费用最少的方案，并说明总费用最少的理由．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建泉州五中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-04-01", "keyPointIds": "16438|16490", "keyPointNames": "和差倍分问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622432030994432", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "580622432030994432", "title": "福建省泉州第五中学2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}, {"id": "562035227977097216", "title": "重庆市长寿中学校2024−2025学年八年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 152, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 152, "timestamp": "2025-07-01T02:18:51.701Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}