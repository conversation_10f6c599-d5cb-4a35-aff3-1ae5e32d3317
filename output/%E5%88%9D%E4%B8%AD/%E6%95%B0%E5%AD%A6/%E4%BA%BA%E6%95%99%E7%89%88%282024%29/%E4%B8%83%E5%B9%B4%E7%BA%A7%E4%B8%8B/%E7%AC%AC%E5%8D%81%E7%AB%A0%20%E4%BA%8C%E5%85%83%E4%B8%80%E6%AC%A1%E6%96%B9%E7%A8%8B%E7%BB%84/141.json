{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 140, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "564578997016764416", "questionArticle": "<p>1．某铁路桥长 $ 1000m $ ，现有一列火车从桥上通过，测得该火车从开始上桥到完全过桥共用了 $ 1{\\rm  \\min } $ ，整列火车完全在桥上的时间共 $ 40s $ .求火车的速度和长度.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-11", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564578973704822784", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564578973704822784", "title": "河北省石家庄市第三十八中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564578996362452992", "questionArticle": "<p>2．（1）解方程组 $ \\begin{cases} 3x-2y=11 \\\\ 2x+3y=16 \\end{cases}  $ </p><p>（2）简便计算： $ 2011{^{2}}-2010\\times 2012 $ ；</p><p>（3）先化简，再求值： $ \\left [ {\\left( { x-y } \\right) ^ {2}}-x\\left ( { 3x-2y } \\right ) +\\left ( { x+y } \\right ) \\left ( { x-y } \\right )  \\right ] \\div x $ ，其中 $ x=1 $ ， $ y=-2 $ ；</p><p>（4）已知 $ a-b=2 $ ， $ ab=3 $ ，求 $ a{^{2}}+b{^{2}} $ 的值；</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-11", "keyPointIds": "16331|16332|16424", "keyPointNames": "平方差公式|完全平方公式|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564578973704822784", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564578973704822784", "title": "河北省石家庄市第三十八中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564578869564448768", "questionArticle": "<p>3．若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x-y=m+2 \\\\ x+3y=m \\end{cases}  $ 的解适合方程 $ x+y=-2 $ ，则<i>m</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -3 $ B．1C．2D．3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-11", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564578858013335552", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564578858013335552", "title": "河北省石家庄市第二十五中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564578994235940864", "questionArticle": "<p>4．已知 $ \\left ( { n-1 } \\right ) x{^{\\left  | { n } \\right  | }}-2y{^{m}}{^{-2018}}{ \\rm{ = } }0 $ 是关于<i>x</i>，<i>y</i>的二元一次方程，则 $ n{^{m}}= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-11", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564578973704822784", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564578973704822784", "title": "河北省石家庄市第三十八中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564938755125059584", "questionArticle": "<p>5．从甲地到乙地有一段上坡与一段平路，如果保持上坡每小时3km，平路每小时走4km，下坡每小时走5km，那么从甲地到乙地需54分钟，从乙地到甲地需42分钟，甲地到乙地全程多少km？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁铁岭 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-04-11", "keyPointIds": "16424|16430", "keyPointNames": "加减消元法解二元一次方程组|行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564938727044194304", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564938727044194304", "title": "辽宁省铁岭市2023−2024学年七年级下学期期中数学考试题", "paperCategory": 1}, {"id": "465703182145986560", "title": "重庆市梁平区2023-2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "565669680595116032", "questionArticle": "<p>6．一方有难，八方支援．某地洪水灾害牵动着数万人的心，众多企业也伸出援助之手．某公司购买了一批救灾物资并安排两种货车运往该地．调查得知，2辆小货车与3辆大货车一次可以满载运输1800件物资；3辆小货车与4辆大货车一次可以满载运输2500件物资．</p><p>(1)求1辆小货车和1辆大货车一次可以分别满载运输多少件物资；</p><p>(2)现有3100件物资需要再次运往该地，准备同时租用这两种货车，每辆货车均全部装满货物，若1辆小货车需租金400元/次，1辆大货车需租金500元/次．若该公司计划支出4000元用于租车，是否够用，请说明理由．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京师范大学附属实验中学分校 · 模拟", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-04-11", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569343959387906048", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "569343959387906048", "title": "2025年北京市北京师范大学附属实验中学九年级中考零模数学试卷", "paperCategory": 1}, {"id": "565669648991035392", "title": "北京市第八中学2024−2025学年九年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "565669824640098304", "questionArticle": "<p>7．某跨学科综合实践小组准备购买一些盒子存放实验材料．现有A，B，C三种型号的盒子，盒子容量和单价如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse; width:186pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 102pt;\"><p style=\"text-align:center;\">盒子型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 28.5pt;\"><p style=\"text-align:center;\">A</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">B</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">C</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 102pt;\"><p style=\"text-align:center;\">盒子容量/升</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 28.5pt;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">4</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 102pt;\"><p style=\"text-align:center;\">盒子单价/元</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 28.5pt;\"><p style=\"text-align:center;\">5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">6</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">9</p></td></tr></table><p>其中A型号盒子做促销活动：购买三个及三个以上可一次性返现金4元，现有28升材料需要存放且每个盒子要装满材料．</p><p>（1）若购买A，B，C三种型号的盒子的个数分别为2，4，3，则购买费用为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元；</p><p>（2）若一次性购买所需盒子且使购买费用不超过58元，则购买A，B，C三种型号的盒子的个数分别为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．（写出一种即可）</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-11", "keyPointIds": "16444|16486", "keyPointNames": "三元一次方程组的应用|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565669799147118592", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "565669799147118592", "title": "北京市中国人民大学附属中学朝阳学校2024~2025学年九年级下学期三月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "565668414276018176", "questionArticle": "<p>8．七年级某班计划购买 $ A，B $ 两款笔记本作为期中奖品．若购买3本 $ A $ 款的笔记本和1本 $ B $ 款的笔记本需用22元；若购买2本 $ A $ 款的笔记本和3本 $ B $ 款的笔记本需用24元．</p><p>(1)每本 $ A $ 款的笔记本和每本 $ B $ 款的笔记本各多少元；</p><p>(2)该班决定购买以上两款的笔记本共40本，总费用不超过210元，那么该班最多可以购买多少本 $ A $ 款的笔记本？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|210000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025辽宁阜新 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 15, "referenceNum": 3, "createTime": "2025-04-11", "keyPointIds": "16424|16438|16485|16486", "keyPointNames": "加减消元法解二元一次方程组|和差倍分问题|解一元一次不等式|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565668384660037632", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "565668384660037632", "title": "2025年辽宁省阜新市第十一中学九年级下学期第一次模拟数学试卷", "paperCategory": 1}, {"id": "460814564768129024", "title": "2024年广东省深圳市深圳大学附属中学中考三模数学试题", "paperCategory": 1}, {"id": "402643043717783552", "title": "重庆市第八中学2023-2024学年九年级上学期数学期末模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564579423162245120", "questionArticle": "<p>9．已知 $ \\begin{cases} x=2, \\\\ y=1 \\end{cases}  $ 是方程 $ kx-y=3 $ 的一个解，则<i>k</i>的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2B． $ -2 $ C．1D． $ -1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北唐山 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-04-11", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564579414748471296", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564579414748471296", "title": "河北省唐山地区2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "491756153606545408", "title": "福建省泉州第五中学2023−2024学年八年级上学期开学测试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564579236826095616", "questionArticle": "<p>10．解方程组：</p><p>（1） $ \\begin{cases} x=6y+4 \\\\ \\dfrac { x } { 6 }-\\dfrac { y } { 2 }=\\dfrac { 1 } { 3 } \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} 6x+5y=7 \\\\ 3x-2y=-1 \\end{cases}  $ </p><p>解下列不等式：</p><p>（3） $ 2\\left ( { x+1 } \\right ) -1\\geqslant  3x+2 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-04-11", "keyPointIds": "16423|16424|16485", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564579207507910656", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564579207507910656", "title": "河北省石家庄市复兴中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "442830222536253440", "title": "河北省石家庄市第四十八中学2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 141, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 141, "timestamp": "2025-07-01T02:17:32.614Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}