{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 92, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "575777545305497600", "questionArticle": "<p>1．《九章算术》是我国东汉初年编订的一部数学经典著作，在它的“方程”里，一次方程组是由算筹布置而成的．《九章算术》中的算筹图是竖排的，为看图方便，我们把它改为横排的，如图1、图2中各行从左到右列出的算筹分别表示未知数<i>x</i>，<i>y</i>的系数与相应的常数项．把图1所示的算筹图用我们现在所熟悉的方程组形式表述出来，就是 $ \\begin{cases} 3x+2y=19 \\\\ x+4y=23 \\end{cases}  $ ，类似地，图2所示的算筹图我们可以表述为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/12/2/1/0/0/0/577106157438803969/images/img_1.png\" style='vertical-align:middle;' width=\"432\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} 2x+y=11 \\\\ 4x+3y=27 \\end{cases}  $</p><p>B． $ \\begin{cases} 2x+y=11 \\\\ 4x+3y=22 \\end{cases}  $</p><p>C． $ \\begin{cases} 3x+2y=19 \\\\ x+4y=23 \\end{cases}  $</p><p>D． $ \\begin{cases} 2x+y=6 \\\\ 4x+3y=27 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|210000|360000|410000|430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025湖南株洲 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 7, "createTime": "2025-05-09", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575777530281500672", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "575777530281500672", "title": "湖南省株洲市外国语学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}, {"id": "532312655622610944", "title": "江西省九江市同文中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "491398563617873920", "title": "湖南省长沙市湖南师范大学附属中学2024−2025学年八年级上学期入学考试数学试题", "paperCategory": 1}, {"id": "440120065565958144", "title": "重庆市求精中学校2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "361602867017326592", "title": "第1部分 第二章  第1节 B考点突破 2列一次方程(组)及其实际应用 【中考必刷题】河南版 数学", "paperCategory": 1}, {"id": "174834773256347648", "title": "河南省开封市第二十七中学2020-2021学年七年级下册4月月考数学试题", "paperCategory": 1}, {"id": "140945093154873344", "title": "辽宁省沈阳市沈河区2020年中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574750189560307712", "questionArticle": "<p>2． $ 2024 $ 年 $ 10 $ 月 $ 30 $ 日，神舟十九号载人飞船成功发射，三名航天员被送入中国天宫空间站，开启了中国航天事业的新篇章．二七区某中学为了培养学生科技创新意识，开设了“蓝天梦想家”航模兴趣社团，计划购进<i>A</i>、<i>B</i>两种航模．据了解购买1件<i>A</i>型航模和2件<i>B</i>型航模需 $ 800 $ 元；购买2件<i>A</i>型航模和3件<i>B</i>型航模需 $ 1300 $ 元．</p><p>(1)求<i>A</i>、<i>B</i>两种航模每件分别多少元？</p><p>(2)张老师欲同时购买两种航模，在采购时恰逢商家推出优惠活动，两种航模均打九折出售，这次采购预计共花费 $ 990 $ 元，请问张老师有哪几种购买方案？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆万州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-05-09", "keyPointIds": "16420|16434|16438", "keyPointNames": "二元一次方程的解|方案问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "560236378417569792", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "560236378417569792", "title": "重庆市万州二中教育集团2024−2025学年七年级下学期数学第一次月考试卷", "paperCategory": 1}, {"id": "574750160636387328", "title": "北京市第五十七中学2024−2025学年七年级下学期数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574750186053869568", "questionArticle": "<p>3．解下列方程组：</p><p>(1) $ \\begin{cases} x+y=1 \\\\ x-2y=4 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 3x=5y \\\\ \\dfrac { x } { 4 }+\\dfrac { y } { 3 }=3 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-05-09", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703598805786624", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569703598805786624", "title": "河北省石家庄市第十七中学2024−2025学年七年级下学期4月考试数学试题", "paperCategory": 1}, {"id": "574750160636387328", "title": "北京市第五十七中学2024−2025学年七年级下学期数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575617226432098304", "questionArticle": "<p>4．某小区内安装垃圾分类的<i>A</i>型固定垃圾箱和<i>B</i>型移动垃圾箱，已知购买3个<i>A</i>型固定垃圾箱和2个<i>B</i>型移动垃圾箱共需560元，1个<i>A</i>型固定垃圾箱和1个<i>B</i>型移动垃圾箱共需200元．</p><p>（1）求<i>A</i>型固定垃圾箱和<i>B</i>型移动垃圾箱的单价各是多少元；</p><p>（2）如果需要购买<i>A</i>型固定垃圾箱和<i>B</i>型移动垃圾箱共90个，且费用不超过6000元，问：那该小区最多可以购买<i>A</i>型固定垃圾箱多少个？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南常德 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 7, "referenceNum": 2, "createTime": "2025-05-08", "keyPointIds": "16441|16486", "keyPointNames": "其他问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575617198825189376", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "575617198825189376", "title": "2025年湖南省常德市中考二模数学试题", "paperCategory": 1}, {"id": "587393714311442432", "title": "2025年安徽省合肥市蜀山区九年级质量调研检测三数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575617104633700352", "questionArticle": "<p>5．已知方程 $ x{^{a+2}}-y{^{3-b}}=2 $ 是二元一次方程，则 $ a+b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南邵阳 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-08", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575617082739433472", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "575617082739433472", "title": "2025年4月湖南省武冈市九年级部分学校联考中考数学模拟冲刺试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575617101664133120", "questionArticle": "<p>6．已知 $ \\left ( { a-3 } \\right ) x{^{\\left  | { a } \\right  | -2}}+3y=1 $ 是关于<i>x</i>，<i>y</i>的二元一次方程，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南邵阳 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-08", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575617082739433472", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575617082739433472", "title": "2025年4月湖南省武冈市九年级部分学校联考中考数学模拟冲刺试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574354116656275456", "questionArticle": "<p>7．根据以下素材，探索完成任务．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>学校如何购买保洁物品</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>问题背景</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>自《义务教育劳动课程标准（2022年版）》的发布，劳动课正式成为中小学的一门独立课程．劳动教育是学生设计能力、问题解决能力、合作能力、实践能力以及社会责任感提升的重要手段．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材1</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>为了保障劳动教育的有序进行，某学校需要增加保洁物品的库存量，计划用不超过480元的总费用购买扫把簸箕套装与毛巾两种物品．考虑两种物品的易损情况，要求毛巾的数量是扫把簸箕套装数量的3倍，扫把簸箕套装不少于50套．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材2</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>商店物品价格情况：买3条毛巾和2套扫把簸箕套装共需18元，买4条毛巾和3套扫把簸箕套装共需26元．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材3</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>商店提供以下两种优惠方案：</p><p>方案1：两种商品按原价的8折出售；</p><p>方案2：两种商品总额不超过400元的按原价付费，超过400元的部分打6折．</p></td></tr><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>确定物品单价</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>请运用所学知识，求出毛巾和扫把簸箕套装的单价．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>探究购买方案</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>如果学校只按商店提供的其中一种优惠方案来购买，学校该购进毛巾和扫把簸箕套装数量分别是多少？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东深圳市罗湖外语学校 · 临考冲刺", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 2, "createTime": "2025-05-08", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574354082443337728", "questionFeatureName": "阅读材料题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574354082443337728", "title": "广东省深圳市罗湖外国语学校2025年中考数学适应性考试试卷", "paperCategory": 1}, {"id": "553369112128299008", "title": "广东省深圳市31校2024−2025学年九年级下学期第一次模拟考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575617865111351296", "questionArticle": "<p>8．为迎接“七·一”党的生日，某校准备组织师生共310人参加一次大型公益活动，租用4辆大客车和6辆小客车恰好全部坐满，已知每辆大客车的座位数比小客车多15个.</p><p>（1）求每辆大客车和小客车的座位数；</p><p>（2）经学校统计，实际参加活动人数增加了40人，学校决定调整租车方案，在保持租用车辆总数不变的情况下，为使所有参加活动的师生均有座位，最多租用小客车多少辆？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|130000|210000|520000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2018辽宁锦州 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 17, "referenceNum": 6, "createTime": "2025-05-08", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "1017118704406528", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "1017118704406528", "title": "辽宁省锦州市2018年中考数学试题", "paperCategory": 1}, {"id": "575617825710059520", "title": "湖南省娄底市娄星区2025年初中毕业学业水平考试数学模拟卷", "paperCategory": 1}, {"id": "523605198817763328", "title": "辽宁省沈阳市浑南区东北育才学校2024−2025学年九年级上学期11月月考数学试题", "paperCategory": 1}, {"id": "458404759055147008", "title": "河北省石家庄市桥西区2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "208549164384296960", "title": "重庆市江北区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "129283674281910272", "title": "贵州省黔东南州2021年中考模拟考试（二）数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574353241955147776", "questionArticle": "<p>9．解方程（组）：</p><p>(1) $ 4x{^{2}}-25=0 $ ；</p><p>(2)解方程组 $ \\begin{cases} 5x+2y=25 \\\\ 3x+4y=15 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025湖北随州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 2, "createTime": "2025-05-08", "keyPointIds": "16287|16424", "keyPointNames": "平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574353219071025152", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574353219071025152", "title": "湖北省随州市曾都区八角楼初级中学教联体2024−2025学年下学期期中质量监测七年级数学试题", "paperCategory": 1}, {"id": "446416832020389888", "title": "湖北省广水市2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574353231792349184", "questionArticle": "<p>10．用代入法解二元一次方程组 $ \\begin{cases} 3x+4y=2① \\\\ 2x-y=5② \\end{cases}  $ 时，最恰当的变形是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．由①得 $ {\\rm \\mathit{x}＝} \\dfrac { 2-4y } { 3 } $ B．由①得 $ {\\rm \\mathit{y}＝} \\dfrac { 2-3x } { 4 } $ </p><p>C．由②得 $ {\\rm \\mathit{x}＝} \\dfrac { y+5 } { 2 } $ D．由②得<i>y</i>＝2<i>x</i>−5</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025湖北随州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-05-08", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574353219071025152", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574353219071025152", "title": "湖北省随州市曾都区八角楼初级中学教联体2024−2025学年下学期期中质量监测七年级数学试题", "paperCategory": 1}, {"id": "446416832020389888", "title": "湖北省广水市2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 93, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 93, "timestamp": "2025-07-01T02:11:49.823Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}