{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 77, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "578015682350067712", "questionArticle": "<p>1．列二元一次方程组解决下列实际问题：</p><p>每年的5月8日是国际红十字日，这一日某校组织献爱心捐款，其中初一（1）有36名同学参加，共捐得1200元，捐款情况如下表：</p><table style=\"border: solid 1px;border-collapse: collapse; width:189pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">捐款（元）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">100</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">50</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">10</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">人数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">2</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/15/2/1/0/0/0/578015622593814529/images/img_21.png\" style=\"vertical-align:middle;\" width=\"92\" alt=\"试题资源网 https://stzy.com\"></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">4</p></td></tr></table><p>表格中捐款50元和20元的人数不小心被墨水污染已看不清楚，请你根据表格提供的信息计算分别有多少同学捐50元和20元．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京市十一学校 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015653266763776", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015653266763776", "title": "北京市十一学校2024-−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015679384694784", "questionArticle": "<p>2．已知关于 $ x{ { &nbsp; } }、{ { &nbsp; } }y $ 的方程组 $ \\begin{cases} 4x+y=2k+1 \\\\ 3x+2y=5k \\end{cases}  $ 满足 $ -2  &lt;  x-y  &lt;  5 $ ，求 $ k $ 的取值范围．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京市十一学校 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015653266763776", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015653266763776", "title": "北京市十一学校2024-−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015676612259840", "questionArticle": "<p>3．解下列方程组或不等式．</p><p>（1） $ \\begin{cases} 2\\left ( { x-2 } \\right ) +4y=-5 \\\\ 4x-5\\left ( { y+1 } \\right ) =6 \\end{cases}  $ </p><p>（2） $ \\dfrac { 2x-1 } { 3 }-\\dfrac { 5x+1 } { 2 }  &lt;  1 $ ，并把解集在数轴上表示出来．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京市十一学校 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16424|16485|28266", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式|在数轴上表示不等式的解集", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015653266763776", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015653266763776", "title": "北京市十一学校2024-−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015669528080384", "questionArticle": "<p>4． $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是关于 $ x，y $ 的二元一次方程 $ mx+y=6 $ 的解，则 $ m $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京市十一学校 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015653266763776", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015653266763776", "title": "北京市十一学校2024-−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015661705703424", "questionArticle": "<p>5．下列方程是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x{^{2}}-3y=0 $ B． $ x+5y=-1 $ C． $ x+y-z=0 $ D． $ 3x-2=4+x $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025北京北京市十一学校 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015653266763776", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015653266763776", "title": "北京市十一学校2024-−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "575478898076459008", "questionArticle": "<p>6．【数学活动回顾】七年级下册教材中我们曾探究过“以方程 $ x-y=0 $ 的解为坐标（ $ x $ 的值为横坐标、 $ y $ 的值为纵坐标）的点的特性”，了解了二元一次方程的解与它的图象上点的坐标的关系．</p><p>规定：以方程 $ x-y=0 $ 的解为坐标的所有点的全体叫做方程 $ x-y=0 $ 的图象．</p><p>结论：一般地，在平面直角坐标系中，任何一个二元一次方程的图象都是一条直线．</p><p>示例：如图1，我们在画方程 $ x-y=0 $ 的图象时，可以取点 $ A\\left ( { -1,-1 } \\right )  $ 和 $ B\\left ( { 2,2 } \\right )  $ 作出直线 $ AB $ ．</p><p>【解决问题】（1）请在图2所给的平面直角坐标系中画出二元一次方程组 $ \\begin{cases} x+y=-1 \\\\ 2x-y=4 \\end{cases}  $ 中的两个二元一次方程的图象．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/08/2/1/0/0/0/575478826676826127/images/img_16.png\" style=\"vertical-align:middle;\" width=\"520\" alt=\"试题资源网 https://stzy.com\"></p><p>（2）观察（1）的图象，两条直线的交点坐标是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，由此得出这个二元一次方程组的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>【拓展延伸】</p><p>（3）①在同一平面直角坐标系中，二元一次方程 $ x+y=1 $ 的图象 $ l{{}_{ 1 } } $ 和 $ x+y=-2 $ 的图象 $ l{{}_{ 2 } } $ ，如图3所示．请根据图象，直接判断方程组 $ \\begin{cases} x+y=1 \\\\ x+y=-2 \\end{cases}  $ 的解的情况是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>②已知点 $ A\\left ( { 3,-2 } \\right )  $ ， $ B\\left ( { -1,2 } \\right )  $ 在二元一次方程 $ ax+by=5 $ 的图象上，求 $ a $ ， $ b $ 的值．</p><p>③在②的条件下，以关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} 2x+3y=15-15m \\\\ 3x+2y=6+10m \\end{cases}  $ 的解为坐标的点在方程 $ ax+by=5 $ 的图象上，当 $ n  &lt;  m $ 时，化简 $ \\left  | { 5n-17 } \\right  | -\\sqrt { {\\left( { 16-5n } \\right) ^ {2}} } $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16424|16540", "keyPointNames": "加减消元法解二元一次方程组|一次函数与二元一次方程（组）", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575478869882347520", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575478869882347520", "title": "福建省福州第十九中学2024−2025学年下学期期中测试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575478896008667136", "questionArticle": "<p>7．为鼓励同学们参加主题为“阅读润泽心灵，文字见证成长”的读书月活动，学校计划购进一批科技类和文学类图书作为活动奖品．已知同类图书中每本书价格相同，购买2本科技类图书和3本文学类图书需120元，购买4本科技类图书和5本文学类图书需210元．</p><p>（1）科技类图书和文学类图书每本各多少元？</p><p>（2）经过评选有300名同学在活动中获奖，学校对每位获奖同学奖励一本科技类或文学类图书．如果学校用于购买奖品的资金不超过7300元，那么文学类图书最多能买多少本？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575478869882347520", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575478869882347520", "title": "福建省福州第十九中学2024−2025学年下学期期中测试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575478892921659392", "questionArticle": "<p>8．解方程组： $ \\begin{cases} 3x-2y=18 \\\\ 2x+y=5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575478869882347520", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575478869882347520", "title": "福建省福州第十九中学2024−2025学年下学期期中测试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575478889587187712", "questionArticle": "<p>9．若 $ \\begin{cases} x=-3 \\\\ y=2 \\end{cases}  $ 是二元一次方程 $ ax+by=2 $ 的一个解，则 $ 2025-6a+4b $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575478869882347520", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575478869882347520", "title": "福建省福州第十九中学2024−2025学年下学期期中测试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575478886563094528", "questionArticle": "<p>10．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} 2x+y=1-m \\\\ 4x-y=5m \\end{cases}  $ （其中 $ m $ 是常数），不论 $ m $ 取什么实数，代数式 $ nx+4y $ （ $ n $ 是常数）的值始终不变，则 $ n $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．11B．12C．13D．14</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575478869882347520", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575478869882347520", "title": "福建省福州第十九中学2024−2025学年下学期期中测试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 78, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 78, "timestamp": "2025-07-01T02:10:02.317Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}