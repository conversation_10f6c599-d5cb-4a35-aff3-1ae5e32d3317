{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 173, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "555512419834961920", "questionArticle": "<p>1．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+y=-2 \\\\ ax-by=-8 \\end{cases}  $ 和 $ \\begin{cases} bx+ay=-4 \\\\ 3x-y=12 \\end{cases}  $ 的解相同，求 $ {\\left( { 2a+b } \\right) ^ {2023}} $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 10, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512420657045504", "questionArticle": "<p>2．已知方程组 $ \\begin{cases} ax-5y=15① \\\\ 4x-by=-2② \\end{cases}  $ ，由于甲看错了方程①中的 $ a $ ，得到方程组的解为 $ \\begin{cases} x=-3 \\\\ y=-1 \\end{cases}  $ ，乙看错了方程②中的 $ b $ ，得到方程组的解为 $ \\begin{cases} x=5 \\\\ y=4 \\end{cases}  $ ，</p><p>(1)求 $ a $ ， $ b $ 的值；  </p><p>(2)求原方程组正确的解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512416743759872", "questionArticle": "<p>3．已知 $ \\begin{cases} x=3 \\\\ y=1 \\end{cases}  $ 是方程 $ { { 3 } }x-ay=2 $ 的解，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512415368028160", "questionArticle": "<p>4．已知 $ 4x-5y-{ { 6 } }={ { 0 } } $ ，用含 $ x $ 的代数式表示 $ y $ ，得<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512410699767808", "questionArticle": "<p>5．若 $ \\begin{cases} x=-2 \\\\ y=1 \\end{cases}  $ 是方程组 $ \\begin{cases} ax+by=1 \\\\ bx+ay=7 \\end{cases}  $ 的解，则 $ a-b $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．  $ -8 $　　　　B．0　　　　C．2　　　　D．8</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555512409059794944", "questionArticle": "<p>6．若 $ \\left ( { m-2024 } \\right ) x{^{\\left  | { m } \\right  | -2023}}+\\left ( { n+4 } \\right ) y{^{\\left  | { n } \\right  | -3}}=2023 $ 是关于<i>x</i>，<i>y</i>的二元一次方程，则（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ m=\\pm 2024,n=\\pm 4 $　　　　B． $ m=-2024，n=\\pm 4 $</p><p>C． $ m=\\pm 2024，n=-4 $　　　　D． $ m=-2024，n=4 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555512407377879040", "questionArticle": "<p>7．解方程组 $ \\begin{cases} 3x+2y=3① \\\\ 3x-2y=-1② \\end{cases}  $ 时 $ ①-② $ 得（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 4y=4 $　　　　B． $ 4y=2 $　　　　C． $ -4y=4 $　　　　D． $ -4y=2 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "553368926442266624", "questionArticle": "<p>8．剪纸是一种镂空艺术，在视觉上给人以透空的感觉和艺术享受，剪纸内容多，寓意广，生活气息浓厚．某商家在春节前夕购进甲、乙两种剪纸装饰套装共60套进行销售，已知购进一套甲种剪纸比购进一套乙种剪纸多10元，购进2套甲种剪纸和3套乙种剪纸共需220元．</p><p>(1)求这两种剪纸购进时的单价分别为多少元？</p><p>(2)设购进甲种剪纸装饰<i>x</i>套( $ x\\leqslant  60 $ )，购买甲、乙两种剪纸装饰共花费<i>y</i>元，求<i>y</i>与<i>x</i>之间的函数关系式；</p><p>(3)若甲种剪纸的售价为65元/套，乙种剪纸的售价为50元/套，该商家计划购进这批剪纸装饰所花的总费用不超过2800元，要使这批剪纸装饰全部售完时商家能获得最大利润，请你帮助商家设计购进方案，并求出最大利润．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000|350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建泉州 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-14", "keyPointIds": "16438|16535|16544", "keyPointNames": "和差倍分问题|一次函数的图象和性质|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553368893848330240", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "553368893848330240", "title": "福建省泉州市南安市区四校联盟2024−2025学年九年级下学期初中毕业班数学综合卷（泉州一检模拟）", "paperCategory": 1}, {"id": "514473733727232000", "title": "陕西省西安市西安高新一中2024−2025学年八年级上学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512412985663488", "questionArticle": "<p>9．某单位组织34人分别到井冈山和瑞金进行革命传统教育，到井冈山的人数是到瑞金的人数的2倍多1人，求到两地的人数各是多少？设到井冈山的人数为<i>x</i>人，到瑞金的人数为<i>y</i>人．下面所列的方程组正确的是</p><p>A． $ \\{\\hspace{-0.5em}  \\begin{array} {l} { \\rm{ x } }+{ \\rm{ y } }=34 \\\\ { \\rm{ x } }+1=2{ \\rm{ y } } \\end{array} \\hspace{-0.5em}  $　　　　B． $ \\{\\hspace{-0.5em}  \\begin{array} {l} { \\rm{ x } }+{ \\rm{ y } }=34 \\\\ { \\rm{ x } }=2{ \\rm{ y } }+1 \\end{array} \\hspace{-0.5em}  $　　　　C． $ \\{\\hspace{-0.5em}  \\begin{array} {l} { \\rm{ x } }+{ \\rm{ y } }=34 \\\\ 2{ \\rm{ x } }={ \\rm{ y } }+1 \\end{array} \\hspace{-0.5em}  $　　　　D． $ \\{\\hspace{-0.5em}  \\begin{array} {l} { \\rm{ x } }+2{ \\rm{ y } }=34 \\\\ { \\rm{ x } }=2{ \\rm{ y } }+1 \\end{array} \\hspace{-0.5em}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|650000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-14", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}, {"id": "130005759044132864", "title": "新疆阿克苏地区沙雅县2019届中考模拟数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555512408195768320", "questionArticle": "<p>10． $ 2x{^{3}}y{^{m+1}} $ 与 $ 3x{^{n}}y{^{2}} $ 是同类项，则<i>m</i>与<i>n</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} m=1 \\\\ n=3 \\end{cases}  $</p><p>B． $ \\begin{cases} m=3 \\\\ n=1 \\end{cases}  $</p><p>C． $ \\begin{cases} m=2 \\\\ n=3 \\end{cases}  $</p><p>D． $ \\begin{cases} m=3 \\\\ n=2 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-14", "keyPointIds": "16314|16426", "keyPointNames": "同类项|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}, {"id": "173444343637778432", "title": "湘教版七年级下册第1章二元一次方程组单元测试", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 174, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 174, "timestamp": "2025-07-01T02:21:27.117Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}