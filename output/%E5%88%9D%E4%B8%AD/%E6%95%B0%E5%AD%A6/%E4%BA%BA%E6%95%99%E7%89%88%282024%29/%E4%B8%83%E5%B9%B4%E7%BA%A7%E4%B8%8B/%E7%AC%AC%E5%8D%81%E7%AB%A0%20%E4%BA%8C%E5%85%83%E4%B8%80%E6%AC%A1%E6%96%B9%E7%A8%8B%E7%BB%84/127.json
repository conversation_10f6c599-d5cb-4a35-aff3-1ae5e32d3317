{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 126, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "567102397304053760", "questionArticle": "<p>1．我国古代数学问题：现有甲、乙两钱袋，甲袋装的黄金比乙袋装的黄金多9枚，从甲袋取8枚黄金放到乙袋，乙袋的黄金数量就是甲袋的三倍．设甲袋原有黄金 $ x $ 枚，乙袋原有黄金 $ y $ 枚，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-y=9 \\\\ x-8=3\\left ( { y+8 } \\right )  \\end{cases}  $ B． $ \\begin{cases} x=y+9 \\\\ 3\\left ( { x-8 } \\right ) =y+8 \\end{cases}  $ C． $ \\begin{cases} x-y=9 \\\\ 3\\left ( { x-8 } \\right ) =y \\end{cases}  $ D． $ \\begin{cases} x=y+9 \\\\ x+8=3\\left ( { y-8 } \\right )  \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市巴蜀中学校 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567102378689732608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567102378689732608", "title": "重庆市巴蜀中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567102663344562176", "questionArticle": "<p>2．已知整式 $ M：a{{}_{ n } }x{^{n}}+a{{}_{ n-1 } }x{^{n-1}}+\\cdots +a{{}_{ 1 } }x+a{{}_{ 0 } } $ ，其中 $ n $ 为自然数， $ a{{}_{ n-1 } },a{{}_{ n-2 } },\\cdots ,a{{}_{ 1 } },a{{}_{ 0 } } $ 均为整数， $ a{{}_{ n } } $ 为非零整数， $ a{{}_{ n } } &gt; a{{}_{ n-1 } } &gt; \\cdots  &gt; a{{}_{ 1 } } &gt; a{{}_{ 0 } } $ ，记 $ k=a{{}_{ n } }+a{{}_{ n-1 } }+\\cdots +a{{}_{ 1 } }+a{{}_{ 0 } } $ ．当 $ k $ 为完全平方数时，则下列说法：</p><p>①当 $ n=1 $ ， $ a{{}_{ n } }=2 $ 时，满足条件的整式 $ M $ 之和是 $ 4x-3 $ ；</p><p>②若 $ a{{}_{ n } }=3 $ ，则存在一个 $ n $ ，使得满足条件的整式 $ M $ 有且仅有 $ 9 $ 个，</p><p>③若 $ a{{}_{ n } }  &lt;  3 $ ，满足条件的整式 $ M $ 共有 $ 14 $ 个．</p><p>其中正确的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 0 $ B． $ 1 $ C． $ 2 $ D． $ 3 $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市第一中学校 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16332|16420", "keyPointNames": "完全平方公式|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567102643673276416", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567102643673276416", "title": "重庆市第一中学2024−2025学年下学期第一次月考八年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567102850498600960", "questionArticle": "<p>3．已知整式 $ M{{}_{ n } }={\\left( { ax } \\right) ^ {n}}-{\\left( { by } \\right) ^ {n}}=\\left ( { ax-by } \\right ) \\left [ {\\left( { ax } \\right) ^ {n-1}}+{\\left( { ax } \\right) ^ {n-2}}by+{\\left( { ax } \\right) ^ {n-3}}{\\left( { by } \\right) ^ {2}}+\\cdots +ax{\\left( { by } \\right) ^ {n-2}}+{\\left( { by } \\right) ^ {n-1}} \\right ]  $ 其中 $ n $ 为正整数，以下说法：</p><p> $ ① $ 根据已知等式，把 $ x{^{15}}-64 $ 分解因式得 $ \\left ( { x{^{5}}-m } \\right ) \\left ( { x{^{10}}-nx{^{p}}+q } \\right )  $ ，则 $ m+n+p+q=21 $ ；</p><p> $ ② $ 若 $ a+b=3 $ （ $ a $ ， $ b $ 均为正整数）， $ M{{}_{ 1 } }+M{{}_{ 2 } }=6 $ 时，则满足条件的整数 $ x $ ， $ y $ 的值只有两组；</p><p> $ ③ $ 若 $ a $ ， $ b $ 是方程 $ m{^{2}}-m-1=0 $ 的两根，则 $ \\dfrac { M{{}_{ 8 } }-M{{}_{ 7 } } } { ax-by } $ 的各项系数之和为 $ 10 $ ．</p><p>其中正确的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 0 $ 个B． $ 1 $ C． $ 2 $ D． $ 3 $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16353|16424|16458", "keyPointNames": "因式分解的应用|加减消元法解二元一次方程组|一元二次方程根与系数的关系", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567102830684708864", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567102830684708864", "title": "重庆市鲁能巴蜀中学校2024−2025学年九年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567103705666854912", "questionArticle": "<p>4．一家电脑公司有 $ \\mathrm{ A } $ 型、 $ B $ 型、 $ C $ 型三种型号的电脑，其中 $ C $ 型每台 $ 2500 $ 元．某中学计划从这家电脑公司购进电脑．</p><p>(1)已知购买2台 $ \\mathrm{ A } $ 型电脑和3台 $ B $ 型电脑需要 $ 24000 $ 元，且购买3台 $ \\mathrm{ A } $ 型电脑和8台 $ B $ 型电脑的费用刚好可以买20台 $ C $ 型电脑．求 $ \\mathrm{ A } $ 型电脑和 $ B $ 型电脑的售价．</p><p>(2)这家电脑公司为提高 $ B $ 型电脑销量，设计了旧电脑抵值活动：购买一台 $ B $ 型电脑时，可以用一台旧电脑抵值1000元．该中学计划只购买 $ B $ 型电脑，拿出的旧电脑和购买的 $ B $ 型电脑数量一共是 $ 30 $ 台．若要使购买 $ B $ 型电脑的数量是旧电脑数量的 $ 2 $ 倍，且购买 $ B $ 型电脑的实际总费用不少于 $ 100000 $ 元，则要在计划的基础上再多买 $ a $ 台 $ B $ 型电脑，此时该中学需要再拿出 $ \\dfrac { 1 } { 3 }a $ 台的旧电脑参加抵值活动，求该中学至少需要再拿出多少台旧电脑进行抵值？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽合肥三十八中学 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567103679154659328", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567103679154659328", "title": "安徽省合肥市第三十八中学2024−2025学年下学期七年级第一次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567103941994913792", "questionArticle": "<p>5．一个旅游团去游览某个水上景点，游客可以沿岸边徒步游览，也可以乘坐游船游览，都是原路去，原路返回，如果乘坐游船，方式和费用为：单程每人100元，往返每人150元．若该旅游团队每个人都至少乘坐一次游船，去程时有9人乘坐游船，返程时有13人乘坐游船，他们乘坐游船的总费用是1800元，则该旅游团队只乘坐一次游船的有<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>人．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京市陈经纶中学分校 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567103920801095680", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567103920801095680", "title": "北京市陈经纶中学分校 2024−2025 学年下学期 3 月月质量监测 九年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567476065339219968", "questionArticle": "<p>6．某校的饮水机有温水、开水两个按钮，温水和开水共用一个出水口．温水的温度为 $ 30{}\\degree { \\rm{ C } } $ ，流速为 $ 20{ \\rm{ m } }{ \\rm{ l } }/{ \\rm{ s } } $ ；开水的温度为 $ 100{}\\degree { \\rm{ C } } $ ，流速为 $ 15{ \\rm{ m } }{ \\rm{ l } }/{ \\rm{ s } } $ ．某学生先接了一会儿温水，又接了一会儿开水，得到一杯 $ 210{ \\rm{ m } }{ \\rm{ l } } $ 温度为 $ 60{}\\degree { \\rm{ C } } $ 的水（不计热损失），求该学生分别接温水和开水的时间．</p><table style=\"border: solid 1px;border-collapse: collapse; width:414.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>物理常识</p><p>开水和温水混合时会发生热传递，开水放出的热量等于温水吸收的热量，可以转化为：开水的体积×开水降低的温度=温水的体积×温水升高的温度．</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567476033223434240", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567476033223434240", "title": "2025年北京市首都师范大学附属中学育鸿学校中考数学零模数学试卷（3月）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567476329341296640", "questionArticle": "<p>7．在“双减”政策推动下，学校开展了丰富多彩的社团活动．书法社和绘画社开始招募新成员．起初，书法社的报名数比绘画社报名数的 $ \\dfrac { 3 } { 4 } $ 还多 $ 5 $ 人；后来，绘画社有 $ 5 $ 人改报了书法社，此时，书法社的报名数是绘画社报名数的 $ 2 $ 倍．设起初报名书法社的为 $ x $ 人，报名绘画社的为 $ y $ 人，则下面所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-\\dfrac { 3 } { 4 }y=5 \\\\ x+5=2\\left ( { y-5 } \\right )  \\end{cases}  $ B． $ \\begin{cases} x-\\dfrac { 3 } { 4 }y=5 \\\\ 2\\left ( { x+5 } \\right ) =y-5 \\end{cases}  $ </p><p>C． $ \\begin{cases} \\dfrac { 3 } { 4 }x-y=5 \\\\ x+5=2\\left ( { y-5 } \\right )  \\end{cases}  $ D． $ \\begin{cases} \\dfrac { 3 } { 4 }x-y=5 \\\\ 2\\left ( { x+5 } \\right ) =y-5 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567476315760140288", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567476315760140288", "title": "2025年广东省深圳市31校联考二模数学", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "568162662145630208", "questionArticle": "<p>8．（1）解方程组 $ \\begin{cases} x=y-3 \\\\ 3x-2y=4 \\end{cases}  $ </p><p>（2）①解方程组 $ \\begin{cases} 2x-y=3, \\\\ 3x-2y=4. \\end{cases}  $ </p><p>②直接写出方程组 $ \\begin{cases} 2\\left ( { x+1 } \\right ) -\\left ( { y-2 } \\right ) =3 \\\\ 3\\left ( { x+1 } \\right ) -2\\left ( { y-2 } \\right ) =4 \\end{cases}  $ 的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568162643246096384", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "568162643246096384", "title": "江苏省南京市第五十中学2024—2025学年下学期3月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568162659167674368", "questionArticle": "<p>9．班级要用40元钱买 $ \\mathrm{ A } $ 、 $ B $ 两种彩笔，两种彩笔必须都买，已知 $ \\mathrm{ A } $ 型彩笔每个6元， $ B $ 型彩笔每个4元，在钱全部用尽的情况下，购买方案有<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>种．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568162643246096384", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "568162643246096384", "title": "江苏省南京市第五十中学2024—2025学年下学期3月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568162655095005184", "questionArticle": "<p>10．写出一个解为  $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $  的二元一次方程是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568162643246096384", "questionFeatureName": "开放性试题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "568162643246096384", "title": "江苏省南京市第五十中学2024—2025学年下学期3月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 127, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 127, "timestamp": "2025-07-01T02:15:52.930Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}