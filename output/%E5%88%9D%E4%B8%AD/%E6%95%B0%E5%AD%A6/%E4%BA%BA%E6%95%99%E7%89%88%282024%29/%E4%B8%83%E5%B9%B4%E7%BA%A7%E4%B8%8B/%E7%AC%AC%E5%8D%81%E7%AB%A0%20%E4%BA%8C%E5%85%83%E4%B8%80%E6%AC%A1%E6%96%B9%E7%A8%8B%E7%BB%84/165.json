{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 164, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "557317617423261696", "questionArticle": "<p>1．我国古代数学著作《孙子算经》有“多人共车”问题：“今有三人共车，二车空；二人共车，九人步．问：人与车各几何？”其大意如下：有若干人要坐车，如果每  $ 3 $   人坐一辆车，那么有  $ 2 $  辆车空；如果每  $ 2 $  人坐一辆车，那么有  $ 9 $  人需要步行，问人与车各多少？设共有  $ x $   人，  $ y $   辆车．则可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．  $ \\begin{cases}3\\left(y+2\\right)=x\\\\ 2y+9=x\\end{cases} $</p><p>B．  $ \\begin{cases}3\\left(y-2\\right)=x\\\\ 2y-9=x\\end{cases} $</p><p>C．  $ \\begin{cases}3\\left(y+2\\right)=x\\\\ 2y-9=x\\end{cases} $</p><p>D．  $ \\begin{cases}3\\left(y-2\\right)=x\\\\ 2y+9=x\\end{cases} $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁沈阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557317606375464960", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "557317606375464960", "title": "辽宁省沈阳市第一二六中学2024−2025学年八年级下学期3月考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557232745493078016", "questionArticle": "<p>2．阅读下列材料，并补全解答过程：</p><p>问题：某饭店工作人员第一次买了13只鸡，5只鸭，9只鹅共用了925元．第二次买了2只鸡，4只鸭，3只鹅共用了320元，试问第三次买了鸡，鸭，鹅各一只共需多少元？（假定三次购买鸡，鸭，鹅的单价不变）</p><p>解：设鸡，鸭，鹅的单价分别为 $ x,y,z $ 元．依题意，得 $ \\begin{cases} 13x+5y+9z=925 \\\\ 2x+4y+3z=320 \\end{cases}  $ </p><p>上述方程组可变形为 $ \\begin{cases} 5\\left ( { x+y+z } \\right ) +4\\left ( { 2x+z } \\right ) =925 \\\\ 4\\left ( { x+y+z } \\right ) -\\left ( { 2x+z } \\right ) =320 \\end{cases}  $ </p><p>设 $ x+y+z=a,2x+z=b $ ，上述方程组可化为 $ \\begin{cases} 5a+4b=925① \\\\ 4a-b=320② \\end{cases}  $ </p><p> $ { \\rm{ ① } }+{ \\rm{ ② } }\\times 4 $ 得： $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，即 $ x+y+z= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>答：第三次买鸡，鸭，鹅各一只共需<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．</p><p>阅读后，细心的你．可以解决下列问题：</p><p>(1)选择题：上述材料中的解答过程运用了<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>思想方法来指导解题．</p><p>A．整体带入&nbsp;&nbsp;&nbsp;&nbsp;B．数形结合&nbsp;&nbsp;&nbsp;&nbsp;C．分类讨论</p><p>(2)某校体育组购买体育用品甲，乙，丙，丁的件数和用钱金额如下表：</p><table style=\"border: solid 1px;border-collapse: collapse; width:358.5pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>品名</p><p>次数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>甲</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>乙</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>丙</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>丁</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>用钱金额（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第一次购买件数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1882</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第二次购买件数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>9</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>7</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>2764</p></td></tr></table><p>那么购买每种体育用品各一件共需多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024福建泉州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16425|16438", "keyPointNames": "二元一次方程组的特殊解法|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232720440500224", "questionFeatureName": "阅读材料题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557232720440500224", "title": "福建省泉州科技中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557232738182406144", "questionArticle": "<p>3．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x+2y=k \\\\ 2x+3y=3k-1 \\end{cases}  $ ．以下结论：①当<i>k</i>＝0时，方程组的解也是方程<i>x</i>−2<i>y</i>＝−4的解；②存在实数<i>k</i>，使得<i>x</i>+<i>y</i>＝0；③不论<i>k</i>取什么实数，<i>x</i>+3<i>y</i>的值始终不变；④若3<i>x</i>+2<i>y</i>＝6，则<i>k</i>＝1．其中正确的序号是 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024福建泉州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232720440500224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557232720440500224", "title": "福建省泉州科技中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557232742737420288", "questionArticle": "<p>4．江汉区某中学组织七年级同学参加校外活动，原计划租用45座客车若干辆，但有15人没有座位；如果租用同样数量的60座客车，则多出一辆，且其余客车刚好坐满．已知45座和60座客车的租金分别为220元／辆和300元／辆．</p><p>(1)设原计划租45座客车 $ x $ 辆，七年级共有学生 $ y $ 人，则 $ y= $ _（用含 $ x $ 的式子表示）；若租用60座客车，则 $ y= $ _（用含 $ x $ 的式子表示）；</p><p>(2)七年级共有学生多少人？</p><p>(3)若同时租用两种型号的客车或只租一种型号的客车，每辆客车恰好坐满并且每个同学都有座位，共有哪几种租车方案？哪种方案更省钱？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024福建泉州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16434|16438", "keyPointNames": "方案问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232720440500224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557232720440500224", "title": "福建省泉州科技中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557232741940502528", "questionArticle": "<p>5．在解方程组 $ \\begin{cases} ax+5y=15 \\\\ 4x-by=-2 \\end{cases}  $ 时，甲看错了方程组中<i>a</i>的值，得到的解为 $ \\begin{cases} x=-3 \\\\ y=-1 \\end{cases}  $ ，乙看错了方程组中<i>b</i>的值，得到的解为 $ \\begin{cases} x=5 \\\\ y=4 \\end{cases}  $ ．求原方程组的解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024福建泉州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232720440500224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557232720440500224", "title": "福建省泉州科技中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557232740732542976", "questionArticle": "<p>6．已知方程组 $ \\begin{cases} 3x+5y=k+2 \\\\ 5x+3y=k \\end{cases}  $ 的解 $ x $ 、 $ y $ 的和等于 $ -1 $ ．求 $ k $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024福建泉州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232720440500224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557232720440500224", "title": "福建省泉州科技中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557232738845106176", "questionArticle": "<p>7．（1）解方程： $ \\dfrac { 3x+5 } { 2 }=\\dfrac { 2x-1 } { 3 } $ ．</p><p>（2）解方程组： $ \\begin{cases} 3x-2y=8 \\\\ 2x+y=3 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024福建泉州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232720440500224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557232720440500224", "title": "福建省泉州科技中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557232739465863168", "questionArticle": "<p>8．若方程组 $ \\begin{cases} 3x-y=7 \\\\ ax+y=b \\end{cases}  $ 和方程组 $ \\begin{cases} x+by=a \\\\ 2x-y=8 \\end{cases}  $ 有相同的解，求方程组的解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024福建泉州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16424|16427", "keyPointNames": "加减消元法解二元一次方程组|同解方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232720440500224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557232720440500224", "title": "福建省泉州科技中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557232737410654208", "questionArticle": "<p>9．由方程组 $ \\begin{cases} 2x+y=m \\\\ x+3y=3-m \\end{cases}  $ 可得出 $ x $ 与 $ y $ 的关系是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024福建泉州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232720440500224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557232720440500224", "title": "福建省泉州科技中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557232735267364864", "questionArticle": "<p>10．已知 $ \\begin{cases} x=-1 \\\\ y=2 \\end{cases}  $ 是二元一次方程 $ mx+2y=1 $ 的解，则m的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024福建泉州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232720440500224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557232720440500224", "title": "福建省泉州科技中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 165, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 165, "timestamp": "2025-07-01T02:20:23.980Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}