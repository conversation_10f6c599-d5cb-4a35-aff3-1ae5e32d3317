{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 45, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "584431163265036288", "questionArticle": "<p>1．盲盒近来火爆，这种不确定的“盲抽”模式受到了大家的喜爱，一服装厂用某种布料生产玩偶<i>A</i>与玩偶<i>B</i>组合成一批盲盒，一个盲盒搭配1个玩偶<i>A</i>和2个玩偶<i>B</i>，已知每米布料可做1个玩偶<i>A</i>或3个玩偶<i>B</i>，现计划用135米这种布料生产这批盲盒（不考虑布料的损耗），设用<i>x</i>米布料做玩偶<i>A</i>，用<i>y</i>米布料做玩偶<i>B</i>，使得恰好配套，则需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>米布料做玩偶<i>A</i>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏无锡市天一实验中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584431136908029952", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584431136908029952", "title": "江苏省无锡市天一实验学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564580923267653632", "questionArticle": "<p>2．解方程组： $ \\begin{cases} 3x+\\dfrac { 1 } { 2 }y=8 \\\\ 2x-\\dfrac { 1 } { 2 }y=2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2020山东淄博 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-06-06", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "207148430937858048", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "207148430937858048", "title": "山东省淄博市2020年中考数学试题", "paperCategory": 1}, {"id": "564580905399918592", "title": "吉林省吉林市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584431156768059392", "questionArticle": "<p>3．表1为二元一次方程 $ a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } $ 的部分解，表2为二元一次方程 $ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } $ 的部分解，则方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ 的解为 （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 30.4pt;\"><p>表1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 16.7pt;\"><p><i>x</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ {-1} $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p>2</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 30.4pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 16.7pt;\"><p><i>y</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ {-1} $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ -2 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 30.4pt;\"><p>表2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 16.7pt;\"><p><i>x</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p>0</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p>2</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 30.4pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 16.7pt;\"><p><i>y</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ -2 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ {-1} $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p>0</p></td></tr></table><p>A． $ \\begin{cases} x=2 \\\\ y=-2 \\end{cases}  $　　　　B． $ \\begin{cases} x=-1 \\\\ y=1 \\end{cases}  $　　　　C． $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $　　　　D． $ \\begin{cases} x=3 \\\\ y=1 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏无锡市天一实验中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584431136908029952", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584431136908029952", "title": "江苏省无锡市天一实验学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "584431150371745792", "questionArticle": "<p>4．如图，足球的表面是由 $ 32 $ 块呈多边形的黑、白皮块缝合而成的，已知黑色皮块数比白色皮块数的一半多 $ 2 $ 块，则白色皮块的块数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/08/2/1/0/0/0/586892335897686017/images/img_1.png\" style='vertical-align:middle;' width=\"91\" alt=\"试题资源网 https://stzy.com\">  </p><p>A．18　　　　B．20　　　　C．22　　　　D．24</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏无锡市天一实验中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584431136908029952", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "584431136908029952", "title": "江苏省无锡市天一实验学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "584431502814912512", "questionArticle": "<p>5．蓝天白云下，青山绿水间，支一顶帐篷，邀亲朋好友，听蝉鸣，闻清风，话家常，好不惬意．某景区为响应文化和旅游部《关于推动露营旅游休闲健康有序发展的指导意见》精神，购买 $ A、B $ 两种型号的帐篷．若购买<i>A</i>种型号帐篷2顶和 $ B $ 种型号帐篷4顶，则需5200元；若购买<i>A</i>种型号帐篷3顶和 $ B $ 种型号帐篷1顶，则需2800元．求每顶<i>A</i>种型号帐篷和每顶 $ B $ 种型号帐篷的价格？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584431473387675648", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "584431473387675648", "title": "吉林省长春市七校2024−2025学年九年级下学期5月阶段质量检测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584432770073534464", "questionArticle": "<p>6．（1）计算： $ \\sqrt[3] { -8 }+{\\left( { -2 } \\right) ^ {-1}}-{\\left( { \\sqrt { 12 } } \\right) ^ {0}} $ ；</p><p>（2）解方程组： $ \\begin{cases} 2x-y=5 \\\\ 4x+3y=-10 \\end{cases}  $ .</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南焦作 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16299|16323|16372|16424", "keyPointNames": "实数的运算|零指数幂|负整数指数幂|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584432745503301632", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584432745503301632", "title": "2025年河南省焦作市焦作中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584432065908617216", "questionArticle": "<p>7．郑州市雾霾天气趋于严重，丹尼斯商场根据民众健康需要，代理销售每台 进价分别为600元、560元的<i>A</i>、<i>B</i>两种型号的空气净化器，如表是近两周的销售情况：</p><table style=\"border: solid 1px;border-collapse: collapse; width:211pt;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> 销售时段</p><p>&nbsp;</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> 销售数量</p><p>&nbsp;</p></td><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> 销售收入</p><p>&nbsp;</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> <i>A</i>种型号</p><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> <i>B</i>种型号</p><p>&nbsp;</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> 第一周</p><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> 4台</p><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> 5台</p><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> 7100元</p><p>&nbsp;</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> 第二周</p><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> 6台</p><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> 10台</p><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> 12600元</p><p>&nbsp;</p></td></tr></table><p>（进价、售价均保持不变，利润=销售收入﹣进货成本）</p><p>（1）求<i>A</i>、<i>B</i>两种型号的空气净化器的销售单价；</p><p>（2）若商场准备用不多于17200元的金额再采购这两种型号的空气净化器共30台，超市销售完这30台空气净化器能否实现利润为6200元的目标，若能，请给出相应的采购方案；若不能，请说明理由．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南许昌 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584432024183681024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "584432024183681024", "title": "河南省许昌市2024−2025学年九年级下学期第三次联考数学试题试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584432052574924800", "questionArticle": "<p>8．（1）解方程组： $ \\begin{cases} x+2y=3 \\\\ x-2y=1 \\end{cases}  $ </p><p>（2）先化简，再求值： $ (x+y){^{2}}+x(x-2y) $ ，其中 $ x=1 $ ， $ y=-2 $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南许昌 · 临考冲刺", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16333|16424", "keyPointNames": "整式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584432024183681024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "584432024183681024", "title": "河南省许昌市2024−2025学年九年级下学期第三次联考数学试题试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584433838966747136", "questionArticle": "<p>9．如图，将正方形 $ ABCD $ 沿 $ AE $ （点<i>E</i>在边 $ CD $ 上）所在直线折叠后，点<i>D</i>的对应点为点 $ D^{′} $ ， $ \\angle BAD^{′} $ 比 $ \\angle EAD{^{\\prime }} $ 大 $ 30{}\\degree  $ ，若设 $ \\angle BAD^{′}=x{}\\degree  $ ， $ \\angle EAD^{′}=y{}\\degree  $ ，则下列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/01/2/1/0/0/0/584433792816816131/images/img_17.png\" style=\"vertical-align:middle;\" width=\"146\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} x-y=30 \\\\ x+2y=90 \\end{cases}  $ B． $ \\begin{cases} x+y=30 \\\\ x+2y=90 \\end{cases}  $ C． $ \\begin{cases} x-y=30 \\\\ 2x+y=90 \\end{cases}  $ D． $ \\begin{cases} x+y=30 \\\\ 2x+y=90 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖北武汉 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16439|26530", "keyPointNames": "几何问题|折叠问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584433822873202688", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "584433822873202688", "title": "2025年湖北省武汉市武汉经开外国语学校初中学业水平考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "584433294118264832", "questionArticle": "<p>10．（1）化简 $ x\\left ( { x+2 } \\right ) +(x-1){^{2}} $ </p><p>（2）解方程组 $ \\begin{cases} 2x-y=5 \\\\ 4x+3y=-10 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南许昌 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16332|16333|16424", "keyPointNames": "完全平方公式|整式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584433271301251072", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "584433271301251072", "title": "2025年河南省许昌市第二次中招模拟考试 九年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 46, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 46, "timestamp": "2025-07-01T02:06:15.008Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}