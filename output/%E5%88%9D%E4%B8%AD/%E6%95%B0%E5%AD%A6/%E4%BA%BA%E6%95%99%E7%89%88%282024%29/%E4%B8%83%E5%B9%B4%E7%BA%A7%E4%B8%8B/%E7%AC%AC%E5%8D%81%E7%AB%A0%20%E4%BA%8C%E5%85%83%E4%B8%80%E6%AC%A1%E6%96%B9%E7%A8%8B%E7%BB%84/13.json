{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 12, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "592872820989800448", "questionArticle": "<p>1．已知方程组 $ \\begin{cases} 2x+5y=-6 \\\\ ax-by=-4 \\end{cases}  $ 和方程组 $ \\begin{cases} 3x-5y=16 \\\\ bx+ay=-8 \\end{cases}  $ 的解相同，求 $ {\\left( { 2a+b } \\right) ^ {2024}} $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024江苏扬州 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592872794754428928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592872794754428928", "title": "江苏省扬州苏东坡中学2023−2024学年下学期七年级数学期末考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592872818812956672", "questionArticle": "<p>2．（1）解方程组：</p><p>（1） $ \\begin{cases} 2x+y=12 \\\\ 3x-y=3 \\end{cases}  $ </p><p>（2）解不等式组： $ \\begin{cases} 5\\left ( { x+1 } \\right ) \\geqslant  2x-1 \\\\ \\dfrac { 2x+5 } { 3 } &gt; x+1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏扬州 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592872794754428928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592872794754428928", "title": "江苏省扬州苏东坡中学2023−2024学年下学期七年级数学期末考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592872814266331136", "questionArticle": "<p>3．若方程组 $ \\begin{cases} 3x+2y=m+2 \\\\ 2x+3y=4m+3 \\end{cases}  $ 的解<i>x</i>、<i>y</i>的和为7，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏扬州 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592872794754428928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592872794754428928", "title": "江苏省扬州苏东坡中学2023−2024学年下学期七年级数学期末考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592872810139136000", "questionArticle": "<p>4．若 $ \\left ( { a-2 } \\right ) x{^{\\left  | { a } \\right  | -1}}+3y=1 $ 是关于 $ x、y $ 的二元一次方程，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏扬州 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592872794754428928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592872794754428928", "title": "江苏省扬州苏东坡中学2023−2024学年下学期七年级数学期末考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592872806766915584", "questionArticle": "<p>5．请欣赏我国古典文学名著《西游记》描述孙悟空追妖精的数学诗：悟空顺风探妖踪，千里只行四分钟，归时四分行六百，风速多少才称雄？解释：孙悟空顺风去查妖精的行踪，4分钟就飞跃1000里，逆风返回时4分钟走了600里．若设孙悟空的速度为<i>x</i>里/分钟，风速为<i>y</i>里/分钟，则可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 4x+y=600 \\\\ 4x-y=1000 \\end{cases}  $ B． $ \\begin{cases} 4\\left ( { x+y } \\right ) =600 \\\\ 4\\left ( { x-y } \\right ) =1000 \\end{cases}  $ </p><p>C． $ \\begin{cases} 4x+y=1000 \\\\ 4x-y=600 \\end{cases}  $ D． $ \\begin{cases} 4\\left ( { x+y } \\right ) =1000 \\\\ 4\\left ( { x-y } \\right ) =600 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏扬州 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592872794754428928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592872794754428928", "title": "江苏省扬州苏东坡中学2023−2024学年下学期七年级数学期末考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592872803335974912", "questionArticle": "<p>6．下列各方程中，是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ y+5x $ B． $ x+y=1 $ </p><p>C． $ \\dfrac { 1 } { 5 }x=y{^{2}}+1 $ D． $ 3x+1=2xy $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024江苏扬州 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592872794754428928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592872794754428928", "title": "江苏省扬州苏东坡中学2023−2024学年下学期七年级数学期末考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "588451861662838784", "questionArticle": "<p>7．成语“五雀六燕”出自中国古代数学名著《九章算术》第八卷《方程》中一道名题．原题为：“今有五雀、六燕，集称之衡，雀俱重，燕俱轻．一雀一燕交而处，衡适平．并燕、雀重一斤．问燕、雀一枚各重几何？”译文为：“今有5只雀、6只燕，分别聚集而且用衡器称之，聚在一起的雀重，燕轻．将一只雀、一只燕交换位置而放，重量相等，5只雀、6只燕重量为1斤．问雀、燕每只各多重？”现设每只雀<i>x</i>斤，每只燕<i>y</i>斤，则可列出方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+6y=1 \\\\ 4y+x=5x+y \\end{cases}  $ B． $ \\begin{cases} 5y+6x=1 \\\\ 4x+y=5y+x \\end{cases}  $ C． $ \\begin{cases} 5x+6y=1 \\\\ 4x+y=5y+x \\end{cases}  $ D． $ \\begin{cases} 5y+6x=1 \\\\ 4y+x=5x+y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津和平 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588451844361334784", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588451844361334784", "title": "2025年天津市和平区九年级三模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "593052205688594432", "questionArticle": "<p>8．手工社团的同学制作两种手工艺品<i>A</i>和<i>B</i>，需要用到彩色纸和细木条，单个手工艺品材料用量如下表．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 63.05pt;\"><p>          材料</p><p>类别</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">彩色纸（张）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">细木条（捆）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 63.05pt;\"><p style=\"text-align:center;\">手工艺品<i>A</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">3</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 63.05pt;\"><p style=\"text-align:center;\">手工艺品<i>B</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">1</p></td></tr></table><p>如果一共用了17张彩色纸和10捆细木条，问他们制作的两种手工艺品各有多少个？设手工艺品<i>A</i>有<i>x</i>个，手工艺品<i>B</i>有<i>y</i>个，则<i>x</i>和<i>y</i>满足的方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+3y=17 \\\\ 2x+y=10 \\end{cases}  $　　　　B． $ \\begin{cases} 5x+3y=10 \\\\ 2x+y=17 \\end{cases}  $</p><p>C． $ \\begin{cases} 5x+2y=17 \\\\ 3x+y=10 \\end{cases}  $　　　　D． $ \\begin{cases} 5x+2y=10 \\\\ 3x+y=17 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16438|16440", "keyPointNames": "和差倍分问题|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593052189322420224", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "593052189322420224", "title": "2025年浙江省中考数学真题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "593050166661263360", "questionArticle": "<p>9．某商店销售<i>A</i>，<i>B</i>两种水果 $ {\\rm ．\\mathit{A}} $ 水果标价14元／千克，<i>B</i>水果标价18元／千克．</p><p>（1）小明陪妈妈在这家商店按标价买了<i>A</i>，<i>B</i>两种水果共3千克，合计付款46元．这两种水果各买了多少千克？</p><p>（2）妈妈让小明再到这家商店买 $ A,B $ 两种水果，要求<i>B</i>水果比<i>A</i>水果多买1千克，合计付款不超过50元．设小明买<i>A</i>水果 $ m $ 千克．</p><p>①若这两种水果按标价出售，求 $ m $ 的取值范围；</p><p>②小明到这家商店后，发现 $ A,B $ 两种水果正在进行优惠活动：<i>A</i>水果打七五折；一次购买<i>B</i>水果不超过1千克不优惠，超过1千克后，超过1千克的部分打七五折．（注：“打七五折”指按标价的 $ 75\\% $ 出售．）若小明合计付款48元，求 $ m $ 的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北 · 中考真题", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16406|16438|16486", "keyPointNames": "销售盈亏问题|和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593050136747487232", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "593050136747487232", "title": "2025年湖北省中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593051374981521408", "questionArticle": "<p>10．为助力乡村振兴，支持惠农富农，某合作社销售我省西部山区出产的甲、乙两种苹果．已知2箱甲种苹果和3箱乙种苹果的售价之和为440元；4箱甲种苹果和5箱乙种苹果的售价之和为800元．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/26/2/1/0/0/0/593425861958742017/images/img_1.png\" style='vertical-align:middle;' width=\"134\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）求甲、乙两种苹果每箱的售价．</p><p>（2）某公司计划从该合作社购买甲、乙两种苹果共12箱，且乙种苹果的箱数不超过甲种苹果的箱数．求该公司最少需花费多少元．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南 · 中考真题", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593051347072622592", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "593051347072622592", "title": "2025年河南省中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 13, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 13, "timestamp": "2025-07-01T02:02:18.466Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}