{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 29, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "589939936511434752", "questionArticle": "<p>1．函数 $ y=\\left ( { 3-m } \\right ) x+n $ （<i>m</i>，<i>n</i>为常数， $ m\\ne 3 $ ）若 $ 2m-n=2 $ ，当 $ -1\\leqslant  x\\leqslant  1 $ 时，函数有最大值0，则 $ n= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东青岛 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16426|16535", "keyPointNames": "二元一次方程组的应用|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589939913774112768", "questionMethodName": "分类讨论思想", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589939913774112768", "title": "山东省青岛市2024−2025学年下学期第三次月考八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589939227200102400", "questionArticle": "<p>2．某中学组织学生参与校园手工制作与义卖实践活动，同学们负责制作并售卖手工艺品纸艺花和手工编织挂件，已知纸艺花每个成本15元，每个售价20元，手工编织挂件每个成本8元，每个售价14元．在第一次义卖活动中，学生共卖出了150件手工艺品，总收入为2496元．</p><p>（1）请求出纸艺花和手工编织挂件各销售了多少个？</p><p>（2）学校计划筹备第二次义卖活动，需制作纸艺花和手工编织挂件共80件，要求总成本不超过885元，且纸艺花的数量不低于手工编织挂件数量的 $ \\dfrac { 5 } { 7 } $ ,请为第二次义卖活动设计一种利润最大的方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏盐城 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16438|16490", "keyPointNames": "和差倍分问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589939200767598592", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589939200767598592", "title": "江苏省盐城市三校联考5月2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589939226373824512", "questionArticle": "<p>3．已知方程组 $ \\begin{cases} x-y=1+3a \\\\ x+y=-7-a \\end{cases}  $ </p><p>（1）若原方程组中 $ x $ 为非正数， $ y $ 为负数，求 $ a $ 的取值范围；</p><p>（2）在（1）的条件下，若 $ 2x-y  <  6 $ ，求 $ a $ 的最小的整数解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏盐城 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589939200767598592", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589939200767598592", "title": "江苏省盐城市三校联考5月2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589935840731639808", "questionArticle": "<p>4．定义：关于<i>x</i>，<i>y</i>的二元一次方程 $ ax+by=c $  （其中 $ a\\ne b\\ne c $ ）中的常数项<i>c</i>与未知数<i>x</i>系数<i>a</i>互换，得到的方程叫“变更方程”，例如： $ ax+by=c $ ”变更方程”为 $ cx+by=a $ ．</p><p>（1）方程 $ 3x+2y=4 $ 与它的“变更方程”组成的方程组的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）已知关于<i>x</i>，<i>y</i>的二元一次方程 $ ax+by=c $ 的系数满足 $ a+b+c=0 $ ，且 $ ax+by=c $ 与它的“变更方程”组成的方程组的解恰好是关于<i>x</i>，<i>y</i>的二元一次方程 $ mx+ny=p $ 的一个解，求代数式 $ \\left ( { m+n } \\right ) m-p\\left ( { n+p } \\right ) +2025 $ 的值；</p><p>（3）已知整数<i>m</i>，<i>n</i>，<i>t</i>且<i>t</i>满足 $ 6  <  t  <  22 $ ，并且 $ \\left ( { 10m-t } \\right ) x+2025y=m+t $ 是关于<i>x</i>，<i>y</i>的二元一次方程 $ \\left ( { 1+n } \\right ) x+2025y=2m+2 $ 的“变更方程”，求<i>m</i>的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025江西抚州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16420|16424|16489", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589935810452959232", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589935810452959232", "title": "江西省抚州市六校2024−2025学年八年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589935684825161728", "questionArticle": "<p>5．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x-y=5a+3 \\\\ x+y=3a+9 \\end{cases}  $ 的解<i>x</i>，<i>y</i>都是正数，</p><p>（1）求 $ a $ 的取值范围.</p><p>（2）化简： $ \\left  | { \\,4a+8\\, } \\right  | \\,-\\,\\left  | { \\,a-3\\, } \\right  |  $ .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西吉安 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16256|16424|16489", "keyPointNames": "化简绝对值|加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589935663299993600", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589935663299993600", "title": "江西省吉安市2024−2025学年八年级下学期第二次阶段性考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589935555363778560", "questionArticle": "<p>6．《九章算术》中记载：“今有甲乙二人持钱不知其数，甲得乙半而钱五十，乙得甲太半而亦钱五十，问甲、乙持钱各几何？”译文是：今有甲、乙两人持钱不知道各有多少，甲若得到乙所有钱的 $ \\dfrac { 1 } { 2 } $ ，则甲有50钱，乙若得到甲所有钱的 $ \\dfrac { 2 } { 3 } $ ，则乙也有50钱，问甲、乙各持钱多少？设甲持钱数为<i>x</i>钱，乙持钱数为<i>y</i>钱，列出关于<i>x</i>，<i>y</i>的二元一次方程组是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江西吉安 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589935538657865728", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589935538657865728", "title": "江西省吉安市2024−2025学年下学期学考模拟考试九年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589935149480980480", "questionArticle": "<p>7．列方程（组）解应用题．</p><p>用2辆<i>A</i>型车和1辆<i>B</i>型车装满货物一次可运货10吨；用1辆<i>A</i>型车和2辆<i>B</i>型车装满货物一次可运货11吨．某物流公司现有27吨货物，计划同时租用<i>A</i>型车<i>a</i>辆，<i>B</i>型车<i>b</i>辆，一次运完，且恰好每辆车都装满货物．根据以上信息，解答下列问题,</p><p>（1）1辆<i>A</i>型车和1辆车<i>B</i>型车都装满货物一次可分别运货多少吨？</p><p>（2）求该公司的租车方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁营口等地 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589935117071593472", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589935117071593472", "title": "辽宁省营口市、鞍山市部分学校2024−2025学年七年级下学期5月联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589935145051795456", "questionArticle": "<p>8．解方程组： $ \\begin{cases} x-y=3 \\\\ 2y+3\\left ( { x-y } \\right ) =11 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁营口等地 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589935117071593472", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589935117071593472", "title": "辽宁省营口市、鞍山市部分学校2024−2025学年七年级下学期5月联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589935142346469376", "questionArticle": "<p>9．若方程组 $ \\begin{cases} m+4n=2+3a \\\\ 5m+2n=1-a \\end{cases}  $ 的解满足 $ m+n=3 $ ，则 $ a {\\rm =} $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁营口等地 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589935117071593472", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589935117071593472", "title": "辽宁省营口市、鞍山市部分学校2024−2025学年七年级下学期5月联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589935137875341312", "questionArticle": "<p>10．观察下表可知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=m \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=n \\end{cases}  $ 的解为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td colspan=\"5\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ a{{}_{ 1 } }x+b{{}_{ 1 } }y=m $ 的解</p></td><td colspan=\"5\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ a{{}_{ 2 } }x+b{{}_{ 2 } }y=n $ 的解</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ x $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ -1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">0</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ x $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ -1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">…</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ y $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">6</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ y $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">0</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">…</p></td></tr></table><p>A． $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ B． $ \\begin{cases} x=4 \\\\ y=5 \\end{cases}  $ C． $ \\begin{cases} x=-1 \\\\ y=6 \\end{cases}  $ D． $ \\begin{cases} x=-1 \\\\ y=3 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁营口等地 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589935117071593472", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589935117071593472", "title": "辽宁省营口市、鞍山市部分学校2024−2025学年七年级下学期5月联考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 30, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 30, "timestamp": "2025-07-01T02:04:19.975Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}