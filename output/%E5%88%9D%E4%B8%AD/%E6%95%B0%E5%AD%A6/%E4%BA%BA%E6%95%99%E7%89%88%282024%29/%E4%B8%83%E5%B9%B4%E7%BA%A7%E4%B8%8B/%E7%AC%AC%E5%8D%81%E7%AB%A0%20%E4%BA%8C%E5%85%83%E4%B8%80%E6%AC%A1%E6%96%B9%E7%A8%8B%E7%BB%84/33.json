{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 32, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "587753245289783296", "questionArticle": "<p>1．中国古代数学著作《九章算术》，中记载了这样一个题目：五只雀、六只燕，共重 $ 16 $ 两，雀重燕轻．互换其中一只，恰好一样重．问：每只雀，燕的重量各为多少？设雀每只 $ x $ 两，燕每只 $ y $ 两，则可列出方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+6y=16 \\\\ 5x+y=6y+x \\end{cases}  $ B． $ \\begin{cases} 5x+6y=16 \\\\ 4x+y=5y+x \\end{cases}  $ </p><p>C． $ \\begin{cases} 6x+5y=16 \\\\ 6x+y=5y+x \\end{cases}  $ D． $ \\begin{cases} 6x+5y=16 \\\\ 5x+y=4y+x \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江绍兴 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-15", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587753230488084480", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "587753230488084480", "title": "浙江省绍兴市绍初教育集团2024−2025学年下学期5月九年级大单元教学效果检测数学试题（二模）", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "587753089240707072", "questionArticle": "<p>2．《孙子算经》是我国古代著名的数学典籍，其中有一道题：“今有木，不知长短．引绳度之，余绳四尺五寸；屈绳度之，不足一尺．木长几何？”意思是：用一根绳子去量一根长木，绳子还剩余4.5尺；将绳子对折再量长木，长木还剩余1尺．问木长多少尺？设木长 $ x $ 尺，绳子长 $ y $ 尺，则可以列出的方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y-x=4.5 \\\\ x-0.5y=1 \\end{cases}  $ B． $ \\begin{cases} y-x=4.5 \\\\ x+0.5y=1 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=4.5 \\\\ x-y=1 \\end{cases}  $ D． $ \\begin{cases} x+y=4.5 \\\\ y-x=1 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏宿迁 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-06-15", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587753074736803840", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "587753074736803840", "title": "江苏省宿迁市钟吾初级中学2025年中考二模数学试卷", "paperCategory": 1}, {"id": "591000972480000000", "title": "2025年湖南省长沙市长郡雨花外国语学校中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "587752838580711424", "questionArticle": "<p>3．解方程组和不等式组：</p><p>（1） $ \\begin{cases} 2x+3y=14 \\\\ x-y=2 \\end{cases}  $ ．</p><p>（2） $ \\begin{cases} 4x-3\\geqslant  9 \\\\ \\dfrac { x+1 } { 2 }\\geqslant  x-2 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏常州 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-15", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587752811506479104", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "587752811506479104", "title": "江苏省常州外国语学校 2024一2025学年下学期九年级二模考试 数 学", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587752216150188032", "questionArticle": "<p>4．解方程组： $ \\begin{cases} x-y=1 \\\\ x-2y=2 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建泉州 · 临考冲刺", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-15", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587752191319908352", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "587752191319908352", "title": "福建省南安市2024−2025学年初中毕业班教学质量监测初三年数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587752100643250176", "questionArticle": "<p>5．已知二元一次方程组 $ \\begin{cases} a-2b=3 \\\\ a+b=-7 \\end{cases}  $ ，则 $ 2a-b $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州等地 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-15", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587752074084917248", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "587752074084917248", "title": "2025年浙江省杭州市余杭临平区九年级中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587751992790921216", "questionArticle": "<p>6．某食品加工厂根据订单的需求会不定期采购<i>A</i>，<i>B</i>两种食材（单位：件），而两种食材的单价会根据市场变化波动．</p><p>（1）第一周，该食品加工厂花费6650元一次性采购<i>A</i>，<i>B</i>两种食材共100件，此时<i>A</i>，<i>B</i>两种食材的单价分别是50元、80元，求食品加工厂采购了<i>A</i>，<i>B</i>两种食材各多少件？</p><p>（2）第二周，由于采购价格发生了变化，食品加工厂分别花费1800元、3600元一次性购买<i>A</i>，<i>B</i>两种食材，已知采购<i>B</i>种食材的数量是<i>A</i>种食材数量的1.5倍，每件<i>A</i>种食材的单价比每件<i>B</i>种食材的单价少20元，求食品加工厂第二周采购<i>A</i>种食材多少件？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆实验中学 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-15", "keyPointIds": "16438|16476", "keyPointNames": "和差倍分问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587751961522384896", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "587751961522384896", "title": "2025年 重庆市巴南区市实验集团九年级中考联考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587751033557786624", "questionArticle": "<p>7．《算法统宗》里有这样一道题：我问开店李三公，众客都来到店中．一房七客多七客，一房九客一房空．李三公家的店有<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>间客房，来了<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>房客．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东滨州 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-15", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587751015341924352", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "587751015341924352", "title": "2025年山东省滨州市初中学生学业质量检测数学试题二", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586669538692870144", "questionArticle": "<p>8．为进一步落实“乡村振兴”工程，某村在政府的扶持下建起了大棚基地，准备种植<i>A</i>，<i>B</i>两种蔬菜．若种植30亩<i>A</i>种蔬菜和50亩<i>B</i>种蔬菜，总收入为42万元；若种植50亩<i>A</i>种蔬菜和30亩<i>B</i>种蔬菜，总收入为38万元．</p><p>（1）求种植<i>A</i>，<i>B</i>两种蔬菜，平均每亩收入各是多少万元？</p><p>（2）村里规划种植这两种蔬菜共250亩，且<i>A</i>种蔬菜的种植面积不少于<i>B</i>种蔬菜种植面积的1.5倍，<i>A</i>种蔬菜至少种植多少亩？</p><p>（3）在（2）的条件下应如何种植<i>A</i>，<i>B</i>两种蔬菜，总收入最大，最大总收入是多少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江西吉安 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-15", "keyPointIds": "16438|16486|16535", "keyPointNames": "和差倍分问题|一元一次不等式的应用|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586669513472520192", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586669513472520192", "title": "江西省吉安市七校联考2024−2025学年八年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587750372401258496", "questionArticle": "<p>9．把一些糖果分给学生，如果每名学生分5颗糖果，那么多3颗糖果；如果每名学生分6颗糖果，那么最后一名学生只有4颗糖果．设一共有 $ x $ 名学生， $ y $ 颗糖果，则可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y=5x+3 \\\\ y=6x-4 \\end{cases}  $ B． $ \\begin{cases} y=5x+3 \\\\ y=6x-2 \\end{cases}  $ </p><p>C． $ \\begin{cases} y=5x-3 \\\\ y=6x+4 \\end{cases}  $ D． $ \\begin{cases} y=5x-3 \\\\ y=6x+2 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-14", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587750359575076864", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "587750359575076864", "title": "2025年江苏省南京市建邺区中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "587750008000126976", "questionArticle": "<p>10．某工厂有甲、乙两个车间，甲车间生产 $ \\mathrm{ A } $ 产品，乙车间生产 $ B $ 产品．已知销售 $ \\mathrm{ A } $ 产品 $ 30 $ 件， $ B $ 产品 $ 20 $ 件，共收入 $ 680 $ 元；销售 $ \\mathrm{ A } $ 产品 $ 50 $ 件， $ B $ 产品 $ 40 $ 件，共收入 $ 1240 $ 元．</p><p>（1）求 $ \\mathrm{ A } $ ， $ B $ 两种产品的销售单价．</p><p>（2）若该工厂销售 $ \\mathrm{ A } $ ， $ B $ 两种产品共 $ 300 $ 件，总收入不超过 $ 4000 $ 元，则最少要销售 $ \\mathrm{ A } $ 产品多少件？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁葫芦岛 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-06-14", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590556085401985024", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590556085401985024", "title": "2025年辽宁省葫芦岛市中考数学二模试卷", "paperCategory": 11}, {"id": "587749978241540096", "title": "2025年湖南省长沙市一中新华都初中学业水平考试模拟数学试卷（二）", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 33, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 33, "timestamp": "2025-07-01T02:04:42.348Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}