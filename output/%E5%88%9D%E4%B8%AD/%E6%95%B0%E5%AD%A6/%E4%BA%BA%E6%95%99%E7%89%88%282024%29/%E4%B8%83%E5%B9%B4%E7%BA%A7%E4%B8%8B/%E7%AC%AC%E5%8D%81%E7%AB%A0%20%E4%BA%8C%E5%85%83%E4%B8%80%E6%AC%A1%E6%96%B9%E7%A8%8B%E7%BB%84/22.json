{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 21, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "589574553749204992", "questionArticle": "<p>1．《九章算术》中有一个问题大意为：五只雀、六只燕，共重1斤(等于 16两)，雀重燕轻．互换其中一只，恰好一样重．问每只雀、燕的质量各为多少．若设每只雀重<i>x</i>两，每只燕重<i>y</i>两，根据题意，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+6y=16 \\\\ 4x+y=5y+x \\end{cases}  $ B． $ \\begin{cases} 5x+6y=16 \\\\ 4x=5y \\end{cases}  $ C． $ \\begin{cases} x+y=16 \\\\ 4x+y=5y+x \\end{cases}  $ D． $ \\begin{cases} x+y=16 \\\\ 4x=5y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州市杨桥中学 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-21", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589574535059386368", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589574535059386368", "title": "2025年福建省福州杨桥中学中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "591263860587278336", "questionArticle": "<p>2．某仓库放置若干个A型部件和B型部件.已知1个A型部件和2个B型部件的总质量为2.8吨，2个A型部件和3个B型部件的质量刚好相等.</p><p>(1)求1个A型部件和1个B型部件的质量各是多少?</p><p style=\"text-indent:6pt;\">(2)现有一种我国自产的卡车，最大额定载重质量为15吨，要用一辆这种卡车运输16个两种部件去往某地，由于其它方面都满足运输要求，只需考虑所载部件的总质量不能超过汽车的最大额定载重量.求这辆卡车最少要运输多少个B型部件?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南平顶山 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-21", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591263833320108032", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "591263833320108032", "title": "2025河南省平顶山市鲁山县四校中考三模数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "591264172454748160", "questionArticle": "<p>3．《九章算术》卷八方程第十题原文为：“今有甲、乙二人持钱不知其数，甲得乙半而钱五十，乙得甲太半而亦钱五十．问：甲、乙持钱各几何？”题目大意是：甲、乙两人各带了若干钱．如果甲得到乙所有钱的一半，那么甲共有钱50；如果乙得到甲所有钱的 $ \\dfrac{2}{3} $ ，那么乙也共有钱50，问：甲、乙两人各带了多少钱？设甲、乙两人持钱的数量分别为 $ x $ 、 $ y $ ，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}x+\\dfrac{1}{2}y=50\\\\ x+\\dfrac{2}{3}y=50\\end{cases} $ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;B． $ \\begin{cases}\\dfrac{1}{2}x+y=50\\\\ x+\\dfrac{2}{3}y=50\\end{cases} $ &nbsp;&nbsp;&nbsp;C． $ \\begin{cases}x+\\dfrac{1}{2}y=50\\\\ y+\\dfrac{2}{3}x=50\\end{cases} $ &nbsp;&nbsp;&nbsp;D． $ \\begin{cases}\\dfrac{1}{2}x+y=50\\\\ y+\\dfrac{2}{3}x=50\\end{cases} $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北十堰 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-21", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591264158710013952", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "591264158710013952", "title": "2025年湖北省十堰市实验中学中考模拟数学试卷", "paperCategory": 11}], "questionTypeCode": "1"}, {"questionId": "591265366325637120", "questionArticle": "<p>4．解方程 $ ( $ 组 $ ) $ ：</p><p> $ (1)\\begin{cases}x-y=2\\\\ 2x+y=7\\end{cases} $ ；</p><p> $ (2)\\dfrac{3}{1-y}=\\dfrac{y}{y-1}-5. $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州第十四中学附属学校 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-21", "keyPointIds": "16424|16471", "keyPointNames": "加减消元法解二元一次方程组|解分式方程", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591265343122747392", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "591265343122747392", "title": "2024−2025学年浙江省杭州十四中附中七年级（下）月考数学试卷（6月份）", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "591265355244285952", "questionArticle": "<p>5．《算法统宗》中有这样一个问题 $ ( $ 如图 $ ) $ ，其大意为：有一群人分银子，如果每人分七两，则剩余四两；如果每人分九两，则还差八两.请问：所分的银子共有多少两？ $ ( $ 注：明代时1斤 $ =16 $ 两，故有“半斤八两”这个成语 $ .) $ 设有<i>x</i>个人，<i>y</i>两银子，根据题意可以列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/20/2/1/0/0/0/591265103871254532/images/img_7.png\" style=\"vertical-align:middle;\" alt=\"试题资源网 https://stzy.com\"></p><p>A．  $ \\begin{cases}4x+7=y\\\\ 9x-8=y\\end{cases} $ B．  $ \\begin{cases}7x+4=y\\\\ 8x-9=y\\end{cases} $ C．  $ \\begin{cases}7x-4=y\\\\ 9x+8=y\\end{cases} $ D．  $ \\begin{cases}7x+4=y\\\\ 9x-8=y\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州第十四中学附属学校 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-21", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591265343122747392", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "591265343122747392", "title": "2024−2025学年浙江省杭州十四中附中七年级（下）月考数学试卷（6月份）", "paperCategory": 11}], "questionTypeCode": "1"}, {"questionId": "591003809159098368", "questionArticle": "<p>6．我们知道方程组的解与方程组中每个方程的系数和常数项有联系，系数和常数项经过一系列变形、运算就可以求出方程组的解．因此，在现代数学的高等代数学科将系数和常数项排成一个表的形式，规定：关于 $ x，y $ 的二元一次方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ 可以写成矩阵 $ \\begin{pmatrix} a{{}_{ 1 } } & b{{}_{ 1 } } & c{{}_{ 1 } } \\\\ a{{}_{ 2 } } & b{{}_{ 2 } } & c{{}_{ 2 } } \\end{pmatrix}  $ 的形式．例如： $ \\begin{cases} 2x+3y=7 \\\\ 4x-5y=3 \\end{cases}  $ 可以写成矩阵 $ \\begin{pmatrix} 2 & 3 & 7 \\\\ 4 & -5 & 3 \\end{pmatrix}  $ 的形式．</p><p>（1）填空：将 $ \\begin{cases} 2y-3=x \\\\ 3x-4y-7=0 \\end{cases}  $ 写成矩阵形式为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）若矩阵 $ \\begin{pmatrix} 3 & a & 5 \\\\ b & -1 & 3 \\end{pmatrix}  $ 所对应的方程组的解为 $ \\begin{cases} x=-1 \\\\ y=-4 \\end{cases}  $ ，求 $ a $ 与 $ b $ 的值．</p><p>（3）若矩阵 $ \\begin{pmatrix} m & 1 & p \\\\ n & -1 & q \\end{pmatrix}  $ 所对应的方程组的解是 $ \\begin{cases} x=3 \\\\ y=-2 \\end{cases}  $ ，求矩阵 $ \\begin{pmatrix} m & 2 & 2m+p \\\\ n & -2 & 2n+q \\end{pmatrix}  $ 对应的方程组的解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆万州第二中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591003768033947648", "questionFeatureName": "阅读材料题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "591003768033947648", "title": "重庆市万州二中教育集团2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "591003800820822016", "questionArticle": "<p>7．（1）解方程（组）：</p><p>① $ 3-2\\left ( { 3x-2 } \\right ) =1 $ ；</p><p>② $ \\begin{cases} 3x+y=-4 \\\\ x+5y=8 \\end{cases}  $ ．</p><p>（2）解不等式组： $ \\begin{cases} 2\\left ( { x-3 } \\right ) +9 > x \\\\ \\dfrac { 5x+2 } { 4 }\\geqslant  2x-1 \\end{cases}  $ ，在数轴上表示出它的解集，并计算所有整数解之和．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/24/2/1/0/0/0/592694764106985473/images/img_1.png\" style='vertical-align:middle;' width=\"244\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆万州第二中学 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-21", "keyPointIds": "16402|16424|16489|28266", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组|解一元一次不等式组|在数轴上表示不等式的解集", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591003768033947648", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "591003768033947648", "title": "重庆市万州二中教育集团2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "591003808030830592", "questionArticle": "<p>8．某奶茶店推出<i>A</i>，<i>B</i>两款新品奶茶，已知购买5杯<i>A</i>奶茶和4杯<i>B</i>奶茶共花费130元，购买3杯<i>A</i>奶茶和2杯<i>B</i>奶茶共花费72元．</p><p>（1）求 $ A $ ， $ B $ 两款奶茶的单价各为多少元？</p><p>（2）某班参加社会实践活动的有56位学生和4名教师，现该班决定团购下午茶，恰逢<i>A</i>，<i>B</i>两款新品奶茶搞促销活动，其中<i>A</i>奶茶在原来单价的基础上优惠5元， $ B $ 奶茶在原来单价的基础上打八折，在班级现有经费648元的情况下，若 $ B $ 奶茶的数量不少于34杯，且所有参加活动的师生均有下午茶享用，则共有哪几种购买方案？哪种方案花费最少？最少费用多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆万州第二中学 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-21", "keyPointIds": "16438|16490", "keyPointNames": "和差倍分问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591003768033947648", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "591003768033947648", "title": "重庆市万州二中教育集团2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590680308959195136", "questionArticle": "<p>9．《九章算术》中有一题：“今有大器五、小器一容三斛；大器一、小器五容二斛．问大、小器各容几何？”译文：今有大容器5个，小容器1个，总容量为3斛（斛：古代容是单位）；大容器1个，小容器5个，总容暴为2斛．问大容器、小容器的容量各是多少斛？设大容器的容量为 $ x $ 斛，小容器的容量为 $ y $ 斛，则可列方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+5y=3 \\\\ 5x+y=2 \\end{cases}  $ B． $ \\begin{cases} 5x+y=3 \\\\ x+5y=2 \\end{cases}  $ C． $ \\begin{cases} 5x=y+3 \\\\ x=5y+2 \\end{cases}  $ D． $ \\begin{cases} 5x=y+2 \\\\ x=5y+3 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都九中 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-20", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590680293637402624", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590680293637402624", "title": "2025年四川省成都市青羊区树德中学中考数学三诊试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590294550381375488", "questionArticle": "<p>10．【综合与实践】</p><p>有言道：“杆秤一头称起人间生计，一头称起天地良心”．某兴趣小组将利用物理学中杠杆原理制作简易杆秤.小组先设计方案，然后动手制作，再结合实际进行调试，请完成下列方案设计中的任务．</p><p>【知识背景】如图，称重物时，移动秤砣可使杆秤平衡，根据杠杆原理推导得： $ \\left ( { m{{}_{ 0 } }+m } \\right ) \\cdot l=M\\cdot (a+y) $ .其中秤盘质量 $ m{{}_{ 0 } } $ 克，重物质量<i>m</i>克，秤砣质量<i>M</i>克，秤纽与秤盘的水平距离为<i>l</i>厘米，秤纽与零刻线的水平距离为<i>a</i>厘米，秤砣与零刻线的水平距离为<i>y</i>厘米．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/17/2/1/0/0/0/590294492395118592/images/img_24.png\" style=\"vertical-align:middle;\" width=\"220\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;</p><p>【方案设计】</p><p>目标：设计简易杆秤．设定 $ m{{}_{ 0 } }=10 $ ， $ M=50 $ ，最大可称重物质量为1000克，零刻线与末刻线的距离定为50厘米．</p><p>任务一：确定<i>l</i>和<i>a</i>的值．</p><p>（1）当秤盘不放重物，秤砣在零刻线时，杆秤平衡，请列出关于<i>l</i>，<i>a</i>的方程；</p><p>（2）当秤盘放入质量为1000克的重物，秤砣从零刻线移至末刻线时，杆秤平衡，请列出关于<i>l</i>，<i>a</i>的方程；</p><p>（3）根据（1）和（2）所列方程，求出<i>l</i>和<i>a</i>的值．</p><p>任务二：确定刻线的位置．</p><p>（4）根据任务一，求<i>y</i>关于<i>m</i>的函数解析式；</p><p>（5）从零刻线开始，每隔100克在秤杆上找到对应刻线，请写出相邻刻线间的距离．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东广州 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-20", "keyPointIds": "16441|16547", "keyPointNames": "其他问题|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590294517636444160", "questionFeatureName": "综合与实践题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590294517636444160", "title": "2025年广东省广州市第五中学中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 22, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 22, "timestamp": "2025-07-01T02:03:25.505Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}