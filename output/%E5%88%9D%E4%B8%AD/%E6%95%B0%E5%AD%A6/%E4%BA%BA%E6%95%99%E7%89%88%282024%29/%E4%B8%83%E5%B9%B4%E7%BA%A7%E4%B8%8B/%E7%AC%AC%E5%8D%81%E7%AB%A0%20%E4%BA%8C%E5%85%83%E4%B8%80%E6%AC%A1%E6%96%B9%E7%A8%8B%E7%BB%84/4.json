{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 3, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "593320467257143296", "questionArticle": "<p>1．某工厂从外地连续两次购得 $ \\mathrm{ A } $ ， $ B $ 两种原料，购买情况如表：现计划租用甲，乙两种货车共8辆将两次购得的原料一次性运回工厂．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54.05pt;\"><p> $ \\mathrm{ A } $ （吨）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 53.15pt;\"><p> $ B $ （吨）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>费用（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>第一次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54.05pt;\"><p>12</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 53.15pt;\"><p>8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>33600</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>第二次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54.05pt;\"><p>8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 53.15pt;\"><p>4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>20800</p></td></tr></table><p>（1） $ \\mathrm{ A } $ ， $ B $ 两种原料每吨的进价各是多少元？</p><p>（2）已知一辆甲种货车可装4吨 $ \\mathrm{ A } $ 种原料和1吨种原料 $ B $ ；一辆乙种货车可装 $ \\mathrm{ A } $ ， $ B $ 两种原料各2吨．甲种货车的运费是每辆400元，乙种货车的运费是每辆350元．设安排甲种货车 $ x $ 辆，总运费为 $ W $ 元，求 $ W $ （元）与 $ x $ （辆）之间的函数关系式； $ x $ 为何值时，总运费 $ W $ 最小，最小值是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南益阳 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-29", "keyPointIds": "16440|16490|16535", "keyPointNames": "表格或图示问题|一元一次不等式组的应用|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593320429508407296", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "593320429508407296", "title": "2025年湖南省益阳市沅江市两校联考中考考前第三次模拟演练数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593320343416119296", "questionArticle": "<p>2．2025年春节档，电影《哪吒之魔童闹海》掀起观影热潮，影片通过粒子水墨技术、动态水墨渲染引擎等技术，将传统水墨画意境融入3<i>D</i>动画，向全球展示了“既古老又充满活力的中国形象”．某文创店订购了印有“哪吒”图案和“敖丙”图案的两种书签．经统计，订购2张“哪吒”书签与3张“敖丙”书签，成本共计47元；而订购4张“哪吒”书签和7张“敖丙”书签，则需花费成本103元．求每张“哪吒”书签和每张“敖丙”书签的成本价分别是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "460000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025海南 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-29", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593320320729128960", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "593320320729128960", "title": "2025年海南省三校中考模拟联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593320125928873984", "questionArticle": "<p>3．为助力珠海打造活力之城，丰富市民的业余文体生活，珠海某社区计划采购一批相同型号白匹克球拍（单位：副）和匹克球（单位：个）．若购买2副匹克球拍和5个匹克球，共花费370元；若购买4副匹克球拍和9个匹克球，共花费730元．</p><p>（1）求匹克球拍与匹克球的单价分别是多少元？</p><p>（2）由于社区参与文体活动的居民人数变化，采购需求有所调整．现需一次性购买匹克球拍匹克球数量之和为50，匹克球拍不少于5副，同时购买的总费用不能超过1500元．求满足件的采购方案有哪些？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东潮实 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593320098804310016", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "593320098804310016", "title": "2025年广东省汕头市潮阳实验学校中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593319951143841792", "questionArticle": "<p>4．某校的饮水机有温水、开水两个按钮，温水和开水共用一个出水口．温水的温度为 $ 30{}\\degree { \\rm{ C } } $ ，流速为 $ 20{ \\rm{ m } }{ \\rm{ l } }/{ \\rm{ s } } $ ；开水的温度为 $ 100{}\\degree { \\rm{ C } } $ ，流速为 $ 15{ \\rm{ m } }{ \\rm{ l } }/{ \\rm{ s } } $ ．某学生先接了一会儿温水，又接了一会儿开水，得到一杯 $ 210{ \\rm{ m } }{ \\rm{ l } } $ 温度为 $ 60{}\\degree { \\rm{ C } } $ 的水（不计热损失），求该学生分别接温水和开水的时间．</p><table style=\"border: solid 1px;border-collapse: collapse; width:414.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>物理常识</p><p>开水和温水混合时会发生热传递，开水放出的热量等于温水吸收的热量，可以转化为开水的体积×开水降低的温度=温水的体积×温水升高的温度．</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京四中 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593319914036834304", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "593319914036834304", "title": "2025年北京市第四中学中考三模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593319286912888832", "questionArticle": "<p>5．我国古代数学名著《张邱建算经》中记载：“今有清酒一斗直粟十斗，醑酒一斗直粟三斗，今持粟三斛，得酒五斗，问清、醑酒各几何？”意思是：现在一斗清酒价值10斗谷子，一斗醑酒价值3斗谷子，现在拿30斗谷子，共换了5斗酒，问清、醑酒各几斗？如果设清酒<i>x</i>斗，醑酒<i>y</i>斗，那么可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=5 \\\\ 10x+3y=30 \\end{cases}  $ B． $ \\begin{cases} x+y=5 \\\\ 3x+10y=30 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=5 \\\\ \\dfrac { x } { 10 }+\\dfrac { y } { 3 }=30 \\end{cases}  $ D． $ \\begin{cases} x+y=5 \\\\ \\dfrac { x } { 3 }+\\dfrac { y } { 10 }=30 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593319271205220352", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "593319271205220352", "title": "2025听四川省成都市七中育才学校中考模拟数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "593318844199907328", "questionArticle": "<p>6．算盘是我国优秀文化遗产．它以排列成串的算珠作为计算工具，中间横梁把算珠分为上、下两部分，每个上珠代表5，每个下珠代表1．如图，小华在百位拨了一颗上珠和一颗下珠，然后对小明说：我将要拨的三位数中，个位数字是十位数字的2倍，若把个位数字与十位数字对调，所得的新的三位数比原三位数大36，请帮小明求出这个三位数．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/26/2/1/0/0/0/593318796837822469/images/img_23.png\" style=\"vertical-align:middle;\" width=\"271\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西高新一中 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593318818526572544", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "593318818526572544", "title": "2025年陕西省西安市高新第一中学中考数学八模试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593319063016742912", "questionArticle": "<p>7．方程组 $ \\begin{cases} 2x+y=5 \\\\ x-y=1 \\end{cases}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $　　　　B． $ \\begin{cases} x=3 \\\\ y=2 \\end{cases}  $　　　　C． $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $　　　　D． $ \\begin{cases} x=2 \\\\ y=3 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江衢州 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593319049704022016", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "593319049704022016", "title": "2025年浙江省衢州市实验学校教育集团九年级中考四模数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589572547252891648", "questionArticle": "<p>8．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} 2x-y=2k+5 \\\\ x-2y=2 \\end{cases}  $ （ $ k $ 为常数）．</p><p>（1）若 $ x-y=1 $ ，则 $ k= $ _；</p><p>（2）若 $ x+y &gt; 5 $ ，求 $ k $ 的取值范围．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025天津天津市第七中学 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589572523492159488", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "589572523492159488", "title": "天津市第七中学2024-2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589572543285080064", "questionArticle": "<p>9．对有理数<i>x</i>，<i>y</i>定义一种新运算“*”： $ x*y=ax+by $ ，其中<i>a</i>，<i>b</i>为常数，等式右边是通常的加法和乘法运算，已知 $ 3*5=15 $ ， $ 5*3=25 $ ，那么 $ a+b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市第七中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589572523492159488", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "589572523492159488", "title": "天津市第七中学2024-2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589572540848189440", "questionArticle": "<p>10．小月去买文具，打算买5支单价相同的签字笔和3本单价相同的笔记本，她与售货员的对话如图所示，那么购买一支签字笔和一本笔记本应付款（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 290.25pt;\"><p>小月：您好，我要买5支签字笔和3本笔记本．</p><p>售货员：好的，那你应付款52元．</p><p>小月：刚才我把两种文具的单价弄反了，以为要付款44元．</p></td></tr></table><p>A．11元B．12元C．13元D．14元</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市第七中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589572523492159488", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "589572523492159488", "title": "天津市第七中学2024-2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 4, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 4, "timestamp": "2025-07-01T02:01:14.434Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}