{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 155, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "559469231730892800", "questionArticle": "<p>1．一只青蛙，位于数轴上的点 $ a{{}_{ k } } $ ，跳动一次后到达 $ a{{}_{ k+1 } } $ ，已知 $ a{{}_{ k } },a{{}_{ k+1 } } $ 满足 $ \\left  | { a{{}_{ k+1 } }-a{{}_{ k } } } \\right  | =1 $ ，我们把青蛙从 $ a{{}_{ 1 } } $ 开始，经 $ n-1 $ 次跳动的位置依次记作 $ A{{}_{ n } }:a{{}_{ 1 } },a{{}_{ 2 } },a{{}_{ 3 } },\\cdots ,a{{}_{ n } } $ ．</p><p>(1)写出一个 $ A{{}_{ 5 } } $ ，使其 $ a{{}_{ 1 } }=a{{}_{ 5 } }=0 $ ，且 $ a{{}_{ 1 } }+a{{}_{ 2 } }+a{{}_{ 3 } }+a{{}_{ 4 } }+a{{}_{ 5 } } &gt; 0 $ ；</p><p>(2)若 $ a{{}_{ 1 } }=12,a{{}_{ 2025 } }=2036 $ ，求 $ a{{}_{ 3000 } } $ 的值；</p><p>(3)对于整数 $ n\\left ( { n\\geqslant  2 } \\right )  $ ，如果存在一个 $ A{{}_{ n } } $ 能同时满足如下两个条件：</p><p>① $ a{{}_{ 1 } }=0 $ ；</p><p>② $ a{{}_{ 1 } }+ $  $ a{{}_{ 2 } }+a{{}_{ 3 } }+\\cdots +a{{}_{ n } }=0 $ ．</p><p>求证： $ 4∣n\\left ( { n-1 } \\right )  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025浙江嘉兴 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-28", "keyPointIds": "16311|16424", "keyPointNames": "规律型：数与式的变化类|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559469208523808768", "questionFeatureName": "规律探究题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "559469208523808768", "title": "浙江省嘉兴市2024−2025学年八年级下学期3月素养调研测试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559469229176561664", "questionArticle": "<p>2．根据表中的素材，完成下面的任务：</p><table style=\"border: solid 1px;border-collapse: collapse; width:414pt;\"><tr><td colspan=\"5\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">制作无盖长方体纸盒</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">素材1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">裁剪长方形纸板</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>将某种规格的长方形纸板按图1、图2所示的两种方法裁剪，分别可裁得2块小长方形纸板和3块小正方形纸板．</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/24/2/1/0/0/0/559469178329014283/images/img_11.png\" style=\"vertical-align:middle;\" width=\"97\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">素材2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">制作无盖长方体纸盒</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>4块相同的小长方形纸板和1块小正方形纸板可做成图3所示的无盖长方体纸盒；3块相同的小长方形纸板和2块小正方形纸板可做成图4所示的无盖长方体纸盒．</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/24/2/1/0/0/0/559469178329014284/images/img_12.png\" style=\"vertical-align:middle;\" width=\"97\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td colspan=\"5\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">任务</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>制作图3、图4规格的纸盒若干个</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>若有21张长方形纸板，且恰好能够完成制作（纸板无剩余），则能做成图3、图4规格的纸盒各多少个？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025浙江嘉兴 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-28", "keyPointIds": "16432|16439|16490", "keyPointNames": "配套问题|几何问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559469208523808768", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "559469208523808768", "title": "浙江省嘉兴市2024−2025学年八年级下学期3月素养调研测试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "560570329875128320", "questionArticle": "<p>3．中国古代数学著作《九章算术》第七章主要内容是“盈不足术”，其中有这样一道盈亏类问题：“今有共买羊，人出五，不足九十；人出五十，适足．问人数、羊价各几何？”题目大意是“有几个人共同购买一只羊，若每人出五元，还差九十元；若每人出五十元，刚好够．问有几个人，羊的价格是多少？”设有<i>x</i>人，羊的价格为<i>y</i>元，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+y=90 \\\\ 50x+y=0 \\end{cases}  $</p><p>B． $ \\begin{cases} 5x-y=90 \\\\ 50x-y=0 \\end{cases}  $</p><p>C． $ \\begin{cases} 5x-y=-90 \\\\ 50x+y=0 \\end{cases}  $</p><p>D． $ \\begin{cases} 5x-y=-90 \\\\ 50x-y=0 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000|510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁朝阳 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 3, "createTime": "2025-03-28", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577298340355809280", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "577298340355809280", "title": "2025年辽宁省朝阳市第四中学九年级四月中考模拟数学试题", "paperCategory": 1}, {"id": "560570316273000448", "title": "辽宁省沈阳市实验中学2024−2025学年下学期九年级数学3月测试试卷", "paperCategory": 1}, {"id": "536335922276638720", "title": "2024年四川省成都市青白江区九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "560570464088662016", "questionArticle": "<p>4．古代一歌谣：栖树一群鸦，鸦树不知数，三个坐一棵，五个地上落；五个坐一棵，闲了一棵树．请你动脑筋，鸦树各几何？若设乌鸦有<i>x</i>只，树有<i>y</i>棵，由题意可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3y+5=x \\\\ 5y-1=x \\end{cases}  $</p><p>B． $ \\begin{cases} 3y-5=x \\\\ 5y=x-1 \\end{cases}  $</p><p>C． $ \\begin{cases} \\dfrac { 1 } { 3 }x+5=y \\\\ 5y=x-5 \\end{cases}  $</p><p>D． $ \\begin{cases} \\dfrac { x-5 } { 3 }=y \\\\ \\dfrac { x } { 5 }=y-1 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|140000|510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 7, "referenceNum": 3, "createTime": "2025-03-28", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "560570448733315072", "questionFeatureName": "数学文化题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "560570448733315072", "title": "2025年四川省成都市第七中学九年级下学期一模数学试题", "paperCategory": 1}, {"id": "201660469404278784", "title": "山西省太原师范学院附属中学九年级2022年中考数学模拟试题", "paperCategory": 1}, {"id": "202428838755737600", "title": "江苏省南通市如皋市实验初中2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "560236403231072256", "questionArticle": "<p>5．规定：形如 $ x+ky=b $ 与 $ kx+y=b $ 的两个关于<i>x</i>，<i>y</i>的方程互为“共轭二元一次方程”，其中 $ k\\ne 1 $ ．由这两个方程组成的方程组 $ \\begin{cases} x+ky=b \\\\ kx+y=b \\end{cases}  $ 叫作“共轭方程组”，<i>k</i>，<i>b</i>称为“共轭系数”.</p><p>(1)方程 $ 3x+y=5 $ 的“共轭二元一次方程”为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，它们组成的“共轭方程组”的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>(2)若关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} x+\\left ( { 2-5a } \\right ) y=-b-4 \\\\ \\left ( { 1-2b } \\right ) x+y=-5-a \\end{cases}  $ 为“共轭方程组”，求此“共轭方程组”的共轭系数．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683866338500608", "questionFeatureName": "新定义问题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "577683866338500608", "title": "重庆市万州第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}, {"id": "560236378417569792", "title": "重庆市万州二中教育集团2024−2025学年七年级下学期数学第一次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "560236401649819648", "questionArticle": "<p>6．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} ax-3y=17 \\\\ 5x+by=4 \\end{cases}  $ ，小明在解方程组时看错<i>a</i>，解得 $ \\begin{cases} x=-2 \\\\ y=7 \\end{cases}  $ ，小红在解方程组时看错<i>b</i>，解得 $ \\begin{cases} x=5 \\\\ y=1 \\end{cases}  $ ．</p><p>(1)求<i>a</i>，<i>b</i>的值．</p><p>(2)求原方程组正确的解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆万州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "560236378417569792", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "560236378417569792", "title": "重庆市万州二中教育集团2024−2025学年七年级下学期数学第一次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "560236397430349824", "questionArticle": "<p>7．若关于 $ x, y $ 的方程组 $ \\begin{cases} 2x+y=3+m \\\\ x+2y=-1 \\end{cases}  $ 的解满足 $ x-y=2 $ ，则实数 $ m $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆万州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "560236378417569792", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "560236378417569792", "title": "重庆市万州二中教育集团2024−2025学年七年级下学期数学第一次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "560236400135675904", "questionArticle": "<p>8．解下列方程（组）：</p><p>(1) $ 2\\left ( { x-1 } \\right ) -\\left ( { 3x-1 } \\right ) =1 $ ；</p><p>(2) $ \\begin{cases} 2x+3y=4① \\\\ 5x-3y=-11② \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆万州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-27", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "560236378417569792", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "560236378417569792", "title": "重庆市万州二中教育集团2024−2025学年七年级下学期数学第一次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "560236388592951296", "questionArticle": "<p>9．九章算术原文：“今有共买金，人出四百，盈三千四百；人出三百，盈一百，问人数、全价咨几何？”译文：“今有人合伙买金，每人出钱400，会多出3400钱；每人出钱300，会多出100钱，问合伙人数、金价各是多少？”设合伙人数为 $ x $ 人，金价为 $ y $ 钱，根据题意列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 400x+3400=y \\\\ 300x+100=y \\end{cases}  $ B． $ \\begin{cases} 400x-3400=y \\\\ 300x+100=y \\end{cases}  $ </p><p>C． $ \\begin{cases} 300x+3400=y \\\\ 400x+100=y \\end{cases}  $ D． $ \\begin{cases} 400x-3400=y \\\\ 300x-100=y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 3, "createTime": "2025-03-27", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577683866338500608", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "577683866338500608", "title": "重庆市万州第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}, {"id": "560236378417569792", "title": "重庆市万州二中教育集团2024−2025学年七年级下学期数学第一次月考试卷", "paperCategory": 1}, {"id": "578016074257440768", "title": "福建省福州教育学院附属中学2024−2025学年七年级下学期半期考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "559869806121361408", "questionArticle": "<p>10．在综合与实践活动课上，李老师让同学们画出矩形 $ ABCD $ ，使其各边均为整数．设矩形 $ ABCD $ 的面积为<i>m</i>，周长为<i>n</i>．</p><p>①若 $ m=8 $ ，则<i>n</i>的所有可能值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>②当 $ 100\\leqslant  2m-n\\leqslant  112 $ 时．若要使得每位同学画出的矩形一定互相全等，则<i>m</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-26", "keyPointIds": "16353|16420", "keyPointNames": "因式分解的应用|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559869776245334016", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "559869776245334016", "title": "四川省成都市七中育才学校2024−2025学年九年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 156, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 156, "timestamp": "2025-07-01T02:19:19.253Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}