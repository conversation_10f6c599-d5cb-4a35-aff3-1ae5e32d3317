{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 96, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "574756247074807808", "questionArticle": "<p>1．已知 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是方程组 $ \\begin{cases} x+y=3 \\\\ ax-2y=-1 \\end{cases}  $ 的解，则 $ a $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3B．2C．−2D．−3</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南岳阳 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-06", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574756233627869184", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574756233627869184", "title": "2025年湖南省岳阳市第十八中学中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574756843588722688", "questionArticle": "<p>2．《九章算术》中第七章《盈不足》记载了一个问题：“今有共买物，人出八，赢三；人出七，不足四．问人数、物价各几何？”译文：“现有一些人合伙购买物品，若每人出8钱，则多出3钱；若每人出7钱，则还差4钱．问人数、物品价格各是多少？”设有 $ x $ 个人，物品价格为 $ y $ 钱，则下列方程组中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 8x+3=y \\\\ 7x-4=y \\end{cases}  $</p><p>B． $ \\begin{cases} 8x-3=y \\\\ 7x+4=y \\end{cases}  $</p><p>C． $ \\begin{cases} 8x-3=y \\\\ 7x-4=y \\end{cases}  $</p><p>D． $ \\begin{cases} 8x+3=y \\\\ 7x+4=y \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000|230000|410000|530000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南洛阳 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 7, "referenceNum": 6, "createTime": "2025-05-06", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584432906874957824", "questionFeatureName": "数学文化题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "584432906874957824", "title": "2025年河南省洛阳市第三次中招模拟考试数学试卷", "paperCategory": 1}, {"id": "574756827130273792", "title": "2025年辽宁省抚顺市中考一模数学试卷", "paperCategory": 1}, {"id": "235123504370196480", "title": "湖南省长沙市青竹湖湘一外国语学校2022-2023学年九年级上学期第一次月考数学试题", "paperCategory": 1}, {"id": "208562415176294400", "title": "云南省西双版纳傣族自治州2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "199855200286318592", "title": "河南省郑州市2022年初中中招适应性测试数学二模试题", "paperCategory": 1}, {"id": "220496532063166464", "title": "黑龙江省哈尔滨市巴彦县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571885627773329408", "questionArticle": "<p>3．如果 $ \\begin{cases} x=m \\\\ y=n \\end{cases}  $ 是方程 $ 2x-3y=2025 $ 的一组解，那么代数式 $ 2024-2m+3n= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆实验中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-05", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571885606487236608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571885606487236608", "title": "重庆市实验中学教育集团2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571885630176665600", "questionArticle": "<p>4．解下列方程及方程组：</p><p>(1) $ {\\left( { x+1 } \\right) ^ {2}}=8 $ </p><p>(2) $ \\begin{cases} x+2y=3 \\\\ 3x-2y=5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆实验中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-05", "keyPointIds": "16287|16424", "keyPointNames": "平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571885606487236608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571885606487236608", "title": "重庆市实验中学教育集团2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886858164019200", "questionArticle": "<p>5．用方程组解决问题：某动物保护机构要准备 $ A，B，C $ 三种类型的食物共310份给需要救助的动物，现安排40名志愿者来准备这些食物，每名志愿者只能准备同一种类型的食物，且要求每名志愿者满工作量．根据以下表格信息，回答问题．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 127.5pt;\"><p style=\"text-align:center;\">食物类型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.55pt;\"><p style=\"text-align:center;\"> $ \\mathrm{ A } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 21.65pt;\"><p style=\"text-align:center;\"> $ B $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.55pt;\"><p style=\"text-align:center;\"> $ C $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 127.5pt;\"><p style=\"text-align:center;\">每名志愿者准备量（份）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.55pt;\"><p style=\"text-align:center;\">6</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 21.65pt;\"><p style=\"text-align:center;\">8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.55pt;\"><p style=\"text-align:center;\">9</p></td></tr></table><p>(1)如果 $ C $ 类型食物安排了16名志愿者，那么 $ A，B $ 两种类型食物各需多少名志愿者？</p><p>(2)现要求每种类型的食物至少安排11名志愿者，求三种类型的食物各需安排多少名志愿者，写出所有可行的方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏南外（NFLS） · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886827847589888", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886827847589888", "title": "江苏省南京市玄武区南京外国语学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886853554479104", "questionArticle": "<p>6．小亮坚持体育锻炼，并用某种健身软件进行记录．小亮周六进行了两组运动，第一组安排30个深蹲，20个开合跳，健身软件显示消耗热量34千卡；第二组安排20个深蹲，40个开合跳，健身软件显示两组运动共消耗热量70千卡．小亮平均每做一个深蹲和一个开合跳分别消耗多少千卡热量？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南外（NFLS） · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886827847589888", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886827847589888", "title": "江苏省南京市玄武区南京外国语学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886852220690432", "questionArticle": "<p>7．解下列方程组</p><p>(1) $ \\begin{cases} x+y=1 \\\\ 2x+y=3 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 0.8x-0.9y=2 \\\\ 6x-3y=2.5 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南外（NFLS） · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886827847589888", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886827847589888", "title": "江苏省南京市玄武区南京外国语学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886849314037760", "questionArticle": "<p>8．已知关于 $ x，y $ 的方程组 $ \\begin{cases} 2x+y=3k+1 \\\\ 4x-3y=-k+5 \\end{cases}  $ ，若 $ x-2y=3 $ ，则 $ k $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南外（NFLS） · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886827847589888", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886827847589888", "title": "江苏省南京市玄武区南京外国语学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886848517120000", "questionArticle": "<p>9． $ A{ \\rm{ \\; } }、{ \\rm{ \\; } }B $ 两地相距440千米，一辆小汽车和一辆客车同时从 $ A{ \\rm{ \\; } }、{ \\rm{ \\; } }B $ 两地相向开出，经过3小时相遇．相遇时，小汽车比客车多行驶70千米，设小汽车和客车的平均速度分别为 $ x $ 千米/时和 $ y $ 千米/时，可列二元一次方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南外（NFLS） · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886827847589888", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886827847589888", "title": "江苏省南京市玄武区南京外国语学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886846868758528", "questionArticle": "<p>10． $ \\begin{cases} x=-1 \\\\ y=2 \\end{cases}  $ 是二元一次方程 $ 3x-ay=11 $ 的一个解，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南外（NFLS） · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886827847589888", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886827847589888", "title": "江苏省南京市玄武区南京外国语学校2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 97, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 97, "timestamp": "2025-07-01T02:12:16.527Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}