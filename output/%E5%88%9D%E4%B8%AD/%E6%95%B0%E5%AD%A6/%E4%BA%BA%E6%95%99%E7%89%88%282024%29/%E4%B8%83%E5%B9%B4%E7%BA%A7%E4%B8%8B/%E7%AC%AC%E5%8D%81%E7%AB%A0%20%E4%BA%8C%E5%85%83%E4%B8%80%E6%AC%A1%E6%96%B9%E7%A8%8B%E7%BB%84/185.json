{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 184, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "551570081395810304", "questionArticle": "<p>1．幻方历史悠久，传说最早出现在夏禹时代的“洛书”（如图1）中，把洛书用今天的数学符号翻译出来，就是一个三阶幻方．如图2所示，三阶幻方的每行、每列、每条对角线上的三个数之和都相等，图3是另一个广义的三阶幻方，则 $ b $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/03/2/1/0/0/0/551570042111959042/images/img_18.png\" style=\"vertical-align:middle;\" width=\"512\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024湖北十堰 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-04", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551570059061141504", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "551570059061141504", "title": "2024年湖北省十堰市实验中学教联体中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551570336845701120", "questionArticle": "<p>2．李老师有一辆电动汽车，为了充电方便，他安装了家庭充电桩．该充电桩峰时充电的电价为0.5元/度，谷时充电的电价为0.3元/度，某月李老师的电动汽车在家庭充电桩的充电量合计为180度，共花电费64元．求这个月李老师的电动汽车峰时和谷时的充电量．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/03/2/1/0/0/0/551570293170413574/images/img_20.png\" style=\"vertical-align:middle;\" width=\"163\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|220000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024山西太原 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-03-04", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "414568738379309056", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "414568738379309056", "title": "山西省太原市2023-2024学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "551570316687876096", "title": "2024年吉林省长春市108中学中考模拟数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549852074101481472", "questionArticle": "<p>3．（1）计算： $ -1{^{2}}+{\\left( { π-3 } \\right) ^ {0}}\\div { \\rm{ c } }{ \\rm{ o } }{ \\rm{ s } }60{}\\degree +\\sqrt[3] { -27 } $ ；</p><p>（2）解二元一次方程组： $ \\begin{cases} 3x-2y=5 \\\\ x+2y=7 \\end{cases}  $ ；</p><p>（3）化简： $ \\left ( { x-y } \\right ) \\left ( { x+y } \\right ) -x\\left ( { x-y } \\right )  $ ；</p><p>（4）化简： $ \\dfrac { 2x{^{2}}-x } { x{^{2}}-2x+1 }\\div \\left ( { x-1-\\dfrac { x{^{2}} } { x-1 } } \\right )  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆九龙坡 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-03-03", "keyPointIds": "16299|16333|16370|16424", "keyPointNames": "实数的运算|整式的混合运算|分式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549852049275396096", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "549852049275396096", "title": "重庆市重庆市九龙坡区四川外国语大学附属外国语学校2024-2025学年九年级下学期开学数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549851405575561216", "questionArticle": "<p>4．优秀文化是文创产品的灵魂．西安肉夹馍、天水麻辣烫本身就是“圈粉”需求的地方代表性特色美食，以其为原型和载体创新文创产品“绒馍馍”和“麻辣烫”，生动展示了本土美食的独特韵味．一盒“绒馍馍”234元，一锅“麻辣烫”108元，某网友一次购买相应规格的“绒馍馍”和“麻辣烫”共10盒（锅），两种产品均享受七五折的优惠，共花费1188元，则该网友购买“绒馍馍”多少盒，购买“麻辣烫”多少锅？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西实验中学 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-03", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549851382867599360", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549851382867599360", "title": "陕西省咸阳市秦都区咸阳市实验中学2024-2025学年八年级下学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549851401901350912", "questionArticle": "<p>5．解方程组： $ \\begin{cases} 2x+\\dfrac { 2 } { 7 }y=3 \\\\ 3x-\\dfrac { 2 } { 7 }y=7 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西实验中学 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-03-03", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549851382867599360", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549851382867599360", "title": "陕西省咸阳市秦都区咸阳市实验中学2024-2025学年八年级下学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549851399103750144", "questionArticle": "<p>6．已知 $ \\begin{cases} x=2 \\\\ y=m \\end{cases}  $ ，是二元一次方程 $ 3x+2y=10 $ 的一个解，则<i>m</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u><u>.</u></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西实验中学 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-03", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549851382867599360", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549851382867599360", "title": "陕西省咸阳市秦都区咸阳市实验中学2024-2025学年八年级下学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549852078778130432", "questionArticle": "<p>7．重外生物组在学习生物解剖这一章节为了让学生体验实验的乐趣，决定从市场购买成年雌蛙和雄蛙若干供实验使用．经调研发现市场每只雌蛙价格比雄蛙少3元，生物组花费140元购买雌蛙，而购买相同数量的雄蛙则需要200元．</p><p>(1)则市场雌蛙与雄蛙的售价分别为多少元/只？</p><p>(2)为了加大培养学生对生物的兴趣，某班级决定再次购买两种蛙，已知班级有54名学生，若刚好花完500元钱，且每个人至少都有一只蛙可以解剖，则至少购买多少只雌蛙？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆九龙坡 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-03", "keyPointIds": "16420|16476", "keyPointNames": "二元一次方程的解|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549852049275396096", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "549852049275396096", "title": "重庆市重庆市九龙坡区四川外国语大学附属外国语学校2024-2025学年九年级下学期开学数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551571271445684224", "questionArticle": "<p>8．《九章算术》是中国古代重要的数学著作，其中“盈不足术”记载：今有共买鸡，人出九，盈十一；人出六，不足十六．问人数鸡价各几何？译文：今有人合伙买鸡，每人出九钱，会多出11钱；每人出6钱，又差16钱．问人数、买鸡的钱数各是多少？设人数为<i>x</i>，买鸡的钱数为<i>y</i>，可列方程组为<u>&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江杭州 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-03", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551571254362284032", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "551571254362284032", "title": "2024年浙江省杭州市西湖区中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551571009100357632", "questionArticle": "<p>9．解方程组： $ \\begin{cases} 2x+y=12 \\\\ 3x-y=3 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏连云港 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-03", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551570986954432512", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "551570986954432512", "title": "2024年江苏省连云港市海州区连云港外国语学校中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549408206075764736", "questionArticle": "<p>10．某学校组织学生到郊外参加义务植树活动，并准备了<i>A</i>，<i>B</i>两种食品作为午餐．这两种食品每包质量均为 $ 50{ \\rm{ g } } $ ，其营养成分表如下：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/04/2/20/0/0/0/552122949685256193/images/img_1.png\" style='vertical-align:middle;' width=\"403\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)若每份午餐需要恰好摄入 $ 4600{ \\rm{ k } }{ \\rm{ J } } $ 热量和 $ 70{ \\rm{ g } } $ 蛋白质，应选用<i>A</i>，<i>B</i>两种食品各多少包？</p><p>(2)考虑到健康饮食的需求，若每份午餐需选用这两种食品共7包，并保证每份午餐中的蛋白质含量不低于 $ 90{ \\rm{ g } } $ ，且脂肪含量要尽可能低．请通过计算，求出符合要求且脂肪含量最低的配餐方案．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京人大附中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-03-03", "keyPointIds": "16440|16486|16543", "keyPointNames": "表格或图示问题|一元一次不等式的应用|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549408180398235648", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549408180398235648", "title": "北京市海淀区人大附中2024~2025学年下学期九年级数学练习1（开学考）", "paperCategory": 1}, {"id": "548191791968722944", "title": "北京市海淀区中国人民大学附属中学2024—2025学年九年级下学期开学考数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 185, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 185, "timestamp": "2025-07-01T02:22:45.603Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}