{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 95, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "574352262996205568", "questionArticle": "<p>1．如图是由同一种长方形墙砖粘贴的部分墙面，其中三块横放的墙砖比一块竖放的墙砖高 $ 10{ \\rm{ c } }{ \\rm{ m } } $ ，两块横放的墙砖比两块竖放的墙砖低 $ 40{ \\rm{ c } }{ \\rm{ m } } $ ，则每块长方形墙砖的周长是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ c } }{ \\rm{ m } } $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/08/2/1/0/0/0/575685102534963201/images/img_1.png\" style='vertical-align:middle;' width=\"180\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西广西壮族自治区南宁市第三中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574352242788048896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "574352242788048896", "title": "广西南宁市第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574352254066532352", "questionArticle": "<p>2．下列各组值中，是方程组 $ \\begin{cases} x+y=3 \\\\ x-y=1 \\end{cases}  $ 的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $　　　　B． $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $　　　　C． $ \\begin{cases} x=3 \\\\ y=0 \\end{cases}  $　　　　D． $ \\begin{cases} x=4 \\\\ y=3 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西广西壮族自治区南宁市第三中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574352242788048896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "574352242788048896", "title": "广西南宁市第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574352630241075200", "questionArticle": "<p>3．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} x+2y=1 \\\\ 2x+y=4 \\end{cases}  $ ，则 $ x-y $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -2 $ B．2C． $ -3 $ D．3</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南平顶山 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574352616097882112", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574352616097882112", "title": "河南省平顶山市2024−2025学年九年级下学期第二次联考数学试题试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574352262136373248", "questionArticle": "<p>4．已知 $ x $ ， $ y $ 满足方程组 $ \\begin{cases} 2x+y=6 \\\\ 3x+4y=-1 \\end{cases}  $ ，则 $ x+y $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000|450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西广西壮族自治区南宁市第三中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-05-07", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574352242788048896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "574352242788048896", "title": "广西南宁市第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}, {"id": "549406958370988032", "title": "陕西省西安市西安高新第一中学2024−2025学年八年级下学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572225940236836864", "questionArticle": "<p>5．如图，规定：上方相邻两数之和等于这两数下方箭头共同指向的数，对于 $ x,y,m {\\rm ，\\mathit{n}} $ 的取值，下列说法：① $ x+y $ 的值一定是2；②若 $ x=1 $ ，则 $ y=3 $ ；③若 $ x-y=0 $ ，则 $ m=3 $ ；④若 $ n=6 $ ，则 $ y=0 $ ；正确的是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/29/2/1/0/0/0/572225876386947080/images/img_8.png\" style=\"vertical-align:middle;\" width=\"164\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东广州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572225920011902976", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572225920011902976", "title": "广东省广州市越秀区育才教育集团2024−2025学年下学期七年级期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572225941616762880", "questionArticle": "<p>6．用适当的方法解下列方程（组）：</p><p>(1) $ {\\left( { x-1 } \\right) ^ {2}}=9 $ </p><p>(2) $ \\begin{cases} 2x+y=3① \\\\ 3x-2y=1② \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东广州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16287|16424", "keyPointNames": "平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572225920011902976", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572225920011902976", "title": "广东省广州市越秀区育才教育集团2024−2025学年下学期七年级期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572225937858666496", "questionArticle": "<p>7．已知 $ \\begin{cases} a\\,{ { + } }{ { 2 } }{ \\rm{ b } }{ \\rm{ = } }{ { 4 } } \\\\ { { 3 } }a{ { + } }{ { 2 } }{ \\rm{ b } }{ \\rm{ = } }{ { 8 } } \\end{cases}  $ ，则 $ a+b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东广州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572225920011902976", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572225920011902976", "title": "广东省广州市越秀区育才教育集团2024−2025学年下学期七年级期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572225933211377664", "questionArticle": "<p>8．《孙子算经》中有一道题，原文是：“今有木，不知长短，引绳度之，余绳四尺五寸；屈绳量之，不足一尺，木长几何？”意思是：用一根绳子去量一根长木，绳子还剩余4.5尺；将绳子对折再量长木，长木还剩余1尺，木长多少尺？若设绳子长<i>x</i>尺，木长<i>y</i>尺，所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-y=4.5 \\\\ 2x+1=y \\end{cases}  $ B． $ \\begin{cases} x-y=4.5 \\\\ \\dfrac { 1 } { 2 }x+1=y \\end{cases}  $ </p><p>C． $ \\begin{cases} y-x=4.5 \\\\ 2x-1=y \\end{cases}  $ D． $ \\begin{cases} x-y=4.5 \\\\ \\dfrac { 1 } { 2 }x-1=y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东广州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-05-07", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572225920011902976", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572225920011902976", "title": "广东省广州市越秀区育才教育集团2024−2025学年下学期七年级期中考试数学试题", "paperCategory": 1}, {"id": "576952618011897856", "title": "2025年四川省宜宾市第二中学校九年级二诊考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574351463066935296", "questionArticle": "<p>9．2025年第九届亚洲冬季运动会在哈尔滨举办．某经销商发现，与吉祥物“滨滨”和“妮妮”相关的甲、乙两款纪念品深受大家喜爱．已知购买3个甲款纪念品和2个乙款纪念品共需180元；购买5个甲款纪念品比购买3个乙款纪念品多15元．</p><p>(1)甲、乙两款纪念品的售价各是多少？</p><p>(2)甲款纪念品的进价为20元，乙款纪念品的进价为38元．若该经销商计划购进甲、乙两款纪念品共60个，且乙款纪念品的购买数量不低于甲款纪念品购买数量的2倍，则应如何进货能使得这批纪念品全部售出后所获利润最大，最大利润是多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东青岛 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-05-07", "keyPointIds": "16438|16544", "keyPointNames": "和差倍分问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574351426459049984", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574351426459049984", "title": "2025年山东省青岛市李沧区、西海岸新区、平度市联考中考一模数学试题", "paperCategory": 1}, {"id": "570063689639829504", "title": "2025年山东省青岛市中考一模数学试卷（李沧、西海岸、平度和胶州）区域联考", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574756122558504960", "questionArticle": "<p>10．2025年2月7日，第九届亚冬会在冰城——哈尔滨盛大开幕，吉祥物“滨滨”“妮妮”特许商品惊喜亮相，特许商品店有A，B两种不同价格的吉祥物，供不同人群购买．已知购买4个A种吉祥物和3个B种吉祥物共需560元；购买2个A种吉祥物和5个B种吉祥物共需700元．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/07/2/1/0/0/0/575323952299417601/images/img_1.png\" style='vertical-align:middle;' width=\"168\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)求A，B两种吉祥物每件的售价分别是多少元；</p><p>(2)某公司举行“追梦新时代  巾帼绽芳华”三八节活动，共设一、二等奖40名，其中一等奖 $ m $ 名，奖励一件B种吉祥物，二等奖不多于 $ (2m+5) $ 名，奖励一件A种吉祥物．公司如何购买最省钱？</p><p>(3)在（2）最省钱的基础上，特许商品店推出A种吉祥物打九折，B种吉祥物打九五折的促销活动，该公司共能省多少钱？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-06", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574756093206765568", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "574756093206765568", "title": "2025年河南省中原名校联盟中考二模考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 96, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 96, "timestamp": "2025-07-01T02:12:10.304Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}