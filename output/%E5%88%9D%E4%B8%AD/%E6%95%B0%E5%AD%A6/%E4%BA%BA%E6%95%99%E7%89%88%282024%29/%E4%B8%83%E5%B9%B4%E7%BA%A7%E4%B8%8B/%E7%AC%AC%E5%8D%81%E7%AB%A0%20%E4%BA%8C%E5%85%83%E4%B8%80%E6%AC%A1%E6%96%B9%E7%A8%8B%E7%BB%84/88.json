{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 87, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "575479810308218880", "questionArticle": "<p>1．解方程</p><p>（1） $ \\begin{cases} 3x-2y=13 \\\\ 4x+y=10 \\end{cases}  $ </p><p>（2） $ \\begin{cases} \\dfrac { x-1 } { 2 }=3y \\\\ 3\\left ( { x+1 } \\right ) -2y=-2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门六中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-12", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575479786685898752", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575479786685898752", "title": "福建省厦门第六中学2024—2025学年七年级下学期期中检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575479807128936448", "questionArticle": "<p>2．已知 $ \\begin{cases} 2x+y=12 \\\\ x-3y=5 \\end{cases}  $ ，则代数式 $ 6x-4y-24= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门六中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-12", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575479786685898752", "questionMethodName": "整体思想", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575479786685898752", "title": "福建省厦门第六中学2024—2025学年七年级下学期期中检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575479799507886080", "questionArticle": "<p>3．用代入法解二元一次方程组 $ \\begin{cases} y=1-2x① \\\\ x+2y=4② \\end{cases}  $ 时，将方程①代入方程②，得到结果正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x-2+4x=4 $ B． $ x+2-4x=4 $ </p><p>C． $ x+2+2x=4 $ D． $ x+2-2x=4 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门六中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-12", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575479786685898752", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575479786685898752", "title": "福建省厦门第六中学2024—2025学年七年级下学期期中检测数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574753131164114944", "questionArticle": "<p>4．解二元一次方程组</p><p>(1) $ \\begin{cases} x=1-2y \\\\ 3x+y=-7 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 3\\left ( { x+y } \\right ) -4\\left ( { x-y } \\right ) =-4 \\\\ \\dfrac { x+y } { 2 }+\\dfrac { x-y } { 6 }=1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津二十五中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-05-12", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574753109164990464", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574753109164990464", "title": "天津市南开区第二十五中学2024-−2025学年七年级下册数学期中测试卷", "paperCategory": 1}, {"id": "196955852737126400", "title": "北京市第十三中学分校2021-2022学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574753117960445952", "questionArticle": "<p>5．下列万程中，是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）.</p><p>A． $ 3x-2y=4z $ B． $ 6xy+9=0 $ C． $ \\dfrac { 1 } { x }+4y=6 $ D． $ 4x=\\dfrac { y-2 } { 4 } $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|120000|230000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025天津二十五中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 4, "createTime": "2025-05-12", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574753109164990464", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574753109164990464", "title": "天津市南开区第二十五中学2024-−2025学年七年级下册数学期中测试卷", "paperCategory": 1}, {"id": "423253607057760256", "title": "黑龙江省哈尔滨市第六十九中学校2022一2023学年七年级下学期三月学科活动数学试卷", "paperCategory": 1}, {"id": "205460642781765632", "title": "黑龙江省哈尔滨市第四十七中学2021-2022学年八年级上学期开学验收数学试题", "paperCategory": 1}, {"id": "202010495724855296", "title": "河北省承德市民族中学（安定里校区）2020-2021学年下学期七年级期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "575777559075397632", "questionArticle": "<p style=\"text-align: justify;\">6．“体育承载着国家强盛、民族振兴的梦想，体育强则中国强，国运兴则体育兴．”为引导学生在体育锻炼中享受乐趣、增强体质，学校开展大课间活动，七年级一班拟组织学生参加跳绳活动，需购买<i>A</i>，<i>B</i>两种跳绳若干，已知购买1根<i>A</i>种跳绳和3根<i>B</i>种跳绳共需105元；购买3根<i>A</i>种跳绳和5根<i>B</i>种跳绳共需215元．</p><p>（1）求<i>A</i>，<i>B</i>两种跳绳的单价；</p><p>（2）如果班级计划购买<i>A</i>，<i>B</i>两种跳绳共48根，总费用不超过1322元，最多可购买<i>A</i>种跳绳多少根？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南株洲 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-11", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575777530281500672", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "575777530281500672", "title": "湖南省株洲市外国语学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575776992965992448", "questionArticle": "<p>7．近日，中国大学生篮球一级联赛正在株洲火热进行中．赛场内掌声雷动，赛场外市集精彩，主题篮球和球衣深受大家喜爱．已知购买3个篮球比购买2件球衣多用120元，购买1个篮球和2件球衣共用200元．</p><p>（1）篮球和球衣的单价分别是多少元？</p><p>（2）某支队伍决定购买篮球和球衣带回学校做纪念品，共70个（件），总费用不超过5000元，则至少应购买球衣多少件？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南株洲二中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-11", "keyPointIds": "16441|16486", "keyPointNames": "其他问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575776951916339200", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "575776951916339200", "title": "湖南省株洲市第二中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575776760899350528", "questionArticle": "<p>8．回答下列问题：</p><p>（1）计算：</p><p>① $ \\left ( { x+2 } \\right ) \\left ( { x+3 } \\right ) = $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；&nbsp;&nbsp;&nbsp;&nbsp;</p><p>② $ \\left ( { x+2 } \\right ) \\left ( { x-3 } \\right ) = $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>③ $ \\left ( { x-2 } \\right ) \\left ( { x+3 } \\right ) = $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>（2）总结公式 $ \\left ( { x+a } \\right ) \\left ( { x+b } \\right ) =x{^{2}}+ $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ x+ab $ </p><p>（3）已知 $ a $ ， $ b $ ， $ m $ 均为整数，且 $ \\left ( { x+a } \\right ) \\left ( { x+b } \\right ) =x{^{2}}+mx+7 $ ．求 $ m $ 的所有可能值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南驻马店 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-11", "keyPointIds": "16327|16420", "keyPointNames": "多项式乘多项式|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575776729270104064", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575776729270104064", "title": "河南省驻马店市实验中学2024−2025学年七年级下学期期中素养评估测评数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575480338232680448", "questionArticle": "<p>9．解方程组： $ \\begin{cases} 2x-y=3 \\\\ 4x+3y=11 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东湛江 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-11", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575480316673957888", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575480316673957888", "title": "广东省湛江市雷州市四校联考2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575480336022282240", "questionArticle": "<p>10．以方程组 $ \\begin{cases} y=x+1 \\\\ y=-x+2 \\end{cases}  $ 的解为坐标的点 $ \\left ( { x,y } \\right )  $ 在第<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>象限．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东湛江 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-11", "keyPointIds": "16424|16499", "keyPointNames": "加减消元法解二元一次方程组|坐标确定位置", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575480316673957888", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575480316673957888", "title": "广东省湛江市雷州市四校联考2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 88, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 88, "timestamp": "2025-07-01T02:11:13.868Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}