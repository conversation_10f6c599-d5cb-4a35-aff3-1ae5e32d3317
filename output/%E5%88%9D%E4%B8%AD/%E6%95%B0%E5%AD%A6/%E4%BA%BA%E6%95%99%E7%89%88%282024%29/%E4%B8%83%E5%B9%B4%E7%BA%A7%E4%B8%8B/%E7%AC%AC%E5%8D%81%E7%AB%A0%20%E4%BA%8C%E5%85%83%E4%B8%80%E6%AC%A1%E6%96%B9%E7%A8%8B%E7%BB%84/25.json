{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 24, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "590678584995389440", "questionArticle": "<p>1．我国古代《算法统宗》里有这样一首诗：“我问开店李三公，众客都来到店中，一房七客多七客，一房九客一房空．”诗中后两句的意思是：如果每一间客房住7人，那么有7人无房住；如果每一间客房住9人，那么就空出一间客房．设该店有客房<i>x</i>间、房客<i>y</i>人，下列方程组中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 7x+7=y \\\\ 9\\left ( { x-1 } \\right ) =y \\end{cases}  $</p><p>B． $ \\begin{cases} 7x+7=y \\\\ 9\\left ( { x+1 } \\right ) =y \\end{cases}  $</p><p>C． $ \\begin{cases} 7x-7=y \\\\ 9\\left ( { x-1 } \\right ) =y \\end{cases}  $</p><p>D． $ \\begin{cases} 7x-7=y \\\\ 9\\left ( { x+1 } \\right ) =y \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "620000|130000|500000|-1|510000|410000|150000|350000|420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川南充 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 17, "referenceNum": 11, "createTime": "2025-06-19", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "457847516068880384", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "457847516068880384", "title": "2024年四川省南充市中考数学试题", "paperCategory": 1}, {"id": "590678568251727872", "title": "重庆市外国语学校2024−2025学年七年级下学期第三次定时作业数学试题", "paperCategory": 1}, {"id": "580242746465624064", "title": "2025年福建省龙岩市中考一模数学试题", "paperCategory": 1}, {"id": "586984804509331456", "title": "2025年福建省福州屏东中学中考二模数学试题", "paperCategory": 1}, {"id": "474319180851879936", "title": "河北省张家口市万全区2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "347498341394063360", "title": "河南省洛阳市2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "398478838722764800", "title": "甘肃省兰州市城关区兰州市第十一中学2022-2023学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "399274866950578178", "title": "甘肃省兰州市城关区兰州市第十一中学2022-2023学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "205356195829293056", "title": "青岛版七年级下册第10章一次方程组单元测试", "paperCategory": 1}, {"id": "209704087423590400", "title": "内蒙古自治区呼伦贝尔市阿荣旗2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "161811753151537152", "title": "湖北省武汉2020年中考冲刺适应数学训练（一）", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590298277708345344", "questionArticle": "<p>2．今有三人共车，二车空；二人共车，九人步．问：人与车各几何？（选自《孙子算经》）题目大意：有若干人要坐车，若每3人坐一辆车，则有2辆空车：若每2人坐一辆车，则有9人需要步行，问人与车各多少？设共有 $ x $ 辆车， $ y $ 个人，可列方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590298253393965056", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590298253393965056", "title": "2025年福建福州延安中学九年级数学6月三模试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590298037534109696", "questionArticle": "<p>3．一家广告公司为某学校制作文艺活动的展板、宣传册和横幅，其中宣传册的数量是展板的5倍，广告公司制作每件产品所需时间和所获利润如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">产品</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">展板</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\">宣传册</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">横幅</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">时间／小时</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\"> $ 0.2 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\"> $ 0.5 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">利润／元</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">60</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\"> $ 3.5 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">20</p></td></tr></table><p>已知制作三种产品共需25小时，所获利润为975元，则这三种产品的件数之和为？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽淮北 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590298007645499392", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590298007645499392", "title": "2025年安徽省淮北市部分学校中考模拟考试数学试卷（6月）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590298166299238400", "questionArticle": "<p>4．随着人们环保观念的不断加深，“低碳环保，绿色出行”成为大家的生活理念，选择自行车出行已是如今社会的一种潮流形式．某公司销售甲、乙两种型号的自行车，其中销售3台甲型自行车和2台乙型自行车，可获利1700元，销售2台甲型自行车和1台乙型自行车，可获利1000元，该公司销售一台甲型、一台乙型自行车的利润各是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽宿州 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590298143322841088", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590298143322841088", "title": "2025年安徽省宿州市部分学校中考模拟考试九年级数学试卷（6月）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590297782696587264", "questionArticle": "<p>5．某校计划租用 $ A，B $ 两种型号的客车送300名师生去劳动实践基地开展综合实践活动．已知租用1辆<i>A</i>型客车和1辆<i>B</i>型客车共需550元，租用2辆<i>A</i>型客车所需的费用比租用3辆<i>B</i>型客车所需的费用多100元．已知每辆<i>A</i>型客车允许载客35人，每辆<i>B</i>型客车允许载客18人．</p><p>（1）分别求租用一辆 $ \\mathrm{ A } $ 型客车和一辆 $ B $ 型客车需要多少元．</p><p>（2）若学校计划租用12辆客车，至少需要租用<i>A</i>型客车多少辆？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西吕梁 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16441|16486", "keyPointNames": "其他问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590297748093579264", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590297748093579264", "title": "2025年山西省吕梁市部分学校中考数学模拟考试", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589934802008059904", "questionArticle": "<p>6．若一个关于<i>x</i>的二次三项式能分解成 $ a\\left ( { x-m } \\right ) \\left ( { x-n } \\right )  $ （其中<i>a</i>为实数，<i>m</i>，<i>n</i>为正整数）的形式，则称这个多项式关于 $ x=\\dfrac { m+n } { 2 } $ 对称．例如： $ 2x{^{2}}-8x+6=2\\left ( { x-1 } \\right ) \\left ( { x-3 } \\right )  $ ，则 $ 2x{^{2}}-8x+6 $ 关于 $ x=\\dfrac { 1+3 } { 2 }=2 $ 对称．</p><p>（1）请写出一个关于<i>x</i>的二次三项式，使它关于 $ x=5 $ 对称；</p><p>（2）若 $ x{^{2}}+\\left ( { t-7 } \\right ) x+\\dfrac { t } { 2 }=\\left ( { x-2m } \\right ) \\left ( { x-m } \\right )  $ 关于 $ x=\\dfrac { 3 } { 2 } $ 对称，求<i>t</i>的值；</p><p>（3）若 $ \\left ( { 2x-b } \\right ) \\left ( { x-c } \\right ) =M $ ，且<i>M</i>关于 $ x=3 $ 对称，求<i>b</i>，<i>c</i>的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川渠中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16327|16420", "keyPointNames": "多项式乘多项式|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934771955871744", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589934771955871744", "title": "四川省达州市渠县中学2024−2025学年八年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589934583161860096", "questionArticle": "<p>7．“黎侯虎”是一种传统手工艺品，起源于山西省黎城县，因黎城古称黎侯国而得名．某网店销售 $ A $ ， $ B $ 两款黎侯虎工艺品摆件，已知 $ B $ 款黎侯虎工艺品摆件的单价比 $ A $ 款黎侯虎工艺品摆件单价的 $ 2 $ 倍少 $ 42 $ 元，购买 $ 4 $ 个 $ A $ 款黎侯虎工艺品摆件所需费用比购买 $ 3 $ 个 $ B $ 款黎侯虎工艺品摆件所需费用多 $ 28 $ 元．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/21/2/1/0/0/0/591605296302170112/images/img_1.png\" style='vertical-align:middle;' width=\"143\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）求 $ A $ ， $ B $ 两款黎侯虎工艺品摆件的单价；</p><p>（2）某校历史社团组织全校开展“山西民俗我知道”的知识竞赛活动，该校历史社团打算购买这两款黎侯虎工艺品摆件共 $ 25 $ 个作为知识竞赛的奖品，且该历史社团的预算不超过 $ 1330 $ 元，求该历史社团最多能购买 $ B $ 款黎侯虎工艺品摆件的数量．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津红桥区第五中学 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934561456336896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589934561456336896", "title": "天津市第五中学2024−2025学年下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589934582234918912", "questionArticle": "<p>8．小明家离学校1880米，其中有一段为上坡路，另一段为下坡路．他跑步去学校共用了16分钟，已知小明在上坡路上的平均速度为4.8千米 $ / $ 时，而他在下坡路上的平均速度为12千米 $ / $ 时，那么小明在上坡路上用了多少分钟？（温馨提示：计算时请注意单位）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津红桥区第五中学 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934561456336896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589934561456336896", "title": "天津市第五中学2024−2025学年下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589934489960230912", "questionArticle": "<p>9．解方程组： $ \\begin{cases} \\dfrac { x+1 } { 2 }-\\dfrac { y-1 } { 3 }=1 \\\\ 2\\left ( { x-y-1 } \\right ) =3\\left ( { 1-y } \\right ) -2 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津二十五中 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934467113857024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589934467113857024", "title": "天津市南开区第二十五中学2024-2025学下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589934580586557440", "questionArticle": "<p>10．解下列方程组：</p><p>（ $ 1 $ ） $ \\begin{cases} 3x-4y=0 \\\\ x+5y=19 \\end{cases}  $ ；                        </p><p>（ $ 2 $ ） $ \\begin{cases} 5x-4y=12 \\\\ 4x+6y=5 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津红桥区第五中学 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934561456336896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589934561456336896", "title": "天津市第五中学2024−2025学年下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 25, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 25, "timestamp": "2025-07-01T02:03:46.128Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}