{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 75, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "578015562132926464", "questionArticle": "<p>1．若 $ \\begin{cases} x=-1 \\\\ y=2 \\end{cases}  $ 是方程 $ 2x+my=4 $ 的解，则 $ m $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京15中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015546098102272", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015546098102272", "title": "北京市第十五中学2024—2025学年下学期七年级期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015559100444672", "questionArticle": "<p>2．如图， $ 9 $ 个大小、形状完全相同的小长方形，组成了一个周长为 $ 46 $ 的大长方形 $ ABCD $ ，若设小长方形的长为<i>x</i>，宽为<i>y</i>，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/15/2/1/0/0/0/578015522555473929/images/img_10.png\" style=\"vertical-align:middle;\" width=\"176\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} 2x=7y \\\\ 2\\left ( { 2x+x+y } \\right ) =46 \\end{cases}  $ B． $ \\begin{cases} 2x=7y \\\\ 7y+2x+x+y=46 \\end{cases}  $ </p><p>C． $ \\begin{cases} 7x=2y \\\\ 2\\left ( { 2x+x+y } \\right ) =46 \\end{cases}  $ D． $ \\begin{cases} 7x=2y \\\\ 7y+2x+x+y=46 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京15中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015546098102272", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015546098102272", "title": "北京市第十五中学2024—2025学年下学期七年级期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578015556458033152", "questionArticle": "<p>3．用加减消元法解二元一次方程组 $ \\begin{cases} 4x+3y=2\\,① \\\\ 2x-5y=7\\,② \\end{cases}  $ 时，下列方法中消元正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ ①\\times 5+② $ B． $ ①+②\\times 3 $ C． $ ①-②\\times 2 $ D． $ ①+②\\times 2 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京15中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015546098102272", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015546098102272", "title": "北京市第十五中学2024—2025学年下学期七年级期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "576951101871665152", "questionArticle": "<p>4．《九章算术》中记载：“今有共买鸡，人出八，盈三；人出七，不足四．问人数，鸡价各几何？”译文：“今天有几个人共同买鸡，每人出8钱，多余3钱，每人出7钱，还缺4钱．问人数和鸡的价钱各是多少？”设人数有 $ x $ 人，鸡的价钱是 $ y $ 钱，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 8x-y=3 \\\\ y-7x=4 \\end{cases}  $</p><p>B． $ \\begin{cases} 8y-x=3 \\\\ 7y-x=4 \\end{cases}  $</p><p>C． $ \\begin{cases} 8y-x=3 \\\\ 7x-y=4 \\end{cases}  $</p><p>D． $ \\begin{cases} 8y+x=3 \\\\ x-7y=4 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北十堰 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-05-17", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584852945482592256", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584852945482592256", "title": "2025年湖北省十堰市名校联盟中考二模数学试题", "paperCategory": 1}, {"id": "576951087153852416", "title": "2025年湖北省恩施市中考第一次适应性考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578016376712900608", "questionArticle": "<p>5．计算：</p><p>（1） $ \\sqrt { 18 }-\\dfrac { 2 } { \\sqrt { 2 } }+\\sqrt { 32 }\\times \\sqrt { 2 } $ </p><p>（2） $ \\begin{cases} 2x-4y=6 \\\\ 3x+2y=17 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门六中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16389|16424", "keyPointNames": "二次根式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578016352809562112", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578016352809562112", "title": "福建省厦门第六中学2024—2025学年下学期八年级期中检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578016102120202240", "questionArticle": "<p>6．随着交通安全意识的增强，某城镇居民开始积极购买头盔以保证骑行安全．某小商店购进 $ \\mathrm{ A } $ 种头盔3个和 $ B $ 种头盔4个共需190元， $ \\mathrm{ A } $ 种头盔4个和 $ B $ 种头盔3个共需195元．</p><p>（1）求 $ \\mathrm{ A } $ ， $ B $ 两种头盔的单价各是多少元；</p><p>（2）若该商店计划正好用450元购进 $ \\mathrm{ A } $ ， $ B $ 两种头盔（ $ \\mathrm{ A } $ ， $ B $ 两种头盔均购买），求该商店共有几种购买方案？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16420|16437", "keyPointNames": "二元一次方程的解|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578016074257440768", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "578016074257440768", "title": "福建省福州教育学院附属中学2024−2025学年七年级下学期半期考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578016099406487552", "questionArticle": "<p>7．解方程（组）</p><p>（1） $ -\\dfrac { 1 } { 8 }{\\left( { x+1 } \\right) ^ {3}}=8 $ </p><p>（2） $ \\begin{cases} x+y=4 \\\\ 2x-y=5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16290|16424", "keyPointNames": "立方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578016074257440768", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "578016074257440768", "title": "福建省福州教育学院附属中学2024−2025学年七年级下学期半期考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578016088421605376", "questionArticle": "<p>8．已知 $ \\begin{cases} x=-1 \\\\ y=2 \\end{cases}  $ 是二元一次方程组 $ \\begin{cases} 3x+2y=m \\\\ x-y=n \\end{cases}  $ 的解，则 $ m-n $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．2C．3D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578016074257440768", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "578016074257440768", "title": "福建省福州教育学院附属中学2024−2025学年七年级下学期半期考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578015970880434176", "questionArticle": "<p>9．在我国大力推进的智慧城市建设中，<i>A</i>市采用了两款由我国自主研发的先进 $ { \\rm{ A } }{ \\rm{ I } } $ 智能系统，用于城市交通流量调控和公共设施智能管理．经过一段时间的数据监测发现，3套交通流量调控 $ { \\rm{ A } }{ \\rm{ I } } $ 系统和2套公共设施智能管理 $ { \\rm{ A } }{ \\rm{ I } } $ 系统协同工作，一小时内能够有效处理180起城市交通与设施管理的综合事务；而2套交通流量调控 $ { \\rm{ A } }{ \\rm{ I } } $ 系统和3套公共设施智能管理 $ { \\rm{ A } }{ \\rm{ I } } $ 系统协同工作时，一小时内能够处理170起相关事务．请问，一套交通流量调控 $ { \\rm{ A } }{ \\rm{ I } } $ 系统和一套公共设施智能管理 $ { \\rm{ A } }{ \\rm{ I } } $ 系统每小时分别能处理多少起城市综合事务？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京人大附中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015941700661248", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015941700661248", "title": "北京市中国人民大学附属中学2024−2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015965062934528", "questionArticle": "<p>10．规定：形如关于 $ x $ 、 $ y $ 的方程 $ x+ky=b $ 与 $ kx+y=b $ 的两个方程互为“友好二元一次方程”，其中 $ k\\ne 1 $ ；由这两个方程组成的方程组 $ \\begin{cases} x+ky=b, \\\\ kx+y=b \\end{cases}  $ 叫做“友好方程组”．</p><p>（1）若关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} x+ay=2a+b， \\\\ \\left ( { b+1 } \\right ) x+y=5 \\end{cases}  $ 为“友好方程组”，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）若关于 $ x $ 、 $ y $ 的“友好方程组” $ \\begin{cases} x+ky=2， \\\\ kx+y=2 \\end{cases}  $ 的解为整数，则整数 $ k $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京人大附中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015941700661248", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015941700661248", "title": "北京市中国人民大学附属中学2024−2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 76, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 76, "timestamp": "2025-07-01T02:09:48.343Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}