{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 185, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "551571142680551424", "questionArticle": "<p>1．用如图①中的长方形和正方形纸板作侧面和底面，做成如图②的竖式和横式的两种无盖纸盒，现在仓库里有<i>m</i>张正方形纸板和<i>n</i>张长方形纸板，如果做两种纸盒若干个，恰好使库存的纸板用完，则<i>m</i>+<i>n</i>的值可能是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/03/2/1/0/0/0/551571098879434753/images/img_20.png\" style=\"vertical-align:middle;\" width=\"307\" alt=\"试题资源网 https://stzy.com\"></p><p>A．2024B．2025C．2026D．2027</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山东青岛 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-03-03", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "452959827330899968", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "452959827330899968", "title": "2024年山东省青岛市部分学校九年级中考二模数学试题", "paperCategory": 1}, {"id": "551571127119683584", "title": "2024年山东省青岛市市南区中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "551571600602079232", "questionArticle": "<p>2．我国古代数学经典著作《九章算术》中记载：“今有黄金九枚，白银一十一枚，称之重适等，交易其一，金轻十三两，问金、银各重几何?”意思是：甲袋中装有黄金9枚（每枚黄金重量相同），乙袋中装有白银11枚（每枚白银重量相同），称重两袋相等，两袋互相交换1枚后，甲袋比乙袋轻了13两（袋子的重量忽略不计），问黄金、白银每枚各重多少两?设每枚黄金重<i>x</i>两，每枚白银重<i>y</i>两，则可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 9x=11y \\\\ 8x+y+13=10y+x \\end{cases}  $ B． $ \\begin{cases} 11x=9y \\\\ 8x+y+13=10y+x \\end{cases}  $ </p><p>C． $ \\begin{cases} 9x=11y \\\\ 8x+y=10y+13 \\end{cases}  $ D． $ \\begin{cases} 11x=9y \\\\ 8x+13=10y+x \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江温州 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-03", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551571587612319744", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "551571587612319744", "title": "2024年 浙江省温州市浙师大协同体九年级下学期二模试题", "paperCategory": 1}, {"id": "460573275069915136", "title": "2024年浙江省温州外国语学校九年级中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "549407747474759680", "questionArticle": "<p>3．某商场准备进货 $ {\\rm \\mathit{A}，} B $ 两种小家电，已知小家电 $ A $ 每件进价300元，小家电 $ B $ 每件进价200元，计划共进货440件，且进货这两种小家电所需的成本之和为112000元．</p><p>(1)求 $ {\\rm \\mathit{A}，} B $ 两种小家电分别计划进货多少件？</p><p>(2)经过洽谈： $ {\\rm \\mathit{A}，} B $ 两种小家电的进价每台都少 $ m $ 元，若仍用112000元投入进货，且分别用于 $ A $ ， $ B $ 两种小家电的计划进货总金额均不变，则进货 $ {\\rm \\mathit{A}，} B $ 两种小家电的数量相同，求 $ m $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市育才中学 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-02", "keyPointIds": "16437|16476", "keyPointNames": "销售利润问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549407717988802560", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549407717988802560", "title": "重庆育才中学教育集团2024——2025学年下学期八年级入学数学试题（模拟一）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549407730496217088", "questionArticle": "<p>4．《孙子算经》中有一道题：“今有木，不知长短，引绳度之，余绳四尺五寸，屈绳量之，不足一尺，木长几何？”意思是用绳子去量一根长木，绳子还剩余4.5尺，将绳子对折再量长木，长木还剩余1尺，问长木长多少尺？设绳子长<i>x</i>尺，长木长<i>y</i>尺，则所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-y=4.5 \\\\ \\dfrac { 1 } { 2 }x-y=1 \\end{cases}  $　　　　B． $ \\begin{cases} y-x=4.5 \\\\ y-2x=1 \\end{cases}  $　　　　C． $ \\begin{cases} x-y=4.5 \\\\ y-\\dfrac { 1 } { 2 }x=1 \\end{cases}  $　　　　D． $ \\begin{cases} x-y=4.5 \\\\ 2y-x=1 \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市育才中学 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-02", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549407717988802560", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549407717988802560", "title": "重庆育才中学教育集团2024——2025学年下学期八年级入学数学试题（模拟一）", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "549406344819810304", "questionArticle": "<p>5．随着“低碳生活，绿色出行”理念的普及，新能源汽车正逐渐成为人们喜爱的交通工具．某汽车销售公司计划购进一批新能源汽车尝试进行销售，已知 $ \\mathrm{ A } $ 型汽车每辆的进价为25万元， $ B $ 型汽车每辆的进价为10万元．若该公司计划正好用200万元购进以上两种型号的新能源汽车（两种型号的汽车均购买），则不同的购买方案共有种<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙市南雅中学 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-02", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549406327052738560", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "549406327052738560", "title": "湖南省长沙市南雅中学2024−2025学年九年级下学期入学考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549406634302283776", "questionArticle": "<p>6．体育文化用品商店购进篮球和排球共20个，进价和售价如下表所示，全部销售完后共获利润260元.</p><p>（1）购进篮球和排球各多少个？</p><p>（2）销售6个排球的利润与销售几个篮球的利润相等？</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/25/2/1/0/0/0/549406580543889416/images/img_9.png\" style=\"vertical-align:middle;\" width=\"273\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东菏泽 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-02", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549406617994829824", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "549406617994829824", "title": "山东省菏泽市牡丹区第二十一中学2024−2025学年八年级下学期开学考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549406632649728000", "questionArticle": "<p>7．（1）计算： $ \\dfrac { 2\\sqrt { 12 }+\\sqrt { 3 } } { \\sqrt { 3 } }+1 $ </p><p>（2）解方程组： $ \\begin{cases} x-2y=3 \\\\ 3x+y=2 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东菏泽 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-02", "keyPointIds": "16299|16379|16424", "keyPointNames": "实数的运算|二次根式的性质和化简|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549406617994829824", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "549406617994829824", "title": "山东省菏泽市牡丹区第二十一中学2024−2025学年八年级下学期开学考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549406627071303680", "questionArticle": "<p>8．已知关于 $ x,y $ 的方程组 $ \\begin{cases} x-3y=m-1 \\\\ x+y=-3m+7 \\end{cases}  $ ．若方程组的解满足 $ x-y  &lt;  5 $ ，则 $ m $ 的最小整数值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -1 $ B． $ -2 $ C．0D．1</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东菏泽 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-02", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549406617994829824", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "549406617994829824", "title": "山东省菏泽市牡丹区第二十一中学2024−2025学年八年级下学期开学考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "549406850896142336", "questionArticle": "<p>9．《九章算术》中有一道阐述“盈不足术”的问题，原文如下：</p><p>今有人共买物，人出八，盈三；人出七，不足四．问人数，物价各几何？</p><p>译文为：</p><p>现有一些人共同买一个物品，每人出8元，还盈余3元；每人出7元，则还差4元，问共有多少人？这个物品的价格是多少？</p><p>请解答上述问题．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西安 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-02", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549406828288843776", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "549406828288843776", "title": "陕西省西安市高新第二初级中学2024−2024学年九年级下学期开学检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549406972094750720", "questionArticle": "<p>10．学校捐资购买了一批120吨的物资打算支援山区，现有甲、乙两种车型供选择，每辆车的运载能力和运费如表所示（假设每辆车均满载），若全部物资都用甲、乙两种车型来运送，需运费8200元，问分别需甲、乙两种车型各几辆？</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 109.45pt;\"><p>车型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>甲</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>乙</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 109.45pt;\"><p>汽车运载量（吨/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>8</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 109.45pt;\"><p>汽车运费（元/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>400</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>500</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西高新一中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-02", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549406958370988032", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549406958370988032", "title": "陕西省西安市西安高新第一中学2024−2025学年八年级下学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 186, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 186, "timestamp": "2025-07-01T02:22:52.230Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}