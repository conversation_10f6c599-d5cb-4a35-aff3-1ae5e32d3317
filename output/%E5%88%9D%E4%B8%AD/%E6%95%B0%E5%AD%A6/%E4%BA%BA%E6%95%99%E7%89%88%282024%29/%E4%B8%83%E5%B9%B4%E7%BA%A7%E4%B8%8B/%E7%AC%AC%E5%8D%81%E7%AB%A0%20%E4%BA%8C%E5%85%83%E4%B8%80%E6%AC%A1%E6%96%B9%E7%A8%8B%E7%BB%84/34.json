{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 33, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "587396604472111104", "questionArticle": "<p>1．某校积极开展劳动教育，两次购买 $ A,B $ 两种型号的劳动用品，购买记录如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.05pt;\"><p><i>A</i>型劳动用品（件）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.05pt;\"><p><i>B</i>型劳动用品（件）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>合计金额（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>第一次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.05pt;\"><p>20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.05pt;\"><p>25</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>1150</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>第二次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.05pt;\"><p>10</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.05pt;\"><p>20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p>800</p></td></tr></table><p>（1）求 $ A，B $ 两种型号劳动用品的单价；</p><p>（2）若该校计划再次购买 $ A，B $ 两种型号的劳动用品共40件，其中<i>A</i>型劳动用品购买数量不少于10件且不多于25件．该校购买这40件劳动用品至少需要多少元？（备注：<i>A</i>，<i>B</i>两种型号劳动用品的单价保持不变）</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南驻马店 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-14", "keyPointIds": "16437|16490|16544", "keyPointNames": "销售利润问题|一元一次不等式组的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587396571165143040", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "587396571165143040", "title": "2025年河南省驻马店市驿城区三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587395611940401152", "questionArticle": "<p>2．五一节前，某商店拟用 $ 1000 $ 元的总价购进 $ A、B $ 两种品牌的电风扇进行销售，为更好的销售，每种品牌电风扇都至少购进1台．已知购进3台<i>A</i>种品牌电风扇所需费用与购进2台<i>B</i>种品牌电风扇所需费用相同，购进1台<i>A</i>种品牌电风扇与2台<i>B</i>种品牌电风扇共需费用 $ 400 $ 元．</p><p>（1）求<i>A</i>、<i>B</i>两种品牌电风扇每台的进价分别是多少元？</p><p>（2）销售时，该商店将<i>A</i>种品牌电风扇定价为 $ 180 $ 元/台，<i>B</i>种品牌电风扇定价为 $ 250 $ 元/台，为能在销售完这两种电风扇后获得最大的利润，该商店应采用哪种进货方案？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东茂名 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-14", "keyPointIds": "16420|16437", "keyPointNames": "二元一次方程的解|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587395588179668992", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "587395588179668992", "title": "2025年广东省茂名市高州市第15周联考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587751578028777472", "questionArticle": "<p>3．（1）解方程组 $ \\begin{cases} x-y=5① \\\\ 2x+y=1② \\end{cases}  $ </p><p>（2）解不等式组 $ \\begin{cases} \\dfrac { 1 } { 2 }x-2\\geqslant  -3 \\\\ 8-2x &gt; 4 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东淄博 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-14", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587751550619000832", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "587751550619000832", "title": "2025年山东省淄博市临淄区中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586667970891395072", "questionArticle": "<p>4．《九章算术》中的算筹图是竖排的，现在改为横排，图中各行从左到右列出的算筹数分别表示未知数<i>x</i>，<i>y</i>的系数与相应的常数项，把图1所示的算筹图用我们现在所熟悉的方程组形式表示出来，就是 $ \\begin{cases} 3x+2y=19 \\\\ x+4y=23 \\end{cases}  $ ，在图2所示的算筹图中有一个图形被墨水覆盖了，若图2所表示的方程组中<i>x</i>的值为3，则被墨水所覆盖的图形为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/07/2/1/0/0/0/586667924292673536/images/img_11.png\" style=\"vertical-align:middle;\" width=\"309\" alt=\"试题资源网 https://stzy.com\"></p><p>A．<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/07/2/1/0/0/0/586667924292673537/images/img_12.png\" style=\"vertical-align:middle;\" width=\"3\" alt=\"试题资源网 https://stzy.com\">B．<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/07/2/1/0/0/0/586667924292673538/images/img_13.png\" style=\"vertical-align:middle;\" width=\"8\" alt=\"试题资源网 https://stzy.com\">C．<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/07/2/1/0/0/0/586667924292673539/images/img_14.png\" style=\"vertical-align:middle;\" width=\"13\" alt=\"试题资源网 https://stzy.com\">D．<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/07/2/1/0/0/0/586667924292673540/images/img_15.png\" style=\"vertical-align:middle;\" width=\"18\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586667956035170304", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586667956035170304", "title": "浙江省杭州市文澜中学2024−2025 学年九年级下学期第九次月考考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "586669124551483392", "questionArticle": "<p>5．当 $ m，n $ 都是实数<b>，</b>且满足 $ 3m+4n=10 $ <b>，</b>就称点 $ P\\left ( { m-1,4n+1 } \\right )  $ 为“34中幸运点”</p><p>例如：点 $ A\\left ( { 2,3 } \\right )  $ ，令 $ \\begin{cases} m-1=2 \\\\ 4n+1=3 \\end{cases}  $ ，解得 $ \\begin{cases} m=3 \\\\ n=\\dfrac { 1 } { 2 } \\end{cases}  $ </p><p>因为 $ 3m+4n=3\\times 3+4\\times \\dfrac { 1 } { 2 }=11\\ne 10 $ ，所以点 $ A\\left ( { 2,3 } \\right )  $ 不是“34中幸运点”</p><p>（1）请你判断点 $ B\\left ( { 3,-1 } \\right )  $ 是不是“34中幸运点”，请说明理由</p><p>（2）若 $ C $ 点为“34中幸运点”，且它的横坐标和纵坐标相等，求 $ C $ 点坐标．</p><p>（3）若点 $ D\\left ( { a,b } \\right )  $ 是“34中幸运点”，请直接写出 $ a $ 与 $ b $ 关系式．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025辽宁大连 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-14", "keyPointIds": "16424|16426|16497", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用|点的坐标", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586669100442624000", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586669100442624000", "title": "辽宁省大连市第三十四中学2024−2025学年下学期5月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586669123779731456", "questionArticle": "<p>6．某农场用2台大收割机和5台小收割机同时工作2小时共收割小麦3.6公顷，3台大收割机和2台小收割机同时工作5小时共收割小麦8公顷.1台大收割机和1台小收割机每小时各收割小麦多少公顷？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁大连 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-14", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586669100442624000", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586669100442624000", "title": "辽宁省大连市第三十四中学2024−2025学年下学期5月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586669121397366784", "questionArticle": "<p>7．解方程组：</p><p>（1） $ \\begin{cases} x-y=3 \\\\ 3x-8y=14 \\end{cases}  $  ；</p><p>（2） $ \\begin{cases} 3x-2y=4 \\\\ 7x+4y=18 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁大连 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586669100442624000", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586669100442624000", "title": "辽宁省大连市第三十四中学2024−2025学年下学期5月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586669119228911616", "questionArticle": "<p>8．小李到文具店购买文具，他发现若购买4支钢笔、2支铅笔、1支水彩笔需要50元，若购买1支钢笔、3支铅笔、4支水彩笔也正好需要50元，则购买1支钢笔、1支铅笔、1支水彩笔需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁大连 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-14", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586669100442624000", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586669100442624000", "title": "辽宁省大连市第三十四中学2024−2025学年下学期5月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586669114690674688", "questionArticle": "<p>9．我国明代《算法统宗》一书中有这样一题：“一支竿子一条索，索比竿子长一托，对折索子来量竿，却比竿子短一托（一托按照5尺计算）．”大意是：现有一根竿和一条绳索，如果用绳索去量竿，绳索比竿长5尺；如果将绳索对折后再去量竿，就比竿短5尺，则绳索长几尺？设竿长<i>x</i>尺，绳索长<i>y</i>尺，根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+5=y \\\\ x-5=\\dfrac { y } { 2 } \\end{cases}  $</p><p>B． $ \\begin{cases} x+5=y \\\\ 2x-5=y \\end{cases}  $</p><p>C． $ \\begin{cases} x=y+5 \\\\ x-5=\\dfrac { y } { 2 } \\end{cases}  $</p><p>D． $ \\begin{cases} x+5=y \\\\ x-5=2y \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东日照 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-06-14", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590999429286502400", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590999429286502400", "title": "山东省日照市北京路中学2024−2025学年七年级下学期6月月考数学试卷", "paperCategory": 1}, {"id": "586669100442624000", "title": "辽宁省大连市第三十四中学2024−2025学年下学期5月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "586669112333475840", "questionArticle": "<p>10．若 $ \\begin{cases} x=3 \\\\ y=-2 \\end{cases}  $ 是关于 $ x $ 、 $ y $ 的方程 $ x+my=-5 $ 的一个解，则 $ m $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．4B．5C．6D．−4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁大连 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-14", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586669100442624000", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586669100442624000", "title": "辽宁省大连市第三十四中学2024−2025学年下学期5月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 34, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 34, "timestamp": "2025-07-01T02:04:49.541Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}