{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 181, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "553052117939822592", "questionArticle": "<p>1．在冰雪旅游时期，某旅游商品经销店欲购进<i>A</i>、<i>B</i>两种冰雪纪念品，若用380元可以购进<i>A</i>种纪念品7件，<i>B</i>种纪念品8件；也可以用380元购进<i>A</i>种纪念品10件，<i>B</i>种纪念品6件．</p><p>(1)求<i>A</i>、<i>B</i>两种纪念品的进价分别为多少?</p><p>(2)若该经销店每件<i>A</i>种纪念品售价25元，每件<i>B</i>种纪念品售价38元，该经销店准备购进<i>A</i>、<i>B</i>两种纪念品共50件，且这两种纪念品全部售出后总获利不低于310元，则该经销店最多可购进<i>A</i>种纪念品多少件?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈尔滨 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-07", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553052095164751872", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "553052095164751872", "title": "黑龙江省哈尔滨市第三十九中学校2024-—2025学年九年级下学期数学素养检测试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553051382326009856", "questionArticle": "<p>2．淇淇的妈妈买了 $ 15 $ 支牙刷和 $ 12 $ 盒牙膏共花了 $ 336 $ 元，一段时间后淇淇以同样的价格（牙刷和牙膏的单价保持不变）又买了 $ 20 $ 支牙刷和 $ 16 $ 盒牙膏共花了 $ 418 $ 元，淇淇的妈妈看到淇淇的购物小票时，说：“记录单的总全额应该算错了”．下列判断正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．淇淇妈妈说的不对，总金额就是 $ 418 $ 元B．淇淇妈妈说的对，淇淇多付了 $ 30 $ 元</p><p>C．淇淇妈妈说的对，淇淇少付了 $ 30 $ 元D．淇淇妈妈说的对，淇淇多付了 $ 40 $ 元</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "620000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025甘肃嘉峪关 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-07", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553051366555426816", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "553051366555426816", "title": "甘肃省嘉峪关市2024——2025学年下学期开学摸底考试九年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "551913277753696256", "questionArticle": "<p>3．某超市用3400元购进<i>A</i>、<i>B</i>两种文具盒共120个，这两种文具盒的进价、标价如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">价格/类型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p style=\"text-align:center;\"><i>A</i>型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p style=\"text-align:center;\"><i>B</i>型</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">进价（元/只）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p style=\"text-align:center;\">15</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p style=\"text-align:center;\">35</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">标价（元/只）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p style=\"text-align:center;\">25</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p style=\"text-align:center;\">50</p></td></tr></table><p>（1）这两种文具盒各购进多少只？</p><p>（2）若<i>A</i>型文具盒按标价的9折出售，<i>B</i>型文具盒按标价的8折出售，那么这批文具盒全部售出后，超市共获利多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南鹤壁外中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-07", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551913258799636480", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "551913258799636480", "title": "河南省鹤壁市外国语中学2024−2025学年七年级下学期开学数学测试卷", "paperCategory": 1}, {"id": "161484795955945472", "title": "新疆和田地区2020年九年级中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551913275639767040", "questionArticle": "<p>4．已知甲、乙二人解关于 $ x $ 、 $ y $ 的方程组 $ \\{\\hspace{-0.5em}  \\begin{array} {} ax+by=2 \\\\ cx-7y=8 \\end{array} \\hspace{-0.5em}  $ ，甲正确地解出 $ \\{\\hspace{-0.5em}  \\begin{array} {} x=3 \\\\ y=-2 \\end{array} \\hspace{-0.5em}  $ ，而乙把 $ c $ 抄错了，结果解得 $ \\{\\hspace{-0.5em}  \\begin{array} {} x=-2 \\\\ y=2 \\end{array} \\hspace{-0.5em}  $ ，求 $ a、b、c $ 的值.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南鹤壁外中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-07", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551913258799636480", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "551913258799636480", "title": "河南省鹤壁市外国语中学2024−2025学年七年级下学期开学数学测试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551913273664249856", "questionArticle": "<p>5．解方程：</p><p>(1) $ 2\\left ( { x-1 } \\right ) -3=x $ </p><p>(2) $ \\dfrac { 5-3x } { 2 }=\\dfrac { 3-5x } { 3 } $ </p><p>(3) $ \\begin{cases} x+2y=3 \\\\ x-2y=1 \\end{cases}  $ </p><p>(4) $ \\begin{cases} 2x+3y=1 \\\\ 3x+5y=2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南鹤壁外中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-07", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551913258799636480", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "551913258799636480", "title": "河南省鹤壁市外国语中学2024−2025学年七年级下学期开学数学测试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551913268169711616", "questionArticle": "<p>6．已知方程组 $ \\begin{cases} 5x-3y=-7 \\\\ 6x+8y=9 \\end{cases}  $ 的解满足 $ x-y=m-1 $ ．则<i>m</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -2 $ B．2C． $ -1 $ D．1</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南鹤壁外中 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-07", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551913258799636480", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "551913258799636480", "title": "河南省鹤壁市外国语中学2024−2025学年七年级下学期开学数学测试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "551913266525544448", "questionArticle": "<p>7．已知方程组 $ \\begin{cases} 2x+y=A \\\\ x+y=3 \\end{cases}  $ 的解为 $ \\begin{cases} x=2 \\\\ y=B \\end{cases}  $ ，则<i>A</i>，<i>B</i>的值分别为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2，3B．1，3C．5，1D．2，4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南鹤壁外中 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-07", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551913258799636480", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "551913258799636480", "title": "河南省鹤壁市外国语中学2024−2025学年七年级下学期开学数学测试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "552902686552662016", "questionArticle": "<p>8．某商场购进甲、乙两种服装后,都加价40%标价出售,春节期间商场搞优惠促销,决定将甲、乙两种服装分别按标价的八折和九折出售．某顾客购买甲、乙两种服装各一件共付款182元,两种服装标价之和为210元．问这两种服装的进价各是多少元?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 7, "createTime": "2025-03-07", "keyPointIds": "16424|16437", "keyPointNames": "加减消元法解二元一次方程组|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "519569833647710208", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "519569833647710208", "title": "第12讲　实际问题与二元一次方程组（诊断）", "paperCategory": 10}, {"id": "552902683469848576", "title": "第7讲 实际问题与二元一次方程组（诊断）", "paperCategory": 10}, {"id": "552437279148515328", "title": "XJ第21讲　实际问题与二元一次方程组（诊断）", "paperCategory": 10}, {"id": "537300180430790656", "title": "第3讲　实际问题与二元一次方程组（诊断）LJ七下", "paperCategory": 10}, {"id": "534033121366286336", "title": "第7讲　实际问题与二元一次方程组（诊断）", "paperCategory": 10}, {"id": "440119463356178432", "title": "四川省内江市威远县威远中学校2023-2024学年七年级下学期期中考试数学试题", "paperCategory": 1}, {"id": "523817712629358592", "title": "第3讲　实际问题与二元一次方程组（诊断）", "paperCategory": 10}], "questionTypeCode": "6"}, {"questionId": "551913272938635264", "questionArticle": "<p>9．已知 $ \\begin{cases} x=a \\\\ y=b \\end{cases}  $ 是方程组 $ \\begin{cases} x-2y=0 \\\\ 2x+y=5 \\end{cases}  $ 的解，则 $ 3a-b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|410000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南鹤壁外中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 3, "createTime": "2025-03-07", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551913258799636480", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "551913258799636480", "title": "河南省鹤壁市外国语中学2024−2025学年七年级下学期开学数学测试卷", "paperCategory": 1}, {"id": "204702037627412480", "title": "山东省东营市垦利区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "161418466896945152", "title": "2020年度广东省茂名市中考模拟试题（一）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551913269033738240", "questionArticle": "<p>10．我国古代数学著作《孙子算经》中有“雉兔同笼”问题：“今有雉兔同笼，上有三十五头，下有九十四足，问雉兔各几何？”其大意是：鸡兔同笼，共有35个头，94条腿，问鸡兔各多少只？设鸡有 $ x $ 只，兔有 $ y $ 只，根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=94 \\\\ 4x+2y=35 \\end{cases}  $ B． $ \\begin{cases} x+y=94 \\\\ 2x+4y=35 \\end{cases}  $ C． $ \\begin{cases} x+y=35 \\\\ 4x+2y=94 \\end{cases}  $ D． $ \\begin{cases} x+y=35 \\\\ 2x+4y=94 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 7, "referenceNum": 2, "createTime": "2025-03-07", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "467993629706985472", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "467993629706985472", "title": "2024年辽宁省中考数学试卷", "paperCategory": 1}, {"id": "551913258799636480", "title": "河南省鹤壁市外国语中学2024−2025学年七年级下学期开学数学测试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 182, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 182, "timestamp": "2025-07-01T02:22:23.141Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}