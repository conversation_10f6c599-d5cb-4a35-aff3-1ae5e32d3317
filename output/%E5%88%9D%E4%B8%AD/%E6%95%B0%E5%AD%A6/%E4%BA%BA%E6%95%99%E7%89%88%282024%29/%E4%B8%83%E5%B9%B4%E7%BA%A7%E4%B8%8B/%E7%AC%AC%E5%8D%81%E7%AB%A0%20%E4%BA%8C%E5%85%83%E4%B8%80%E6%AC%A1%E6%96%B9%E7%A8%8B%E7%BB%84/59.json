{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 58, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "580243037504184320", "questionArticle": "<p>1．《九章算术》中记载：“今有共买羊，人出五，不足四十五；人出七，不足三，问人数、羊价各几何？”其大意是：今有人合伙买羊，若每人出5钱，还差45钱；若每人出7钱，还差3钱，问合伙人数、羊价各是多少？</p><p>设合伙人数为<i>x</i>人，羊价为<i>y</i>钱，根据题意，下列是甲、乙、丙、丁四名同学列的方程或方程组，</p><p>甲同学： $ \\begin{cases} y=5x+45 \\\\ y=7x+3 \\end{cases}  $ ；乙同学： $ \\begin{cases} y=5x-45 \\\\ y=7x+3 \\end{cases}  $ ；丙同学： $ \\dfrac { y-45 } { 5 }=\\dfrac { y-3 } { 7 } $ ；丁同学： $ \\dfrac { y+45 } { 5 }=\\dfrac { y+3 } { 7 } $ ．</p><p>正确的有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1个B．2个C．3个D．4个</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-28", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580243021016375296", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580243021016375296", "title": "2025年湖北省九年级G20五月联考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "581954041766256640", "questionArticle": "<p>2．若关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} 2x+5y=-26, \\\\ ax-by=-4 \\end{cases}  $ 和 $ \\begin{cases} 3x-5y=36, \\\\ bx+ay=-8 \\end{cases}  $ 有相同的解．</p><p>（1）求这两个方程组的解；</p><p>（2）求代数式 $ {\\left( { 2a+b } \\right) ^ {2022}} $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东中山市第一中学 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-27", "keyPointIds": "16424|30400", "keyPointNames": "加减消元法解二元一次方程组|有理数的乘方", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581954017384767488", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "581954017384767488", "title": "广东中山第一中学2024−2025学年下学期4月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581954040377942016", "questionArticle": "<p>3．解方程组: $ \\begin{cases} x-{ { 4 } }y=-1 \\\\ 2x+y=16 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东中山市第一中学 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-27", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581954017384767488", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "581954017384767488", "title": "广东中山第一中学2024−2025学年下学期4月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581954038272401408", "questionArticle": "<p>4．若 $ \\begin{cases} x=n \\\\ y=m \\end{cases}  $ 是二元一次方程 $ x-2y-3=0 $ 的解，则 $ 2n-4m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东中山市第一中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-27", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581954017384767488", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "581954017384767488", "title": "广东中山第一中学2024−2025学年下学期4月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581954031821561856", "questionArticle": "<p>5．方程 $ mx-2y=2x+5 $ 是二元一次方程，请你推断<i>m</i>的值属于下列情况中的（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．不可能是 $ -1 $</p><p>B．不可能是 $ -2 $</p><p>C．不可能是1</p><p>D．不可能是2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东中山市第一中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-27", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581954017384767488", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "581954017384767488", "title": "广东中山第一中学2024−2025学年下学期4月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580243946334367744", "questionArticle": "<p>6．解下列方程组：</p><p>（1） $ \\begin{cases} 3x-2y=8 \\\\ 2x+y=3 \\end{cases}  $ （用代入法）；</p><p>（2） $ \\begin{cases} 3x+4y=16 \\\\ 5x-6y=33 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京西城 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-05-27", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580243921529253888", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580243921529253888", "title": "北京市西城区第三十九中学2024−2025学年下学期七年级期中考试数学试题卷", "paperCategory": 1}, {"id": "196959291999297536", "title": "北京师范大学附属实验中学2021-2022学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580243932874846208", "questionArticle": "<p>7．已知 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ 是二元一次方程组 $ \\begin{cases} ax+by=7 \\\\ ax-by=1 \\end{cases}  $ 的解，则 $ a-b $ 的值为 $ ( $ 　　 $ ) $ </p><p>A．−1B．1C．2D．3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|610000|230000|110000|630000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025北京西城 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 5, "createTime": "2025-05-27", "keyPointIds": "16305|16420|16424", "keyPointNames": "代数式求值|二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580243921529253888", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580243921529253888", "title": "北京市西城区第三十九中学2024−2025学年下学期七年级期中考试数学试题卷", "paperCategory": 1}, {"id": "379043905164058624", "title": "山东省济南市济南稼轩学校2023-2024学年八年级上学期期中数学试题", "paperCategory": 1}, {"id": "461662634149978112", "title": "陕西省延安市宝塔区2022-2023学年八年级下学期期末考试数学试题", "paperCategory": 1}, {"id": "478027305773735936", "title": "青海省西宁市城西区海湖中学2023−2024学年八年级上学期开学数学试题", "paperCategory": 1}, {"id": "208526307193298944", "title": "黑龙江省牡丹江市宁安市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580244083542634496", "questionArticle": "<p>8．我国古代数学著作《增删算法统宗》中有一首诗，其大意是：今有绢与布40定，卖得680贯钱，若……，……欲问绢布有多少，分开把价算，若人算得无差错，你的名字城镇到处扬．若设有绢 $ x $ 定，布 $ y $ 定，可列出符合题意的方程组 $ \\begin{cases} x+y=40 \\\\ \\dfrac { 90 } { 4 }x+\\dfrac { 50 } { 3 }y=680 \\end{cases}  $ ，根据已有信息，题中用“……，……”表示的缺失条件应为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．4定绢价50贯，3定布价90贯B．4定绢价90贯，3定布价50贯</p><p>C．4定布价90贯，3定绢价50贯D．4定布价50贯，3定绢价90贯</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|210000|350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁本溪 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 3, "createTime": "2025-05-27", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576952218005315584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "576952218005315584", "title": "2025年辽宁省本溪市中考一模数学试题", "paperCategory": 1}, {"id": "580244070536097792", "title": "福建省厦门市松柏中学2024−2025学年下学期七年级数学期中考卷", "paperCategory": 1}, {"id": "559472301730734080", "title": "山东省济宁市邹城市第十二中学2024−2025学年九年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "581953728640491520", "questionArticle": "<p>9．解方程：</p><p>（1） $ 2{\\left( { x-1 } \\right) ^ {2}}=288 $ ；</p><p>（2） $ \\begin{cases} x+2y=7 \\\\ 3x+4y=17 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-27", "keyPointIds": "16287|16424", "keyPointNames": "平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581953704812650496", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "581953704812650496", "title": "北京市第六十六中学2024—2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581953727206039552", "questionArticle": "<p>10．对 $ x $ ， $ y $ ， $ z $ 定义一种新运算 $ F $ ，规定： $ F(x,y,z)=ax+by+cz $ ，其中 $ a $ ， $ b $ 为非负数．若 $ F(3,2,1)=5，F(1,2,-3)=1 $ ，设 $ H=a+2b+c $ ，则 $ H $ 的取值范围是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-27", "keyPointIds": "16443|16482", "keyPointNames": "解三元一次方程组|不等式的性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581953704812650496", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "581953704812650496", "title": "北京市第六十六中学2024—2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 59, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 59, "timestamp": "2025-07-01T02:07:46.713Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}