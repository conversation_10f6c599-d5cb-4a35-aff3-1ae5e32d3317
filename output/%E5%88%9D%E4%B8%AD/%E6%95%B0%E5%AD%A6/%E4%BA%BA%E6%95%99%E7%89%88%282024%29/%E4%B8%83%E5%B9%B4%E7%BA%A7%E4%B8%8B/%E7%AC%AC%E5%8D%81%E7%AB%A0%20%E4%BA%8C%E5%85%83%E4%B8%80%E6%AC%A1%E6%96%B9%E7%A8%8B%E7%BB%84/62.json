{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 61, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "580623351988662272", "questionArticle": "<p>1．已知方程组 $ \\begin{cases} 2x-y=1+2a \\\\ x+4y=2+a \\end{cases}  $ 的解满足 $ -1  &lt;  x+y\\leqslant  2 $ ，</p><p>（1）求 $ a $ 的取值范围；</p><p>（2）求 $ a $ 为何整数时，不等式 $ 2ax-x &gt; 2a-1 $ 的解集为 $ x  &lt;  1 $ ？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南郑州外国语学校分校 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16426|16482|16489", "keyPointNames": "二元一次方程组的应用|不等式的性质|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580623327091273728", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580623327091273728", "title": "河南省郑州市外国语中学2024−2025学年下学期八年级期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581954044119261184", "questionArticle": "<p>2．在平面直角坐标系 $ xOy $ 中，对于点 $ P(x,y) $ ，若点<i>Q</i>的坐标为 $ (ax+y,x+ay) $ ，则称点<i>Q</i>是点<i>P</i>的“<i>a</i>阶派生点”（其中<i>a</i>为常数，且 $ a\\ne 0 $ ）．例如：点 $ P(1,4) $ 的“2阶派生点”为点 $ Q(2\\times 1+4,1+2\\times 4) $ ，即点 $ Q(6,9) $ ．</p><p>（1）若点<i>P</i>的坐标为 $ (-1,5) $ ，则它的“3阶派生点”的坐标为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）若点<i>P</i>的“5阶派生点”的坐标为 $ (-9,3) $ ，求点<i>P</i>的坐标；</p><p>（3）若点 $ P(c+1,2c-1) $ 先向左平移2个单位长度，再向上平移1个单位长度后得到了点 $ P{{}_{ 1 } } $ ．点 $ P{{}_{ 1 } } $ 的“ $ -4 $ 阶派生点” $ P{{}_{ 2 } } $ 位于坐标轴上，求点 $ P{{}_{ 2 } } $ 的坐标．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东中山市第一中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 16, "referenceNum": 2, "createTime": "2025-05-26", "keyPointIds": "16441|16497", "keyPointNames": "其他问题|点的坐标", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581954017384767488", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "581954017384767488", "title": "广东中山第一中学2024−2025学年下学期4月月考七年级数学试题", "paperCategory": 1}, {"id": "151286849620713472", "title": "江苏省泰州市靖江市实验学校2021-2022学年八年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581954042437345280", "questionArticle": "<p>3．疫情期间为保护学生和教师的健康，某学校储备“抗疫物资”，用19000元购进甲、乙两种医用口罩共计900盒，甲、乙两种口罩的售价分别是20元/盒，25元/盒．</p><p>（1）求甲、乙两种口罩各购进了多少盒？</p><p>（2）现已知甲、乙两种口罩的数量分别是20个/盒，25个/盒，按照市教育局要求，学校必须储备足够使用10天的口罩，该校师生共计900人，每人每天2个口罩，问购买的口罩数量是否能满足市教育局的要求？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东中山市第一中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-05-26", "keyPointIds": "16278|16424|16437", "keyPointNames": "有理数的混合运算|加减消元法解二元一次方程组|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581954017384767488", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "581954017384767488", "title": "广东中山第一中学2024−2025学年下学期4月月考七年级数学试题", "paperCategory": 1}, {"id": "425200511895248896", "title": "湖南省岳阳市弘毅新华中学2022-2023学年七年级下学期第一次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580903188582146048", "questionArticle": "<p>4．学校根据上级文件要求，打算安排七、八年级师生进行研学活动．某班两位同学关于租车方案讨论如下：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/22/2/1/0/0/0/580903140955824129/images/img_16.jpg\" style=\"vertical-align:middle;\" width=\"554\" alt=\"试题资源网 https://stzy.com\"></p><p>根据他们的对话得到以下四个结论：</p><p>①每辆甲车的载客量要比乙车多15人；②共有两种租车方案；③租车最低费用是2160元；④两种方案的租车费用一样多．其中正确的结论是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．①②B．①②③C．②③D．①②④</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东日照 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-25", "keyPointIds": "16441|16490", "keyPointNames": "其他问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580903171163201536", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580903171163201536", "title": "山东省日照市东港区2024−2025年九年级中考二模考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580902746083074048", "questionArticle": "<p>5．长沙交警正在全市范围内开展“一盔一带”安全守护行动，旨在提升摩托车、电动自行车骑乘人员和机动车驾乘人员的交通安全防护水平．某超市计划购进一批头盔用于销售．已知购进4个 $ \\mathrm{ A } $ 型头盔和3个 $ B $ 型头盔需要315元，购进3个 $ \\mathrm{ A } $ 型头盔和4个 $ B $ 型头盔需要350元．</p><p>（1）求 $ \\mathrm{ A } $ ， $ B $ 两种型号的头盔单价分别为多少元；</p><p>（2）若该商场准备购进100个这两种型号的头盔，总费用不超过4400元，则最多可购进 $ B $ 型头盔多少个？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南怀化 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-25", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580902713606578176", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580902713606578176", "title": "湖南省怀化市2025年中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580901526958284800", "questionArticle": "<p>6．新年前夕，国家主席习近平通过中央广播电视总台和 互联网，发表二○二五年新年贺词，其中提到：“我们因地制宜培育新质生产力，新产业新业态新模式竞相涌现，新能源汽车年产量首次突破1000万辆，集成电路、人工智能、量子通信等 领域取得新成果．”随着新能源汽车的发展，某市计划引进一批新能源公交车投入运营．新能源公交车有 $ A，B $ 两种车型，若购买<i>A</i>型公交车30辆，<i>B</i>型公交车10辆，共需2600万元；若购买<i>A</i>型公交车20辆，<i>B</i>型公交车30辆，共需3600万元．</p><p>（1）求购买<i>A</i>型和<i>B</i>型新能源公交车每辆分别需要多少万元．</p><p>（2）交通管理部门调研发现：<i>A</i>型新能源公交车适合支线道路运营，<i>B</i>型新能源公交车适合主干道运营．若本批次计划购买 $ A，B $ 两种新能源公交车共80辆，且支线道路运营车辆不超过主干道运营车辆为 $ \\dfrac { 1 } { 4 } $ ，请问分别购买多少辆 $ A，B $ 两种新能源公交车可使得政府投入的费用最少？并求出最少费用．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南南阳 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-05-25", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583057126869737472", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583057126869737472", "title": "河南省南阳市2024—2025学年九年级下学期第三次联考数学试题试卷", "paperCategory": 1}, {"id": "580901490308456448", "title": "河南省焦作市2024−2025学年九年级下学期第三次联考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580901914700722176", "questionArticle": "<p>7．方程组 $ \\begin{cases} x+y=1 \\\\ 3x-y=3 \\end{cases}  $ 的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南洛阳 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-25", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580901895893463040", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580901895893463040", "title": "河南省洛阳市 2025年九年级中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580904925632176128", "questionArticle": "<p>8．春风送暖，与爱同行！为践行“雷锋精神”，某学校举办科创爱心义卖活动，将所得款项全部用于资助西藏贫困学生．在义卖活动中，某摊位将班级的科创作品进行义卖，以下是该摊位销售情况的部分记录：</p><table style=\"border: solid 1px;border-collapse: collapse; width:311.25pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">交易编码</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">品类与数量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">销售总价（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">一辆电动风力小车、两个简易电动风扇</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 18 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 2 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">两辆电动风力小车、三个简易电动风扇</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 31 $ </p></td></tr></table><p>（1）求该摊位一辆电动风力小车和一个简易电动风扇的售价分别是多少元？</p><p>（2）若该摊位希望总捐款金额不低于 $ 2000 $ 元，计划出售电动风力小车与简易电动风扇共 $ 300 $ 件，那么电动风力小车至少需要多少辆？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东深圳 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-25", "keyPointIds": "16438|16440|16486", "keyPointNames": "和差倍分问题|表格或图示问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580904895840034816", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580904895840034816", "title": "2025年广东省深圳市福田区中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580905259440054272", "questionArticle": "<p>9．日常生活收纳物品时，人们通常以空间利用率（ $ 空间利用率=\\dfrac { 物品实际占用体积 } { 容器体积 }\\times 100\\% $ ）来衡量收纳效果．如图，某长方体储物箱的内部尺寸为长 $ 40{ \\rm{ c } }{ \\rm{ m } } $ ，宽 $ 20{ \\rm{ c } }{ \\rm{ m } } $ ，高 $ 25{ \\rm{ c } }{ \\rm{ m } } $ ，收纳口在储物箱的上方．现计划收纳<i>A</i>，<i>B</i>两种长方体物品（数量足够多），其中<i>A</i>物品的尺寸为长 $ 20{ \\rm{ c } }{ \\rm{ m } } $ ，宽 $ 10{ \\rm{ c } }{ \\rm{ m } } $ ，高 $ 4{ \\rm{ c } }{ \\rm{ m } } {\\rm ，\\mathit{B}} $ 物品的尺寸为长 $ 10{ \\rm{ c } }{ \\rm{ m } } $ ，宽 $ 5{ \\rm{ c } }{ \\rm{ m } } $ ，高 $ 3{ \\rm{ c } }{ \\rm{ m } } $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/22/2/1/0/0/0/580905183674150922/images/img_26.png\" style=\"vertical-align:middle;\" width=\"328\" alt=\"试题资源网 https://stzy.com\"></p><p>根据实际要求，收纳物品时，储物箱内的同一层只能以同一种方式摆放同一种物品，不同层可以改变摆放方式，但物品的叠加高度不得超过储物箱的高度，物品叠加时储物箱及物品都不会产生形变 $ {\\rm ．\\mathit{A}} $ 物品可选择方式①②③进行摆放，<i>B</i>物品只按方式④进行摆放．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/22/2/1/0/0/0/580905183674150923/images/img_27.png\" style=\"vertical-align:middle;\" width=\"278\" alt=\"试题资源网 https://stzy.com\"></p><p>阅读以上材料，完成下列问题：</p><p>（1）若储物箱只收纳<i>A</i>物品且以方式①摆放，求储物箱最多可收纳<i>A</i>物品的数量（单位：件）；</p><p>（2）若储物箱同时收纳<i>A</i>，<i>B</i>两种物品且<i>A</i>物品以方式①摆放，请你判断储物箱的空间利用率是否可以达到 $ 100\\% $ ．若能，请分别求出收纳<i>A</i>，<i>B</i>两种物品的数量（单位：件）；若不能，请说明理由；</p><p>（3）若储物箱同时收纳<i>A</i>，<i>B</i>两种物品，且箱子的承重量足够，已知每个<i>A</i>物品重 $ 0.6{ \\rm{ k } }{ \\rm{ g } } $ ，每个<i>B</i>物品重 $ 0.1{ \\rm{ k } }{ \\rm{ g } } $ ，现选择其中若干种摆放方式进行组合，请你直接写出一种空间利用率最大的组合方式及收纳物品的总重量．（要求：组合方式及收纳物品的总重量的回答格式：如“一层①和两层④组合，总重量 $ {\\rm ***} { \\rm{ k } }{ \\rm{ g } } $ ”、“一层①、两层②、一层③组合，总重量 $ {\\rm ***} { \\rm{ k } }{ \\rm{ g } } $ ”；本题将综合考虑“空间利用率最大”和“收纳物品的总重量”给分，空间利用率不是最大的不得分，空间利用率最大但总重量不是最大的酌情得分，空间利用率最大且总重量最大的才能得满分．）</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建泉州 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-24", "keyPointIds": "16441|28548", "keyPointNames": "其他问题|有理数混合运算的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580905224811880448", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580905224811880448", "title": "2025年福建省泉州市初中毕业班中考模拟考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580245082680373248", "questionArticle": "<p>10．如图，嘉淇将一正方形纸片 $ ABCD $ 裁剪成①，②，③，④四块，其中①～③是三块小矩形，④是一块小正方形．若已知矩形②和③的周长和为20，则正方形 $ ABCD $ 与正方形④的周长和为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/21/2/1/0/0/0/580245038631788544/images/img_12.png\" style=\"vertical-align:middle;\" width=\"147\" alt=\"试题资源网 https://stzy.com\"></p><p>A．20B．30C．35D．40</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北唐山 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-23", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580245062824538112", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580245062824538112", "title": "河北省唐山市第十二中学2025年中考第二次模拟考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 62, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 62, "timestamp": "2025-07-01T02:08:07.131Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}