{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 23, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "591207017181655040", "questionArticle": "<p>1．我国古代算书《四元玉鉴》里有这样一道题：“九百九十九文钱，甜果苦果买一千，甜果九个十一文，苦果七个四文钱，试问甜苦果几个？”其大意是用九百九十九文钱共买了一千个甜果和苦果，其中十一文钱可以买甜果九个，四文钱可以买苦果七个，问甜果苦果各买几个？若设买甜果<i>x</i>个，苦果<i>y</i>个，根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=1000 \\\\ 9x+7y=999 \\end{cases}  $　　　　B． $ \\begin{cases} x+y=999 \\\\ 11x+4y=1000 \\end{cases}  $</p><p>C． $ \\begin{cases} x+y=1000 \\\\ \\dfrac { 11 } { 9 }x+\\dfrac { 4 } { 7 }y=999 \\end{cases}  $　　　　D． $ \\begin{cases} x+y=1000 \\\\ \\dfrac { 9 } { 11 }x+\\dfrac { 7 } { 4 }y=999 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川眉山 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-20", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591206999209062400", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "591206999209062400", "title": "2025年四川省眉山市中考数学真题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589575908266123264", "questionArticle": "<p>2．在平面直角坐标系<i>xOy</i>中，点 $ A\\left ( { x{{}_{ 1 } },y{{}_{ 1 } } } \\right ) ,B\\left ( { x{{}_{ 2 } },y{{}_{ 2 } } } \\right )  $ ，若 $ x{{}_{ 2 } }-x{{}_{ 1 } }=y{{}_{ 2 } }-y{{}_{ 1 } } $ ，则称点 $ \\mathrm{ A } $ 与点 $ B $ 互为“神秘点”．例如，点 $ A(-1,3) $ ，点 $ B(2,6) $ ，因为 $ 2-(-1)=6-3 $ ，所以点 $ \\mathrm{ A } $ 与点 $ B $ 互为“神秘点”．</p><p>（1）若点 $ \\mathrm{ A } $ 的坐标是 $ (4,-2) $ ，且点 $ \\mathrm{ A } $ 与点 $ B(-1,a) $ 互为“神秘点”，求 $ a $ 的值．</p><p>（2）若点 $ A(4,1) $ 与“神秘点” $ B(m,-n) $ 互为“神秘点”，若<i>m</i>，<i>n</i>均为正整数，求点 $ B $ 的坐标．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽芜湖 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-20", "keyPointIds": "16420|16497", "keyPointNames": "二元一次方程的解|点的坐标", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575884794798080", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575884794798080", "title": "安徽省芜湖市2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589575909205647360", "questionArticle": "<p>3．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} 3x+y=5a-4 \\\\ x-y=-a \\end{cases}  $ ，其中 $ a $ 为实数．</p><p>（1）当 $ a=2 $ 时，方程组的解为_；</p><p>（2）当 $ x+y &gt; 0 $ 时，求 $ a $ 的取值范围；</p><p>（3）试说明无论 $ a $ 取何数时，代数式 $ 2x-y $ 的值始终不变．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽芜湖 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-20", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575884794798080", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575884794798080", "title": "安徽省芜湖市2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589575903644000256", "questionArticle": "<p>4．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+y=2\\sqrt { 2 }m \\\\ x+2y=-2\\sqrt { 2 }m+3n \\end{cases}  $ 的解满足 $ x-y=2 $ ．</p><p>（1）若 $ x+y=3 $ ，则 $ n $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）若<i>m</i>，<i>n</i>为有理数，则 $ m-n $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽芜湖 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-20", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575884794798080", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575884794798080", "title": "安徽省芜湖市2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589575900687015936", "questionArticle": "<p>5．关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} 2x+my=15 \\\\ x-2y=0 \\end{cases}  $ 的解为正整数，则所有满足条件的整数 $ m $ 之和是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3B．5C．8D．11</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽芜湖 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-20", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575884794798080", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575884794798080", "title": "安徽省芜湖市2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589575909998370816", "questionArticle": "<p>6．某校举行数学竞赛，需要购买钢笔与笔记本作奖品．已知购买60支钢笔和30本笔记本需要1080元，购买50支钢笔和10本笔记本需要840元．</p><p>（1）购买1支钢笔和1本笔记本各需多少元？</p><p>（2）若需要购买100件奖品，且购买费用不超过1360元，则最多可以买多少支钢笔？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽芜湖 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-20", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575884794798080", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575884794798080", "title": "安徽省芜湖市2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589575899898486784", "questionArticle": "<p>7．《九章算术》是中国传统数学的重要著作，方程术是它的最高成就．其中记载：“今有醇酒一斗，直钱五十；行酒一斗，直钱一十．今将钱三十，得酒二斗．问醇、行酒各得几何？”译文：今有优质酒1斗的价格是50钱，普通酒1斗的价格是10钱，现在买了两种酒2斗，共付30钱．问优质酒、普通酒各买多少斗？ 如果设买优质酒<i>x</i>斗，普通酒<i>y</i>斗，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/15/2/1/0/0/0/589575867526852617/images/img_9.png\" style=\"vertical-align:middle;\" width=\"72\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} x+y=2\\, \\\\ 50x+10y=30 \\end{cases}  $ B． $ \\begin{cases} x+y=2\\, \\\\ 10x+50y=30 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=30\\, \\\\ 50x+10y=2 \\end{cases}  $ D． $ \\begin{cases} x+y=2\\, \\\\ 50x-10y=30 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽芜湖 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-20", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575884794798080", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575884794798080", "title": "安徽省芜湖市2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589575898245931008", "questionArticle": "<p>8．已知二元一次方程组 $ \\begin{cases} 2x+3y=2 \\\\ 3x+2y=7 \\end{cases}  $ 则 $ x-y $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．2C．5D．9</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽芜湖 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-20", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575884794798080", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575884794798080", "title": "安徽省芜湖市2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589575905564991488", "questionArticle": "<p>9．解方程组： $ \\begin{cases} \\dfrac { 1 } { 2 }x+y=-1 \\\\ x-y=7 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽芜湖 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-20", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575884794798080", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575884794798080", "title": "安徽省芜湖市2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589575893510561792", "questionArticle": "<p>10．下列方程是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x-2y=0 $ B． $ 3x-6=x $ C． $ x{^{2}}-1=0 $ D． $ 2x=xy $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽芜湖 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-20", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575884794798080", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575884794798080", "title": "安徽省芜湖市2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 24, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 24, "timestamp": "2025-07-01T02:03:38.690Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}