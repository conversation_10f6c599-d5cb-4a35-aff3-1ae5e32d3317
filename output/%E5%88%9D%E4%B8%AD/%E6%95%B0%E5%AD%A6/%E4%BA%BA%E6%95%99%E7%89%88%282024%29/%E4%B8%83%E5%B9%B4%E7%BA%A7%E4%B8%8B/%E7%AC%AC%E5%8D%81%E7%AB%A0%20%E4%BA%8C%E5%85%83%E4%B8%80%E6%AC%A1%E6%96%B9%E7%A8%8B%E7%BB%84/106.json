{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 105, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "572226671345967104", "questionArticle": "<p>1．小丽在用“加减消元法”解二元一次方程组 $ \\begin{cases} 3x-2y=4① \\\\ 4x+3y=9② \\end{cases}  $ 时，利用 $ ①\\times a+②\\times b $ 消去 $ y $ ，则<i>a</i><i>，</i><i>b</i>的值可能是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ a=3,b=2 $　　　　B． $ a=2,b=3 $　　　　C． $ a=-2,b=3 $　　　　D． $ a=3,b=-2 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄市第四十中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226659476086784", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572226659476086784", "title": "河北省 石家庄市第四十中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "572226393615933440", "questionArticle": "<p>2．【问题背景】对于未知数为<i>x</i>，<i>y</i>的二元一次方程组，如果方程组的解<i>x</i>，<i>y</i>满足 $ x-y=1 $ ，我们就说方程组的解<i>x</i>与<i>y</i>具有“邻好关系”．</p><p>【数学理解】（1）方程组 $ \\begin{cases} 3x+2y=28 \\\\ y=2x-7 \\end{cases}  $ 的解<i>x</i>与<i>y</i>是否具有“邻好关系”？请说明理由；</p><p>【逆向思考】（2）已知关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} 2x+y=5k+1 \\\\ x+2y=4k+2 \\end{cases}  $ 的解<i>x</i>与<i>y</i>具有“邻好关系”，求<i>k</i>的值．</p><p>【深入探究】（3）未知数为<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x-y=5 \\\\ ax+y=7 \\end{cases}  $ ，其中<i>a</i>与<i>x</i>，<i>y</i>都是正整数，该方程组的解<i>x</i>与<i>y</i>是否具有“邻好关系”？如果具有，请求出<i>a</i>的值及方程组的解；如果不具有，请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东中山市第一中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226368257171456", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572226368257171456", "title": "广东省中山市第一中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572226389694259200", "questionArticle": "<p>3．（综合与实践）如图，某综合实践小组在课后利用小球和水做实验，根据图中给出的信息，解答下列问题：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/01/2/1/0/0/0/573154904497233921/images/img_1.png\" style='vertical-align:middle;' width=\"351\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)放入一个小球水面升高<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ c } }{ \\rm{ m } } $ ，放入一个大球水面升高<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ c } }{ \\rm{ m } } $ ；</p><p>(2)如果放入10个球且使水面恰好上升到 $ 52{ \\rm{ c } }{ \\rm{ m } } $ ，应放入大球、小球各多少个？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东中山市第一中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226368257171456", "questionFeatureName": "综合与实践题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572226368257171456", "title": "广东省中山市第一中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572226387454500864", "questionArticle": "<p>4．小明先将图1中的矩形沿虚线剪开分成四个全等的小矩形，再将这四个小矩形拼成如图2的正方形，那么图1中矩形的面积为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．   </p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/01/2/1/0/0/0/573151477885083648/images/img_1.png\" style='vertical-align:middle;' width=\"234\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东中山市第一中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226368257171456", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572226368257171456", "title": "广东省中山市第一中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572226383688015872", "questionArticle": "<p>5．《九章算术》中的算筹图是竖排的，现在改为横排，图中各行从左到右列出的算筹数分别表示未知数<i>x</i>，<i>y</i>的系数与相应的常数项，把图1所示的算筹图用我们现在所熟悉的方程组形式表示出来，就是 $ \\begin{cases} 3x+2y=19 \\\\ {x+4y=23} \\end{cases}  $ ，在图2所示的算筹图中有一个图形被墨水覆盖了，若图2所表示的方程组中<i>x</i>与<i>y</i>的值相等，则被墨水所覆盖的图形为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/01/2/1/0/0/0/573152267227930625/images/img_1.png\" style='vertical-align:middle;' width=\"449\" alt=\"试题资源网 https://stzy.com\"></p><p>A．<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/01/2/1/0/0/0/573152267227930626/images/img_2.png\" style='vertical-align:middle;' width=\"4\" alt=\"试题资源网 https://stzy.com\">　　　　B．<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/01/2/1/0/0/0/573152267227930627/images/img_3.png\" style='vertical-align:middle;' width=\"16\" alt=\"试题资源网 https://stzy.com\">　　　　C．<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/01/2/1/0/0/0/573152267227930628/images/img_4.png\" style='vertical-align:middle;' width=\"28\" alt=\"试题资源网 https://stzy.com\">　　　　D．<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/01/2/1/0/0/0/573152267227930629/images/img_5.png\" style='vertical-align:middle;' width=\"40\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市长寿中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-04-29", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579474623105904640", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "579474623105904640", "title": "重庆市长寿区重庆市长寿中学校2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}, {"id": "572226368257171456", "title": "广东省中山市第一中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "572226385214742528", "questionArticle": "<p>6．方程组 $ \\begin{cases} x+3y=-1 \\\\ 2x-3y=7 \\end{cases}  $ 的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东中山市第一中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226368257171456", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572226368257171456", "title": "广东省中山市第一中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570265609746817024", "questionArticle": "<p>7．科学研究表明：树叶在光合作用后产生的分泌物能够吸附空气中的悬浮颗粒物，具有滞尘净化空气的作用．已知一片银杏树叶一年的平均滞尘量比一片国槐树叶一年的平均滞尘量的2倍少4毫克，两片银杏树叶与三片国槐树叶一年的平均滞尘总量为146毫克．设一片银杏树叶一年的平均滞尘量为 $ x $ 毫克，一片国槐树叶一年的平均滞尘量为 $ y $ 毫克．依据题意，可列方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁盘锦 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570265592705359872", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "570265592705359872", "title": "辽宁省盘锦市2024−2025学年九年级下学期数学第二阶段测试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884961495556096", "questionArticle": "<p>8．近年来教育部要求学校积极开展素质教育，落实“双减”政策，泸县某中学把足球和篮球列为该校的特色项目．学校准备从体育用品商店一次性购买若干个篮球和足球．若购买3个篮球和2个足球共490元，购买2个篮球和3个足球共460元．</p><p>(1)篮球、足球的单价各是多少元？</p><p>(2)根据学校实际需要，需一次性购买篮球和足球共100个，要求购买篮球和足球的总费用不超过9200元，且购买篮球的数量不少于足球数量的一半，请求出最省钱的一种购买方案．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南长郡 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 8, "referenceNum": 3, "createTime": "2025-04-29", "keyPointIds": "16438|16490|16547", "keyPointNames": "和差倍分问题|一元一次不等式组的应用|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552912547323617280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552912547323617280", "title": "2025年湖南省长沙市长郡教育集团联考九年级中考一模数学试题", "paperCategory": 11}, {"id": "571884921242820608", "title": "四川省成都市实验外国语学校2024—2025学年下学期第一次诊断性考试九年级数学试题", "paperCategory": 1}, {"id": "445718728442945536", "title": "2024年湖南省长沙市青竹湖湘一外国语学校中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884781874487296", "questionArticle": "<p>9．已知二元一次方程组 $ \\begin{cases} x+y=1 \\\\ * \\end{cases}  $ 的解是 $ \\begin{cases} x=-2 \\\\ y=a \\end{cases}  $ ，则*表示的方程可能是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x-y=-1 $ B． $ x+2y=-8 $ C． $ 2x-y=-7 $ D． $ 2x+3y=-13 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-04-29", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884753671987200", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884753671987200", "title": "省级重点 福建省福州第十六中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}, {"id": "446276140971368448", "title": "福建省厦门市外国语学校2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571884780125462528", "questionArticle": "<p>10．用大小完全相同的长方形在直角坐标系中摆成如图所示图案，已知点<i>A</i>的坐标为 $ \\left ( { -14,6 } \\right )  $ ，则点<i>B</i>的坐标是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/28/2/1/0/0/0/571884722080489486/images/img_14.png\" style=\"vertical-align:middle;\" width=\"180\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\left ( { 1,-7 } \\right )  $ B． $ \\left ( { -1,7 } \\right )  $ C． $ \\left ( { 2,-10 } \\right )  $ D． $ \\left ( { -2,10 } \\right )  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-04-29", "keyPointIds": "16439|16501", "keyPointNames": "几何问题|坐标与图形性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884753671987200", "questionMethodName": "数形结合思想", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884753671987200", "title": "省级重点 福建省福州第十六中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}, {"id": "446276140971368448", "title": "福建省厦门市外国语学校2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 106, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 106, "timestamp": "2025-07-01T02:13:21.329Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}