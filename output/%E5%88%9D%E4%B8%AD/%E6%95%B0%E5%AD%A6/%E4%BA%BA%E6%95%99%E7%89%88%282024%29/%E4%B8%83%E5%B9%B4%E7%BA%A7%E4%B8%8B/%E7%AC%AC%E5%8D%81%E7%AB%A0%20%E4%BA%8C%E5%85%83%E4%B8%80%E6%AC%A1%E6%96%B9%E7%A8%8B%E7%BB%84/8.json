{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 7, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "592868759460687872", "questionArticle": "<p>1．《九章算术》是我国东汉初年编订的一部数学经典著作．在它的“方程”一章里，一次方程组是由算筹布置而成的．《九章算术》中的算筹图是竖排的，现在我们把它改为横排，如图1、图2，图中各行从左到右列出的算筹数分别表示未知数 $ x、y $ 的系数与相应的常数项，把图1所示的算筹图用我们现在所熟悉的方程组形式表述出来就是 $ \\begin{cases} 3x+2y=19 \\\\ x+4y=23 \\end{cases}  $ ，类似的，图2所示的算筹图我们可以用方程组形式表述为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/25/2/1/0/0/0/592868716527788032/images/img_13.png\" style=\"vertical-align:middle;\" width=\"244\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024安徽阜阳 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592868735515406337", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592868735515406337", "title": "安徽省阜阳实验中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590998766427086848", "questionArticle": "<p>2．若关于<i>x</i>，<i>y</i>的方程组的 $ \\begin{cases} 2x+y=k+2 \\\\ x+5y=2k-1 \\end{cases}  $ 解满足 $ x+2y &gt; -1 $ ，则<i>k</i>的取值范围是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ k &gt; -\\dfrac { 4 } { 3 } $ B． $ k  &lt;  -\\dfrac { 4 } { 3 } $ C． $ k &gt; -\\dfrac { 2 } { 3 } $ D． $ k  &lt;  -\\dfrac { 2 } { 3 } $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16426|16485", "keyPointNames": "二元一次方程组的应用|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998752703324160", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590998752703324160", "title": "湖南省永州市冷水滩区京华中学2024−2025学年七年级下学期第三次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592868674693799936", "questionArticle": "<p>3．某旅游商品经销店欲购进<i>A</i>、<i>B</i>两种纪念品，若用380元购进<i>A</i>种纪念品7件，<i>B</i>种纪念品8件；也可以用380元购进<i>A</i>种纪念品13件，<i>B</i>种纪念品4件．</p><p>（1）求<i>A</i>、<i>B</i>两种纪念品的进价分别为多少？</p><p>（2）若该商店每销售1件<i>A</i>种纪念品可获利5元，每销售1件<i>B</i>种纪念品可获利7元，该商店准备用不超过900元购进<i>A</i>、<i>B</i>两种纪念品40件，且这两种纪念品全部售出后总获利不低于216元，问共有几种方案并求出利润最大值？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024安徽黄山 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592868635514806272", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592868635514806272", "title": "安徽省黄山地区2023−2024学年七年级下学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592868761977270272", "questionArticle": "<p>4．解下列方程组： $ \\begin{cases} 2x+5y=25 \\\\ 4x+3y=15 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024安徽阜阳 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592868735515406337", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592868735515406337", "title": "安徽省阜阳实验中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592868544540356609", "questionArticle": "<p>5．若 $ \\begin{cases} x=2 \\\\ y=3 \\end{cases}  $ 关于<i>x</i>，<i>y</i>的二元一次方程 $ mx-y=3 $ 的一个解，则<i>m</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -\\dfrac { 5 } { 3 } $ B． $ \\dfrac { 5 } { 3 } $ C． $ -3 $ D．3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024北京密云 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592868534297866240", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592868534297866240", "title": "北京市密云区2023−2024学年下学期七年级数学期末 试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592868664304508928", "questionArticle": "<p>6．已知 $ \\begin{cases} x+2y=4k \\\\ 2x+y=2k+1 \\end{cases}  $ 且 $ -1  &lt;  x-y  &lt;  0 $ ，则<i>k</i>的取值范围为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024安徽黄山 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16426|16489", "keyPointNames": "二元一次方程组的应用|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592868635514806272", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592868635514806272", "title": "安徽省黄山地区2023−2024学年七年级下学期期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592869578604064768", "questionArticle": "<p>7．随着科技的快速发展和自动化水平的不断提升，人们日常生活中的手工制作机会逐渐减少，然而近年来，追求个性化体验与情感价值的 $ { \\rm{ D } }{ \\rm{ I } }{ \\rm{ Y } } $ （自主创作）模式却逆势兴起，成为现代人平衡技术便利与人文体验的重要生活方式．根据以下素材，请用方程（组）或不等式（组）完成任务．</p><table style=\"border: solid 1px;border-collapse: collapse; width:415.5pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 49.5pt;\"><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>素材1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 161.25pt;\"><p>端午期间， $ { \\rm{ D } }{ \\rm{ I } }{ \\rm{ Y } } $ 小店推出 $ \\mathrm{ A } $ 、 $ B $ 、 $ C $ 三款包装盒供顾客选择，1个 $ C $ 款包装盒的销售单价是12元，2个 $ \\mathrm{ A } $ 款包装盒的售价比3个 $ B $ 款包装盒的售价多2元，3个 $ \\mathrm{ A } $ 款包装盒和5个 $ B $ 款包装盒的售价之和等于5个 $ C $ 款包装盒的售价：</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 202.5pt;\"><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/25/2/1/0/0/0/592869511625220096/images/img_17.png\" style=\"vertical-align:middle;\" width=\"251\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 49.5pt;\"><p>&nbsp;</p><p>素材2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 161.25pt;\"><p>为了有更好的体验感， $ { \\rm{ D } }{ \\rm{ I } }{ \\rm{ Y } } $ 小店特意准备了制作粽子的原材料，可制作红豆粽、蛋黄粽、肉粽三种口味，制作单价分别是：4元、5元、6元一个；同时推出优惠活动：每制作10个粽子，赠送一个红豆粽；</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 202.5pt;\"><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/25/2/1/0/0/0/592869511625220097/images/img_18.png\" style=\"vertical-align:middle;\" width=\"258\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 49.5pt;\"><p>素材3</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 363.75pt;\"><p>一份礼盒的售价由制作的粽子价格与包装盒的价格组合构成．已知 $ \\mathrm{ A } $ 款包装盒可装 $ 12 $ 个粽子， $ B $ 款包装盒可装 $ 18 $ 个粽子， $ C $ 款包装盒可装 $ 15 $ 个粽子．</p></td></tr><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 413.25pt;\"><p>问题解决 </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 49.5pt;\"><p>任务1</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 363.75pt;\"><p>请问 $ \\mathrm{ A } $ 款包装盒和 $ B $ 款包装盒的销售单价各是多少元？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 49.5pt;\"><p>&nbsp;</p><p>任务2</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 363.75pt;\"><p>小语妈妈计划用不超过 $ 830 $ 元制作 $ \\mathrm{ A } $ 、 $ B $ 两款礼盒各5盒，每盒均装满，其中 $ \\mathrm{ A } $ 款礼盒包含2个红豆粽， $ m $ 个肉粽，其余是蛋黄粽； $ B $ 款礼盒包含4个红豆粽， $ \\left ( { m-1 } \\right )  $ 个肉粽，其余是蛋黄粽，且蛋黄粽的个数不超过 $ \\dfrac { 3 } { 2 }m $ 个，请你通过计算说明小语妈妈的计划能成功吗？如果能成功，有几种搭配方式呢？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592869537717989376", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592869537717989376", "title": "重庆实验外国语学校2024−2025学年七年级下学期数学期末复习试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592869566063095808", "questionArticle": "<p>8．解下列方程组：</p><p>（1） $ \\begin{cases} 3x-y=-7 \\\\ 2y-5x=7 \\end{cases}  $ </p><p>（2） $ \\begin{cases} \\dfrac { 2x-1 } { 3 }-\\dfrac { y+2 } { 2 }=4 \\\\ 2\\left ( { 3y-1 } \\right ) =5x+3 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592869537717989376", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592869537717989376", "title": "重庆实验外国语学校2024−2025学年七年级下学期数学期末复习试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592869559071191040", "questionArticle": "<p>9．已知 $ a{{}_{ 0 } },a{{}_{ 1 } }x,a{{}_{ 2 } }x{^{2}},a{{}_{ 3 } }x{^{3}},\\cdots ,a{{}_{ n } }x{^{n}} $ ，其中 $ n,a{{}_{ 0 } } $ 为非负整数， $ a{{}_{ 1 } },a{{}_{ 2 } },a{{}_{ 3 } },\\cdots ,a{{}_{ n } } $ 均为正整数．规定： $ M{{}_{ 0 } }=a{{}_{ 0 } },M{{}_{ 1 } }=a{{}_{ 1 } }x, $   $ M{{}_{ 2 } }=a{{}_{ 2 } }x{^{2}}+M{{}_{ 0 } }=a{{}_{ 2 } }x{^{2}}+a{{}_{ 0 } },\\cdots ,M{{}_{ n } }=a{{}_{ n } }x{^{n}}+M{{}_{ n-2 } }\\left ( { n\\geqslant  2 } \\right ) , $  整式 $ M{{}_{ n } } $ 的所有系数的和记作 $ F\\left ( { M{{}_{ n } } } \\right )  $ ．如：因为 $ M{{}_{ 0 } }=a{{}_{ 0 } } $ ，所以 $ F\\left ( { M{{}_{ 0 } } } \\right ) =a{{}_{ 0 } } $ ；因为 $ M{{}_{ 1 } }=a{{}_{ 1 } }x $ ，所以 $ F\\left ( { M{{}_{ 1 } } } \\right ) =a{{}_{ 1 } } $ ；因为 $ M{{}_{ 2 } }=a{{}_{ 2 } }x{^{2}}+a{{}_{ 0 } } $ ，所以 $ F\\left ( { M{{}_{ 2 } } } \\right ) =a{{}_{ 2 } }+a{{}_{ 0 } } $ ．以下说法：</p><p>① $ M{{}_{ 4 } }\\mathrm{ = }a{{}_{ 4 } }x{^{4}}+a{{}_{ 2 } }x{^{2}} $ </p><p>②若 $ a{{}_{ 0 } }=1,a{{}_{ 1 } }=2,a{{}_{ 2 } }=3,a{{}_{ 3 } }=4 $ ，则 $ F\\left ( { M{{}_{ 3 } } } \\right ) =10 $ </p><p>③若 $ F\\left ( { M{{}_{ 3 } } } \\right ) =3, $ 则所有满足条件的整式 $ M{{}_{ 3 } } $ 的和为 $ 3x{^{3}}+3x; $ </p><p>④若 $ n+F\\left ( { M{{}_{ n } } } \\right ) =6, $ ，则所有满足条件的整式 $ M{{}_{ n } } $ 有9个．</p><p>其中正确的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0B．1C．2D．3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592869537717989376", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592869537717989376", "title": "重庆实验外国语学校2024−2025学年七年级下学期数学期末复习试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592869555627667456", "questionArticle": "<p>10．小华从家骑车到学校经过一段平路和一段下坡路，在平路、上坡路和下坡路上，他骑车的速度分别为 $ 12{ \\rm{ k } }{ \\rm{ m } }{ \\rm{ / } }{ \\rm{ h } } $ 、 $ 10{ \\rm{ k } }{ \\rm{ m } }{ \\rm{ / } }{ \\rm{ h } } $ 、 $ 16{ \\rm{ k } }{ \\rm{ m } }{ \\rm{ / } }{ \\rm{ h } } $ ．他骑车从家到学校需要30分钟；骑车从学校回家需要40分钟．设小华从家到学校的平路有 $ x{ \\rm{ k } }{ \\rm{ m } } $ ，下坡路有 $ y{ \\rm{ k } }{ \\rm{ m } } $ ，则依题意所列的方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} \\dfrac { x } { 12 }+\\dfrac { y } { 16 }=30 \\\\ \\dfrac { x } { 12 }+\\dfrac { y } { 10 }=40 \\end{cases}  $ B． $ \\begin{cases} \\dfrac { x } { 16 }+\\dfrac { y } { 12 }=30 \\\\ \\dfrac { x } { 10 }+\\dfrac { y } { 12 }=40 \\end{cases}  $ C． $ \\begin{cases} \\dfrac { x } { 12 }+\\dfrac { y } { 10 }=\\dfrac { 30 } { 60 } \\\\ \\dfrac { x } { 12 }+\\dfrac { y } { 16 }=\\dfrac { 40 } { 60 } \\end{cases}  $ D． $ \\begin{cases} \\dfrac { x } { 12 }+\\dfrac { y } { 16 }=\\dfrac { 30 } { 60 } \\\\ \\dfrac { x } { 12 }+\\dfrac { y } { 10 }=\\dfrac { 40 } { 60 } \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592869537717989376", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592869537717989376", "title": "重庆实验外国语学校2024−2025学年七年级下学期数学期末复习试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 8, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 8, "timestamp": "2025-07-01T02:01:43.491Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}