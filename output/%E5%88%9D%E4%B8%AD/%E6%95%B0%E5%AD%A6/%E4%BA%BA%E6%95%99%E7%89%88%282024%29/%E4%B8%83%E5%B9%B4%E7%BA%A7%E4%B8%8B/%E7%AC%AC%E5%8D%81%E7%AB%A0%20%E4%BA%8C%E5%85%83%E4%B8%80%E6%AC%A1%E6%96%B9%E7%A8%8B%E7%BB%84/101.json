{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 100, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "572595278437457920", "questionArticle": "<p>1．阅读理解：</p><p>例：若 $ \\left ( { x-2 } \\right )  $ 是多项式 $ x{^{3}}+3x{^{2}}-8x+k $ 的一个因式，求 $ k $ 的值．</p><p>解：设 $ x{^{3}}+3x{^{2}}-8x+k=A\\left ( { x-2 } \\right )  $ ，当 $ x-2=0 $ 时，有 $ x{^{3}}+3x{^{2}}-8x+k=0 $ ，将 $ x=2 $ 代入 $ x{^{3}}+3x{^{2}}-8x+k=0 $ ，得 $ 8+12-16+k=0 $ ，解得 $ k=-4 $ ．</p><p>仿照上例解法，解答下列的问题：</p><p>(1)若 $ \\left ( { x+1 } \\right )  $ 是多项式 $ x{^{2}}-4x+k $ 的一个因式，求 $ k $ 的值；</p><p>(2)若 $ \\dfrac { a{^{2}}+b{^{2}}+1+6a } { a+3 } $ 可化为整式，求化简后的整式；</p><p>(3)若 $ \\left ( { x-1 } \\right )  $ 和 $ \\left ( { x-2 } \\right )  $ 是多项式 $ x{^{4}}+mx{^{3}}+nx-16 $ 的两个因式，求 $ \\dfrac { 1 } { m }-\\dfrac { 1 } { n } $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西高新一中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16353|16424", "keyPointNames": "因式分解的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572595246820794368", "questionFeatureName": "阅读材料题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572595246820794368", "title": "陕西省西安市西安高新第一中学2024~2025学年度第二学期期中考试试题八年级数学", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572595522709528576", "questionArticle": "<p>2．“体育承载着国家强盛、民族振兴的梦想，体育强则中国强，国运兴则体育兴．”为引导学生在体育锻炼中享受乐趣、增强体质，学校开展大课间活动，七年级五班拟组织学生参加跳绳活动，需购买<i>A</i>，<i>B</i>两种跳绳若干，已知购买3根<i>A</i>种跳绳和1根<i>B</i>种跳绳共需105元；购买5根<i>A</i>种跳绳和3根<i>B</i>种跳绳共需215元，</p><p>(1)求<i>A</i>，<i>B</i>两种跳绳的单价各是多少元?</p><p>(2)如果班级计划购买<i>A</i>，<i>B</i>两型跳绳共48根，总费用不超过1388元，那么最多可以购买<i>B</i>种跳绳多少根?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成外（CFLS） · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572595484449087488", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "572595484449087488", "title": "四川省成都外国语学校20242025学年下学期期中考试九年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572226065503920128", "questionArticle": "<p>3．北京时间 $ 2024 $ 年 $ 6 $ 月 $ 25 $ 日，嫦娥六号返回器准确着陆，标志着我国探月工程嫦娥六号任务取得圆满成功．某超市为了满足广大航天爱好者的需求，计划购进 $ \\mathrm{ A } $ ， $ B $ 两种航天飞船模型进行销售，据了解， $ 2 $ 件 $ \\mathrm{ A } $ 种航天飞船模型和 $ 1 $ 件 $ B $ 种航天飞船模型的进价共计 $ 200 $ 元； $ 3 $ 件 $ \\mathrm{ A } $ 种航天飞船模型和 $ 2 $ 件 $ B $ 种航天飞船模型的进价共计 $ 340 $ 元．</p><p>(1)求 $ \\mathrm{ A } $ ， $ B $ 两种航天飞船模型每件的进价分别为多少元？</p><p>(2)若该超市计划用 $ 520 $ 元购进以上两种航天飞船模型（两种航天飞船模型均有购买），请你求出所有购买方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东广州 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16434|16438", "keyPointNames": "方案问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226039591510016", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "572226039591510016", "title": "广东省广州市中山大学附属中学2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572226061334781952", "questionArticle": "<p>4．解下列方程或方程组</p><p>(1) $ {\\left( { x+1 } \\right) ^ {2}}-49=0 $ ；</p><p>(2) $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} 2x-y=3 \\\\ 3x+2y=8 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ ；</p><p>(3) $ \\begin{cases} \\dfrac { x } { 3 }-\\dfrac { y+1 } { 2 }=1 \\\\ 4x-\\left ( { 2y-5 } \\right ) =8 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东广州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16287|16424", "keyPointNames": "平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226039591510016", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "572226039591510016", "title": "广东省广州市中山大学附属中学2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572226058860142592", "questionArticle": "<p>5．若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} ax+by=c \\\\ mx+ny=d \\end{cases}  $ 的解为 $ \\begin{cases} x=1 \\\\ y=3 \\end{cases}  $ ，则方程组 $ \\begin{cases} a\\left ( { x-2 } \\right ) +3by=c \\\\ m\\left ( { x-2 } \\right ) +3ny=d \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东广州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226039591510016", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "572226039591510016", "title": "广东省广州市中山大学附属中学2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572594532962836480", "questionArticle": "<p>6．为了参加学校举办的“校长杯”足球联赛，某中学七（1）班学生去商场购买了 $ \\mathrm{ A } $ 品牌足球1个、 $ B $ 品牌足球2个，共花费210元，七（2）班学生购买了 $ \\mathrm{ A } $ 品牌足球3个、 $ B $ 品牌足球1个，共花费230元．</p><p>（1）求购买一个 $ \\mathrm{ A } $ 品牌、一个 $ B $ 品牌的足球各需多少元？</p><p>（2）为响应习总书记“足球进校园”的号召，学校使用专项经费1500元全部购买 $ \\mathrm{ A } $ 、 $ B $ 两种品牌的足球供学生使用，那么学校有多少种购买足球的方案？请你帮助学校分别设计出来．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏扬州 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16434|16438", "keyPointNames": "方案问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572594502625435648", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "572594502625435648", "title": "江苏省扬州市邗江区实验学校2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572594526767849472", "questionArticle": "<p>7．若关于<i>x</i>、<i>y</i>的二元一次方程组 $ \\begin{cases} ax+y=b \\\\ cx-y=d \\end{cases}  $ 的解是 $ \\begin{cases} x=3 \\\\ y=-2 \\end{cases}  $ ，则关于<i>x</i><i>、</i><i>y</i>的方程组 $ \\begin{cases} ax+2y=2a+b \\\\ cx-2y=2c+d \\end{cases}  $ 的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏扬州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572594502625435648", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "572594502625435648", "title": "江苏省扬州市邗江区实验学校2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572594522330275840", "questionArticle": "<p>8．已知 $ \\left  | { x+3y+3 } \\right  | +{\\left( { x-y-1 } \\right) ^ {2}}=0 $ ，则 $ {\\left( { x+y } \\right) ^ {2025}}= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏扬州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16257|16424|30400", "keyPointNames": "绝对值非负性的应用|加减消元法解二元一次方程组|有理数的乘方", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572594502625435648", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "572594502625435648", "title": "江苏省扬州市邗江区实验学校2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572594516386947072", "questionArticle": "<p>9．《九章算术》中记载了一个问题，大意是：有几个人一起去买一件物品，每人出8元，多3元；每人出7元，少4元．若设共有 $ x $ 人，该物品价值 $ y $ 元，则根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 8x-3=y \\\\ 7x+4=y \\end{cases}  $ B． $ \\begin{cases} 8x+3=y \\\\ 7x+4=y \\end{cases}  $ C． $ \\begin{cases} 8x+3=y \\\\ 7x-4=y \\end{cases}  $ D． $ \\begin{cases} 8x-3=y \\\\ 7x-4=y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏扬州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572594502625435648", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "572594502625435648", "title": "江苏省扬州市邗江区实验学校2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "572594513685815296", "questionArticle": "<p>10．已知方程组 $ \\begin{cases} x+2y=k \\\\ 2x+y=1 \\end{cases}  $ 的解满足<i>x</i>与<i>y</i>互为相反数，则<i>k</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B． $ -2 $ C．2D． $ -1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏扬州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-01", "keyPointIds": "16252|16424", "keyPointNames": "相反数的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572594502625435648", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "572594502625435648", "title": "江苏省扬州市邗江区实验学校2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 101, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 101, "timestamp": "2025-07-01T02:12:44.828Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}