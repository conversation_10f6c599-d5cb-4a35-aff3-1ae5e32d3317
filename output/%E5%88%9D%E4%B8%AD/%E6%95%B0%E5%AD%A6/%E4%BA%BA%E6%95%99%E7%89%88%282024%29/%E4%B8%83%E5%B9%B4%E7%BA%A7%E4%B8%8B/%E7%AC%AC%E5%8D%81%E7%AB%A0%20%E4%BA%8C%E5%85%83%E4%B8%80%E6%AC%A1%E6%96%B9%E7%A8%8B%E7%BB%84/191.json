{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 190, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "545400969695109120", "questionArticle": "<p>1．《九章算术》是中国古代一部重要的数学著作，其卷八方程第十题题目大意是，甲、乙两人各带了若干钱．如果甲得到乙所有钱的一半，那么甲共有钱50．如果乙得到甲所有钱的 $ \\dfrac { 2 } { 3 } $ ，那么乙也共有钱50．甲、乙两人各带了多少钱？设甲、乙两人持钱的数量分别为<i>x</i>和<i>y</i>，则可列方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+2y=50 \\\\ x+\\dfrac { 2 } { 3 }y=50 \\end{cases}  $　　　　B． $ \\begin{cases} x+2y=50, \\\\ \\dfrac { 2 } { 3 }x+y=50 \\end{cases}  $　　　　C． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=50 \\\\ x+\\dfrac { 2 } { 3 }y=50 \\end{cases}  $　　　　D． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=50 \\\\ \\dfrac { 2 } { 3 }x+y=50 \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|520000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025贵州贵阳 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-02-19", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545400965404336128", "questionFeatureName": "数学文化题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "545400965404336128", "title": "贵州省贵阳市2024−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}, {"id": "553050999373471744", "title": "天津市河西区南开翔宇学校2024−2025学年九年级数学开学考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "545400893925007360", "questionArticle": "<p>2．工作人员从仓库领取如图1中的长方形和正方形纸板作侧面和底面，做成如图2的竖式和横式的两种无盖纸盒若干个，恰好使领取的纸板用完．</p><p>(1)下表是工作人员两次领取纸板数的记录：</p><table style=\"border: solid 1px;border-collapse: collapse; width:225pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">次数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">正方形纸板（张）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">长方形纸板（张）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">第一次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">560</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">940</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">第二次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">420</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">1002</p></td></tr></table><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/14/2/1/0/0/0/545400861909884928/images/img_11.png\" style=\"vertical-align:middle;\" width=\"246\" alt=\"试题资源网 https://stzy.com\"></p><p>①仓库管理员在核查时，发现一次记录有误，请判断第几次的记录有误，并说明理由；</p><p>②记录正确的那一次，利用领取的纸板做了竖式和横式纸盒各多少个？</p><p>(2)若工作人员某次领取的正方形纸板数与长方形纸板数之比为1:3，请你求出利用这些纸板做出的竖式纸盒与横式纸盒个数的比值；</p><p>(3)拓展延伸：现在仓库里有 $ m $ 张正方形纸板和 $ n $ 张长方形纸板，如果做两种纸盒若干个，恰好使库存的纸板用完，则 $ m+n $ 的值可能是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2013&nbsp;&nbsp;&nbsp;&nbsp;B．2014&nbsp;&nbsp;&nbsp;&nbsp;C．2015&nbsp;&nbsp;&nbsp;&nbsp;D．2016</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广西崇左 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-19", "keyPointIds": "16432|16586|16810", "keyPointNames": "配套问题|认识立体图形|比例的性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545400885213437952", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "545400885213437952", "title": "广西壮族自治区崇左市2024−2025学年七年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "545400892868042752", "questionArticle": "<p>3．解方程组 $ \\begin{cases} 3x+4y=2 \\\\ x-4y=6 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西崇左 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545400885213437952", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "545400885213437952", "title": "广西壮族自治区崇左市2024−2025学年七年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "547222071413088256", "questionArticle": "<p>4．玩具批发市场<i>A</i>,<i>B</i>玩具的批发价分别为每件30元和50元，张阿姨花1200元购进<i>A</i>,<i>B</i>两种玩具若干件，并分别以每件35元与60元价格出售．设购入<i>A</i>玩具为<i>x</i>件，<i>B</i>玩具为<i>y</i>件．</p><p>（1）若张阿姨将玩具全部出售赚了220元，则张阿姨购进<i>A</i>,<i>B</i>型玩具各多少件？</p><p>（2）若要求购进<i>A</i>玩具的数量不得少于<i>B</i>玩具的数量，问如何购进玩具<i>A</i>,<i>B</i>的数量并全部出售才能获得最大利润，此时最大利润为多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西工大附中 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-19", "keyPointIds": "16437|16565", "keyPointNames": "销售利润问题|销售问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "547222063376801792", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "547222063376801792", "title": "2025年陕西省西安市工业大学附属中学中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "547221664762732544", "questionArticle": "<p>5．小明是某蛋糕店的会员，他有一张会员卡，在该店购买的商品均按定价打八五折．周末他去蛋糕店，发现店内正在举办特惠活动：任选两件商品，第二件打七折，如果两件商品不同价，则按照低价商品的价格打折，并且特惠活动不能使用会员卡．小明打算在该店购买两个面包，他计算后发现，使用会员卡与参加特惠活动两者的花费相差0.9元，则<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>花费较少（直接填写序号：①使用会员卡；②参加特惠活动）；两个面包的定价相差<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000|350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建福建师范大学附属小学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-02-19", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568161135431557120", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "568161135431557120", "title": "福建师范大学附属中学2024−2025学年九年级下学期4月期中考试数学试卷", "paperCategory": 1}, {"id": "547221657263316992", "title": "2024年北京市海淀区首都师范大学附属中学中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "546486835934961664", "questionArticle": "<p>6．某小区为了改善绿化环境，计划购买<i>A</i>、<i>B</i>两种树苗共100棵，其中<i>A</i>树苗每棵40元，<i>B</i>树苗每棵35元．经测算购买两种树苗一共需要3800元．</p><p>(1)计划购买<i>A</i>、<i>B</i>两种树苗各多少棵？</p><p>(2)在实际购买中，小区与商家协商：两种树苗的售价均下降<i>a</i>元（ $ a  &lt;  10 $ ），且每降低1元，小区就多购买<i>A</i>树苗2棵，<i>B</i>树苗3棵．小区实际购买这两种树苗的费用比原计划费用多了300元，求该小区实际购买两种树苗的售价下降额<i>a</i>（元）的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|510000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东东莞 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 3, "createTime": "2025-02-19", "keyPointIds": "16437|16463", "keyPointNames": "销售利润问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "546486828397797376", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "546486828397797376", "title": "广东东莞市部分学校2024−2025学年九年级上学期期末自查数学试题", "paperCategory": 1}, {"id": "534113778692038656", "title": "四川省成都市树德中学2024−2025学年九年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "346778605798596608", "title": "重庆市渝中区巴蜀中学2022-2023学年九年级上学期入学数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "546878929103003648", "questionArticle": "<p>7．（1）计算： $ \\sqrt[3] { -8 }-{\\left( { { \\rm{ π } }-2024 } \\right) ^ {0}}+\\left  | { 2-\\sqrt { 2 } } \\right  | +{\\left( { \\dfrac { 1 } { 3 } } \\right) ^ {-1}} $ ；</p><p>（2）解方程组： $ \\begin{cases} \\dfrac { x+1 } { 5 }-\\dfrac { y-1 } { 2 }=1 \\\\ 3x+y=-2 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东深圳市深圳大学附属中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-18", "keyPointIds": "16299|16323|16372|16423", "keyPointNames": "实数的运算|零指数幂|负整数指数幂|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "546878917648359424", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "546878917648359424", "title": "广东省深圳市深圳大学附属中学2024—2025学年八年级上学期期末考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "545400225675911168", "questionArticle": "<p>8．已知长方体的长、宽、高分别为正整数<i>a</i>，<i>b</i>，<i>c</i>，且满足 $ ab-2bc=2 $ ， $ a+c=2b $ ，则长方体的表面积是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．20　　　　B．22　　　　C．24　　　　D．26</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建泉州 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-18", "keyPointIds": "16353|16433", "keyPointNames": "因式分解的应用|数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545400220454002688", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "545400220454002688", "title": "福建省泉州市2024−2025学年八年级上学期期末考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "545400811783757824", "questionArticle": "<p>9．某电器超市销售每台进价为200元，170元的<i>A</i>、<i>B</i>两种型号的电风扇．如表所示是近2周的销售情况：（进价、售价均保持不变，利润 $ = $ 销售收入 $ \\mathbf{ - } $ 进货成本）</p><table style=\"border: solid 1px;border-collapse: collapse; width:210.8pt;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">销售时段</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">销售数量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">销售收入</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><i>A</i>种型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><i>B</i>种型号</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">第一周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">1750元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">第二周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">10</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">3000元</p></td></tr></table><p>(1)求<i>A</i>、<i>B</i>两种型号电风扇的销售单价；</p><p>(2)超市销售完<i>A</i>、<i>B</i>两种型号的电风扇共25台，能否实现利润恰好为1200元的目标？请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西北海 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-18", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545400803885883392", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "545400803885883392", "title": "广西壮族自治区北海市2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "545400811179778048", "questionArticle": "<p>10．（1）计算： $ {\\left( { -1 } \\right) ^ {3}}\\times 2+{\\left( { -2 } \\right) ^ {4}}\\div 4-\\left  | { -2 } \\right  |  $ ；</p><p>（2）解方程组： $ \\begin{cases} \\dfrac { x-1 } { 2 }+\\dfrac { y+1 } { 3 }=1① \\\\ x+y=4② \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西北海 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-18", "keyPointIds": "16278|16423", "keyPointNames": "有理数的混合运算|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545400803885883392", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "545400803885883392", "title": "广西壮族自治区北海市2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 191, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 191, "timestamp": "2025-07-01T02:23:25.487Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}