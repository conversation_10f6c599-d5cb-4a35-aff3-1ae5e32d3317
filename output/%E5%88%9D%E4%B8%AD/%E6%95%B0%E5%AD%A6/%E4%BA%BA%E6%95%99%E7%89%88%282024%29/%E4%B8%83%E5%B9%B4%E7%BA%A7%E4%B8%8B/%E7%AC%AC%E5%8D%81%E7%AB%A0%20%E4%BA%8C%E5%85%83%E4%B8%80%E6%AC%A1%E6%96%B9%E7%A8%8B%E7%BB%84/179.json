{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 178, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "552660262391685120", "questionArticle": "<p>1．随着哈尔滨市全力打造旅游城市政策的实施，哈尔滨这座历史悠久的北方名城，吸引了国内外多方友人奔赴而来，极大促进了哈市经济的发展，中央大街某商家抓住了这一商机，该商家决定购进甲､乙两种纪念品进行销售，若购进甲种纪念品1件和乙种纪念品2件共需要 $ 180 $ 元，若购进甲种纪念品2件和乙种纪念品3件共需要 $ 310 $ 元．</p><p>(1)求购进甲､乙两种纪念品每件各需要多少元？</p><p>(2)该商场决定购进甲､乙两种纪念品共 $ 100 $ 件，若每件甲种纪念品的售价为 $ 160 $ 元，每件乙种纪念品的售价为 $ 110 $ 元，销售完这 $ 100 $ 件纪念品所获得的利润不低于 $ 7200 $ 元，则该商场最少购进甲种纪念品多少件？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南雅礼 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-03-12", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570063103481651200", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "570063103481651200", "title": "2025年湖南省长沙市雅礼集团初中学业质量测卷（二）九年级数学试题", "paperCategory": 1}, {"id": "552660234830913536", "title": "陕西省西安市陕西师范大学附属中学2024−2025学年八年级下学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552660256343498752", "questionArticle": "<p>2．解方程组： $ \\begin{cases} \\dfrac { 3x-4 } { 4 }=\\dfrac { y } { 8 } \\\\ 2(x-y)=8-3y \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西陕西师范大学附属中学 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-03-12", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552660234830913536", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "552660234830913536", "title": "陕西省西安市陕西师范大学附属中学2024−2025学年八年级下学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552660666579984384", "questionArticle": "<p>3．（1）解方程组 $ \\begin{cases} 2x+y=8, \\\\ x-y=1. \\end{cases}  $ &nbsp;&nbsp;&nbsp;&nbsp;</p><p>（2）计算： $ \\dfrac { a+3b } { a+b }+\\dfrac { a-b } { a+b } $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-11", "keyPointIds": "16369|16424", "keyPointNames": "分式的加减|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552660645000290304", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552660645000290304", "title": "浙江省杭州市西湖区紫金港中学2024−2025学年九年级下学期开学考试题数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552660662754779136", "questionArticle": "<p>4．已知 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ ，是二元一次方程组 $ \\begin{cases} ax+by=7, \\\\ ax-by=1 \\end{cases}  $ 的解，则 $ 6a-b $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-11", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552660645000290304", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552660645000290304", "title": "浙江省杭州市西湖区紫金港中学2024−2025学年九年级下学期开学考试题数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553370648728346624", "questionArticle": "<p>5．甲乙两个车间共有150人，若将甲车间的16名工人调到乙车间则两车间人数相等，求甲车间有多少人？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西安铁一中学 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-11", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553370627429670912", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "553370627429670912", "title": "2025年陕西省西安市铁一中学九年级下学期中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553371684004208640", "questionArticle": "<p>6．某快递公司的快递件分为甲类件和乙类件，快递员送甲类件每件收入1元，送乙类件每件收入2元．累计工作1小时，只送甲类件，最多可送30件，只送乙类件，最多可送10件；累计工作2小时，只送甲类件，最多可送55件，只送乙类件，最多可送20件；…，经整理形成统计表如表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 148.5pt;\"><p>累计工作时长最多件数（时）</p><p>种类（件）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.4pt;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>6</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>7</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>8</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 148.5pt;\"><p>甲类件</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>30</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>55</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>80</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>100</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.4pt;\"><p>115</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>125</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>135</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>145</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 148.5pt;\"><p>乙类件</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>10</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p>30</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.4pt;\"><p>50</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>60</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>70</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>80</p></td></tr></table><p>（1）如果快递员一天工作8小时，且只送某一类件，那么他一天的最大收入为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元；</p><p>（2）如果快递员一天累计送<i>x</i>小时甲类件，<i>y</i>小时乙类件，且<i>x</i>+<i>y</i>＝8，<i>x</i>，<i>y</i>均为正整数，那么他一天的最大收入为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京北京35中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-11", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553371658842578944", "questionMethodName": "分类讨论思想", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "553371658842578944", "title": "北京市第三十五中学2024−2025学年九年级下学期2月月考数学试题", "paperCategory": 1}, {"id": "341168528836304896", "title": "北京市东城区北京市第二中学2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553369518329864192", "questionArticle": "<p>7．（1）化简： $ {\\left( { x-1 } \\right) ^ {2}}-x\\left ( { x-2 } \\right )  $ </p><p>（2）解方程组： $ \\begin{cases} 2x+3y=17 \\\\ \\dfrac { x+y } { 2 }=y-2 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南郑州 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-03-11", "keyPointIds": "16333|16424", "keyPointNames": "整式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553369495475101696", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "553369495475101696", "title": "河南省郑州市高新2024−2025学年九年级下学期第一次质量检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552660358105702400", "questionArticle": "<p>8．某快递公司为了提高工作效率，计划购买 $ A，B $ 两种型号的机器人来搬运货物，已知每台 $ A $ 型机器人比每台 $ B $ 型机器人每天多搬运25吨，并且3台 $ A $ 型机器人和2台 $ B $ 型机器人每天共搬运货物450吨．</p><p>(1)求每台 $ A $ 型机器人和每台 $ B $ 型机器人每天分别搬运货物多少吨？</p><p>(2)每台 $ A $ 型机器人售价3万元，每台 $ B $ 型机器人售价2.5万元，该公司采购 $ A，B $ 两种型号的机器人各若干台，费用恰好是40万元，求该公司共有几种采购方案？ $ A，B $ 两种机器人分别采购了多少台？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西光中学 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-03-11", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552660325679538176", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "552660325679538176", "title": "陕西省西安市新城区西光中学教育集团2024−2025学年八年级下学期开学收心考数学试题", "paperCategory": 1}, {"id": "421085578748272640", "title": "陕西省西安市碑林区铁一中学2023-2024学年八年级下学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552660353160617984", "questionArticle": "<p>9．解下列方程组</p><p>(1) $ \\begin{cases} y-2x=0 \\\\ 3x+y=15 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x+y } { 3 }-\\dfrac { x-y } { 2 }=1 \\\\ 2x+3y=14 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西光中学 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 14, "referenceNum": 3, "createTime": "2025-03-11", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552660325679538176", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "552660325679538176", "title": "陕西省西安市新城区西光中学教育集团2024−2025学年八年级下学期开学收心考数学试题", "paperCategory": 1}, {"id": "421085578748272640", "title": "陕西省西安市碑林区铁一中学2023-2024学年八年级下学期开学考试数学试题", "paperCategory": 1}, {"id": "380468539000397824", "title": "广东省深圳实验学校中学部2023-2024学年八年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553374174728724480", "questionArticle": "<p>10．（1）计算： $ 2{ \\rm{ s } }{ \\rm{ i } }{ \\rm{ n } }30{}\\degree -\\sqrt { 4 }+{\\left( { \\dfrac { 1 } { 2 } } \\right) ^ {-1}} $ ；</p><p>（2）解方程组： $ \\begin{cases} 2x+3y=-4 \\\\ x-y=3 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江温州 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-11", "keyPointIds": "16299|16372|16424|16834", "keyPointNames": "实数的运算|负整数指数幂|加减消元法解二元一次方程组|特殊角的三角函数值", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553374152440193024", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "553374152440193024", "title": "浙江省温州市瑞安市安阳实验中学2024−2025学年九年级下学期数学开学考试", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 179, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 179, "timestamp": "2025-07-01T02:22:02.714Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}