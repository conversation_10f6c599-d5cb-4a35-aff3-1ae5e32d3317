{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 94, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "574754473500778496", "questionArticle": "<p>1．解方程组：</p><p>(1) $ \\begin{cases} y=3x-60 \\\\ 3y-x=20 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} x+2y=11 \\\\ 3x-2y=5 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市育才中学校 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574754447810666496", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "574754447810666496", "title": "重庆市育才中学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574754469893677056", "questionArticle": "<p>2．关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} mx+3y=9 \\\\ 4x-3y=0 \\end{cases}  $ 有正整数解，且关于<i>x</i>的不等式组 $ \\begin{cases} 2x+m\\geqslant  5 \\\\ \\dfrac { 2x+5 } { 3 }  <  7-x \\end{cases}  $ 有解，则满足条件的整数<i>m</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市育才中学校 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574754447810666496", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "574754447810666496", "title": "重庆市育才中学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574754464705323008", "questionArticle": "<p>3．春暖花开时节，小育一家人去广阳岛露营，小育准备了一些草莓，如果每人分3个，则多出5个；如果每人分4个，则有一人少1个．设这一行人共有<i>x</i>人，草莓一共有<i>y</i>个，则下列方程组中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3x-5=y \\\\ 4x-1=y \\end{cases}  $　　　　B． $ \\begin{cases} 3x-5=y \\\\ 4x+1=y \\end{cases}  $　　　　C． $ \\begin{cases} 3x+5=y \\\\ 4x+1=y \\end{cases}  $　　　　D． $ \\begin{cases} 3x+5=y \\\\ 4x-1=y \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市育才中学校 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574754447810666496", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "574754447810666496", "title": "重庆市育才中学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574351639336755200", "questionArticle": "<p>4．2025年亚洲冬季运动会于2月7日至2月14日在哈尔滨举行，某经销店调查发现：吉祥物“滨滨”和“妮妮”深受青少年喜爱．已知购进3个“滨滨”比购进2个“妮妮”多用80元；购进1个“滨滨”和2个“妮妮”共用160元．该商店决定购进“滨滨”和“妮妮”各100个，其总费用为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．11000元B．10200元C．10000元D．9900元</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东泰安 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574351615676686336", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574351615676686336", "title": "2025年山东省泰安市初中学业水平考试数学模拟试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574754459726684160", "questionArticle": "<p>5．下列是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ xy-1=\\dfrac { 1 } { 3 } $　　　　B． $ x-3=4y $　　　　C． $ x+3=-2 $　　　　D． $ \\dfrac { 1 } { x }+y=2 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025重庆重庆市育才中学校 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574754447810666496", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "574754447810666496", "title": "重庆市育才中学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574352014429167616", "questionArticle": "<p>6．已知关于<i>x</i>、<i>y</i>的二元一次方程<i>ax</i>＋<i>b</i>＝<i>y</i>，下表列出了当<i>x</i>分别取值时对应的<i>y</i>值．则关于<i>x</i>的不等式<i>ax</i>＋<i>b</i>＜0的解集为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 16.7pt;\"><p style=\"text-align:center;\"><i>x</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p style=\"text-align:center;\">…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">﹣2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">﹣1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p style=\"text-align:center;\">0</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p style=\"text-align:center;\">…</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 16.7pt;\"><p style=\"text-align:center;\"><i>y</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p style=\"text-align:center;\">…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p style=\"text-align:center;\">0</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">﹣1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p style=\"text-align:center;\">﹣2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p style=\"text-align:center;\">…</p></td></tr></table><p>A．<i>x</i>＜1B．<i>x</i>＞1C．<i>x</i>＜0D．<i>x</i>＞0</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽合肥 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574351998234959872", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574351998234959872", "title": "安徽省合肥市庐阳区庐阳中学2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574352016215941120", "questionArticle": "<p>7．关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} x-y=3m-2 \\\\ x+3y=-4 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ 的解满足 $ x+y &gt; 0 $ ，则<i>m</i>的取值范围（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ m &gt; 2 $ B． $ m  &lt;  2 $ C． $ m &gt; 6 $ D． $ m  &lt;  6 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽合肥 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574351998234959872", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574351998234959872", "title": "安徽省合肥市庐阳区庐阳中学2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574352267479916544", "questionArticle": "<p>8．【问题情景】</p><p>南宁的种植大户李大叔，在武鸣区通过土地流转承包了320亩农田种植沃柑．到了沃柑成熟的季节，看着满园金灿灿的果实，李大叔满心欢喜，可在租用沃柑采摘设备的问题上犯了难，请你帮李大叔设计租赁方案．</p><p>【调研发现】</p><p>市场上有大型和小型两种沃柑采摘设备可供租赁．一台大型采摘设备每小时采摘沃柑的数量是一台小型采摘设备每小时采摘沃柑的数量的2倍，2台大型采摘设备和3台小型采摘设备每小时共采摘沃柑28亩．</p><p>【解决问题】</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/08/2/1/0/0/0/575666770716962817/images/img_1.png\" style='vertical-align:middle;' width=\"123\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)设一台大型采摘设备每小时采摘沃柑<i>x</i>亩，一台小型采摘设备每小时采摘沃柑<i>y</i>亩．</p><p>请填空：2台大型采摘设备每小时采摘沃柑<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>亩；3台小型采摘设备每小时采摘沃柑<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>亩．</p><p>(2)大、小两种采摘设备每小时分别可以采摘多少亩沃柑？</p><p>(3)由于要保证新鲜成熟的沃柑能够尽快送到市场销售，李大叔要求一天把沃柑正好全部采摘完，两种采摘设备都要租用，并且租来的设备都工作满10小时，现计划租用大型采摘设备<i>m</i>台，小型采摘设备<i>n</i>台，请你帮李大叔设计一下有哪几种租赁方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广西广西壮族自治区南宁市第三中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16420|16441", "keyPointNames": "二元一次方程的解|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574352242788048896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "574352242788048896", "title": "广西南宁市第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574352525085679616", "questionArticle": "<p>9．解下列方程或方程组：</p><p>(1) $ x{^{2}}-4x=-3 $ </p><p>(2) $ \\begin{cases} 2x-3y=4① \\\\ 5x+3y=10② \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西梧州 · 一模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16424|16456", "keyPointNames": "加减消元法解二元一次方程组|因式分解法解一元二次方程", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574352501316558848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574352501316558848", "title": "广西壮族自治区梧州市2025年初中学业水平考试第一次模拟测试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574352263788929024", "questionArticle": "<p>10．（1）计算： $ {\\left( { -1 } \\right) ^ {2025}}+\\left  | { 1-\\sqrt { 5 } } \\right  | +\\sqrt[3] { 8 } $ ；  </p><p>（2）解方程组： $ \\begin{cases} x+y=4 \\\\ x-2y=1 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西广西壮族自治区南宁市第三中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16256|16290|16299|16424", "keyPointNames": "化简绝对值|立方根|实数的运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574352242788048896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "574352242788048896", "title": "广西南宁市第三中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 95, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 95, "timestamp": "2025-07-01T02:12:04.003Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}