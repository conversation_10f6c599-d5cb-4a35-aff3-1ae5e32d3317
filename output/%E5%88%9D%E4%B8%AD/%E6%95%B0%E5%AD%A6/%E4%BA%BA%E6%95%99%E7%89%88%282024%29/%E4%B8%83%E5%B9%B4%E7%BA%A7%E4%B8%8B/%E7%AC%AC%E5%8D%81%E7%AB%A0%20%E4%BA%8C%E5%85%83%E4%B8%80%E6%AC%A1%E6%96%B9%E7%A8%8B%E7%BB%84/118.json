{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 117, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "568252763152359424", "questionArticle": "<p>1．已知关于<i>x</i>,<i>y</i>的二元一次方程组 $ \\begin{cases}2x-5y=3n+7,\\\\ x-3y=4\\end{cases} $ 的解相等,则<i>n</i>的值是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3B． $ {\\rm -} \\dfrac{1}{3} $ C．1D． $ \\dfrac{1}{3} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时2 代入法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568252762359635968", "questionArticle": "<p>2．判断 $ \\begin{cases}x=3,\\\\ y=-5\\end{cases} $  是不是二元一次方程组 $ \\begin{cases}4x+2y=2,\\\\ x+y=-1\\end{cases} $  的解.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时2 代入法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252883247865856", "questionArticle": "<p>3．已知关于<i>x</i>,<i>y</i>的二元一次方程组 $ \\begin{cases}2x+3y=m,\\\\ 3x+2y=-1\\end{cases} $ 的解满足<i>x</i>+<i>y</i>=−5,则<i>m</i>的值是<u>　　　　</u><i>.&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252885235965952", "questionArticle": "<p>4．阅读理解:解方程组 $ \\begin{cases}\\dfrac{3}{x}+\\dfrac{2}{y}=7,\\\\ \\dfrac{2}{x}-\\dfrac{1}{y}=14\\end{cases} $ 时,如果设 $ \\dfrac{1}{x} {\\rm =\\mathit{m}} $ , $ \\dfrac{1}{y} {\\rm =\\mathit{n}} $ ,则原方程组可变形为关于<i>m</i>,<i>n</i>的方程组 $ \\begin{cases}3m+2n=7,\\\\ 2m-n=14,\\end{cases} $ 解这个方程组得到它的解为 $ \\begin{cases}m=5,\\\\ n=-4.\\end{cases} $ 由 $ \\dfrac{1}{x} {\\rm =5} $ , $ \\dfrac{1}{y} {\\rm =-4} $ ,求得原方程组的解为 $ \\begin{cases}x=\\dfrac{1}{5},\\\\ y=-\\dfrac{1}{4}.\\end{cases} $ 利用上述方法解方程组: $ \\begin{cases}\\dfrac{5}{x}+\\dfrac{2}{y}=11,\\\\ \\dfrac{3}{x}-\\dfrac{2}{y}=13.\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 9, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "questionFeatureName": "阅读材料题", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252761768239104", "questionArticle": "<p>5．用代入消元法解下列方程组:</p><p>(1) $ \\begin{cases}x=3-y,①\\\\ 2x-3y=1;②\\end{cases} $ </p><p>(2) $ \\begin{cases}5n+3m=8,①\\\\ 2m-n=1;②\\end{cases} $ </p><p>(3) $ \\begin{cases}0.3x+0.2y=-0.1,\\\\ 3x-2(2x+y)=7;\\end{cases} $ </p><p>(4) $ \\begin{cases}\\dfrac{x-2}{2}+\\dfrac{y+1}{3}=-4,\\\\ 3(2x-y)-2(x-3y)=7.\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时2 代入法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252884569071616", "questionArticle": "<p>6．已知关于<i>x</i>,<i>y</i>的方程组 $ \\begin{cases}x-y=4a-3,\\\\ x+2y=-5a.\\end{cases} $ </p><p>(1)①当<i>a</i>=0时,该方程组的解是<u>　　　　</u>;&nbsp;</p><p>②<i>x</i>与<i>y</i>的数量关系是<u>　　　　</u>(不含字母<i>a</i>);&nbsp;</p><p>(2)是否存在有理数<i>a</i>,使得|<i>x</i>+3| $ {\\rm +\\mathit{y}^{2}=0} $ ?请写出你的思考过程.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252883843457024", "questionArticle": "<p>7．我国古代很早就对二元一次方程组进行研究,在《九章算术》中记载用算筹表示二元一次方程组,发展到现代就用矩阵表示.例如:对于二元一次方程组 $ \\begin{cases}2x+5y=1,①\\\\ x-y=6,②\\end{cases} $ 我们把<i>x</i>,<i>y</i>的系数和方程右边的常数分离出来组成一个矩阵: $ [\\begin{matrix}2& 5& 1\\\\ 1& -1& 6\\end{matrix}] $ ,用加减消元法解二元一次方程组的过程,就是对方程组中各方程中未知数的系数和常数项进行变换的过程.若将②×5,则得到矩阵 $ [\\begin{matrix}2& 5& 1\\\\ 5& -5& 30\\end{matrix}] $ ,用加减消元法可以消去<i>y.</i>解二元一次方程组 $ \\begin{cases}3x-4y=1,\\\\ 2x-3y=2\\end{cases} $ 时,我们要用加减消元法消去<i>x</i>,得到的矩阵是<u>　　　　　　　　</u><i>.&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424|16425", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的特殊解法", "isInBasket": false, "collectFlag": false, "jumpType": 0, "questionFeatureName": "阅读材料题", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252760451227648", "questionArticle": "<p>8．用代入法解方程组 $ \\begin{cases}y=1-x,①\\\\ x-2y=4②\\end{cases} $ 时,将方程①代入方程②正确的是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．<i>x</i>−2−2<i>x</i>=4B．<i>x</i>−2+2<i>x</i>=4</p><p>C．<i>x</i>−2+<i>x</i>=4D．<i>x</i>−2-<i>x</i>=4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时2 代入法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568252758924500992", "questionArticle": "<p>9．已知 $ \\begin{cases}x=-1,\\\\ y=2\\end{cases} $ 是二元一次方程组 $ \\begin{cases}3x+2y=m,\\\\ nx-y=1\\end{cases} $ 的解,则<i>m</i>-<i>n</i>的值是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．2C．3D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时2 代入法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568252881930854400", "questionArticle": "<p>10．解方程组: $ \\begin{cases}4x-3y=1,①\\\\ 3x-2y=-1.②\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 118, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 118, "timestamp": "2025-07-01T02:14:44.630Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}