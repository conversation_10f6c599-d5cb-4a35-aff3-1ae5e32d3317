{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 71, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "579476672673521664", "questionArticle": "<p>1．若 $ x{^{∣2m-3∣}}+\\left ( { m-2 } \\right ) y=10 $ 是关于<i>x</i>，<i>y</i>的二元一次方程，则<i>m</i>的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．任何数C．2D．1或2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025广东广大附中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579476659159474176", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579476659159474176", "title": "广东省广州市广州大学附属中学2024−2025学年 七年级数学下学期期中考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579476546542415872", "questionArticle": "<p>2．解方程组 $ \\begin{cases} x-2y=5 \\\\ 3x+4y=25 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京首师大附中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-05-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579475629088747520", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579475629088747520", "title": "北京首都师范大学附属中学2024−2025学年下学期七年级期中考试数学试卷", "paperCategory": 1}, {"id": "579476525377957888", "title": "广东省广州市广东广雅中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579476543971307520", "questionArticle": "<p>3．若方程组 $ \\begin{cases} x+y=∗ \\\\ 2x-y=7 \\end{cases}  $ 的解为 $ \\begin{cases} x=2 \\\\ y={ \\rm{ Δ } } \\end{cases}  $ ，则“*”表示的数是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东广州市广雅中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579476525377957888", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579476525377957888", "title": "广东省广州市广东广雅中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579476534903222272", "questionArticle": "<p>4．已知一个二元一次方程组的解是 $ \\begin{cases} x=-1 \\\\ y=-2 \\end{cases}  $ ，则这个方程组可以是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2x=y \\\\ y-x=-3 \\end{cases}  $ B． $ \\begin{cases} x+y=-3 \\\\ x-2y=1 \\end{cases}  $ C． $ \\begin{cases} x+y=-3 \\\\ x-y=-1 \\end{cases}  $ D． $ \\begin{cases} x+y=-3 \\\\ 3x+y=-5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东广州市广雅中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579476525377957888", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579476525377957888", "title": "广东省广州市广东广雅中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578018366889177088", "questionArticle": "<p>5．某体育用品商场销售 $ \\mathrm{ A } $ ， $ B $ 两款足球，售价和进价如表：已知该商场购进5个 $ \\mathrm{ A } $ 款足球和12个 $ B $ 款足球需1120元；购进10个 $ \\mathrm{ A } $ 款足球和15个 $ B $ 款足球需1700元．</p><table style=\"border: solid 1px;border-collapse: collapse; width:184.5pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>类型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>进价/（元/个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>售价/（元/个）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ \\mathrm{ A } $ 款</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ m $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>120</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ B $ 款</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ n $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>90</p></td></tr></table><p>（1）求 $ m $ 和 $ n $ 的值．</p><p>（2）为了提高销量，商场实施“买足球送跳绳”的促销活动：买1个 $ \\mathrm{ A } $ 款足球送1根跳绳，买3个 $ B $ 款足球送2根跳绳．每根跳绳的成本为10元，某日商场售卖这两款足球总计盈利600元，则该日商场销售 $ \\mathrm{ A } $ ， $ B $ 两款足球各多少个？（每款都有销售）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建厦外 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16420|16440", "keyPointNames": "二元一次方程的解|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578018344772612096", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578018344772612096", "title": "福建省厦门外国语学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578018353085722624", "questionArticle": "<p>6．二元一次方程组 $ \\begin{cases} x-y=1, \\\\ x+y=3 \\end{cases}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=2, \\\\ y=1 \\end{cases}  $ B． $ \\begin{cases} x=-1, \\\\ y=-2 \\end{cases}  $ C． $ \\begin{cases} x=3, \\\\ y=2 \\end{cases}  $ D． $ \\begin{cases} x=1, \\\\ y=2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦外 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578018344772612096", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578018344772612096", "title": "福建省厦门外国语学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578017959160885248", "questionArticle": "<p>7．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} ax+2by=4 \\\\ x-y=3 \\end{cases}  $ 与 $ \\begin{cases} x+y=1 \\\\ bx+\\left ( { a-1 } \\right ) y=3 \\end{cases}  $ 有相同的解．</p><p>（1）请求出这个相同的解；</p><p>（2）求<i>a</i>，<i>b</i>的值；</p><p>（3）请判断“无论<i>m</i>取何值，（1）中的解都是关于<i>x</i>，<i>y</i>的方程 $ \\left ( { 3+m } \\right ) x+\\left ( { 2m+1 } \\right ) y=5 $ 的解”，这句话是否正确？并说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建福州一中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017930094358528", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "578017930094358528", "title": "福建省福州第一中学2024−2025学年下学期七年级期中考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578017962256281600", "questionArticle": "<p>8．学校组织甲、乙两支队伍共75位学生，参加文艺演出，并购买演出服（每人一套），下表是服装厂给出的演出服价格表：</p><table style=\"border: solid 1px;border-collapse: collapse; width:352.5pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">购买服装的套数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 1～39 $ 套（含39套）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 40～69 $ 套（含69套）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">70套及以上</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">每套服装的价格</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">80元</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">70元</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">60元</p></td></tr></table><p>甲队人数少于70人，且甲队的人数多于乙队．若甲乙两队分别各自购买演出服，两队共需花费5600元，请回答下列问题：</p><p>（1）如果甲、乙两队联合起来购买演出服，那么比各自分别购买节省<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元；</p><p>（2）甲、乙两队各有多少位学生？</p><p>（3）现从甲、乙两队分别抽调一部分学生去福利院演出（要求两队抽调的人数均不为0），并在演出后与小朋友们开展“心连心活动”．若甲队每位学生对接3位小朋友，乙队每位学生对接4位小朋友，恰好使得60位小朋友全部得到“心连心活动”的温暖，共有几种抽调方案？请列举出来．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建福州一中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16420|16440|16490", "keyPointNames": "二元一次方程的解|表格或图示问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017930094358528", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "578017930094358528", "title": "福建省福州第一中学2024−2025学年下学期七年级期中考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578018139985719296", "questionArticle": "<p>9．定义：对任意一个两位数 $ a $ ，若 $ a $ 满足个位数字与十位数字互不相同，且都不为零，则称这个两位数为“相异数”，将一个“相异数”的个位数字与十位数字对调后得到一个新的两位数，把这个新两位数与原两位数的和与11的商记为 $ f\\left ( { a } \\right )  $ ．</p><p>例如： $ a=12 $ ，对调个位数字与十位数字得到新两位数21，新两位数与原两位数的和为 $ 21+12=33 $ ，和与11的商为 $ 33\\div 11=3 $ ，所以 $ f\\left ( { 12 } \\right ) =3 $ ．根据以上定义，回答下列问题：</p><p>（1）下列两位数：20，52，44中，“相异数”为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）如果“相异数” $ b $ 满足 $ f\\left ( { b } \\right ) =4 $ ，直接写出所有“相异数” $ b $ 的值<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（3）如果 $ m $ ， $ n $ 都是“相异数”，且 $ m+n=100 $ ，请判断 $ f\\left ( { m } \\right ) +f\\left ( { n } \\right ) -10 $ 值是否为常数，并说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建福建师范大学附属小学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578018110046777344", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578018110046777344", "title": "福建省福州市福建师范大学附属中学2024−2025学年下学期七年级期中考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578018138932948992", "questionArticle": "<p>10．根据如表素材，探索解决任务．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>新年礼盒生产方案的设计</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>某工厂准备在春节前生产甲、乙两种型号的新年礼盒共70万套．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>甲礼盒的成本为20元/套，售价为24元/套；</p><p>乙礼盒的成本为25元/套，售价为30元/套．</p></td></tr><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>该工厂计划筹集资金1540万元，且全部用于生产甲、乙两种礼盒，则这两种礼盒各生产多少万套？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>经过市场调查，该厂决定在原计划的基础上增加生产甲种礼盒 $ m $ 万套，增加生产乙种礼盒 $ n $ 万套（ $ m $ ， $ n $ 都为正整数），且两种礼盒售完后所获得的总利润为368万元，请问该工厂有几种生产方案？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福建师范大学附属小学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16420|16441", "keyPointNames": "二元一次方程的解|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578018110046777344", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578018110046777344", "title": "福建省福州市福建师范大学附属中学2024−2025学年下学期七年级期中考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 72, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 72, "timestamp": "2025-07-01T02:09:17.469Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}