{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 134, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "564939100421136384", "questionArticle": "<p>1．如图1，在平面直角坐标系中，<i>A</i>（<i>a</i>，0），<i>B</i>（<i>b</i>，3），<i>C</i>（4，0），且满足 $ {\\rm （\\mathit{a}+\\mathit{b}）^{2}+} $ |<i>a</i>﹣<i>b</i>+6|=0．</p><p>（1）求点<i>A</i>、<i>B</i>的坐标及三角形<i>ABC</i>的面积．</p><p>（2）点<i>P</i>为<i>x</i>轴上一点，若三角形<i>BCP</i>的面积等于三角形<i>ABC</i>面积的两倍，求点<i>P</i>的坐标．</p><p>（3）若点<i>P</i>的坐标为 $ {\\rm （0，\\mathit{m}）} $ ，设以点<i>P</i>、<i>O</i>、<i>C</i>、<i>B</i>为顶点的四边形面积为<i>S</i>，请用含<i>m</i>的式子表示<i>S</i>（直接写出结果）．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/08/2/1/0/0/0/564939033895280641/images/img_12.png\" style=\"vertical-align:middle;\" width=\"428\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "150000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024内蒙古鄂尔多斯 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16257|16424|16501", "keyPointNames": "绝对值非负性的应用|加减消元法解二元一次方程组|坐标与图形性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939074085101568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564939074085101568", "title": "内蒙古自治区鄂尔多斯市东胜区东胜区实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939099506778112", "questionArticle": "<p>2．围棋起源于中国，古代称为“弈”，是棋类鼻祖，距今已有4000多年的历史．某商家销售<i>A</i>，<i>B</i>两种材质的围棋，每套进价分别为210元、180元，下表是近两个月的销售情况：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">销售时段</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">销售数量</p></td><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">销售收入</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><i>A</i>种材质</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><i>B</i>种材质</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">第一个月</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">3套</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">5套</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">1800元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">第二个月</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">4套</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">10套</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">3100元</p></td></tr></table><p>(1)求<i>A</i>，<i>B</i>两种材质的围棋每套的售价；</p><p>(2)若商家再采购<i>A</i>，<i>B</i>两种材质的围棋共30套，购买金额不超过5760元，求<i>A</i>种材质的围棋最多能采购多少套？</p><p>(3)在（2）的条件下，商店销售完这30套围棋能否实现利润为1030元的目标？请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "150000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024内蒙古鄂尔多斯 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16406|16437|16486", "keyPointNames": "销售盈亏问题|销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939074085101568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564939074085101568", "title": "内蒙古自治区鄂尔多斯市东胜区东胜区实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939096814034944", "questionArticle": "<p>3．古运河是扬州的母亲河．为打造古运河风光带，现有一段长为180米的河道整治任务由<i>A</i>、<i>B</i>两工程队先后接力完成 $ {\\rm ．\\mathit{A}} $ 工程队每天整治12米，<i>B</i>工程队每天整治8米，共用时20天．</p><p>(1)根据题意，甲、乙两名同学分别列出尚不完整的方程组如下：</p><p>甲： $ \\begin{cases} x+y=\\left ( { \\,\\,\\,\\, } \\right )  \\\\ 12x+8y=\\left ( { \\,\\,\\,\\, } \\right )  \\end{cases}  $ ；乙： $ \\begin{cases} x+y=\\left ( { \\,\\,\\,\\, } \\right )  \\\\ \\dfrac { x } { 12 }+\\dfrac { y } { 8 }=\\left ( { \\,\\,\\,\\, } \\right )  \\end{cases}  $ </p><p>根据甲、乙两名同学所列的方程组，请你分别指出未知数<i>x</i>、<i>y</i>表示的意义，然后在括号内补全甲、乙两名同学所列的方程组：</p><p>甲：<i>x</i>表示<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，<i>y</i>表示<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>乙：<i>x</i>表示<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，<i>y</i>表示<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>(2)求<i>A</i>、<i>B</i>两工程队分别整治河道多少米．（写出完整的解答过程）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "150000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024内蒙古鄂尔多斯 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16431", "keyPointNames": "工程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939074085101568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564939074085101568", "title": "内蒙古自治区鄂尔多斯市东胜区东胜区实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939095006289920", "questionArticle": "<p>4．先阅读下列材料，再完成任务：</p><p>有些关于方程组的问题，欲求的结果不是每一个未知数的值，而是关于未知数的代数式的值，如以下问题：</p><p>已知实数<i>x</i>、<i>y</i>满足 $ 3x-y=5 $ ①， $ 2x+3y=7 $ ②，求 $ x-4y $ 和 $ 7x+5y $ 的值．</p><p>本题常规思路是将①②两式联立组成方程组，解得<i>x</i>、<i>y</i>的值再代入欲求值的代数式得到答案，常规思路运算量比较大．其实，仔细观察两个方程未知数的系数之间的关系，本题还可以通过适当变形整体求得代数式的值，如由 $ ①-② $ 可得 $ x-4y=-2 $ ，由①+②×2可得 $ 7x+5y=19 $ ．这样的解题思想就是通常所说的“整体思想”．</p><p>解决问题：</p><p>(1)已知二元一次方程组 $ \\begin{cases} 2x+y=7 \\\\ x+2y=8 \\end{cases}  $ ，则 $ x-y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ x+y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)若关于<i>x</i>、<i>y</i>的二元一次方程组 $ \\begin{cases} 2x+y=-3m+2 \\\\ x+2y=4 \\end{cases}  $ 的解满足 $ x+y &gt; 3 $ ．则<i>m</i>的取值范围是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(3)某班级组织活动购买小奖品，买20支铅笔、3块橡皮、2本日记本共需32元，买39支铅笔、5块橡皮、3本日记本共需58元，则购买1支铅笔、1块橡皮、1本日记本共需多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "150000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024内蒙古鄂尔多斯 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16424|16437|16486", "keyPointNames": "加减消元法解二元一次方程组|销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939074085101568", "questionFeatureName": "阅读材料题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564939074085101568", "title": "内蒙古自治区鄂尔多斯市东胜区东胜区实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939094087737344", "questionArticle": "<p>5．（1）计算： $ \\sqrt { {\\left( { -2 } \\right) ^ {2}} }+\\sqrt[3] { -64 }-\\left  | { 2-\\sqrt { 3 } } \\right  |  $ </p><p>（2）解不等式，并把解集表示在数轴上： $ \\dfrac { x } { 4 }-1  &lt;  \\dfrac { x-3 } { 3 } $ </p><p>（3）解方程 $ 2x{^{2}}+7=15 $ </p><p>（4）解方程组 $ \\begin{cases} 3\\left ( { x-1 } \\right ) =y+5 \\\\ \\dfrac { y-1 } { 3 }=\\dfrac { x } { 2 }-1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "150000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024内蒙古鄂尔多斯 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16290|16424|16485", "keyPointNames": "立方根|加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939074085101568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564939074085101568", "title": "内蒙古自治区鄂尔多斯市东胜区东胜区实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939091655041024", "questionArticle": "<p>6．一服装厂用某种布料生产一批冬装，已知每米布料可做衣身1个或衣袖3只，现计划用136米这种布料生产这批冬装（不考虑布料的损耗），设用<i>x</i>米布料做衣身，用<i>y</i>米布料做衣袖，使得恰好配套（一个衣身配两只衣袖），则可列方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "150000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024内蒙古鄂尔多斯 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939074085101568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564939074085101568", "title": "内蒙古自治区鄂尔多斯市东胜区东胜区实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939368240029696", "questionArticle": "<p>7．小明家需要用钢管做防盗窗，按设计要求，其中需要长为 0.8<i>m</i>，2.5<i>m </i>且粗细相同的钢管分别为 100 根，32 根，并要求这些用料不能是焊接而成的．现钢材市场的这种规格的钢管每根为 6<i>m</i>．</p><p>（1）试问一根 6<i>m </i>长的圆钢管有哪些裁剪方法呢？请填写下空（余料作废）． </p><p>方法①：当只裁剪长为 0.8<i>m </i>的用料时，最多可剪_根；</p><p>方法②：当先剪下 1 根 2.5<i>m </i>的用料时，余下部分最多能剪 0.8<i>m </i>长的用料<u>&nbsp;&nbsp;</u>根； </p><p>方法③：当先剪下 2 根 2.5<i>m </i>的用料时，余下部分最多能剪 0.8<i>m </i>长的用料_根．</p><p>（2）分别用（1）中的方法②和方法③各裁剪多少根 6<i>m </i>长的钢管，才能刚好得到所需要的相应数量的材料？</p><p>（3）试探究：除（2）中方案外，在（1）中还有哪两种方法联合，所需要 6<i>m </i>长的钢管与（2） 中根数相同？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16434|16438", "keyPointNames": "方案问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939346324791296", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939346324791296", "title": "山西省晋城市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939086726733824", "questionArticle": "<p>8．为了更好地开展阳光大课间活动，某班级计划购买跳绳和呼啦圈两种体育用品．已知一个跳绳8元，一个呼啦圈12元，童威准备用120元钱全部用于购买这两种体育用品（两种都买），该班级的购买方案共有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3种B．4种C．5种D．6种</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "150000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024内蒙古鄂尔多斯 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939074085101568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564939074085101568", "title": "内蒙古自治区鄂尔多斯市东胜区东胜区实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564939367468277760", "questionArticle": "<p>9．阅读与思考</p><p>阅读下列材料，完成后面的任务．</p><p>善于思考的李同学在解方程组 $ \\begin{cases} 2(m+2)+3\\left ( { n-\\dfrac { 2 } { 3 } } \\right ) =1 \\\\ 7(m+2)+6\\left ( { n-\\dfrac { 2 } { 3 } } \\right ) =2 \\end{cases}  $ 时，采用了一种“整体换元”的解法．</p><p>解：把 $ m+2,n-\\dfrac { 2 } { 3 } $ 看成一个整体，设 $ m+2=x $ ， $ n-\\dfrac { 2 } { 3 }=y $ ．</p><p>原方程组可化为 $ \\begin{cases} 2x+3y=1 \\\\ 7x+6y=2 \\end{cases}  $ ，解得 $ \\left \\{\\hspace{-0.5em}  \\begin{array} {l} x=0 \\\\ y=\\dfrac { 1 } { 3 } \\end{array} \\hspace{-0.5em} ,\\therefore \\left \\{\\hspace{-0.5em}  \\begin{array} {l} m+2=0 \\\\ n-\\dfrac { 2 } { 3 }=\\dfrac { 1 } { 3 } \\end{array} \\hspace{-0.5em} ,\\therefore \\right. \\right.  $ 原方程组的解为 $ \\begin{cases} m=-2 \\\\ n=1 \\end{cases}  $ ．</p><p>任务：</p><p>(1)方程组 $ \\begin{cases} 3x-2y=1 \\\\ 9x-2y=19 \\end{cases}  $ 的解是 $ \\begin{cases} x=3 \\\\ y=4 \\end{cases}  $ ，则方程组 $ \\begin{cases} 3(a+b)-2(a-b)=1 \\\\ 9(a+b)-2(a-b)=19 \\end{cases}  $ 的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)仿照上述解题方法，用“整体换元”法解方程组 $ \\begin{cases} 3(x+y)-4(x-y)=4 \\\\ \\dfrac { x+y } { 2 }+\\dfrac { x-y } { 6 }=1 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939346324791296", "questionFeatureName": "阅读材料题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939346324791296", "title": "山西省晋城市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939366667165696", "questionArticle": "<p>10．某学校开发一块试验田作为劳动教育实践基地，通过初步设计，由大小形状完全相同的8块小长方形试验田组成，如图所示，经测量，该实践基地的宽为60米，请计算该实践基地的面积．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/08/2/1/0/0/0/564939326393458701/images/img_13.png\" style=\"vertical-align:middle;\" width=\"146\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939346324791296", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939346324791296", "title": "山西省晋城市2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 135, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 135, "timestamp": "2025-07-01T02:16:49.810Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}