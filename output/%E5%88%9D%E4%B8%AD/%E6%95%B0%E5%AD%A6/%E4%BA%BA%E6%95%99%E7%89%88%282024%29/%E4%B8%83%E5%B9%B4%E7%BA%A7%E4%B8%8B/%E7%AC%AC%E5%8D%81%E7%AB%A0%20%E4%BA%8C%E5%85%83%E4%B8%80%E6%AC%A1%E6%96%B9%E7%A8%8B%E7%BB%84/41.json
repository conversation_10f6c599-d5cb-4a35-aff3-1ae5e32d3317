{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 40, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "585961039449075712", "questionArticle": "<p>1．《九章算术》中有这样一个题，其大意是：今有醇酒(优质酒)1斗，价值50钱；行酒(劣质酒)1斗，价值10钱；现有30钱，买得2斗酒．问醇酒、行酒各能买多少？设醇酒买了<i>x</i>斗，行酒买了<i>y</i>斗，则可列二元一次方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=2 \\\\ 50x+10y=30 \\end{cases}  $ B． $ \\begin{cases} x-y=2 \\\\ 50x+10y=30 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=2 \\\\ 10x+50y=30 \\end{cases}  $ D． $ \\begin{cases} x+y=2 \\\\ 10x+30y=50 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|320000|120000|530000|410000|350000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 16, "referenceNum": 8, "createTime": "2025-06-09", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}, {"id": "578018110046777344", "title": "福建省福州市福建师范大学附属中学2024−2025学年下学期七年级期中考数学试卷", "paperCategory": 1}, {"id": "569342775004536832", "title": "2025年天津市河东区九年级中考一模数学试题", "paperCategory": 1}, {"id": "449184612356497408", "title": "福建师范大学附属中学2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "443902015472705536", "title": "2024年广东省深圳市中考二模数学试题", "paperCategory": 1}, {"id": "533728153467920384", "title": "山东省济南市稼轩学校2024—2025学年上学期八年级12月月考数学试题", "paperCategory": 1}, {"id": "210029109061132288", "title": "云南省曲靖市麒麟区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "162489239652835328", "title": "江苏省南通市2021年中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "585961036735361024", "questionArticle": "<p>2．若单项式2<i>x</i><sup>2</sup><i>y</i><i><sup>a+b</sup></i>与 $ {\\rm -} \\dfrac { 1 } { { { 3 } } } {\\rm \\mathit{x}\\mathit{^{a-b}}\\mathit{y}^{4}} $ 是同类项，则<i>a，b</i>的值分别为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．<i>a</i>＝3，<i>b</i>＝1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;B．<i>a</i>＝−3，<i>b</i>＝1</p><p>C．<i>a</i>＝3，<i>b</i>＝−1D．<i>a</i>＝−3，<i>b</i>＝−1</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|-1|510000|410000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 20, "referenceNum": 6, "createTime": "2025-06-09", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}, {"id": "502233406681423872", "title": "四川省成都市温江区东辰外国语学校2024—−2025学年八年级上学期10月月考数学试题", "paperCategory": 1}, {"id": "402643504353026048", "title": "重庆市渝北区渝北区第二实验中学校2023-2024学年七年级上学期期末数学试题", "paperCategory": 1}, {"id": "277836006291709952", "title": "湖南省衡阳八中教育集团成章联校2021-2022学年七年级下学期期中数学试卷", "paperCategory": 1}, {"id": "174521170611445760", "title": "2022年七年级上册湘教版数学第二章2.5整式的加法和减法课时练习", "paperCategory": 1}, {"id": "169374338595987456", "title": "2022年七年级下册人教版数学第八章8.1二元一次方程组课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "585961036009746432", "questionArticle": "<p>3．用代入法解方程组 $ \\begin{cases} 2x-y=5① \\\\ y=1+x② \\end{cases}  $ 时，把②代入①后得到方程是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 2x-1+x=5 $ B． $ 1+x=2x+5 $ C． $ 5-2x=1+x $ D． $ 2x-1-x=5 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-06-09", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}, {"id": "555175874812747776", "title": "黑龙江省哈尔滨市松雷中学2024—2025学年七年级下学期数学开学考试试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "585961034294276096", "questionArticle": "<p>4．方程组 $ \\begin{cases} x+y=5 \\\\ 2x+y=10 \\end{cases}\\hspace{-0.5em}   \\begin{array} {} ① \\\\ ② \\end{array} \\hspace{-0.5em}  $ ，由②-①，得到的方程是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 3x=10 $ B． $ &nbsp;3x=-5 $ C． $ x=5 $ D． $ &nbsp;x=-5 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-06-09", "keyPointIds": "16315|16424", "keyPointNames": "合并同类项|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}, {"id": "255408981006393344", "title": "河北省石家庄市裕华区石家庄外国语学校2021-2022学年七年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "585961178804826112", "questionArticle": "<p>5．某商场在“双11”前准备从供货商家处新选购一批商品，已知按进价购进1件甲种商品和2件乙种商品共需320元，购进3件甲种商品和2件乙种商品共需520元．</p><p>（1）求甲、乙两种商品每件的进价分别是多少元？</p><p>（2）若甲种商品的售价为每件120元，乙种商品的售价为每件140元，该商场准备购进甲、乙两种商品共50件，且这两种商品全部售出后总利润不少于1350元，不高于1375元．若购进甲种商品<i>m</i>件，请问该商场共有哪几种进货方案？</p><p>（3）根据往年销售情况，商场计划在“双11”当天将现有的甲、乙两种商品共46件按（2）中的售价全部售完．但因受拉尼娜现象形成的冷空气持续影响，当天出现的雨雪天气使得46件商品没有全部售完，两种商品的实际销售利润总和为1220元．那么，“双11”当天商场至少卖出乙种商品多少件？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "530000|450000|420000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖北黄石 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 12, "referenceNum": 3, "createTime": "2025-06-09", "keyPointIds": "16434|16490", "keyPointNames": "方案问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961147863445504", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585961147863445504", "title": "湖北省黄石十四中教联体2024—2025学年下学期八年级数学培优测试卷", "paperCategory": 1}, {"id": "476808603464146944", "title": "广西南宁市第四十七中学2023−2024学年八年级上学期开学数学试题", "paperCategory": 1}, {"id": "208560932707934208", "title": "云南省昭通市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585961177022246912", "questionArticle": "<p>6．阅读材料：我们把多元方程（组）的非负整数解叫做这个方程（组）的“好解”．例如： $ \\begin{cases} x=1 \\\\ y=8 \\end{cases}  $ 就是方程3<i>x</i>+<i>y</i>＝11的一组“好解”； $ \\begin{cases} x=1 \\\\ y=2 \\\\ z=3 \\end{cases}  $ 是方程组 $ \\begin{cases} x-2y+z=0 \\\\ x+y+z=6 \\end{cases}  $ 的一组“好解”．</p><p>（1）求方程<i>x</i>+2<i>y</i>＝5的所有“好解”；</p><p>（2）关于<i>x</i>，<i>y</i>，<i>k</i>的方程组 $ \\begin{cases} x+y+k=15 \\\\ x+5y+3k=27 \\end{cases}  $ 有“好解”吗？若有，请求出对应的“好解”；若没有，请说明理由．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000|420000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖北黄石 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-06-09", "keyPointIds": "16420|16443", "keyPointNames": "二元一次方程的解|解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961147863445504", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585961147863445504", "title": "湖北省黄石十四中教联体2024—2025学年下学期八年级数学培优测试卷", "paperCategory": 1}, {"id": "174209214402830336", "title": "吉林省长春外国语学校2020-2021学年七年级下学期第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586668097265770496", "questionArticle": "<p>7． $ 2023 $ 年 $ 7 $ 月 $ 28 $ 日至 $ 8 $ 月 $ 8 $ 日，第 $ 31 $ 届世界大学生运动会将在成都举行．“当好东道主，热情迎嘉宾”，成都某知名小吃店计划购买 $ \\mathrm{ A } $ ， $ B $ 两种食材制作小吃．已知购买 $ 1 $ 千克 $ \\mathrm{ A } $ 种食材和 $ 1 $ 千克 $ B $ 种食材共需 $ 68 $ 元，购买 $ 5 $ 千克 $ \\mathrm{ A } $ 种食材和 $ 3 $ 千克 $ B $ 种食材共需 $ 280 $ 元．</p><p>（1）求 $ \\mathrm{ A } $ ， $ B $ 两种食材的单价；</p><p>（2）该小吃店计划购买两种食材共 $ 36 $ 千克，其中购买 $ \\mathrm{ A } $ 种食材千克数不少于 $ B $ 种食材千克数的 $ 2 $ 倍，当 $ \\mathrm{ A } $ ， $ B $ 两种食材分别购买多少千克时，总费用最少？并求出最少总费用．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2023四川成都 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-06-09", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "350027842543984640", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "350027842543984640", "title": "2023年四川省成都市数学中考真题", "paperCategory": 1}, {"id": "586668067532349440", "title": "四川省成都市玉林中学2024−2025学年八年级下学期5月月考数学试题", "paperCategory": 1}, {"id": "536336218658742272", "title": "2024年四川省泸州市龙马潭区中考数学一模模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584850970217062400", "questionArticle": "<p>8．（1）解不等式组 $ \\begin{cases} 2x-6  &lt;  0 \\\\ 2x  &lt;  x+1 \\end{cases}  $ ，并将解集表示在数轴上；</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/02/2/1/0/0/0/584850926940237843/images/img_20.png\" style=\"vertical-align:middle;\" width=\"226\" alt=\"试题资源网 https://stzy.com\"></p><p>（2）三个二元一次方程 $ x-y=3 $ ， $ x+y=7 $ ， $ y=3x-1 $ ．请在这三个方程中任选两个方程，组成一个二元一次方程组，并解该方程组．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "520000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025贵州黔南布依族苗族自治州 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-09", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584850946712182784", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "584850946712182784", "title": "贵州省黔南州2025年九年级中考二模考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584850711776636928", "questionArticle": "<p>9．根据如表素材，探索完成任务．</p><table style=\"border: solid 1px;border-collapse: collapse; width:392.25pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>背景</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 364.5pt;\"><p>为了迎接2024年杭州茶文化“西湖悦读节”，某班级开展知识竞赛活动，去奶茶店购买<i>A</i>，<i>B</i>两种款式的奶茶作为奖品．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>素材</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 249pt;\"><p>若买10杯<i>A</i>款奶茶，5杯<i>B</i>款奶茶，共需160元；若买15杯<i>A</i>款奶茶，10杯<i>B</i>款奶茶，共需270元．</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 115.5pt;\"><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/29/2/1/0/0/0/594508128135192576/images/img_1.png\" style='vertical-align:middle;' width=\"122\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 392.25pt;\"><p>问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>任务1</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 364.5pt;\"><p>问<i>A</i>款奶茶和<i>B</i>款奶茶的销售单价各是多少元？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>任务2</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 364.5pt;\"><p>如果购买<i>A</i>，<i>B</i>两种款式的奶茶（两种都要），刚好花200元，请问购买方案分别是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东莞中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-09", "keyPointIds": "16420|16437", "keyPointNames": "二元一次方程的解|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584850683985178624", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584850683985178624", "title": "广东省东莞市东城区东莞中学2024—2025学年下学期七年级数学期中考试试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584850704323358720", "questionArticle": "<p>10． $ \\begin{cases} x=2 \\\\ y=-3 \\end{cases}  $ 是方程 $ ax+y=1 $ 的解，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东莞中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-09", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584850683985178624", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584850683985178624", "title": "广东省东莞市东城区东莞中学2024—2025学年下学期七年级数学期中考试试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 41, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 41, "timestamp": "2025-07-01T02:05:39.186Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}