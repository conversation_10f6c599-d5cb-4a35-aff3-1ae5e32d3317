{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 192, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "544975172652015616", "questionArticle": "<p>1．小明想购买一副羽毛球拍与5盒羽毛球，他发现 $ \\mathrm{ A } $ 、 $ B $ 两商场的每副羽毛球拍与每盒羽毛球的标价均相同，这两项合计为300元，但他们的售卖方案不同．</p><p> $ \\mathrm{ A } $ 商场的售卖方案是：顾客每购买一副羽毛球拍赠送一盒羽毛球，另外购买的羽毛球则按原价出售．</p><p> $ B $ 商场的售卖方案是：顾客购买的羽毛球拍与羽毛球均按原价的9折出售．</p><p>小明发现，他要购买的羽毛球拍与羽毛球在这两家商场应付的钱一样多，问：羽毛球拍与羽毛球的单价分别是多少？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西九江 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-15", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544975164041109504", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544975164041109504", "title": "江西省九江市2024−2025学年上学期期末考试 七年级 数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544976303931301888", "questionArticle": "<p>2．为丰富学生的社会实践活动，八年级（1）班开展了一次水果售卖体验活动．其中第一小组花380元从水果批发市场批发了苹果和桔子共50千克到零售市场售卖，苹果和桔子当天的批发价与零售价如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:right;\">价格水果种类</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 106.5pt;\"><p style=\"text-align:center;\">批发价（元／千克）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 106.5pt;\"><p style=\"text-align:center;\">零售价（元／千克）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">苹果</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 106.5pt;\"><p style=\"text-align:center;\">6</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 106.5pt;\"><p style=\"text-align:center;\">8.4</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">桔子</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 106.5pt;\"><p style=\"text-align:center;\">10</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 106.5pt;\"><p style=\"text-align:center;\">13</p></td></tr></table><p>(1)第一小组当天批发苹果和桔子各多少千克？（要求用二元一次方程组解决问题）</p><p>(2)该小组当天售卖完这些苹果和桔子可赚多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁锦州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-15", "keyPointIds": "16437|28548", "keyPointNames": "销售利润问题|有理数混合运算的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544976294900965376", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "544976294900965376", "title": "辽宁省锦州市2024−2025学年 八年级上学期期末数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544976302878531584", "questionArticle": "<p>3．解方程组：</p><p>(1) $ \\begin{cases} 2x-y=1 \\\\ y=3x-4 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 2x-3y=5 \\\\ x+6y=-5 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁锦州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-15", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544976294900965376", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544976294900965376", "title": "辽宁省锦州市2024−2025学年 八年级上学期期末数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544977387043201024", "questionArticle": "<p>4．“一笔一世界，一划一时光”．如图是一款便携小楷软头笔——钢笔式毛笔，巧妙地将传统毛笔的韵味与现代钢笔的便捷融为一体，让书写变得更加自由流畅．某文具店用3800元购进一批钢笔式毛笔和匹配的墨囊，已知一支钢笔式毛笔的进价为30元，一支墨囊的进价为2元，为吸引顾客，文具店将1支钢笔式毛笔和4支墨囊搭配成套装进行销售，所购进的钢笔式毛笔和墨囊恰好配套．求该文具店购进钢笔式毛笔和匹配的备用墨囊的数量．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/12/2/1/0/0/0/544977359067193349/images/img_12.png\" style=\"vertical-align:middle;\" width=\"222\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西太原 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-02-15", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544977379250184192", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "544977379250184192", "title": "山西省太原市2024−2025学年八年级上学期期末数学试卷", "paperCategory": 1}, {"id": "534754089294405632", "title": "山西省太原市2024−2025学年第一学期八年级期末数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544977386581827584", "questionArticle": "<p>5．解方程组： $ \\begin{cases} 3x-2y=7① \\\\ 3x-y=5② \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西太原 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-02-15", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544977379250184192", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "544977379250184192", "title": "山西省太原市2024−2025学年八年级上学期期末数学试卷", "paperCategory": 1}, {"id": "534754089294405632", "title": "山西省太原市2024−2025学年第一学期八年级期末数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544977384404983808", "questionArticle": "<p>6．某工厂去年的总利润为200万元，今年的总收入比去年增加了 $ 20\\% $ ，总交出比去年减少了 $ 10\\% $ ，今年的总利润为780万元．小明列出二元一次方程组 $ \\begin{cases} x-y=200 \\\\ (1+20{ \\rm{ \\% } })x-(1-10{ \\rm{ \\% } })y=780 \\end{cases}  $ 刻画这一情境中的等量关系，则方程组中的<i>x</i>，<i>y</i>表示的未知量分别为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．今年的总收入为<i>x</i>万元，总支出为<i>y</i>万元</p><p>B．今年的总支出为<i>x</i>万元，总收入为<i>y</i>万元</p><p>C．去年的总收入为<i>x</i>万元，总支出为<i>y</i>万元</p><p>D．去年的总支出为<i>x</i>万元，总收入为<i>y</i>万元</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西太原 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-02-15", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544977379250184192", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "544977379250184192", "title": "山西省太原市2024−2025学年八年级上学期期末数学试卷", "paperCategory": 1}, {"id": "534754089294405632", "title": "山西省太原市2024−2025学年第一学期八年级期末数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544973594176036864", "questionArticle": "<p>7．计算、解方程（组）：</p><p>(1)解方程： $ 8x=-2\\left ( { x+4 } \\right )  $ ；</p><p>(2)解方程： $ \\dfrac { 3x-1 } { 2 }-1=\\dfrac { 5x-7 } { 3 } $ ；</p><p>(3)计算： $ \\sqrt { 64 }-\\left  | { 2-\\sqrt { 5 } } \\right  | +\\sqrt[3] { -27 }+\\sqrt { 5 } $ ；</p><p>(4)解方程组： $ \\begin{cases} 3x+4y=5 \\\\ 5x-2y=-9 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江绥化 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-14", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544973585732902912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544973585732902912", "title": "黑龙江省绥化市2024—2025学年上学期七年级期末考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544973593714663424", "questionArticle": "<p>8．若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+3y=4-m \\\\ 3x+2y=m+5 \\end{cases}  $ 的解满足 $ y=x+3 $ ，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江绥化 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544973585732902912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544973585732902912", "title": "黑龙江省绥化市2024—2025学年上学期七年级期末考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544973804587491328", "questionArticle": "<p>9．某中学在运动会前夕准备购买篮球、足球作为奖品．若购买3个篮球和2个足球共花费520元，且购买一个篮球比购买一个足球多花40元．</p><p>(1)请问：购买一个篮球，一个足球各需多少元?</p><p>(2)今年学校计划购买这种篮球和足球共20个，恰逢商场正在开展促销活动，篮球打八折，足球打七五折，若此次购买两种球的总费用不超过1600元，则最多可购买多少个篮球?</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南怀化 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-14", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544973796081442816", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "544973796081442816", "title": "湖南省怀化市2024—2025学年上学期八年级数学期末抽测卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544973043933683712", "questionArticle": "<p>10．甲、乙两车分别从相距 $ 210 $ 千米的 $ \\mathrm{ A } $ ， $ B $ 两地相向而行．</p><p>(1)两车均保持匀速行驶且甲车的速度是乙车速度的2倍，若甲车比乙车提前2小时出发，则甲车出发后3小时两车相遇．求甲、乙两车的速度分别是多少（单位：千米/小时）？</p><p>(2)如果甲、乙两车保持（1）中的速度，两车同时出发相向而行，求经过多少小时两车相距30千米？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河北承德 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-14", "keyPointIds": "16402|16414|16430", "keyPointNames": "解一元一次方程|行程问题|行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544973036073558016", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "544973036073558016", "title": "河北省承德市2024−2025学年上学期期末学业质量监测七年级数学（冀教版）", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 193, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 193, "timestamp": "2025-07-01T02:23:39.341Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}