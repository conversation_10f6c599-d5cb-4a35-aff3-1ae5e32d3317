{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 68, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "578367488456433664", "questionArticle": "<p>1．幻方起源于中国，是我国古代数学的杰作之一．“洛书”即三阶幻方，它的每行、每列、每条对角线上三个数之和均相等，也称为三阶幻方．如图是一个幻方，则 $ x+y $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ y $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ -5 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ -2 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p>0</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 26.05pt;\"><p> $ x $ </p></td></tr></table><p>A． $ -1 $ B．1C．5D．9</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578367471184289792", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578367471184289792", "title": "2025年湖北省部分学校中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579475655575777280", "questionArticle": "<p>2．定义：对于关于<i>x</i>，<i>y</i>的二元一次方程 $ ax+by=c $ （其中 $ a\\ne b\\ne c $ ），若将其<i>x</i>的系数<i>a</i>与常数<i>c</i>互换，得到的新方程 $ cx+by=a $ 称为原方程 $ ax+by=c $ 的“对称方程”．例如方程 $ 3x+2y=7 $ 的“对称方程”为 $ 7x+2y=3 $ ．</p><p>（1）写出方程 $ 2x-3y=-1 $ 的“对称方程”<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，以及它们组成的方程组的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）若关于<i>x</i>，<i>y</i>的二元一次方程 $ 3x+my=8 $ 与它的“对称方程”组成的方程组的解为 $ \\begin{cases} x=m \\\\ y=n \\end{cases}  $ ，求<i>m</i>，<i>n</i>的值；</p><p>（3）若关于<i>x</i>，<i>y</i>的二元一次方程 $ ax+by=c $ 的系数满足 $ a-b+c=0 $ ，且与它的“对称方程”组成的方程组的解恰是关于<i>x</i>，<i>y</i>的二元一次方程 $ mx+ny=p $ 的一个解，直接写出代数式 $ \\left ( { m-n } \\right ) m+p\\left ( { n-p } \\right ) +2025 $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京首师大附中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579475629088747520", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579475629088747520", "title": "北京首都师范大学附属中学2024−2025学年下学期七年级期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579475654715944960", "questionArticle": "<p>3．列二元一次方程组解决实际问题：</p><p>为丰富课余生活，加强体育锻炼，七年级（1）班计划购置跳绳和排球作为锻炼器材．已知购买2个排球和5根跳绳共需350元；购买4个排球和3根跳绳则需490元．该班共有45名学生，需为每人配备1根跳绳，且每三名学生共用1个排球．若该班统一采购这两种器材，已筹集经费2700元．请问这笔经费是否能满足本次采购需求？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京首师大附中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579475629088747520", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579475629088747520", "title": "北京首都师范大学附属中学2024−2025学年下学期七年级期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579475649431121920", "questionArticle": "<p>4．甲、乙、丙三人在<i>A</i>、<i>B</i>两块地植树，<i>A</i>地需植900棵，<i>B</i>地需植1250棵．甲每天植24棵且仅在<i>A</i>地工作，乙每天植32棵且仅在<i>B</i>地工作，丙每天植30棵且每天可选择在<i>A</i>地或<i>B</i>地植树．</p><p>（1）若甲和丙一起在<i>A</i>地植树2天，之后<i>A</i>地剩余的植树任务由甲单独完成，甲还需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>天完成；</p><p>（2）若两地从同一天开始植树，且恰好在同一天完成，则丙在<i>A</i>地植树的天数比在<i>B</i>地少<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>天．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京首师大附中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16405|16431", "keyPointNames": "工程问题|工程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579475629088747520", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579475629088747520", "title": "北京首都师范大学附属中学2024−2025学年下学期七年级期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579475645454921728", "questionArticle": "<p>5．若 $ \\begin{cases} x=3 \\\\ y=-2 \\end{cases}  $ 是关于 $ x,y $ 的方程 $ ax+y=7 $ 的一组解，则常数 $ a $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京首师大附中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579475629088747520", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579475629088747520", "title": "北京首都师范大学附属中学2024−2025学年下学期七年级期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579475642950922240", "questionArticle": "<p>6．若将关于 $ x,y $ 的方程组 $ \\begin{cases} 3x-2y=4 \\\\ 2x+y=7 \\end{cases}  $ 的解记作 $ \\begin{cases} x=a \\\\ y=b \\end{cases}  $ ，则代数式 $ 5-2a+6b $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -1 $ B．2C．8D．11</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京首师大附中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579475629088747520", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579475629088747520", "title": "北京首都师范大学附属中学2024−2025学年下学期七年级期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579475642091089920", "questionArticle": "<p>7．3月14日数学节当天，我校初一年级学生积极参与“速算游园”活动．活动中，小阳和小光展开了如下对话：</p><p>小阳说：“我比你多解了3道题！”</p><p>小光回应：“如果你给我3道题，我的解题数量就是你的两倍啦．”</p><p>若两人的陈述均为真，设小阳解了<i>x</i>道题，小光解了<i>y</i>道题，则可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=y+3 \\\\ y+3=2\\left ( { x-3 } \\right )  \\end{cases}  $ B． $ \\begin{cases} y=x+3 \\\\ y+3=2\\left ( { x-3 } \\right )  \\end{cases}  $ </p><p>C． $ \\begin{cases} x=y+3 \\\\ x+3=2\\left ( { y-3 } \\right )  \\end{cases}  $ D． $ \\begin{cases} y=x+3 \\\\ x+3=2\\left ( { y-3 } \\right )  \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京首师大附中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579475629088747520", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579475629088747520", "title": "北京首都师范大学附属中学2024−2025学年下学期七年级期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579475264939270144", "questionArticle": "<p>8．为提高饮水质量，越来越多的居民选购家用净水器．一商场抓住商机，从厂家购进了A、B两种型号家用净水器共160台，A型号家用净水器进价是150元/台，B型号家用净水器进价是350元/台，购进两种型号的家用净水器共用去36000元．</p><p>（1）求A、B两种型号家用净水器各购进了多少台；</p><p>（2）为使每台B型号家用净水器的毛利润是A型号的2倍，且保证售完这160台家用净水器的毛利润不低于11000元，求每台A型号家用净水器的售价至少是多少元．（注：毛利润=售价﹣进价）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16441|16490", "keyPointNames": "其他问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579475234459262976", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "579475234459262976", "title": "北京市第三十一中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579475260883378176", "questionArticle": "<p>9．解方程或方程组</p><p>（1） $ x{^{2}}-1=\\dfrac { 7 } { 9 } $ </p><p>（2） $ {\\left( { x-1 } \\right) ^ {3}}-8=0 $ </p><p>（3） $ \\begin{cases} x+y=-1 \\\\ 2x-3y=8 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16287|16290|16423", "keyPointNames": "平方根|立方根|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579475234459262976", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "579475234459262976", "title": "北京市第三十一中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579475257725067264", "questionArticle": "<p>10．若 $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ 是二元一次方程 $ mx+ny=-2 $ 的一个解，则 $ 2m-n-6 $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579475234459262976", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "579475234459262976", "title": "北京市第三十一中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 69, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 69, "timestamp": "2025-07-01T02:08:55.276Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}