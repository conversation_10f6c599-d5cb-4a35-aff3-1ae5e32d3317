{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 177, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "554987665797455872", "questionArticle": "<p>1．已知关于<i>x</i>,<i>y</i>的二元一次方程组 $ \\begin{cases}3x-y=4m+1,\\\\ x+y=2m-5\\end{cases} $ 的解满足<i>x</i>-<i>y</i>=4,则<i>m</i>的值为&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0&nbsp;&nbsp;B.1&nbsp;&nbsp;C.2&nbsp;&nbsp;D.3</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024全国 · 专题模块", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-03-12", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "中考分类集训2　方程(组)", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "554781641006161920", "questionArticle": "<p>2．若方程组   $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } }    \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $   的解是    $ \\begin{cases} x=5 \\\\ y=6 \\end{cases}  $   则方程组    $ \\begin{cases} 5a{{}_{ 1 } }x-3b{{}_{ 1 } }y=4c{{}_{ 1 } } \\\\  5a{{}_{ 2 } }x-3b{{}_{ 2 } }y=4c{{}_{ 2 } } \\end{cases}  $ 的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025浙江蛟川书院 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-12", "keyPointIds": "16420|16425", "keyPointNames": "二元一次方程的解|二元一次方程组的特殊解法", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554781616112967680", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "554781616112967680", "title": "2025年浙江省宁波市镇海蛟川书院中考数学一模试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780936774131712", "questionArticle": "<p>3．已知关于<i>x</i>，<i>y</i>的二元一次方程 $ ax+y=3b $ （<i>a</i>，<i>b</i>均为常数，且 $ a\\ne 0 $ ）．</p><p>(1)当 $ a=-2,b=1 $ 时，用<i>x</i>的代数式表示<i>y</i>；</p><p>(2)若 $ \\begin{cases} x=a-2b \\\\ y=b{^{2}}+3b \\end{cases}  $ 是该二元一次方程的一个解，</p><p>①探索<i>a</i>与<i>b</i>的数量关系，并说明理由；</p><p>②无论<i>a</i>、<i>b</i>取何值，该方程有一个固定的解，则这个解是 _．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-12", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780913382498304", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780913382498304", "title": "江苏省南京市鼓楼区鼓楼实验中学2023−2024学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780933137670144", "questionArticle": "<p>4．解方程组：</p><p>(1) $ \\begin{cases} x+2y=3 \\\\ 3x-2y=5 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 4x+y=15 \\\\ y=x+5 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-12", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780913382498304", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780913382498304", "title": "江苏省南京市鼓楼区鼓楼实验中学2023−2024学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780927601188864", "questionArticle": "<p>5．已知 $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ 是关于<i>x</i>，<i>y</i>的二元一次方程 $ 2kx+y=7 $ 的解，则<i>k</i>的值是 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-12", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780913382498304", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780913382498304", "title": "江苏省南京市鼓楼区鼓楼实验中学2023−2024学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780925457899520", "questionArticle": "<p>6．已知<i>x</i>与<i>y</i>互为相反数，并且 $ 2x-y=6 $ ，则 $ x{^{y}} $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-12", "keyPointIds": "16252|16372|16424", "keyPointNames": "相反数的应用|负整数指数幂|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780913382498304", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780913382498304", "title": "江苏省南京市鼓楼区鼓楼实验中学2023−2024学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553048901592326144", "questionArticle": "<p>7．“世界读书日”是在每年的4月23日，设立目的是推动更多的人去阅读和写作，希望所有人都能尊重和感谢为人类文明做出过巨大贡献的文学、文化、科学、思想大师们，保护知识产权某批发商在“世界读书日”前夕，订购<i>A</i>、<i>B</i>两种具有纪念意义的书签进行销售，若订购<i>A</i>种书签100张，<i>B</i>种书签200张，共花费5000元；订购<i>A</i>种书签120张，<i>B</i>种书签400张，共花费8400元．</p><p>(1)求<i>A</i>、<i>B</i>两种书签的进价分别为多少元：</p><p>(2)该批发商准备在进价的基础上将<i>A</i>、<i>B</i>两种书签提高 $ 40\\% $ 售出，若该批发商购进<i>A</i>、<i>B</i>两种书签共计500张，并且<i>A</i>种书签不超过230张，则该批发商所获最大利润为多少元．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西安 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-12", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553048867547160576", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "553048867547160576", "title": "2024年陕西省西安市高新第二初级中学中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552661558830080000", "questionArticle": "<p>8．贵阳黔灵山公园自从2024年1月1日免费开放以来，公园内的大熊猫馆人气火爆，其中国宝“海浜”“星宝”憨态可掬，获得不少市民喜爱，公园的文创超市售有相应的熊猫抱枕和熊猫挂件．已知每个熊猫抱枕的成本为30元，每个熊猫挂件成本为7元，每个熊猫抱枕售价比熊猫挂件售价贵30元，购买8个熊猫抱枕和18个熊猫挂件共花费500元．</p><p>(1)求熊猫抱枕和熊猫挂件的单价分别为多少元？</p><p>(2)为了迎接暑假，该超市准备投入成本不超过16200元购进熊猫抱枕和熊猫挂件共1000个．如果最后全部售完，则该超市应该购进多少个熊猫抱枕，才可以获得最大利润？最大利润是多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "520000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024贵州贵阳 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-12", "keyPointIds": "16438|16535|16544", "keyPointNames": "和差倍分问题|一次函数的图象和性质|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552661536839344128", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552661536839344128", "title": "2024年贵州省贵阳市观山湖区中考数学二模试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553371681894473728", "questionArticle": "<p>9．《九章算术》中记载：“今有大器五、小器一容三斛；大器一、小器五容二斛．问大、小器各容几何？”其大意是，今有大容器5个，小容器1个，总容量为3斛；大容器1个，小容器5个，总容量为2斛．问大容器、小容器的容积各是多少斛？设大容器的容积为<i>x</i>斛，小容器的容积为<i>y</i>斛，根据题意，可列方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>（斛：古量器名，容量单位）．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/29/2/1/0/0/0/561194111849504768/images/img_1.png\" style='vertical-align:middle;' width=\"131\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京35中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-12", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553371658842578944", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "553371658842578944", "title": "北京市第三十五中学2024−2025学年九年级下学期2月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780592161726464", "questionArticle": "<p>10．哈市欲购进甲、乙两种丁香进行绿化．若购进甲种 $ 2 $ 株，乙种 $ 3 $ 株，则共需成本 $ 170 $ 元；若购进甲种 $ 3 $ 株，乙种 $ 1 $ 株，则共需成本 $ 150 $ 元．</p><p>(1)求甲、乙两种丁香每株的价格分别为多少元?</p><p>(2)若购进的乙种丁香的株数比甲种丁香的 $ 3 $ 倍还多 $ 90 $ 株，购进两种丁香的总费用不超过 $ 15700 $ 元，求最多购进甲种丁香多少株?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南长沙 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-12", "keyPointIds": "16435|16486", "keyPointNames": "分配问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780568124170240", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780568124170240", "title": "湖南省长沙市一中双语实验学校2024−2025学年九年级下学期入学考试数学试题", "paperCategory": 1}, {"id": "429953494528860160", "title": "2023年黑龙江省哈尔滨市中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 178, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 178, "timestamp": "2025-07-01T02:21:56.392Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}