{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 167, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "554779475336011776", "questionArticle": "<p>1．一个两位数，十位上的数字与个位上的数字的和是10，把十位上的数字与个位上的数字对调后，得到的新数比原数大18，则原来的两位数是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南南阳 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-16", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779459481542656", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "554779459481542656", "title": "河南省南阳市2024—2025学年下学期多校联考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554779474593619968", "questionArticle": "<p>2．将一副三角板按如图所示的方式摆放在一起，且 $ \\angle 1 $ 比 $ \\angle 2 $ 大 $ 28{}\\degree  $ ，则 $ \\angle 1= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/19/2/1/0/0/0/557546662723362817/images/img_1.png\" style='vertical-align:middle;' width=\"123\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西临汾 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-16", "keyPointIds": "16424|16606", "keyPointNames": "加减消元法解二元一次方程组|角的运算", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562034021342945280", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "562034021342945280", "title": "山西省临汾市霍州市多校联考2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}, {"id": "554779459481542656", "title": "河南省南阳市2024—2025学年下学期多校联考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554779473213693952", "questionArticle": "<p>3．请写出一个关于 $ x,y $ 的二元一次方程<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南南阳 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-16", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779459481542656", "questionFeatureName": "开放性试题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "554779459481542656", "title": "河南省南阳市2024—2025学年下学期多校联考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554779468662874112", "questionArticle": "<p>4．已知关于<i>x</i>，<i>y</i>的二元一次方程 $ 3x-ky=7 $ 有一组解为 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ ，则<i>k</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）．</p><p>A．1　　　　B． $ -1 $　　　　C． $ -2 $　　　　D． $ -4 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南南阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-03-16", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779459481542656", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "554779459481542656", "title": "河南省南阳市2024—2025学年下学期多校联考七年级数学试题", "paperCategory": 1}, {"id": "562773223684743168", "title": "山西省临汾一中第一附属学校2024−2025 学年七年级下学期第一次月质量监测数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "554779466376978432", "questionArticle": "<p>5．下列方程组为二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=1 \\\\ xy=2 \\end{cases}  $　　　　B． $ \\begin{cases} x+\\dfrac { 1 } { y }=1 \\\\ x-y=2 \\end{cases}  $　　　　C． $ \\begin{cases} x+y=2 \\\\ x-z=3 \\end{cases}  $　　　　D． $ \\begin{cases} x+y=5 \\\\ 2x=6 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南南阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-16", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779459481542656", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "554779459481542656", "title": "河南省南阳市2024—2025学年下学期多校联考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "554779585222582272", "questionArticle": "<p>6．计算或解方程组：</p><p>(1) $ {\\left( { -1 } \\right) ^ {2026}}+\\left  | { 1-\\sqrt { 2 } } \\right  | +3\\dfrac { 1 } { 8 }\\times \\left ( { -\\dfrac { 8 } { 25 } } \\right )  $ ；</p><p>(2) $ \\begin{cases} 2x+y=4① \\\\ 3x+2y=10② \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南郑州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-16", "keyPointIds": "16254|16423|30400", "keyPointNames": "绝对值的定义及求一个数的绝对值|代入消元法解二元一次方程组|有理数的乘方", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779563273789440", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554779563273789440", "title": "河南省郑州市金水区2024−2025学年九年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555796953247817728", "questionArticle": "<p>7．定义：如果一个分式能化成一个整式与一个分子为常数的分式的和的形式，则称这个分式为和谐分式”.如：</p><p> $ \\dfrac { x+1 } { x-1 }=\\dfrac { x-1+2 } { x-1 }=\\dfrac { x-1 } { x-1 }+\\dfrac { 2 } { x-1 }=1+\\dfrac { 2 } { x-1 }, $ </p><p> $ \\dfrac { a{^{2}}-2a+3 } { a-1 }=\\dfrac { (a-1){^{2}}+2 } { a-1 }=a-1+\\dfrac { 2 } { a-1 }, $ </p><p>则 $ \\dfrac { x+1 } { x-1 } $ 和 $ \\dfrac { a{^{2}}-2a+3 } { a-1 } $ 都是“和谐分式”</p><p>(1)下列各式中，属于和谐分式”的是：<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>(填序号)：</p><p>① $ \\dfrac { x+1 } { x } $ ;② $ \\dfrac { 2+x } { 2 } $ ;③ $ \\dfrac { x+2 } { x+1 } $ ; $ {\\rm 4} \\dfrac { y{^{2}}+1 } { y{^{2}} } $ </p><p>(2)将和谐分式 $ \\dfrac { x{^{2}}-4x+7 } { x-2 } $ 化成一个整式与一个分子为常数的分式的和的形为： $ \\dfrac { x{^{2}}-4x+7 } { x-2 }= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p><p>(3)应用：已知方程组 $ \\begin{cases} x+my=11 \\\\ x+3m=2y \\end{cases}  $ 有正整数解，求整数 $ m $ 的值.</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏盐城 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16361|16424", "keyPointNames": "分式的基本性质|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555796927855501312", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555796927855501312", "title": "江苏省盐城市景山中学2024−2025学年八年级下学期3月月考数学试题", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "554779800478457856", "questionArticle": "<p>8．为了让市民树立起“珍惜水、保护水”的用水概念，某市从2017年6月起，居民生活用水按阶梯式水价计费，下表是该市居民“一户一表”生活用水计费价格表的部分信息：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>自来水销售价格</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>污水处理价格</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>每户每月用水量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>单价：元/吨</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>单价：元/吨</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>20吨及以下</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ a $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 0.8 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>超过20吨但不超过30吨的部分</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ b $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 0.8 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>超过30吨的部分</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 3.3 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 0.8 $ </p></td></tr></table><p>（说明：①每户产生的污水量等于该户自来水用水量；②水费=自来水费用+污水处理费用）</p><p>已知小李家2017年6月份用水20吨，交水费49元，7月份用水25吨，交水费65.4元．</p><p>(1)求表中<i>a</i>，<i>b</i>的值；</p><p>(2)小李家8月份的水费正好是小李家庭月收入的2%，已知小李家的月收入为 $ 8190 $ 元，试求小李家8月份的用水量．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈工大附中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779775455240192", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554779796426760192", "questionArticle": "<p>9．解方程：</p><p>(1) $ \\begin{cases} x-2y=1， \\\\ 3x+4y=23； \\end{cases}  $ </p><p>(2) $ \\begin{cases} 33x+17y=83， \\\\ 17x+33y=67； \\end{cases}  $ </p><p>(3) $ \\begin{cases} 3x+2y=5x+2， \\\\ 2\\left ( { 3x+2y } \\right ) =11x+7； \\end{cases}  $ </p><p>(4) $ \\begin{cases} 3x-y+z=4， \\\\ 2x+3y-z=12， \\\\ x+y+z=6. \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈工大附中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16423|16424|16425", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组|二元一次方程组的特殊解法", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779775455240192", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554779795629842432", "questionArticle": "<p>10．已知方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ 的解是 $ \\begin{cases} x=1 \\\\ y=4 \\end{cases}  $ ，则方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=2c{{}_{ 1 } }-b{{}_{ 1 } } \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=2c{{}_{ 2 } }-b{{}_{ 2 } } \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈工大附中 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779775455240192", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 168, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 168, "timestamp": "2025-07-01T02:20:43.854Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}