{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 81, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "577679949475131392", "questionArticle": "<p>1．对于二元一次方程组 $ \\begin{cases} \\cdot \\cdot \\cdot \\cdot \\cdot \\cdot \\cdot \\cdot \\cdot \\cdot \\cdot \\cdot ① \\\\ x-3y=7② \\end{cases}  $ ，将①式代入②式，消去<i>y</i>可以得 $ x-3x+6=7 $ ，则方程①是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ y=x-2 $ B． $ y=x+2 $ </p><p>C． $ x=2y-1 $ D． $ x=2y+1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北邢台 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679937009659904", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577679937009659904", "title": "河北省邢台市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577679944462938112", "questionArticle": "<p>2．下列方程是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x=3y $ B． $ x-3y $ </p><p>C． $ x+\\dfrac { 1 } { y }=-2 $ D． $ xy+y=-1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025河北邢台 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679937009659904", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577679937009659904", "title": "河北省邢台市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578014503696117760", "questionArticle": "<p>3．每年的3月14日是国际数学节．2025年3月，一零一数学“π”对再度开启，学生可以通过参加智慧类和思维类数学游戏，收集印章兑换奖品，游戏规则和兑奖规则如下图：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/15/2/1/0/0/0/578014319201263619/images/img_9.png\" style=\"vertical-align:middle;\" width=\"524\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）小宁想兑换一枚冰箱贴和一个钥匙扣，她至少要参加<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>次数学游戏．</p><p>（2）小华兑换冰箱贴和钥匙扣共用了34个智慧印章和21个思维印章，请问她兑换了多少个冰箱贴和多少个钥匙扣？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京市一零一中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578014491536830464", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578014491536830464", "title": "2025年北京市一零一中教育集团2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578014499875106816", "questionArticle": "<p>4．若 $ \\begin{cases} x=1 \\\\ y=3 \\end{cases}  $ 是关于 $ x $ 和 $ y $ 的二元一次方程 $ ax+y=1 $ 的解，则 $ a $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -3 $ B．2C． $ -2 $ D．3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京市一零一中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578014491536830464", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578014491536830464", "title": "2025年北京市一零一中教育集团2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578016910131900416", "questionArticle": "<p>5．甲、乙两名同学在解方程组 $ \\begin{cases} mx+y=5 \\\\ 4x-ny=20 \\end{cases}  $ 时，甲解题时看错了 $ m $ ，解得 $ \\begin{cases} x=\\dfrac { 7 } { 2 } \\\\ y=-2 \\end{cases}  $ ；乙解题时看错了 $ n $ ，解得 $ \\begin{cases} x=4 \\\\ y=-11 \\end{cases}  $ ．请你根据以上两种结果，求 $ 4m+3n $ 的平方根．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北十堰 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16287|16420", "keyPointNames": "平方根|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578016884513091584", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578016884513091584", "title": "湖北省十堰市2024—2025学年下学期七年级数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578016909330788352", "questionArticle": "<p>6．解下列方程及方程组：</p><p>（1） $ 16{\\left( { x+1 } \\right) ^ {2}}=49 $ ；</p><p>（2） $ \\begin{cases} x-2y=0 \\\\ 3x+2y=8 \\end{cases}  $ ；</p><p>（3） $ \\begin{cases} 5x+2y=8 \\\\ 4x-3y=-\\dfrac { 1 } { 2 } \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北十堰 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16287|16424", "keyPointNames": "平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578016884513091584", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578016884513091584", "title": "湖北省十堰市2024—2025学年下学期七年级数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578016902796062720", "questionArticle": "<p>7．已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} 2x+3y=k \\\\ 3x+2y=k+1 \\end{cases}  $ ，其中 $ x+y=7 $ ，则 $ k= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北十堰 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578016884513091584", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578016884513091584", "title": "湖北省十堰市2024—2025学年下学期七年级数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578016898643701760", "questionArticle": "<p>8．我国明代《算法统宗》一书中有这样一题：“一支竿子一条索，索比竿子长一托，对折索子来量竿，却比竿子短一托（一托按照5尺计算）．”大意是现有一根竿和一条绳索，如果用绳索去量竿，绳索比竿长5尺；如果将绳索对折后再去量竿，就比竿短5尺，则绳索长几尺？设竿长<i>x</i>尺，绳索长<i>y</i>尺，根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+5=y \\\\ x-5=\\dfrac { y } { 2 } \\end{cases}  $　　　　B． $ \\begin{cases} x+5=y \\\\ 2x-5=y \\end{cases}  $　　　　C． $ \\begin{cases} x=y+5 \\\\ x-5=\\dfrac { y } { 2 } \\end{cases}  $　　　　D． $ \\begin{cases} x+5=y \\\\ x-5=2y \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北十堰 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578016884513091584", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "578016884513091584", "title": "湖北省十堰市2024—2025学年下学期七年级数学期中试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578016628215951360", "questionArticle": "<p>9．下列是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x+2y=3 $ B． $ x{^{2}}+y=1 $ </p><p>C． $ y+\\dfrac { 1 } { x }=2 $ D． $ 2xy-1=5 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|210000|440000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025广东东莞 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 15, "referenceNum": 4, "createTime": "2025-05-15", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578016619181420544", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "578016619181420544", "title": "广东省东莞市2024−2025学年下学期七年级数学期中试题", "paperCategory": 1}, {"id": "519617830829989888", "title": "辽宁省沈阳市第一二六中学教育集团2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}, {"id": "522160159528886272", "title": "辽宁省沈阳市第一二六中学2024−2025学年八年级上学期期中考试数学试卷", "paperCategory": 1}, {"id": "174284793256910848", "title": "2022年七年级下册湘教版数学第一章1.1建立二元一次方程组课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "575477531974868992", "questionArticle": "<p>10．清明果是上饶的特色美食之一．某美食商铺推出了萝卜馅清明果和肉馅清明果．下表列出了小李、小艺在该美食商铺的购买数量（单位：个）和付款金额（单位：元）．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\"></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p style=\"text-align:center;\">萝卜馅清明果／个</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p style=\"text-align:center;\">肉馅清明果／个</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">付款金额／元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">小李</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p style=\"text-align:center;\">40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p style=\"text-align:center;\">10</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">85</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">小艺</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p style=\"text-align:center;\">20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 85.5pt;\"><p style=\"text-align:center;\">20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">80</p></td></tr></table><p>根据上表，求萝卜馅清明果和肉馅清明果的单价．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西上饶 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-15", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575477504149856256", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575477504149856256", "title": "江西省上饶市2025年九年级质量模拟检测数学试题卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 82, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 82, "timestamp": "2025-07-01T02:10:29.947Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}