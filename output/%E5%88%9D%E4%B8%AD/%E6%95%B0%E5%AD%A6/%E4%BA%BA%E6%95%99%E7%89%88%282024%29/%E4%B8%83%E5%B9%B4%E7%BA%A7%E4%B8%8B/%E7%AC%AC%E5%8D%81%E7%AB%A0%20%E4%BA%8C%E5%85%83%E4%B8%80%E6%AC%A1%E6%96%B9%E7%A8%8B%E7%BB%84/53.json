{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 52, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "581950877126144000", "questionArticle": "<p>1．国家“双减”政策实施后，某班开展了主题为“书香满校园”的读书活动．班级决定为在活动中表现突出的同学购买笔记本和碳素笔进行奖励（两种奖品都买），其中笔记本每本3元，碳素笔每支2元，共花费28元，则共有几种购买方案（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．5B．4C．3D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川内江 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950860290207744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950860290207744", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "581950875410673664", "questionArticle": "<p>2．在长方形 $ ABCD $ 中，放入5个形状大小相同的小长方形（空白部分），其中 $ AB=8{ \\rm{ c } }{ \\rm{ m } } $ ， $ BC=12{ \\rm{ c } }{ \\rm{ m } } $ ，则阴影部分图形的总面积为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;） $ { \\rm{ c } }{ \\rm{ m } }{^{2}} $ </p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/25/2/1/0/0/0/581950832377114629/images/img_7.png\" style=\"vertical-align:middle;\" width=\"142\" alt=\"试题资源网 https://stzy.com\"></p><p>A．27B．29C．34D．36</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川内江 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950860290207744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950860290207744", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "581950873829421056", "questionArticle": "<p>3．若 $ {\\left( { a+b+5 } \\right) ^ {2}}+\\left  | { 2a-b+1 } \\right  | =0， $ 则 $ {\\left( { b-a } \\right) ^ {2017}}= $ （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．−1C． $ 5{^{2017}} $ D． $ -5{^{2017}} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川内江 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16257|16424", "keyPointNames": "绝对值非负性的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950860290207744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950860290207744", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "581951502987599872", "questionArticle": "<p>4．南淝河，古称施水，长江流域巢湖的支流，是合肥的母亲河．为了确保河道畅通，现需要对一段河道进行清淤处理，清淤任务由两栖反铲式清淤机和小型链斗式清淤船进行．右表是工程队给出的两个工程预备方案，环保部门要求6天内必须完成任务．如果工程部门提供2台清淤机和2台清淤船，共同完成此项任务，那么能否按要求完成任务？</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>清淤机</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>清淤船</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>时间</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>方案一</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>1台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>2台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>8天</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>方案二</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>2台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>1台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>7天</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽蚌埠 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16431", "keyPointNames": "工程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581951476378935296", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "581951476378935296", "title": "2025年安徽省蚌埠市部分学校中考三模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581951249823604736", "questionArticle": "<p>5．为落实“垃圾分类”的环保理念，某学校同时购进绿色和灰色两种颜色的垃圾桶，若购进2个绿色垃圾桶和3个灰色垃圾桶共需340元；若购进3个绿色垃圾桶和2个灰色垃圾桶共需360元．</p><p>（1）求绿色垃圾桶和灰色垃圾桶每个进价分别为多少元？</p><p>（2）为创建垃圾分类示范学校，学校预计用不超过3500元的资金购入两种垃圾桶共计50个，且绿色垃圾桶数量不少于灰色垃圾桶数量的 $ 60\\% $ ，请求出共有几种购买方案？</p><p>（3）为落实垃圾分类的环保理念，县政府对学校采购垃圾桶进行补贴．每购买一个绿色垃圾桶和灰色垃圾桶，政府分别补贴<i>m</i>元和<i>n</i>元，如果（2）中所有购买方案补贴后的费用相同，求<i>m</i>与<i>n</i>之间的数量关系．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581951220270538752", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "581951220270538752", "title": "重庆市万州中学教育集团2024—2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581951248489816064", "questionArticle": "<p>6．甲、乙两人共同解关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} ax+5y=15①, \\\\ 4x-by=-2②, \\end{cases}  $ 由于甲看错方程①中的<i>a</i>，得到方程组的解为 $ \\begin{cases} x=-2, \\\\ y=6, \\end{cases}  $ 由于乙看错方程②中的<i>b</i>，得到方程组的解为 $ \\begin{cases} x=5, \\\\ y=2, \\end{cases}  $ 试计算 $ a{^{2014}}+{\\left( { -b } \\right) ^ {2015}} $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581951220270538752", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "581951220270538752", "title": "重庆市万州中学教育集团2024—2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581951246124228608", "questionArticle": "<p>7．解方程（组）</p><p>（1） $ \\dfrac { 5x+3 } { 4 }-\\dfrac { x-2 } { 2 }=1 $ ；</p><p>（2） $ \\begin{cases} 2x+y=5 \\\\ 8x+3y=21 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581951220270538752", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "581951220270538752", "title": "重庆市万州中学教育集团2024—2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581951243586674688", "questionArticle": "<p>8．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } }， \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ 的解为 $ \\begin{cases} x=2， \\\\ y=1， \\end{cases}  $ 则关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} a{{}_{ 1 } }\\left ( { x+3 } \\right ) +b{{}_{ 1 } }y-2b{{}_{ 1 } }=c{{}_{ 1 } }, \\\\ a{{}_{ 2 } }\\left ( { x+3 } \\right ) +b{{}_{ 2 } }y-2b{{}_{ 2 } }=c{{}_{ 2 } } \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581951220270538752", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "581951220270538752", "title": "重庆市万州中学教育集团2024—2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581951241929924608", "questionArticle": "<p>9．小明在超市帮妈妈买回一袋纸杯，他把纸杯整齐地叠放在一起，如图所示，请你根据图中的信息，若小明把60个纸杯整齐叠放在一起时，它的高度约是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ c } }{ \\rm{ m } } $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/07/2/1/0/0/0/586666357359423489/images/img_1.png\" style='vertical-align:middle;' width=\"252\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581951220270538752", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "581951220270538752", "title": "重庆市万州中学教育集团2024—2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581951234300485632", "questionArticle": "<p>10．用加减法解方程组 $ \\begin{cases} 2x+3y=1①, \\\\ 3x-6y=5② \\end{cases}  $ 时，消去<i>y</i>应为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ { \\rm{ ① } }\\times 2-{ \\rm{ ② } } $　　　　B． $ { \\rm{ ① } }\\times 3+{ \\rm{ ② } }\\times 2 $</p><p>C． $ { \\rm{ ① } }\\times 2+{ \\rm{ ② } } $　　　　D． $ { \\rm{ ① } }\\times 3-{ \\rm{ ② } }\\times 2 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581951220270538752", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "581951220270538752", "title": "重庆市万州中学教育集团2024—2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 53, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 53, "timestamp": "2025-07-01T02:07:04.192Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}