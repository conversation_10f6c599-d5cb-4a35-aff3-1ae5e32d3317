{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 115, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "570267582755807232", "questionArticle": "<p>1．解下列方程（组）：</p><p>（1） $ \\begin{cases} 2x+y=-5 \\\\ 4x-5y=11 \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} \\dfrac { y } { 3 }-\\dfrac { x+1 } { 6 }=3 \\\\ 2\\left ( { x-\\dfrac { y } { 2 } } \\right ) =3\\left ( { x+\\dfrac { y } { 18 } } \\right )  \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东淄博实验中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-24", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267563923382272", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267563923382272", "title": "山东省 淄博市张店区实验中学2024−2025学年七年级下学期期中考试数学试卷（五四制）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570267576468545536", "questionArticle": "<p>2．《九章算术》是我国古代数学的经典著作，书中有一个问题：“今有黄金九枚，白银一十一枚，称之重适等．交易其一，金轻十三两．问金、银一枚各重几何？”．意思是甲袋中装有黄金9枚（每枚黄金重量相同），乙袋中装有白银11枚（每枚白银重量相同），称重两袋相等．两袋互相交换1枚后，甲袋比乙袋轻了13两（袋子重量忽略不计）．问黄金、白银每枚各重多少两？设每枚黄金重<i>x</i>两，每枚白银重<i>y</i>两，根据题意得（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 11x=9y \\\\ （10y+x）-（8x+y）=13 \\end{cases}  $ </p><p>B． $ \\begin{cases} 10y+x=8x+y \\\\ 9x+13=11y \\end{cases}  $ </p><p>C． $ \\begin{cases} 9x=11y \\\\ （8x+y）-（10y+x）=13 \\end{cases}  $ </p><p>D． $ \\begin{cases} 9x=11y \\\\ （10y+x）-（8x+y）=13 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|510000|360000|110000|230000|410000|450000|420000|310000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东淄博实验中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 7, "referenceNum": 14, "createTime": "2025-04-24", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267563923382272", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267563923382272", "title": "山东省 淄博市张店区实验中学2024−2025学年七年级下学期期中考试数学试卷（五四制）", "paperCategory": 1}, {"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}, {"id": "527583381254610944", "title": "江西省吉安市2024−2025学年八年级上学期12月考数学试题", "paperCategory": 1}, {"id": "527897134026235904", "title": "江西省吉安市八校联考2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "455856157661372416", "title": "黑龙江省哈尔滨市萧红中学2023-2024学年七年级下学期月考数学试题", "paperCategory": 1}, {"id": "422534385352613888", "title": "广东省深圳市福田区红岭中学（红岭教育集团）2023-2024学年九年级下学期开学考试数学试题", "paperCategory": 1}, {"id": "446275918731976704", "title": "北京市朝阳外国语学校2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "478027557683634176", "title": "山东省德州市德城区德州市第五中学2023−2024学年八年级上学期开学数学试题", "paperCategory": 1}, {"id": "202444783062654976", "title": "广西壮族自治区钦州市钦北区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "202444309458624512", "title": "广东省江门市新会区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "174572081421524992", "title": "上海市浦东新区上海民办建平远翔学校2020-2021学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "141117274350460928", "title": "河南省信阳市淮滨县王店二中2019年3月份月考普通高中招生考试数学试题", "paperCategory": 1}, {"id": "164352896598843392", "title": "四川省乐山市犍为县2020年九年级毕业会考模拟数学试题", "paperCategory": 1}, {"id": "160695986565718016", "title": "湖北恩施白果中学2020年中考模拟数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570267573641584640", "questionArticle": "<p>3．用加减消元法解二元一次方程组 $ \\begin{cases} x+3y=4① \\\\ 2x-y=1② \\end{cases}  $ 时，下列方法中无法消元的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．①×2﹣②</p><p>B．②×（﹣3）﹣①</p><p>C．①×（﹣2）+②</p><p>D．①﹣②×3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|-1|410000|330000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2020浙江舟山 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 8, "referenceNum": 10, "createTime": "2025-04-24", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "129185125384888320", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "129185125384888320", "title": "浙江省舟山市2020年中考数学试题", "paperCategory": 1}, {"id": "202067089883963392", "title": "浙江省嘉兴市2020年中考数学试题", "paperCategory": 1}, {"id": "570267563923382272", "title": "山东省 淄博市张店区实验中学2024−2025学年七年级下学期期中考试数学试卷（五四制）", "paperCategory": 1}, {"id": "418221360302825472", "title": "山东省菏泽市2023-2024学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "425633431206273024", "title": "2023-2024学年七年级下册人教版数学第八章8.2消元——解二元一次方程组（2）课时练习", "paperCategory": 1}, {"id": "202423362550800384", "title": "河南省三门峡市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "174285162338885632", "title": "2022年七年级下册湘教版数学第一章1.2二元一次方程组的解法课时练习", "paperCategory": 1}, {"id": "147792461103931392", "title": "山东省枣庄市第十五中学2021-2022学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "220489988512718848", "title": "河南省南阳市方城县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "209613275977261056", "title": "广东省汕头市龙湖区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570267692428468224", "questionArticle": "<p>4．规定：若 $ P\\left ( { x,y } \\right )  $ 是以 $ x,y $ 为未知数的二元一次方程 $ ax+by=c $ 的整数解，则称此时点 $ P $ 为二元一次方程 $ ax+by=c $ 的“理想点”．请回答以下关于 $ x,y $ 的二元一次方程的相关问题．</p><p>(1)已知 $ A\\left ( { -2,2 } \\right ) ,B\\left ( { 2,-1 } \\right ) ,C\\left ( { 3,-2 } \\right )  $ ，请问哪些点是方程 $ 3x+y=5 $ 的“理想点”？哪些点不是方程 $ 3x+y=5 $ 的“理想点”？并说明理由；</p><p>(2)已知 $ m,n $ 为非负整数，且 $ 2\\sqrt { m }+\\left  | { n } \\right  | =5 $ ，若 $ P\\left ( { \\sqrt { m },\\left  | { n } \\right  |  } \\right )  $ 是方程 $ x+2y=4 $ 的“理想点”，求 $ 2m+n $ 的平方根；</p><p>(3)已知 $ k $ 是正整数，且 $ P\\left ( { x,y } \\right )  $ 是方程 $ 2x+y=2 $ 和 $ kx+2y=6 $ 的“理想点”，求点 $ P $ 的坐标．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|450000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川泸州高中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 3, "createTime": "2025-04-24", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267665626865664", "questionFeatureName": "阅读材料题|新定义问题", "questionMethodName": "分类讨论思想", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267665626865664", "title": "四川省泸州高级中学校2024—2025学年下学期期中考试七年级数学试题", "paperCategory": 1}, {"id": "450071323353587712", "title": "湖南省长沙市长郡教育集团2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "493852063371141120", "title": "广西南宁市青秀区三美学校2024−2025学年八年级上学期开学数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570267687349166080", "questionArticle": "<p>5．解方程组 $ \\begin{cases} 2x+y=1 \\\\ 8x+3y=9 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000|510000|110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川泸州高中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 5, "referenceNum": 3, "createTime": "2025-04-24", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267665626865664", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267665626865664", "title": "四川省泸州高级中学校2024—2025学年下学期期中考试七年级数学试题", "paperCategory": 1}, {"id": "196634114262343680", "title": "北京市第一六六中学2021-2022学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "209705952408281088", "title": "江西省宜春市高安市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569342456757526528", "questionArticle": "<p>6．“人间烟火气，最抚凡人心．”在这喧嚣的世界里，地摊的存在，让人们感受到了那份朴实无华的温暖，也让城市多了一份生活的温度，某个体户购买了腊梅，百合两种鲜花摆摊销售，若购进腊梅5束，百合3束，需要114元；若购进腊梅8束，百合6束，需要204元．</p><p>(1)求腊梅，百合两种鲜花的进价分别是每束多少元？</p><p>(2)若每束腊梅的售价为20元，每束百合的售价为30元．结合市场需求，该个体户决定购进两种鲜花共80束，计划购买成本不超过1260元，且购进百合的数量不少于腊梅数量的 $ \\dfrac { 2 } { 3 } $ ，两种鲜花全部销售完时，求销售的最大利润及相应的进货方案．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川德阳 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 3, "createTime": "2025-04-24", "keyPointIds": "16435|16490|16544", "keyPointNames": "分配问题|一元一次不等式组的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569342430559903744", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569342430559903744", "title": "2025年四川省德阳市九年级中考一模数学试题", "paperCategory": 1}, {"id": "450068402620637184", "title": "广东省深圳市龙岗区外国语学校2023-2024学年八年级下学期期中数学试题", "paperCategory": 1}, {"id": "448976097511776256", "title": "广东省深圳市福田区外国语学校2023-2024学年八年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570065332552900608", "questionArticle": "<p>7．为提升学生的劳动意识，某校组织植树活动，已知在甲处植树的有23人，在乙处植树的有17人．现调20人去支援，使甲处植树的人数是乙处植树人数的2倍，问应调往甲、乙两处各多少人？若设应调往甲处<i>x</i>人，乙处<i>y</i>人，则下列方程（组）中，与题意不符的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 23+x=2\\left ( { 17+20-x } \\right )  $</p><p>B． $ 23+20-y=2\\left ( { 17+y } \\right )  $</p><p>C． $ \\begin{cases} x=20-y \\\\ 23+x=2\\left ( { 17+y } \\right )  \\end{cases}  $</p><p>D． $ \\begin{cases} x+y=20 \\\\ 23+x=2\\left ( { 17-y } \\right )  \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江湖州 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-23", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570065320481693696", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570065320481693696", "title": "2025年浙江省湖州市九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570065223064788992", "questionArticle": "<p>8．我国古代数学著作《九章算术》卷七“盈不足”篇中记载了这样一个问题：“今有人共买物，人出八，盈三；人出七，不足四．问人数，物价各几何？”大意是，现有几人共同购买一件物品，如果每人出8钱，就会多出3钱；如果每人出7钱，那么还差4钱，问共有多少人？这件物品价格是多少？设共有 $ x $ 人，物品价格是 $ y $ 钱，则可以列出的方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 8x-y=3 \\\\ y-7x=4 \\end{cases}  $</p><p>B． $ \\begin{cases} 8x-y=3 \\\\ x-7y=4 \\end{cases}  $</p><p>C． $ \\begin{cases} x-8y=3 \\\\ y-7x=4 \\end{cases}  $</p><p>D． $ \\begin{cases} y-8x=3 \\\\ 7x-y=4 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津西青 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-23", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570065208745435136", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570065208745435136", "title": "2025年天津市西青区中考数学一模试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570065103141249024", "questionArticle": "<p>9．方程组 $ \\begin{cases} x+y=2 \\\\ 3x+2y=3 \\end{cases}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $</p><p>B． $ \\begin{cases} x=1 \\\\ y=0 \\end{cases}  $</p><p>C． $ \\begin{cases} x=-1 \\\\ y=3 \\end{cases}  $</p><p>D． $ \\begin{cases} x=3 \\\\ y=-1 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津红桥 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-23", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570065090382176256", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570065090382176256", "title": "2025年天津市红桥区中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570064970869678080", "questionArticle": "<p>10．我国明代《算法统宗》一书中有这样一题：“一支竿子一条索，索比竿子长一托，对折索子来量竿，却比竿子短一托（一托按照 $ 5 $ 尺计算）．”大意是，现有一根竿和一条绳索，如果用绳索去量竿，绳索比竿长 $ 5 $ 尺；如果将绳索对折后再去量竿，就比竿短 $ 5 $ 尺，问竿子、绳索各多少尺？设竿长 $ x $ 尺，绳索长 $ y $ 尺，根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=y+5 \\\\ x-5=\\dfrac { y } { 2 } \\end{cases}  $</p><p>B． $ \\begin{cases} x=y+5 \\\\ x-5=2y \\end{cases}  $</p><p>C． $ \\begin{cases} x+5=y \\\\ x-5=\\dfrac { y } { 2 } \\end{cases}  $</p><p>D． $ \\begin{cases} x+5=y \\\\ 2x-5=y \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000|120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁盘锦 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-04-23", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591001611310247936", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "591001611310247936", "title": "2025年辽宁省盘锦市辽河油田教育集团中考二模数学试题", "paperCategory": 1}, {"id": "570064954176348160", "title": "2025年天津市部分区九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 116, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 116, "timestamp": "2025-07-01T02:14:29.836Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}