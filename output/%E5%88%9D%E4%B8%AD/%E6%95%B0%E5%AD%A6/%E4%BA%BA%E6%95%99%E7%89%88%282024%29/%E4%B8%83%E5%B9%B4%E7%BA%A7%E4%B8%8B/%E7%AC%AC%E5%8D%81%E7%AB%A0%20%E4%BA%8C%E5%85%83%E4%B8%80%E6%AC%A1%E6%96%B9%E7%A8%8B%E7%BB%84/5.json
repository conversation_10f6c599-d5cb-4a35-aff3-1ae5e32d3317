{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 4, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "589572545667444736", "questionArticle": "<p>1．解下列方程组：</p><p>（1） $ \\begin{cases} y=x+3 \\\\ 7x+5y=9 \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} 3s-t=5 \\\\ 5s+2t=15 \\end{cases}  $ ；</p><p>（3） $ \\begin{cases} 2x+3y=-5 \\\\ 3x-4y=18 \\end{cases}  $ ；</p><p>（4） $ \\begin{cases} \\dfrac { 3(x-y) } { 2 }+\\dfrac { y } { 4 }=1 \\\\ 2(x+2y)=5(x+y)+5 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市第七中学 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589572523492159488", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "589572523492159488", "title": "天津市第七中学2024-2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589572539921248256", "questionArticle": "<p>2．我国古代数学名著《九章算术》中记载：“今有共买物，人出八，盈三；人出七，不足四．问人数、物价各几何？”大意是：现在有数人一起去买某物品，如果每人出8钱，则多了3钱；如果每人出7钱，则少了4钱．问共有多少人，物品的价格是多少钱？若设人数共有 $ x $ 人，物品的价格为 $ y $ 钱，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 8x+3=y \\\\ 7x-4=y \\end{cases}  $ B． $ \\begin{cases} 8x+3=y \\\\ 7x+4=y \\end{cases}  $ C． $ \\begin{cases} 8x-3=y \\\\ 7x-4=y \\end{cases}  $ D． $ \\begin{cases} 8x-3=y \\\\ 7x+4=y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市第七中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589572523492159488", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "589572523492159488", "title": "天津市第七中学2024-2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592870420816109568", "questionArticle": "<p>3．某校为丰富学生的校园生活，准备从某体育用品商店一次性购买若干个足球和篮球（每个足球的价格相同，每个篮球的价格相同），若购买3个足球和2个篮球共需210元，购买2个足球和1个篮球共需130元．</p><p>（1）购买一个足球，一个篮球各需多少元？</p><p>（2）根据学校的实际情况，需从该体育用品商店一次性购买足球和篮球共60个，要求购买足球和篮球的总费用不超过2650元，这所中学最多可以购买多少个足球？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025新疆乌鲁木齐一中 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870398695350272", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "592870398695350272", "title": "新疆维吾尔自治区乌鲁木齐市第一中学2024−2025学年七年级下学期数学期末素养调查模拟卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592870409755729920", "questionArticle": "<p>4．小亮的妈妈用30元钱买了甲、乙两种水果，甲种水果每千克3元，乙种水果每千克5元，且乙种水果比甲种水果少买了2千克，求小亮妈妈两种水果各买了多少千克？设小亮妈妈买了甲种水果<i>x</i>千克，乙种水果<i>y</i>千克，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3x+5y=30 \\\\ x=y-2 \\end{cases}  $ B． $ \\begin{cases} 3x+5y=30 \\\\ x=y+2 \\end{cases}  $ C． $ \\begin{cases} 5x+3y=30 \\\\ x=y-2 \\end{cases}  $ D． $ \\begin{cases} 5x+3y=30 \\\\ x=y+2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025新疆乌鲁木齐一中 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870398695350272", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "592870398695350272", "title": "新疆维吾尔自治区乌鲁木齐市第一中学2024−2025学年七年级下学期数学期末素养调查模拟卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592870412721102848", "questionArticle": "<p>5．如图，一个长方形图案是由8个大小相同的小长方形拼成，宽为60cm，设每个小长方形的长为 $ x {\\rm cm} $ ，宽为 $ y {\\rm cm} $ ，可列方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/25/2/1/0/0/0/592870361449934857/images/img_9.png\" style=\"vertical-align:middle;\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025新疆乌鲁木齐一中 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870398695350272", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "592870398695350272", "title": "新疆维吾尔自治区乌鲁木齐市第一中学2024−2025学年七年级下学期数学期末素养调查模拟卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592870408438718464", "questionArticle": "<p>6．若 $ {\\rm (\\mathit{x}＋\\mathit{y}－5)^{2}＋} $ |2<i>x</i>−3<i>y</i>−10|＝0，则（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=3 \\\\ y=2 \\end{cases}  $ B． $ \\begin{cases} x=2 \\\\ y=3 \\end{cases}  $ C． $ \\begin{cases} x=5 \\\\ y=0 \\end{cases}  $ D． $ \\begin{cases} x=0 \\\\ y=5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025新疆乌鲁木齐一中 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16257|16424", "keyPointNames": "绝对值非负性的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870398695350272", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "592870398695350272", "title": "新疆维吾尔自治区乌鲁木齐市第一中学2024−2025学年七年级下学期数学期末素养调查模拟卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590998887436951552", "questionArticle": "<p>7．为了响应习主席提出的“足球进校园”的号召，某中学开设了“足球大课间活动”，该校从商店购买了 <i>A</i> 种品牌的足球 50 个， <i>B</i> 种品牌的足球 25 个，共花费 4500 元，已知 <i>B</i> 种品牌足球的单价比 <i>A</i> 种品牌足球的单价高30 元．</p><p>（1）求 <i>A</i>、 <i>B</i> 两种品牌足球的单价各多少元？</p><p>（2）根据需要，学校决定再次购进 <i>A</i>、 <i>B</i> 两种品牌的足球 50 个，正逢体育用品商店“优惠促销”活动， <i>A</i> 种品牌的足球单价优惠 4 元， <i>B</i> 种品牌的足球单价打 8 折．如果此次学校购买 <i>A</i>、 <i>B</i> 两种品牌足球的总费用不超过2750 元，且购买 <i>B</i> 种品牌的足球不少于 23 个，则有几种购买方案？为了节约资金，学校应选择哪种方案？该方案的购进费用为多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南周南 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998860165586944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590998860165586944", "title": "湖南省长沙市周南教育集团2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592868445013712896", "questionArticle": "<p>8．随着人们生活水平的提高，很多家庭都购置了小汽车．大多数小汽车是前轮驱动和转向的，所以前轮的磨损程度比后轮严重．某汽车前轮轮胎在行驶6万公里时报废，后轮轮胎在行驶8万公里时报废，每个新轮胎报废时的总磨损量为1．（轮胎的磨损量等于汽车行驶的单位路程的磨损量乘以汽车行驶的路程）</p><p>（1）若每个新轮胎报废时的总磨损量为1，则安装在前轮的轮胎每行驶1万公里的磨损量为_；</p><p>（2）若在轮胎使用寿命内只交换一次前、后轮轮胎，请问行驶多少万公里后交换前后轮继续行驶，可使两对轮胎同时报废，并判断报废时行驶里程是否达到 $ 6.8 $ 万公里．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京首师大附中 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592868410389733376", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592868410389733376", "title": "北京市首都师范大学附属中学九年级中考三模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590998883284590592", "questionArticle": "<p>9．解下列方程组：</p><p>（1） $ \\begin{cases} x=2y \\\\ 2x+y=4 \\end{cases}  $ ．</p><p>（2） $ \\begin{cases} 2x+3y=2 \\\\ 2x-6y=-1 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南周南 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998860165586944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590998860165586944", "title": "湖南省长沙市周南教育集团2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590998879224504320", "questionArticle": "<p>10．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} ax-y=4 \\\\ 3x+by=4 \\end{cases}  $ 的解为 $ \\begin{cases} x=2 \\\\ y=-2 \\end{cases}  $ ，则 $ a+3b $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南周南 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-28", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998860165586944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590998860165586944", "title": "湖南省长沙市周南教育集团2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 5, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 5, "timestamp": "2025-07-01T02:01:20.847Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}