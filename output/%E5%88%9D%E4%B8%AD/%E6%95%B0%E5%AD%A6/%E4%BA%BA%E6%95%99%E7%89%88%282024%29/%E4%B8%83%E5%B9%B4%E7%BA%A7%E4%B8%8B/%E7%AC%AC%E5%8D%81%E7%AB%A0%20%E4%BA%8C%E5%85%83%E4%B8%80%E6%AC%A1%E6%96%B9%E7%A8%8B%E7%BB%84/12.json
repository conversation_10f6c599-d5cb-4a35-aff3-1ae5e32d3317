{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 11, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "590998988280606720", "questionArticle": "<p>1．某蔬菜公司收购到某种蔬菜140吨，准备加工后上市销售．该公司的加工能力是：每天可以精加工6吨或者粗加工16吨． 现计划用15天完成加工任务．</p><p>（1）该公司应安排几天精加工，几天粗加工，才能按期完成任务？</p><p>（2）如果每吨蔬菜粗加工后的利润为1000元，精加工后为2000元，那么照此安排，该公司出售这些加工后的蔬菜共获利多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春市第二实验中学 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16431", "keyPointNames": "工程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998965379706880", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590998965379706880", "title": "吉林省第二实验学校（高新、朝阳校区）2024−2025学年七年级下学期第三次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590998985050992640", "questionArticle": "<p>2．（1）解方程组： $ \\begin{cases} 3x-4y=10 \\\\ 5x+6y=42 \\end{cases}  $ </p><p>（2）解不等式： $ \\dfrac { x-1 } { 3 }-\\dfrac { x+4 } { 2 } &gt; -2 $ ，并在数轴上表示出不等式的解集．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/19/2/1/0/0/0/590998946681495553/images/img_27.png\" style=\"vertical-align:middle;\" width=\"360\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春市第二实验中学 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16424|16485|28266", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式|在数轴上表示不等式的解集", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998965379706880", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590998965379706880", "title": "吉林省第二实验学校（高新、朝阳校区）2024−2025学年七年级下学期第三次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590998160379523072", "questionArticle": "<p>3．解方程组： $ \\begin{cases} 2x-y=1 \\\\ 3x+2y=5 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江温州 · 临考冲刺", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998130285391872", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590998130285391872", "title": "2025年浙江省温州市初中数学学业水平考试适应性卷（三）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593323595742355456", "questionArticle": "<p>4．某网店销售甲、乙两种羽毛球，已知甲种羽毛球每筒的售价比乙种羽毛球多15元，王老师从该网店购买了2筒甲种羽毛球和3筒乙种羽毛球，共花费255元．</p><p>（1）该网店甲、乙两种羽毛球每筒的售价各是多少元？</p><p>（2）根据消费者需求，该网店决定用不超过8780元购进甲、乙两种羽毛球共200筒，且甲种羽毛球的数量大于乙种羽毛球数量的 $ \\dfrac { 3 } { 5 } $ ．已知甲种羽毛球每筒的进价为50元，乙种羽毛球每筒的进价为40元．若设购进甲种羽毛球<i>m</i>筒，则该网店有哪几种进货方案？若所购进羽毛球均可全部售出，请求出网店所获最大利润是多少？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆万州第二中学 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593323568928169984", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "593323568928169984", "title": "重庆市重庆市万州第二高级中学2024−2025学年七年级下学期期末数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593323591787126784", "questionArticle": "<p>5．（1）解下列方程（组）：</p><p>① $ \\dfrac { 5-x } { 2 }=\\dfrac { 2x+4 } { 3 } $ ；                        </p><p>② $ \\begin{cases} 2x+3y=9 \\\\ 2x-y=5 \\end{cases}  $ ．</p><p>（2）解不等式组 $ \\begin{cases} 4x\\leqslant  3\\left ( { x+1 } \\right )  \\\\ 2x-\\dfrac { x-1 } { 3 } > \\dfrac { 2x-5 } { 3 } \\end{cases}  $ ，将其解集在数轴上表示出来，并写出这个不等式组的最小整数解．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/27/2/1/0/0/0/593778979456200705/images/img_1.png\" style='vertical-align:middle;' width=\"262\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆万州第二中学 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16402|16424|16489", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593323568928169984", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "593323568928169984", "title": "重庆市重庆市万州第二高级中学2024−2025学年七年级下学期期末数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593323582945533952", "questionArticle": "<p>6．《九章算术》是中国古代重要的数学著作，其中“方程术”记载：今有甲乙二人持钱不知其数．甲得乙半而钱五十，乙得甲太半而亦钱五十，问甲、乙持钱各几何？其大意为：甲、乙两人各有若干钱，如果甲得到乙所有钱的一半，那么甲共有50钱；如果乙得到甲所有钱的三分之二，那么乙也共有50钱．问甲、乙两人各有多少钱？设甲、乙分别有<i>x</i>、<i>y</i>钱，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2x+y=50 \\\\ x+\\dfrac { 3 } { 2 }y=50 \\end{cases}  $ B． $ \\begin{cases} \\dfrac { 3 } { 2 }x+y=50 \\\\ x+2y=50 \\end{cases}  $ C． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=50 \\\\ \\dfrac { 2 } { 3 }x+y=50 \\end{cases}  $ D． $ \\begin{cases} x+\\dfrac { 2 } { 3 }y=50 \\\\ \\dfrac { 1 } { 2 }x+y=50 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆万州第二中学 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593323568928169984", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "593323568928169984", "title": "重庆市重庆市万州第二高级中学2024−2025学年七年级下学期期末数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "593321964548173824", "questionArticle": "<p>7．如图是由长方形和三角形组合而成的广告牌，重叠部分面积是 $ { { 4 } }{ \\rm{ m } }{^{{ { 2 } }}} $ ，整个广告牌所占面积是 $ { { 3 } }0{ \\rm{ m } }{^{{ { 2 } }}} $ ，除重叠部分外，长方形剩余部分面积比三角形剩余部分的面积多 $ { { 2 } }{ \\rm{ m } }{^{{ { 2 } }}} $ ．设长方形的面积为 $ x { \\rm{ m } }{^{{ { 2 } }}} $ ，三角形面积为 $ y\\,{ \\rm{ m } }{^{{ { 2 } }}} $ ，则根据题意可列二元一次方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/30/2/1/0/0/0/594869349250277376/images/img_1.png\" style='vertical-align:middle;' width=\"147\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} x+y-4=30 \\\\ \\left ( { x-4 } \\right ) -\\left ( { y-4 } \\right ) =2 \\end{cases}  $</p><p>B． $ \\begin{cases} x+y+4=30 \\\\ \\left ( { x-4 } \\right ) -\\left ( { y-4 } \\right ) =2 \\end{cases}  $</p><p>C． $ \\begin{cases} x+y-4=30 \\\\ \\left ( { y-4 } \\right ) -\\left ( { x-4 } \\right ) =2 \\end{cases}  $</p><p>D． $ \\begin{cases} x-y+4=30 \\\\ x-y=2 \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东执信中学 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-06-26", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593321950644056064", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "593321950644056064", "title": "广东省广州市执信中学2024−2025学年中考数学二模试卷", "paperCategory": 1}, {"id": "140857356221456384", "title": "河北省石家庄市长安区2018年中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "593322601306431488", "questionArticle": "<p>8．某超市准备购进<i>A</i>，<i>B</i>两种商品，进3件<i>A</i>，4件<i>B</i>需要270元；进5件<i>A</i>，2件<i>B</i>需要310元．</p><p>（1） $ \\mathrm{ A } $ 种商品每件的进价和 $ B $ 种商品每件的进价各是多少元？</p><p>（2）超市计划用不超过1560元的资金购进 $ \\mathrm{ A } $ ， $ B $ 两种商品共40件，其中 $ \\mathrm{ A } $ 种商品的数量不低于 $ B $ 种商品数量的一半，该超市有几种进货方案？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏钟英中学 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-06-26", "keyPointIds": "16424|16438|16486", "keyPointNames": "加减消元法解二元一次方程组|和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593322575096225792", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "593322575096225792", "title": "江苏省南京秦淮区钟英中学2024—2025学年下学期期末考试七年级数学模拟试题", "paperCategory": 1}, {"id": "424698918628270080", "title": "河南省洛阳市偃师市实验中学2022-2023学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592872822638161920", "questionArticle": "<p>9．一家商店进行装修，若请甲、乙两个装修组同时施工，8天可以完成，需付给两组费用共 $ 3520 $ 元；若先请甲组单独做6天，再请乙组单独做 $ 12 $ 天可以完成，需付给两组费用共 $ 3480 $ 元，问：</p><p>（1）甲、乙两组单独工作一天，商店应各付多少元？</p><p>（2）已知甲组单独完成需要 $ 12 $ 天，乙组单独完成需要 $ 24 $ 天，若装修完后，商店每天可盈利 $ 200 $ 元，你认为如何安排施工有利于商店经营？说说你的理由．（提示：三种施工方式：方式一甲单独完成；方式二乙组单独完成；方式三甲、乙两个装修组同时施工．）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024江苏扬州 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16431", "keyPointNames": "工程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592872794754428928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592872794754428928", "title": "江苏省扬州苏东坡中学2023−2024学年下学期七年级数学期末考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592872815847583744", "questionArticle": "<p>10．若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 3x-ay=16 \\\\ 2x+by=15 \\end{cases}  $ 的解是 $ \\begin{cases} x=7 \\\\ y=1 \\end{cases}  $ ，则方程组 $ \\begin{cases} 3\\left ( { x+2 } \\right ) -a\\left ( { y-1 } \\right ) =16 \\\\ 2\\left ( { x+2 } \\right ) +b\\left ( { y-1 } \\right ) =15 \\end{cases}  $ 的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏扬州 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592872794754428928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592872794754428928", "title": "江苏省扬州苏东坡中学2023−2024学年下学期七年级数学期末考试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 12, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 12, "timestamp": "2025-07-01T02:02:11.720Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}