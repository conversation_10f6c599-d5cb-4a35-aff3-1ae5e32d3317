{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 0, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "593318194556743680", "questionArticle": "<p>1．近期，我国国产动画电影“哪吒2魔童闹海”票房突破了90亿，商家推出<i>A</i>、<i>B</i>两种类型的哪吒纪念娃娃．已知购进4件<i>A</i>种娃娃和购进5件<i>B</i>种娃娃的费用相同；每个<i>A</i>种娃娃的进价比每个<i>B</i>种娃娃的进价多2元，且<i>A</i>种娃娃售价为15元/个，<i>B</i>种娃娃售价为10元/个．</p><p>（1）每个<i>A</i>种娃娃和每个<i>B</i>种娃娃的进价分别是多少元？</p><p>（2）根据网上预约的情况，该商家计划用不超过1700元的资金购进<i>A</i>、<i>B</i>两种娃娃共200个，若这200个娃娃全部售完，选择哪种进货方案，商家获利最大？最大利润是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南衡阳 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-07-01", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584853066295324672", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "584853066295324672", "title": "2025年湖南省衡阳市二模数学试题", "paperCategory": 1}, {"id": "567100015757271040", "title": "陕西省西安市西光中学2024−2025学年八年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593318182737195008", "questionArticle": "<p>2．我国古典数学文献《增删算法统宗，六均输》中有一个“隔沟计算”的问题：“甲乙隔沟牧放，二人暗里参详，甲云得乙九只羊，多乙一倍之上，乙说得甲九只，两家之数相当，二人闲坐恼心肠，画地算了半晌”其大意为：甲、乙两人一起放牧，两人心里暗中数羊，如果乙给甲9只羊，那么甲的羊数为乙的2倍；如果甲给乙9只羊，那么两人的羊数相同，请问甲，乙各有多少只羊？设甲有羊<i>x</i>只，乙有羊<i>y</i>只，根据题意列方程组正确的为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2x+9=y-9 \\\\ x-9=2y+9 \\end{cases}  $ B． $ \\begin{cases} x+9=2y-9 \\\\ 2x-9=y+9 \\end{cases}  $ </p><p>C． $ \\begin{cases} 2\\left ( { x+9 } \\right ) =y-9 \\\\ x-9=y+9 \\end{cases}  $ D． $ \\begin{cases} x+9=2\\left ( { y-9 } \\right )  \\\\ x-9=y+9 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2023新疆乌鲁木齐 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-07-01", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "343111195530928128", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "343111195530928128", "title": "新疆维吾尔自治区乌鲁木齐市天山区第一中学2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "593317735444033536", "questionArticle": "<p>3．当下电子产品更新换代速度加快，废旧智能手机数量不断增加．科学处理废旧智能手机，既可减少环境污染，还可回收其中的可利用资源．据研究，从每吨废旧智能手机中能提炼出的白银比黄金多 $ 760 $ 克．已知从 $ 2.5 $ 吨废旧智能手机中提炼出的黄金，与从 $ 0.6 $ 吨废旧智能手机中提炼出的白银克数相等．求从每吨废旧智能手机中能提炼出黄金与白银各多少克．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/26/2/1/0/0/0/593317694381793280/images/img_16.jpg\" style=\"vertical-align:middle;\" width=\"132\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|460000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 2, "createTime": "2025-07-01", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "460510027415068672", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "460510027415068672", "title": "2024年山西省中考数学真题", "paperCategory": 1}, {"id": "492112380719046656", "title": "广东省惠州市河南岸中学2024−2025学年九年级开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593317519181520896", "questionArticle": "<p>4．南宁是中国著名的水果产地，盛产香蕉和芒果．某水果商准备收购一批香蕉和芒果，运往外地销售．已知 $ 2 $ 吨香蕉和 $ 3 $ 吨芒果的收购成本为 $ 2.8 $ 万元； $ 4 $ 吨香蕉和 $ 1 $ 吨芒果的收购成本为 $ 2.6 $ 万元．</p><p>（1）每吨香蕉和每吨芒果的收购成本各是多少万元？</p><p>（2）该水果商计划租用货车运输水果，货车公司规定：若运输总重量不超过 $ 6 $ 吨，每吨运费 $ 120 $ 元；若超过 $ 6 $ 吨，超过部分每吨运费 $ 180 $ 元．水果商希望运费不超过 $ 990 $ 元，那么他最多能收购并运输多少吨水果？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广西南宁二中 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593317488915423232", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "593317488915423232", "title": "2025年广西南宁市第二中学中考三模数学测试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592871349120442368", "questionArticle": "<p>5．为了拓宽学生视野，某校计划组织900名师生开展以“追寻红色足迹，传承红色精神”为主题的研学活动．某旅游公司有 $ A，B $ 两种型号的客车可以租用，已知1辆 $ \\mathrm{ A } $ 型车和1辆 $ B $ 型车可以载乘客85人，3辆 $ \\mathrm{ A } $ 型车和2辆 $ B $ 型车可以载乘客210人．</p><p>（1）一辆 $ \\mathrm{ A } $ 型客车和一辆 $ B $ 型客车分别可以载乘客多少人？</p><p>（2）若租用 $ \\mathrm{ A } $ 型客车和 $ B $ 型客车（两种都租）刚好能装载这900名师生，请求出所有的租车方案？</p><p>（3）该校计划租用 $ A，B $ 两种型号的客车共22辆，其中 $ \\mathrm{ A } $ 型客车数量的一半不少于 $ B $ 型客车的数量，共有多少种租车方案？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川宜宾 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16434|16438|16490", "keyPointNames": "方案问题|和差倍分问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871319680622592", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592871319680622592", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期末模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592871338387218432", "questionArticle": "<p>6．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} ax+3y=12 \\\\ x-3y=0 \\end{cases}  $ 的解都为整数，且关于<i>x</i>的不等式组 $ \\begin{cases} 2\\left ( { x+1 } \\right )   &lt;  x+5 \\\\ 4x &gt; a-5 \\end{cases}  $ ，恰有3个整数解，则所有满足条件的整数<i>a</i>的和为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．10B．8C．6D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川宜宾 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871319680622592", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592871319680622592", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期末模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592871346041823232", "questionArticle": "<p>7．解下列方程（组）</p><p>（1） $ \\dfrac { x-1 } { 2 }-\\dfrac { x+1 } { 6 }=1 $ ；</p><p>（2） $ \\begin{cases} x+y=3① \\\\ 2x+3y=8② \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川宜宾 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871319680622592", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592871319680622592", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期末模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592871335442817024", "questionArticle": "<p>8．若关于<i>x</i>和<i>y</i>的方程组 $ \\begin{cases} 2x+y=3k+7 \\\\ x-y=k-1 \\end{cases}  $ 的解满足方程 $ x+2y=-2 $ ，则<i>k</i>为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）．</p><p>A． $ -5 $ B．5C． $ -\\dfrac { 1 } { 5 } $ D． $ \\dfrac { 1 } { 5 } $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川宜宾 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871319680622592", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592871319680622592", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期末模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592871742890094592", "questionArticle": "<p>9．有 $ \\mathrm{ A } $ 、 $ B $ 两种型号呼吸机，若购买 $ 6 $ 台 $ \\mathrm{ A } $ 型呼吸机和 $ 2 $ 台 $ B $ 型呼吸机共需 $ 12 $ 万元．若购买 $ 3 $ 台 $ \\mathrm{ A } $ 型呼吸机和 $ 5 $ 台 $ B $ 型呼吸机共需 $ 10.8 $ 万元．</p><p>（1）求 $ \\mathrm{ A } $ 、 $ B $ 两种型号呼吸机每台分别多少万元？</p><p>（2）采购员想采购 $ \\mathrm{ A } $ 、 $ B $ 两种型号呼吸机共 $ 30 $ 台，预计总费用低于 $ 40 $ 万元，请问 $ \\mathrm{ A } $ 型号呼吸机最多购买几台？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西光中学 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871718114340864", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "592871718114340864", "title": "陕西省西安市新城区西光中学教育集团多校协作2024−2025学年八年级下学期6月期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593317404110790656", "questionArticle": "<p>10．今年1月，受国际油价影响，加油成本不断升高．某茶叶销售公司决定将公司的运输货车换装为新能源货车，换装后，公司发现运输成本降低了．销售公司需要将旗下英红5号，英红9号两款产品定期运往广州某大型超市代销，每次运输产品的箱数不变，两种商品原来的运费和现在的运费（单位：元/箱）如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>品种</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>英红5号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>英红9号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>总运费</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>换车前运费</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>9</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>2400</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>换车后运费</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>6</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>1800</p></td></tr></table><p>（1）请分别求出每次运送的英红5号，英红9号各有多少箱？</p><p>（2）换车后，代销超市追加订单，每次运送的两种茶叶总箱数共增加200箱，但增加箱数后，每次运送英红5号的运费不能超过英红9号的运费，请问最多可以增加多少箱英红5号？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东清远 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593317364105519104", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "593317364105519104", "title": "2025年广东省清远市中考二模九年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 1, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 1, "timestamp": "2025-07-01T02:00:53.963Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}