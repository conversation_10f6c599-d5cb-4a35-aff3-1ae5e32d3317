{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 170, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "554780196039073792", "questionArticle": "<p>1．方程组 $ \\begin{cases} 3x-y+2z=3 \\\\ 2x+y-3z=11 \\\\ x+y+z=12 \\end{cases}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=3 \\\\ y=6 \\\\ z=3 \\end{cases}  $ B． $ \\begin{cases} x=5 \\\\ y=4 \\\\ z=3 \\end{cases}  $ C． $ \\begin{cases} x=2 \\\\ y=8 \\\\ z=2 \\end{cases}  $ D． $ \\begin{cases} x=3 \\\\ y=8 \\\\ z=1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780184961916928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780184961916928", "title": "湖南省永州市冷水滩区京华中学2023−2024学年七年级下学期第一次数学课后练习试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "554780191093989376", "questionArticle": "<p>2．下列方程组中是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 4x+6y=7 \\\\ 7x+8z=14 \\end{cases}  $ B． $ \\begin{cases} \\dfrac { 2 } { x }+4y=13 \\\\ x-\\dfrac { 1 } { y }=2 \\end{cases}  $ C． $ \\begin{cases} xy=1 \\\\ 2x+3y=0 \\end{cases}  $ D． $ \\begin{cases} \\dfrac { x } { 2 }+5y=6 \\\\ \\dfrac { x } { 2 }+\\dfrac { y } { 2 }=4 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780184961916928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780184961916928", "title": "湖南省永州市冷水滩区京华中学2023−2024学年七年级下学期第一次数学课后练习试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555173834510344192", "questionArticle": "<p>3．某一商场经销的<i>A</i>，<i>B</i>两种商品，<i>A</i>商品每件进价40元，利润率为 $ 50\\% $ ；<i>B</i>商品每件售价80元．在“元旦”期间，该商场对<i>A</i>，<i>B</i>两种商品开展如下的优惠促销活动：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 148.5pt;\"><p style=\"text-align:center;\">打折前一次性购物总金额</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 274.5pt;\"><p style=\"text-align:center;\">优惠措施</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 148.5pt;\"><p style=\"text-align:center;\">少于等于450元</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 274.5pt;\"><p style=\"text-align:center;\">不优惠</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 148.5pt;\"><p style=\"text-align:center;\">超过450元，但不超过600元</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 274.5pt;\"><p style=\"text-align:center;\">按总售价打九折</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 148.5pt;\"><p style=\"text-align:center;\">超过600元</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 274.5pt;\"><p style=\"text-align:center;\">其中600元部分八折优惠，超过600元的部分打七折优惠</p></td></tr></table><p>按上述优惠条件，若小华一次性购买<i>A</i>，<i>B</i>两种商品（两种商品每种商品不少于1件），实际共付款522元．则以下说法正确的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>①可能购买<i>A</i>商品3件，<i>B</i>商品5件；</p><p>②购买<i>A</i>商品与<i>B</i>商品的总件数可能为8件、9件、10件；</p><p>③如果在打折前买相同的物品，要比打折后多付58元或138元．</p><p>A．0</p><p>B．1</p><p>C．2</p><p>D．3</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东日照 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-14", "keyPointIds": "16420|16434", "keyPointNames": "二元一次方程的解|方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555173818915921920", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555173818915921920", "title": "山东省日照市新营中学2024-2025年九年级下学期3月月考数学试卷", "paperCategory": 1}, {"id": "537437845406916608", "title": "重庆市南岸区2024—2025学年八年级上学期期末质量监测数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "554780208978501632", "questionArticle": "<p>4．某校为改善学校多媒体课室教学设施，计划购进一批电脑和电子白板．经过市场考察得知，购买 $ 1 $ 台电脑和 $ 2 $ 台电子白板需要 $ 3.6 $ 万元，购买 $ 2 $ 台电脑和 $ 3 $ 台电子白板需要 $ 5.6 $ 万元．求每台电脑和每台电子白板各是多少万元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "460000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-14", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780184961916928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780184961916928", "title": "湖南省永州市冷水滩区京华中学2023−2024学年七年级下学期第一次数学课后练习试题", "paperCategory": 1}, {"id": "552662415063687168", "title": "2024年海南省海口市第二次中考模拟数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780208319995904", "questionArticle": "<p>5．计算： $ \\begin{cases} 3x-5y=2m\\,\\,\\,\\,① \\\\ x-3y=3\\,\\,\\,\\,\\,② \\end{cases}  $ ，其中 $ x-y=2 $ ，求 $ m $ 值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780184961916928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780184961916928", "title": "湖南省永州市冷水滩区京华中学2023−2024学年七年级下学期第一次数学课后练习试题", "paperCategory": 1}, {"id": "128787028397826048", "title": "广西北流市2020年九年级学业水平适应性质量评价检测（二）数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780196831797248", "questionArticle": "<p>6．二元一次方程 $ x+3y=10 $ 的正整数解的个数有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．2C．3D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000|430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-03-14", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780184961916928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780184961916928", "title": "湖南省永州市冷水滩区京华中学2023−2024学年七年级下学期第一次数学课后练习试题", "paperCategory": 1}, {"id": "439959083354988544", "title": "福建省福州第十八中学2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "554780120570961920", "questionArticle": "<p>7．长沙市某公园的门票价格如下表所示:</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>购票人数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 51.4pt;\"><p>1～50人</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 61.9pt;\"><p>51～100人</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 61.9pt;\"><p>100人以上</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>票价</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 51.4pt;\"><p>10元/人</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 61.9pt;\"><p>8元/人</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 61.9pt;\"><p>5元/人</p></td></tr></table><p>&nbsp;&nbsp;某校九年级甲、乙两个班共100多人去该公园举行毕业联欢活动,其中甲班有50多人,乙班不足50人,如果以班为单位分别买门票,两个班一共应付920元;如果两个班联合起来作为一个团体购票,一共要付515元,问甲、乙两班分别有多少人?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000|350000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 3, "createTime": "2025-03-14", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780097598758912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780097598758912", "title": "湖南省永州市第十二中学2023−2024学年七年级下学期第一次月考数学模拟试题", "paperCategory": 1}, {"id": "196578904005451776", "title": "福建省厦门市湖里区湖里中学2021-2022学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "201693464722972672", "title": "新疆乌鲁木齐市新市区新疆师范大学附属中学2021-2022学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780110139727872", "questionArticle": "<p>8．我国古代数学名著《孙子算经》中记载：“今有木，不知长短，引绳度之，余绳四尺五寸；屈绳量之，不足一尺，木长几何？”意思是：用一根绳子去量一根木条，绳子还剩余4.5尺；将绳子对折再量木条，木条剩余1尺，问木条长多少尺？如果设木条长x尺，绳子长y尺，那么可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y=x+4.5 \\\\ 0.5y=x-1 \\end{cases}  $ B． $ \\begin{cases} y=x+4.5 \\\\ y=2x-1 \\end{cases}  $ </p><p>C． $ \\begin{cases} y=x-4.5 \\\\ 0.5y=x+1 \\end{cases}  $ D． $ \\begin{cases} y=x-4.5 \\\\ y=2x-1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|-1|410000|350000|420000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2023湖北等地 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 14, "referenceNum": 8, "createTime": "2025-03-14", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "333627691755675648", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "333627691755675648", "title": "2023年湖北省荆州市中考数学真题", "paperCategory": 1}, {"id": "554780097598758912", "title": "湖南省永州市第十二中学2023−2024学年七年级下学期第一次月考数学模拟试题", "paperCategory": 1}, {"id": "465341656176304128", "title": "河南省洛阳市2023-2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "492111939356631040", "title": "福建省福州十九中学2024−2025学年九年级上学期开学数学试题", "paperCategory": 1}, {"id": "461662446295490560", "title": "江苏省南京市联合体2023-2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "473971799383908352", "title": "湖南省衡阳市第三中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "170287431018651648", "title": "2022年八年级上册北师版数学第五章5应用二元一次方程组——里程碑上的数课时练习", "paperCategory": 1}, {"id": "129289304577187840", "title": "湖北省枣阳市2021年中考适应性考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "554779802252648448", "questionArticle": "<p>9．随着“低碳生活，绿色出行”理念的普及，新能源汽车正逐渐成为人们喜爱的交通工具．某汽车销售公司计划购进一批新能源汽车尝试进行销售，据了解2辆 $ A $ 型汽车、3辆 $ B $ 型汽车的进价共计80万元；3辆 $ A $ 型汽车、2辆 $ B $ 型汽车的进价共计95万元．</p><p>(1)求<i>A</i>,<i>B</i>两种型号的汽车每辆进价分别为多少万元？</p><p>(2)若该公司计划正好用200万元购进以上两种型号的新能源汽车（两种型号的汽车均购买），请你帮助该公司设计购买方案；</p><p>(3)若该汽车销售公司销售1辆<i>A</i>型汽车可获利8000元，销售1辆<i>B</i>型汽车可获利5000元，在（2）中的购买方案中，假如这些新能源汽车全部售出，哪种方案获利最大？最大利润是多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|230000|410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈工大附中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 4, "createTime": "2025-03-14", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779775455240192", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}, {"id": "462770248028037120", "title": "江苏省无锡市天一实验学校2023-2024学年七年级下学期数学5月月考试题", "paperCategory": 1}, {"id": "362359411594010624", "title": "黑龙江省佳木斯市第二十中学2023-2024学年八年级上学期开学考试数学试题", "paperCategory": 1}, {"id": "418226612074749952", "title": "河南省焦作市联考2023-2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554779798075121664", "questionArticle": "<p>10．列二元一次方程组解应用题</p><p>随着“互联网”时代的到来，一种新型打车方式受到大众欢迎，该打车方式的总费用由里程费和耗时费组成，其中里程费按<i>x</i>元/公里计算，耗时费按<i>y</i>元/分钟计算（总费用不足9元按9元计价），小明、小刚两人用该打车方式出行，按上述计价规则，其打车总费用、行驶里程数与打车时间如表,</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54.65pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 141.6pt;\"><p>里程数（公里）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 124.2pt;\"><p>时间（分钟）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 106.85pt;\"><p>车费（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54.65pt;\"><p>小明</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 141.6pt;\"><p>8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 124.2pt;\"><p>8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 106.85pt;\"><p>12</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54.65pt;\"><p>小刚</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 141.6pt;\"><p>10</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 124.2pt;\"><p>12</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 106.85pt;\"><p>16</p></td></tr></table><p>(1)求出<i>x</i>，<i>y</i>的值；</p><p>(2)周末小华去图书馆进行阅读也采用该打车方式，打车行驶了15公里，用时12分钟，那么小华的打车总费用为多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈工大附中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-14", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779775455240192", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}, {"id": "452593711161057280", "title": "广东省广州市海珠区中山大学附属中学2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 171, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 171, "timestamp": "2025-07-01T02:21:04.542Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}