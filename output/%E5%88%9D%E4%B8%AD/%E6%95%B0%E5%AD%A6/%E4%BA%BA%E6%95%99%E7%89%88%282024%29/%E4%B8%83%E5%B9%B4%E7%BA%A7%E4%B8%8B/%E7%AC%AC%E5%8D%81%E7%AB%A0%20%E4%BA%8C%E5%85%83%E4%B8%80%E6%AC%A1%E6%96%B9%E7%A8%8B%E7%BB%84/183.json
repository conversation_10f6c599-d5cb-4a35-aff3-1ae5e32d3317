{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 182, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "551912912492732416", "questionArticle": "<p>1．《孙子算经》是中国传统数学的重要著作，其中有一道题，原文是“今有木，不知长短．引绳度之，余绳四尺五寸；屈绳量之，不足一尺，木长几何？”意思是，用一根绳子去量一根木头的长，绳子还剩余 $ 4.5 $ 尺；将绳子对折再量木头，则木头还剩余1尺，问木头长多少尺？可设木头长为<i>x</i>尺，绳子长为<i>y</i>尺，则所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y-x=4.5 \\\\ 0.5y=x-1 \\end{cases}  $</p><p>B． $ \\begin{cases} y=x+4.5 \\\\ y=2x-1 \\end{cases}  $</p><p>C． $ \\begin{cases} y-x=4.5 \\\\ 0.5y=x+1 \\end{cases}  $</p><p>D． $ \\begin{cases} y=x-4.5 \\\\ y=2x-1 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|610000|410000|220000|340000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳实验学校 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 6, "createTime": "2025-03-07", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551912901272969216", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "551912901272969216", "title": "广东省深圳市深圳实验学校中学部2024−2025学年下学期九年级开学考数学试卷", "paperCategory": 1}, {"id": "527896733096910848", "title": "河南省实验中学2024−2025学年八年级上学期第二次月考数学试题", "paperCategory": 1}, {"id": "526523894435454976", "title": "山西省太原市迎泽区太原师范学院附属中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "414558686058684416", "title": "安徽省亳州市2023-2024学年七年级上学期期末数学试题", "paperCategory": 1}, {"id": "401873987586793472", "title": "吉林省长春市朝阳区第二实验学校2023-2024学年七年级上学期期末数学试题", "paperCategory": 1}, {"id": "507302669276454912", "title": "陕西省西安市西安高新第一中学2023−2024学年八年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "553052675920666624", "questionArticle": "<p>2．在长方形<i>ABCD</i>中，放入5个形状大小相同的小长方形（空白部分），其中 $ {\\rm \\mathit{AB}＝7cm，} BC=11 $ 求阴影部分图形的总面积（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/10/2/20/0/0/0/554282385849753601/images/img_1.png\" style='vertical-align:middle;' width=\"152\" alt=\"试题资源网 https://stzy.com\"></p><p>A．18cm<sup>2</sup></p><p>B．21cm<sup>2</sup></p><p>C．24cm<sup>2</sup></p><p>D．27cm<sup>2</sup></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|610000|450000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西高新一中 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 4, "createTime": "2025-03-07", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553052665720119296", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "553052665720119296", "title": "陕西省西安市高新区第一中学2024−2025学年七年级下学期开学考试数学试题", "paperCategory": 1}, {"id": "408038926856790016", "title": "浙江省温州市2023-2024学年七年级上学期期末数学模拟试题", "paperCategory": 1}, {"id": "164373535955132416", "title": "广西壮族自治区崇左市江州区2021-2022学年七年级上学期期末数学试题", "paperCategory": 1}, {"id": "161051873318838272", "title": "江苏省无锡市惠山区2021-2022学年七年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "552662824478089216", "questionArticle": "<p>3．为进一步普及新观状病毒疫情防控知识，提高学生自我保护能力，时代中学复学后采取了新冠状病毒疫情防控知识竞赛活动，对于成绩突出的同学进行表彰奖励，计划购买甲、乙两种笔记本作为奖品已知3本甲型笔记本和5本乙型笔记本共需50元，2本甲型笔记本和3本乙型笔记本共需31元．</p><p>（1）求1本甲型笔记本和1本乙型笔记本的售价各是多少元？</p><p>（2）学校准备购买这两种类型的笔记本共200本，要求甲型笔记本的本数不超过乙型笔记本的本数的3倍，请设计出最省钱的购买方案，并求出花费最低的钱数．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "630000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024青海西宁 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-06", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552662799446482944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552662799446482944", "title": "2024年青海省西宁市虎台中学中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551569990924673024", "questionArticle": "<p>4．六月毕业季，花店准备购进向日葵与百合两种鲜花，若购进向日葵 $ 40 $ 支，百合 $ 80 $ 支，需要 $ 800 $ 元；若购进向日葵 $ 50 $ 支，百合 $ 20 $ 支， 需要 $ 360 $ 元．</p><p>(1)求花店购进向日葵与百合两种鲜花每支需要多少元?</p><p>(2)若花店准备 $ 1000 $ 元全部用来购进向日葵与百合两种鲜花，计划销售每支向日葵可获利润 $ 3 $ 元，销售每支百合可获利润 $ 5 $ 元，且销售两种鲜花的总利润不低于 $ 700 $ 元，那么花店需要最多购进百合多少支?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024黑龙江哈尔滨市第十七中学 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-06", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551569961325469696", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "551569961325469696", "title": "2024年黑龙江省哈尔滨市第十七中学校中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552662527592669184", "questionArticle": "<p>5．春节期间，某礼品店经销 $ A，B $ 两种礼品盒，王先生第一次购进 $ \\mathrm{ A } $ 种礼品盒 $ 10 $ 盒， $ B $ 种礼品盒 $ 15 $ 盒，共花费 $ 2800 $ 元；第二次购进 $ \\mathrm{ A } $ 种礼品盒 $ 6 $ 盒， $ B $ 种礼品盒 $ 5 $ 盒，共花费 $ 1200 $ 元．求购进 $ A，B $ 两种礼品盒的单价分别是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "460000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024海南省直辖县级行政单位 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-06", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552662506327547904", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552662506327547904", "title": "2024年海南省文昌市部分学校中考数学二模试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552662253654286336", "questionArticle": "<p>6．贵州出产的茶叶品种众多，畅销各地，茶产业是农民增加收入的一种重要途径．某县重点推出了<i>A</i>，<i>B</i>两种品牌茶叶，已知某商店购买1盒<i>A</i>茶叶和1盒<i>B</i>茶叶共用540元，购买2盒<i>A</i>茶叶和3盒<i>B</i>茶叶共用1340元．</p><p>(1)购买<i>A</i>，<i>B</i>两种茶叶的单价各是多少元？</p><p>(2)该店计划用不超过27800元购买<i>A</i>，<i>B</i>两种茶叶共100盒，且<i>A</i>的数量不低于<i>B</i>数量的 $ \\dfrac { 3 } { 2 } $ ，若两种茶叶的售价均为每盒350元，该店如何安排进货，使销售完两种茶叶获得利润最大，并求这个最大利润．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "520000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024贵州遵义 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-06", "keyPointIds": "16437|16490|16544", "keyPointNames": "销售利润问题|一元一次不等式组的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552662227083370496", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552662227083370496", "title": "2024年贵州省遵义市初中学业水平考试第二次模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552662022216785920", "questionArticle": "<p>7．方程是刻画现实世界数量关系的一个有效模型，这个名词最早出现在我国古代数学专著 《九章算术》中．请用方程思想解决下列问题：</p><p>某单位组织联谊活动，需采购可乐、橙汁两种饮料，已知购买4箱可乐、2箱橙汁需320元， 购买3箱可乐、1箱橙汁需210元．</p><p>(1)求可乐、橙汁每箱的价格；</p><p>(2)单位计划经费不超过1100元，购买两种饮料共20箱，且橙汁不少于8箱，则共有哪几种购买方案？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "520000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024贵州六盘水 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-06", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552661996593782784", "questionMethodName": "函数与方程思想", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552661996593782784", "title": "2024年贵州省六盘水市初中学业水平第二次模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551569698707513344", "questionArticle": "<p>8．近年来，露营成为广受人们欢迎的假日休闲方式，从家边绿地到旷野山林，各具特色的露营地吸引着消费者前去体验．某露营地提供了 $ \\mathrm{ A } $ 、 $ B $ 两种型号帐篷供游客租用．已知租用1顶 $ \\mathrm{ A } $ 型帐篷和2顶 $ B $ 型帐篷一天的费用是190元；租用2顶 $ \\mathrm{ A } $ 型帐篷和1顶 $ B $ 型帐篷一天的费用是140元．</p><p>(1)求租用每顶 $ \\mathrm{ A } $ 型帐篷和每顶 $ B $ 型帐篷一天的费用；</p><p>(2)若某游学机构需要租用该景区 $ \\mathrm{ A } $ 、 $ B $ 两种帐篷共30顶，租用 $ \\mathrm{ A } $ 型帐篷的数量不超过 $ B $ 型帐篷数量的 $ \\dfrac { 1 } { 2 } $ ，为使租用帐篷的总费用最低，应租用多少顶 $ \\mathrm{ A } $ 型帐篷？租用帐篷一天的总费用最低为多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024福建厦门 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-06", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551569669326413824", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "551569669326413824", "title": "2024年福建省厦门市湖里区五缘实验学校中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551912533868716032", "questionArticle": "<p>9．我国古代数学名著《算法统宗》中记载：“今有里长值月议云每里科出银五钱依帐买物以辨酒席多银三两五钱每里科出四钱亦多五钱问合用银并里数若干”．意为：里长们（“里”是指古代的一种基层行政单位）在月度会上商议出银子购买物资办酒席之事．若每里出5钱，则多出35钱；若每里出4钱，则多出5钱．问办酒席需多少银子，里的数量有多少个？若设里的数量有<i>x</i>个，办酒席需要用<i>y</i>钱银子，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5y=x+35 \\\\ 4y=x-5 \\end{cases}  $ B． $ \\begin{cases} 5y=x+35 \\\\ 4y=x+5 \\end{cases}  $ C． $ \\begin{cases} 5x=y+35 \\\\ 4x=y-5 \\end{cases}  $ D． $ \\begin{cases} 5x=y+35 \\\\ 4x=y+5 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州一中等校 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-06", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551912520212062208", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "551912520212062208", "title": "福建省福州第一中学、福州三收中学2024−2025学年九年级下学期 数学期初适应性练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "551569607544315904", "questionArticle": "<p>10．人工智能与实体经济融合能够引领产业转型，提升人们生活品质．某科创公司计划投入一笔资金购进 $ \\mathrm{ A } $ 、 $ B $ 两种型号的芯片．已知购进2片 $ \\mathrm{ A } $ 型芯片和1片 $ B $ 型芯片共需900元，购进1片 $ \\mathrm{ A } $ 型芯片和3片 $ B $ 型芯片共需950元．</p><p>(1)求购进1片 $ \\mathrm{ A } $ 型芯片和1片 $ B $ 型芯片各需多少元？</p><p>(2)若该科创公司计划购进 $ \\mathrm{ A } $ 、 $ B $ 两种型号的芯片共10万片，根据生产的需要，购进 $ \\mathrm{ A } $ 型芯片的数量不低于 $ B $ 型芯片数量的4倍，问该公司如何购买芯片所需资金最少？最少资金是多少万元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024福建厦门第二中学 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-03-06", "keyPointIds": "16441|16547", "keyPointNames": "其他问题|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551569579467644928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "551569579467644928", "title": "2024年福建省厦门第二中学中考二模数学试题", "paperCategory": 1}, {"id": "451118968922742784", "title": "2024年广东省深圳市南山区第二外国语学校（集团）学府中学中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 183, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 183, "timestamp": "2025-07-01T02:22:30.320Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}