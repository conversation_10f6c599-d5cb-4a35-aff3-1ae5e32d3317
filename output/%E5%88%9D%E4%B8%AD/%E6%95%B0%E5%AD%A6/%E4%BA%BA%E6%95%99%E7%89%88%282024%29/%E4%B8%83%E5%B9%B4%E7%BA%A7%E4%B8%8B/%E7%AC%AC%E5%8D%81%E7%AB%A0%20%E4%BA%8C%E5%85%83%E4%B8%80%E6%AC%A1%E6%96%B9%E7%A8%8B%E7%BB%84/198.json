{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 197, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "544317486780424192", "questionArticle": "<p>1．请写出一个关于 $ x $ ， $ y $ 的二元一次方程，使其满足 $ x $ 的系数是大于2的整数， $ y $ 的系数是小于 $ -1 $ 的整数，且 $ x=1 $ ， $ y=3 $ 是这个二元一次方程的解.这个方程可以是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16419|16420", "keyPointNames": "二元一次方程的定义|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317484809101312", "questionFeatureName": "开放性试题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317484809101312", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.1 二元一次方程组的概念 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317663503228928", "questionArticle": "<p>2．陕西是联合国粮农组织认定的世界苹果最佳优生区之一，是全球集中连片种植苹果最大区域.某苹果园现有一批苹果，计划租用A、B两种型号的货车将苹果运往外地销售，已知满载时，用3辆A型车和2辆B型车一次可运苹果13吨；用4辆A型车和3辆B型车一次可运苹果18吨.求1辆A型车和1辆B型车满载时一次分别运苹果多少吨.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317661406076928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317661406076928", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时1 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317486558126080", "questionArticle": "<p>3．方程 $ \\mathrm{①}2x-\\dfrac{y}{3}=12 $ ， $ \\mathrm{②}xy+2=3 $ ， $ \\mathrm{③}5(x+y)=7(x-y) $ ， $ \\mathrm{④}\\dfrac{1}{2x}+y=4 $ 中，是二元一次方程的是<u>&nbsp;&nbsp;</u>（填序号）.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317484809101312", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317484809101312", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.1 二元一次方程组的概念 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317486360993792", "questionArticle": "<p>4．已知 $ \\begin{cases}x=-1,\\\\ y=2\\end{cases} $ 是二元一次方程组 $ \\begin{cases}3x+2y=m,\\\\ nx-y=1\\end{cases} $ 的解，则 $ m-n $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． 1B． 2C． 3D． 4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317484809101312", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317484809101312", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.1 二元一次方程组的概念 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317662760837120", "questionArticle": "<p>5．五一黄金周期间，几位同学一起去郊外游玩,男同学都背着红色的旅行包，女同学都背着黄色的旅行包，其中一位男同学说：“我看到的红色旅行包个数是黄色旅行包个数的1.5倍.”另一位女同学却说：“我看到的红色旅行包个数是黄色旅行包个数的2倍.”如果这两位同学说的都对，那么女同学的人数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2B．4C．6D．8</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317661406076928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317661406076928", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时1 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317486168055808", "questionArticle": "<p>6．方程 $ m+n=3 $ 的正整数解的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． 2B． 3C． 4D． 5</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317484809101312", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317484809101312", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.1 二元一次方程组的概念 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317662530150400", "questionArticle": "<p>7．元宵节又称灯节，我国各地都有在元宵节这天挂灯笼的习俗,灯笼分为宫灯、纱灯等.若购买1个宫灯和1个纱灯共需75元，小田用690元购买了6个同样的宫灯和10个同样的纱灯.若根据题意可得二元一次方程组 $ \\begin{cases}x+y=75,\\\\ 6x+10y=690,\\end{cases} $ 则方程组中 $ x $ , $ y $ 分别表示为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．每个宫灯的价格，每个纱灯的价格</p><p>B．每个纱灯的价格，每个宫灯的价格</p><p>C．宫灯的数量，纱灯的数量</p><p>D．纱灯的数量，宫灯的数量</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317661406076928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317661406076928", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时1 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317485975117824", "questionArticle": "<p>8．关于 $ x $ , $ y $ 的方程 $ kx-3y=2x+1 $ 是二元一次方程，则 $ k $ 的取值范围是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．  $ k\\ne 0 $ B．  $ k\\ne 3 $ C．  $ k\\ne 2 $ D．  $ k\\ne -2 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317484809101312", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317484809101312", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.1 二元一次方程组的概念 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317662983135232", "questionArticle": "<p>9．如图，足球的表面是由32块呈多边形的黑、白皮块缝合而成的，已知黑色皮块数比白色皮块数的一半多2块，则白色皮块的块数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/11/2/1/0/0/0/544317584730005504/images/img_1.jpg\" style=\"vertical-align:middle;\" width=\"47\" alt=\"试题资源网 https://stzy.com\"></p><p>A．18B．20C．22D．24</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317661406076928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317661406076928", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时1 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317735607508992", "questionArticle": "<p>10．随着康养医疗社会需求的进一步增大，某康养中心正在扩大规模，准备装修一批新房舍.若甲、乙两个装修公司合作，需6周完成，共需装修费为5.2万元；若甲公司单独做4周后，剩下的由乙公司来做，还需9周才能完成，共需装修费4.8万元.康养中心研究后决定只选一个公司单独完成（设工作总量为1）.</p><p>（1） 甲公司每周的工作效率为 $ m $ ，乙公司每周的工作效率为 $ n $ ，则可列出方程为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2） 如果从节约时间的角度考虑，应选哪家公司？请说明理由；</p><p>（3） 如果从节约开支的角度考虑,应选哪家公司？请说明理由.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16431|16439", "keyPointNames": "工程问题|几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317732746993664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317732746993664", "title": "2024—2025学年七年级下册人教版（2024）数学第十章10.3 实际问题与二元一次方程组课时2 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 198, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 198, "timestamp": "2025-07-01T02:24:15.332Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}