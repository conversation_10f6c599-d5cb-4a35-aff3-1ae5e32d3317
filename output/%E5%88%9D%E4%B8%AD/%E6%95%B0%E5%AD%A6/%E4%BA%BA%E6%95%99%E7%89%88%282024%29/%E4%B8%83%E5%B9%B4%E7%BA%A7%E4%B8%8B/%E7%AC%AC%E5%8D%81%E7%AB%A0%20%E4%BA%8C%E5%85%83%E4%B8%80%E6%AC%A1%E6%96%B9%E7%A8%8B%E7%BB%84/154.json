{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 153, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "559470824127766528", "questionArticle": "<p>1．某电器超市销售每台进价分别为160元、120元的<i>A</i>，<i>B</i>两种型号的电风扇，第一周销售<i>A</i>型号2台，<i>B</i>型号5台，销售收入为1150元；第二周销售<i>A</i>型号8台，<i>B</i>型号2台，销售收入为1900元．</p><p>(1)求<i>A</i>，<i>B</i>两种型号电风扇的销售单价；</p><p>(2)若超市准备用不超过7000元的金额再采购这两种型号的电风扇共50台，求<i>A</i>型号的电风扇最多能采购多少台？</p><p>(3)在（2）的条件下，超市销售完这50台电风扇能否实现利润超过1700元的目标？若能，请给出相应的采购方案；若不能，请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽蚌埠 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-01", "keyPointIds": "16434|16438|16486", "keyPointNames": "方案问题|和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559470800492863488", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "559470800492863488", "title": "安徽省蚌埠市2024−2025学年七年级下学期第一次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559854551471267840", "questionArticle": "<p>2．某景区为响应《关于推动露营旅游休闲健康有序发展》精神，需要购买 $ \\mathrm{ A } $ 、 $ B $ 两种型号的帐篷．若购买 $ \\mathrm{ A } $ 种型号帐篷 $ 2 $ 顶和 $ B $ 种型号帐篷 $ 4 $ 顶，则需 $ 5200 $ 元；若购买 $ \\mathrm{ A } $ 种型号帐篷 $ 3 $ 顶和 $ B $ 种型号帐篷 $ 1 $ 顶，则需 $ 2800 $ 元．</p><p>(1)求每顶 $ \\mathrm{ A } $ 种型号帐篷和每顶 $ B $ 种型号帐篷的价格；</p><p>(2)若该景区需要购买 $ \\mathrm{ A } $ 、 $ B $ 两种型号的帐篷共 $ 20 $ 顶（两种型号的帐篷均需购买），购买 $ \\mathrm{ A } $ 种型号帐篷数量不超过购买 $ B $ 种型号帐篷数量的 $ \\dfrac { { { 1 } } } { { { 3 } } } $ ，为使购买帐篷的总费用最低，应购买 $ \\mathrm{ A } $ 种型号帐篷和 $ B $ 种型号帐篷各多少顶？购买帐篷的总费用最低为多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "530000|410000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2023云南 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 12, "referenceNum": 3, "createTime": "2025-04-01", "keyPointIds": "16435|16486|16543", "keyPointNames": "分配问题|一元一次不等式的应用|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "326390750219902976", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "326390750219902976", "title": "2023年云南省中考数学真题", "paperCategory": 1}, {"id": "559854526766817280", "title": "广东省茂名市高州市联考2024−2025学年九年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "557696593618575360", "title": "河南省信阳市2024−2025学年九年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "561681388610560000", "questionArticle": "<p>3．某服装店老板到厂家选购<i>A</i>、<i>B</i>两种品牌的儿童服装．店主统计了前两周的销售情况，发现第一周<i>A</i>品牌儿童服装的销量是10件，<i>B</i>品牌儿童服装的销量是12件，总利润是280元；第二周<i>A</i>品牌儿童服装的销量是18件，<i>B</i>品牌儿童服装的销量是20件，总利润是480元．</p><p>(1)请求出<i>A</i>品牌和<i>B</i>品牌儿童服装每件的利润分别是多少元？</p><p>(2)店主在第三周调整了价格，<i>A</i>品牌儿童服装每件涨价<i>a</i>元，<i>B</i>品牌儿童服装每件降价<i>a</i>元，统计后发现，调整后的这周<i>A</i>、<i>B</i>两种品牌儿童服装的销量一样，并且<i>A</i>品牌儿童服装的利润达240元，<i>B</i>品牌儿童服装的利润达260元，求出<i>a</i>的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建福州一中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-31", "keyPointIds": "16437|16476", "keyPointNames": "销售利润问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "561681362048032768", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "561681362048032768", "title": "福建省福州第一中学2024—2025学年下学期九年级数学适应性练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "561681374148599808", "questionArticle": "<p>4．《孙子算经》记载：今有3人共车，二车空；二人共车，九人步，问人与车各几何？译文：今有若干人乘车，若每三人乘一辆车，最终剩余2辆车；若每2人乘一辆车，最终剩余9人无车可乘．问共有多少人？多少辆车？如果设有<i>x</i>人，<i>y</i>辆车，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）．</p><p>A． $ \\begin{cases} x+6=3y, \\\\ 2y+9=x \\end{cases}  $ B． $ \\begin{cases} x-3y=6, \\\\ 2y-x=9 \\end{cases}  $ C． $ \\begin{cases} x+3y=6, \\\\ 2y+x=9 \\end{cases}  $ D． $ \\begin{cases} x-6=3y, \\\\ x-2y=9 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏苏州等地 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-31", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017032400056320", "questionFeatureName": "数学文化题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578017032400056320", "title": "江苏省苏州市昆山市、常熟市、太仓市、张家港市2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "561681362048032768", "title": "福建省福州第一中学2024—2025学年下学期九年级数学适应性练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "559471076553564160", "questionArticle": "<p>5．已知实数 $ x $ ， $ y $ 满足 $ x+3y-6=0 $ ， $ y &gt; 3 $ ， $ x &gt; -5 $ ，下列正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．满足条件 $ x $ 的值有两个整数B． $ y &gt; \\dfrac { 11 } { 3 } $ </p><p>C． $ -2  &lt;  x+y  &lt;  \\dfrac { 2 } { 3 } $ D． $ -\\dfrac { 26 } { 3 }  &lt;  x-y  &lt;  -6 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽合肥四十五中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-31", "keyPointIds": "16420|16482", "keyPointNames": "二元一次方程的解|不等式的性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559471061365989376", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "559471061365989376", "title": "安徽省合肥四十五中学2024−2025学年下学期3月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "559853123214286848", "questionArticle": "<p>6．解二元一次方程组</p><p>(1) $ \\begin{cases} 3x-2y=11 \\\\ 2x+3y=16 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 3(x+y)-4(x-y)=-9 \\\\ \\dfrac { x+y } { 2 }+\\dfrac { x-y } { 6 }=1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东淄博 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-31", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559853104558022656", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559853104558022656", "title": "山东省淄博市张店区第七中学2024—2025学年下学期3月月考七年级数学试题（五四学制）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559853120966139904", "questionArticle": "<p>7．若 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ 是关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} ax+by=2 \\\\ bx+ay=7 \\end{cases}  $ 的解，则 $ a+b $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东淄博 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-31", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559853104558022656", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559853104558022656", "title": "山东省淄博市张店区第七中学2024—2025学年下学期3月月考七年级数学试题（五四学制）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559853126653616128", "questionArticle": "<p>8．已知2辆<i>A</i>型车和1辆<i>B</i>型车载满货物一次可运货10吨．用1辆<i>A</i>型车和2辆<i>B</i>型车载满货物一次可运货11吨．某物流公司现有31吨货物，计划同时租用<i>A</i>型车<i>a</i>辆和<i>B</i>型车<i>b</i>辆，一次运完，且每辆车都满载货物．根据以上信息解答下列问题：</p><p>（1）1辆<i>A</i>型车和1辆<i>B</i>型车载满货物一次分别可运货物多少吨？</p><p>（2）请帮助物流公司设计租车方案.</p><p>（3）若<i>A</i>型车每辆车租金每次100元，<i>B</i>型车每辆车租金每次120元．请选出最省钱的租车方案，并求出最少的租车费．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川眉山 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-31", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575335369685639168", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "575335369685639168", "title": "四川省眉山市东坡区苏辙中学共同体2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 11}, {"id": "559853104558022656", "title": "山东省淄博市张店区第七中学2024—2025学年下学期3月月考七年级数学试题（五四学制）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559853118462140416", "questionArticle": "<p>9．如图，在长方形 $ ABCD $ 中，放入六个形状、大小相同的小长方形，所标尺寸分别为 $ 14{ \\rm{ c } }{ \\rm{ m } } $ 和 $ 6{ \\rm{ c } }{ \\rm{ m } } $ ，如图所示，则图中阴影部分的总面积为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;） $ { \\rm{ c } }{ \\rm{ m } }{^{2}} $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/25/2/1/0/0/0/559853066855424009/images/img_11.png\" style=\"vertical-align:middle;\" width=\"216\" alt=\"试题资源网 https://stzy.com\"></p><p>A．36B．44C．84D．96</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东淄博 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-31", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559853104558022656", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559853104558022656", "title": "山东省淄博市张店区第七中学2024—2025学年下学期3月月考七年级数学试题（五四学制）", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "559853112594309120", "questionArticle": "<p>10．《九章算术》是中国古代第一部数学专著，它对我国古代后世的数学家产生了深远的影响，该书中记载了一个问题，大意是：有几个人一起去买一件物品，每人出 $ 8 $ 元，多 $ 3 $ 元；每人出 $ 7 $ 元，少 $ 4 $ 元，问有多少人？该物品价几何？设有 $ x $ 人，物品价值 $ y $ 元，则所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 8y+3=x \\\\ 7y-4=x \\end{cases}  $ B． $ \\begin{cases} 8x+3=y \\\\ 7x-4=y \\end{cases}  $ </p><p>C． $ \\begin{cases} 8y-3=x \\\\ 7y+4=x \\end{cases}  $ D． $ \\begin{cases} 8x-3=y \\\\ 7x+4=y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东淄博 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-31", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559853104558022656", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559853104558022656", "title": "山东省淄博市张店区第七中学2024—2025学年下学期3月月考七年级数学试题（五四学制）", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 154, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 154, "timestamp": "2025-07-01T02:19:05.909Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}