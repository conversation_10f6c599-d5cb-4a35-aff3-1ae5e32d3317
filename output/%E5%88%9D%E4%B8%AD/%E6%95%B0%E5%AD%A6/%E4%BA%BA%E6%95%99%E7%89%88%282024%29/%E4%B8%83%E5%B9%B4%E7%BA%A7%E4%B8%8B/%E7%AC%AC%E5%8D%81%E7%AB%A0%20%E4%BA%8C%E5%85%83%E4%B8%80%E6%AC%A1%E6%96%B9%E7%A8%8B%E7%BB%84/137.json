{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 136, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "564939625912901632", "questionArticle": "<p>1．一服装厂生产一款上衣，已知每米布料可做1个衣身或3个衣袖，现计划用45米这种布料生产这款上衣，设可用 $ x $ 米布料做衣身， $ y $ 米布料做衣袖，使得恰好配套（1件衣身配2个衣袖），依题意，可列方程组：<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西临汾 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939609844523008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939609844523008", "title": "山西省临汾市尧都区多校2023−2024学年七年级下学期期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564939622943334400", "questionArticle": "<p>2．已知关于<i>x</i>，<i>y</i>的二元一次方程组为 $ \\begin{cases} x+y=2m+1 \\\\ 2x-y=m+8 \\end{cases}  $ ，则 $ x-y $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．6B．5C．4D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西临汾 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939609844523008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939609844523008", "title": "山西省临汾市尧都区多校2023−2024学年七年级下学期期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564939621328527360", "questionArticle": "<p>3．某货运公司有大、小两种货车，已知9辆小货车一次运货的质量比7辆大货车少6吨，11辆小货车一次运货的质量比7辆大货车一次运货的质量多2吨，则1辆小货车一次可以运货的质量为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/08/2/1/0/0/0/564939590768828420/images/img_8.png\" style=\"vertical-align:middle;\" width=\"324\" alt=\"试题资源网 https://stzy.com\"></p><p>A．6吨B．5吨C．4吨D．3吨</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西临汾 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939609844523008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939609844523008", "title": "山西省临汾市尧都区多校2023−2024学年七年级下学期期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564939615796240384", "questionArticle": "<p>4．对于二元一次方程 $ 3x+2y=10 $ ，若 $ x=2 $ ，则 $ y $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．5B．4C．3D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西临汾 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939609844523008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939609844523008", "title": "山西省临汾市尧都区多校2023−2024学年七年级下学期期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564939619826966528", "questionArticle": "<p>5．二元一次方程组 $ \\begin{cases} 7x+2y=11 \\\\ x-2y=-3 \\end{cases}  $ 的解为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=-1, \\\\ y=2 \\end{cases}  $ B． $ \\begin{cases} x=1, \\\\ y=-2 \\end{cases}  $ C． $ \\begin{cases} x=-1, \\\\ y=-2 \\end{cases}  $ D． $ \\begin{cases} x=1, \\\\ y=2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西临汾 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564939609844523008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564939609844523008", "title": "山西省临汾市尧都区多校2023−2024学年七年级下学期期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "566751366183428096", "questionArticle": "<p>6．任意一个无理数介于两个整数之间，我们定义，若无理数<i>T</i>： $ m  &lt;  T  &lt;  n $ ， $ ( $ 其中<i>m</i>为满足不等式的最大整数，<i>n</i>为满足不等式的最小整数 $ ) $ ，则称无理数<i>T</i>的“立信区间”为 $ \\left ( { m,n } \\right )  $ ，如 $ 1  &lt;  \\sqrt { 3 }  &lt;  2 $ ，所以 $ \\sqrt { 3 } $ 的立信区间为 $ \\left ( { 1,2 } \\right )  $ ．</p><p>(1)无理数 $ \\sqrt { 11 } $ 的“立信区间”是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)若其中一个无理数的“立信区间”为 $ \\left ( { m,n } \\right )  $ 且满足 $ 0  &lt;  m+\\sqrt { n }  &lt;  12 $ ，其中 $ \\begin{cases} x=m \\\\ y=\\sqrt { n } \\end{cases}  $ 是关于<i>x</i>、<i>y</i>的方程 $ mx-ny=C $ 的一组正整数解，求<i>C</i>值．</p><p>(3)实数<i>x</i>、<i>y</i>、<i>m</i>满足关系式： $ \\sqrt { 2x+3y-m }+\\sqrt { 3x+4y-2m }=\\sqrt { x+y-24 }+\\sqrt { 24-x-y } $ ，求<i>m</i>的算术平方根的“立信区间”．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南长沙 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16298|16420|28421", "keyPointNames": "估算无理数的大小|二元一次方程的解|算术平方根非负性的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "566751343437717504", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "566751343437717504", "title": "湖南省长沙市开福区立信中学2024−2025学年七年级下学期第一次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "566751355550867456", "questionArticle": "<p>7．某足球队在一次联赛中共进行了 $ 13 $ 场比赛，积分规则为：胜一场得3分，平一场得1分，负一场得0分．已知该队负了4场，共得 $ 19 $ 分．那么这个队胜场数为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3场B．4场C．5场D．6场</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "566751343437717504", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "566751343437717504", "title": "湖南省长沙市开福区立信中学2024−2025学年七年级下学期第一次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "565668842787086336", "questionArticle": "<p>8．（1）计算： $ -2{^{-2}}-{\\tan}30{}\\degree +\\left  | { 1-\\sqrt { 3 } } \\right  |  $ ；</p><p>（2）解二元一次方程组： $ \\begin{cases} 3a-2b=5 \\\\ a+2b=3 \\end{cases}  $ ；</p><p>（3）化简： $ 2a\\left ( { a-b } \\right ) -{\\left( { a-2b } \\right) ^ {2}} $ ；</p><p>（4）化简： $ \\left ( { \\dfrac { x{^{2}}+2 } { x-1 }-x } \\right ) \\div \\dfrac { 4+4x+x{^{2}} } { -x{^{2}}+x } $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16333|16370|16424|16834", "keyPointNames": "整式的混合运算|分式的混合运算|加减消元法解二元一次方程组|特殊角的三角函数值", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565668818812444672", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "565668818812444672", "title": "2025年重庆实验外国语学校九年级下学期一诊数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564940156668518400", "questionArticle": "<p>9．对于未知数为 $ x $ ， $ y $ 的二元一次方程组，如果方程组的解 $ x $ ， $ y $ 满足 $ \\left  | { x-y } \\right  | =1 $ ，我们就说方程组的解 $ x $ 与 $ y $ 具有“邻好关系”．</p><p>（1）方程组 $ \\begin{cases} x+2y=7 \\\\ x=y+1 \\end{cases}  $ 的解 $ x $ 与 $ y $ 是否具有“邻好关系”?说明你的理由：</p><p>（2）若方程组 $ \\begin{cases} 4x-y=6 \\\\ 2x+y=4m \\end{cases}  $ 的解 $ x $ 与 $ y $ 具有“邻好关系”，求 $ m $ 的值：</p><p>（3）未知数为 $ x $ ， $ y $ 的方程组 $ \\begin{cases} x+ay=7 \\\\ 2y-x=5 \\end{cases}  $ ，其中 $ a $ 与 $ x $ 、 $ y $ 都是正整数，该方程组的解 $ x $ 与 $ y $ 是否具有“邻好关系”?如果具有，请求出 $ a $ 的值及方程组的解：如果不具有，请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江杭州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 27, "referenceNum": 3, "createTime": "2025-04-14", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564940131838238720", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564940131838238720", "title": "浙江省杭州市拱墅区文澜中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "473973218178539520", "title": "江苏省苏州市2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "201985945670819840", "title": "江苏省泰州市海陵区第二中学附属初中2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564940144299515904", "questionArticle": "<p>10．利用两块大小一样的长方体木块测量一张桌子的高度，首先按图①方式放置，再交换两木块的位置，按图②方式放置，测量的数据如图，则桌子的高度是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/08/2/1/0/0/0/564940110619254784/images/img_5.png\" style=\"vertical-align:middle;\" width=\"260\" alt=\"试题资源网 https://stzy.com\"></p><p>A．73cmB．74cmC．75cmD．76cm</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|330000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江杭州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-04-14", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564940131838238720", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564940131838238720", "title": "浙江省杭州市拱墅区文澜中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "220605006721884160", "title": "山东省临沂市临沭县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "205449160316723200", "title": "广东省汕头市龙湖实验中学2021-2022学年八年级上学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 137, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 137, "timestamp": "2025-07-01T02:17:05.451Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}