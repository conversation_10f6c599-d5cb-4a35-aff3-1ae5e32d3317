{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 74, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "577681178901127168", "questionArticle": "<p>1．解下列方程组：</p><p>（1） $ \\begin{cases} x=y-1 \\\\ 3x+2y=12 \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} 3x+2y=12 \\\\ 3x-2y=0 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京师范大学附属中学新城初级中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577681155975061504", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577681155975061504", "title": "江苏省南京市新城中学四校2024−2025学年七年级下学期数学期中考试试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577681169224867840", "questionArticle": "<p>2．当 $ x $ 依次取1，3，5，7时，小淇算得多项式 $ kx+b $ 的值分别为0，5，11，17，经验证，只有一个结果是错误的，这个错误的结果是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．当 $ x=1 $ 时， $ kx+b=0 $ B．当 $ x=3 $ 时， $ kx+b=5 $ </p><p>C．当 $ x=5 $ 时， $ kx+b=11 $ D．当 $ x=7 $ 时， $ kx+b=17 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏南京师范大学附属中学新城初级中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577681155975061504", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577681155975061504", "title": "江苏省南京市新城中学四校2024−2025学年七年级下学期数学期中考试试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577681501342441472", "questionArticle": "<p>3．解方程组： $ \\begin{cases} 2x-y=4 \\\\ x+3y=2 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏苏州 · 一模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577681475606192128", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "577681475606192128", "title": "江苏省苏州市高新区2025年中考一模考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577682143922401280", "questionArticle": "<p>4．解方程组：</p><p>（1） $ \\begin{cases} x=2y \\\\ 3x+4y=20 \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} 3x-2y=1 \\\\ 2x+3y=18 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏无锡 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577682119423471616", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577682119423471616", "title": "江苏省无锡市江南中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577682141418401792", "questionArticle": "<p>5．关于<i>x</i>、<i>y</i>的方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ 的解是 $ \\begin{cases} x=6 \\\\ y=-4 \\end{cases}  $ ，则方程组 $ \\begin{cases} 3a{{}_{ 1 } }x+2b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ 3a{{}_{ 2 } }x+2b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ 的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏无锡 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577682119423471616", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577682119423471616", "title": "江苏省无锡市江南中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577682133759602688", "questionArticle": "<p>6．某班37名学生在爱心图书捐赠活动中共捐92本书，其中男生平均每人捐3本，女生平均每人捐2本，设该班男生有<i>x</i>人，女生有<i>y</i>人．根据题意，所列方程组正确的（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=92 \\\\ 3x+2y=37 \\end{cases}  $ B． $ \\begin{cases} x+y=92 \\\\ 2x+3y=37 \\end{cases}  $ C． $ \\begin{cases} x+y=37 \\\\ 2x+3y=92 \\end{cases}  $ D． $ \\begin{cases} x+y=37 \\\\ 3x+2y=92 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏无锡 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577682119423471616", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577682119423471616", "title": "江苏省无锡市江南中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577682130311884800", "questionArticle": "<p>7．下列四对数值，哪对是二元一次方程 $ 5x+2y=1 $ 的解（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ B． $ \\begin{cases} x=-1 \\\\ y=2 \\end{cases}  $ C． $ \\begin{cases} x=-1 \\\\ y=-2 \\end{cases}  $ D． $ \\begin{cases} x=1 \\\\ y=-2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏无锡 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577682119423471616", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577682119423471616", "title": "江苏省无锡市江南中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578015572958425088", "questionArticle": "<p>8．2024年12月4日，“春节——中国人庆祝传统新年的社会实践”列入联合国教科文组织人类非文化遗产代表作名录．截至目前，我国有44个项目列入联合国教科文组织非物质文化遗产名录、名册，总数位居世界第一．每逢春节，为了营造喜庆祥和的氛围，家家户户都会挂上红红的灯笼．在春节前夕，某商家购进 $ A,B $ 两种型号的灯笼共100对，共用去3780元，这两种型号的灯笼的进价、售价如下表：</p><table style=\"border: solid 1px;border-collapse: collapse; width:181.5pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">进价（元/对）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">售价（元/对）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ \\mathrm{ A } $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">54</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">72</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ B $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">27</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">32</p></td></tr></table><p>（1）求该商家购进 $ A,B $ 两种型号的灯笼各多少对？</p><p>（2）为迎接新春到来，某单位购买 $ A,B $ 两种型号的灯笼（两种型号都购买）共花费336元，请你计算购买 $ A,B $ 两种型号的灯笼各多少对？并计算此时商家获利多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京15中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16420|16440", "keyPointNames": "二元一次方程的解|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015546098102272", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015546098102272", "title": "北京市第十五中学2024—2025学年下学期七年级期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015567212228608", "questionArticle": "<p>9．解方程组 $ \\begin{cases} 2x+y=5 \\\\ 3x-2y=4 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京15中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015546098102272", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015546098102272", "title": "北京市第十五中学2024—2025学年下学期七年级期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015564968275968", "questionArticle": "<p>10．算盘起源于中国，是我国的优秀文化遗产．以排列成串的算珠作为计算工具，成串算珠称为档，中间横梁把上珠分为上、下两部分，每个上珠代表5，每个下珠代表1，每串算珠从右至左依次可代表十进位值制的个位、十位、百位……，不拨出空档表示0．小华在百位拨了一颗上珠和一颗下珠，且个位数字与十位数字的和等于百位上的数，个位数字比十位数字多4，则小华要表示的这个三位数是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/15/2/1/0/0/0/578015522555473933/images/img_14.png\" style=\"vertical-align:middle;\" width=\"227\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京15中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015546098102272", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015546098102272", "title": "北京市第十五中学2024—2025学年下学期七年级期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 75, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 75, "timestamp": "2025-07-01T02:09:40.681Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}