{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 107, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "571879432136204288", "questionArticle": "<p>1．为响应“全民植树增绿,共建美丽中国”的号召,学校组织学生到郊外参加义务植树活动,并准备了A,B两种食品作为午餐.这两种食品每包质量均为50 g,营养成分表如下.</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/28/2/1/0/0/0/571879384459550722/images/img_3.png\" style=\"vertical-align:middle;\" width=\"200\" alt=\"试题资源网 https://stzy.com\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/28/2/1/0/0/0/571879384463745024/images/img_4.png\" style=\"vertical-align:middle;\" width=\"200\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)若要从这两种食品中摄入4 600 kJ热量和70 g蛋白质,应选用A,B两种食品各多少包?</p><p>(2)运动量大的人或青少年对蛋白质的摄入量应更多.若每份午餐选用这两种食品共7包,要使每份午餐中的蛋白质含量不低于90 g,且热量最低,应如何选用这两种食品?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16440|16486", "keyPointNames": "表格或图示问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训4 方程(组)及其应用", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "571879431410589696", "questionArticle": "<p>2．“今有鸡兔同笼,上有三十五头,下有九十四足,问鸡兔各几何”是《孙子算经》卷中著名数学问题.意思是鸡兔同笼,从上面数,有35个头;从下面数,有94条腿.问鸡兔各有多少只?若设鸡有<i>x</i>只,兔有<i>y</i>只,则所列方程组正确的是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）&nbsp;</p><p>A． $ \\begin{cases}x+y=35,\\\\ 4x+2y=94\\end{cases} $ B． $ \\begin{cases}x+y=35,\\\\ 2x+4y=94\\end{cases} $ C． $ \\begin{cases}x+y=94,\\\\ 4x+2y=35\\end{cases} $ D． $ \\begin{cases}x+y=94,\\\\ 2x+4y=35\\end{cases} $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训4 方程(组)及其应用", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "571879430026469376", "questionArticle": "<p>3．中国古代数学著作《九章算术》中记载了这样一个题目:今有共买琎,人出半,盈四;人出少半,不足三.问人数,琎价各几何?其大意是今有人合伙买琎石,每人出 $ \\dfrac{1}{2} $ 钱,会多出4钱;每人出 $ \\dfrac{1}{3} $ 钱,又差了3钱.问人数,琎价各是多少?设人数为<i>x</i>,琎价为<i>y</i>,则可列方程组为&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}y=\\dfrac{1}{2}x+4,\\\\ y=\\dfrac{1}{3}x+3\\end{cases} $ B． $ \\begin{cases}y=\\dfrac{1}{2}x-4,\\\\ y=\\dfrac{1}{3}x+3\\end{cases} $ C． $ \\begin{cases}y=\\dfrac{1}{2}x-4,\\\\ y=\\dfrac{1}{3}x-3\\end{cases} $ D． $ \\begin{cases}y=\\dfrac{1}{2}x+4,\\\\ y=\\dfrac{1}{3}x-3\\end{cases} $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训4 方程(组)及其应用", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "571879426004131840", "questionArticle": "<p>4．某条城际铁路线共有A,B,C三个车站,每日上午均有两班次列车从A站驶往C站,其中D1001次列车从A站始发,经停B站后到达C站,G1002次列车从A站始发,直达C站,两个车次的列车在行驶过程中保持各自的行驶速度不变.某校数学学习小组对列车运行情况进行研究,收集到列车运行信息如下表所示.</p><p>列车运行时刻表</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 40pt;\"><p>车次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>A站</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 120pt;\"><p>B站</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>C站</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>发车时刻</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>到站时刻</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>发车时刻</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>到站时刻</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 40pt;\"><p>D1001</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>8:00</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>9:30</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>9:50</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>10:50</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 40pt;\"><p>G1002</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>8:25</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 120pt;\"><p>途经B站,不停车</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>10:30</p></td></tr></table><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>请根据表格中的信息,解答下列问题:</p><p>(1)D1001次列车从A站到B站行驶了<u>　　　　</u>分钟,从B站到C站行驶了<u>　　　　</u>分钟;&nbsp;</p><p>(2)记D1001次列车的行驶速度为<i>v</i><sub>1</sub>,离A站的路程为<i>d</i><sub>1</sub>;G1002次列车的行驶速度为<i>v</i><sub>2</sub>,离A站的路程为<i>d</i><sub>2</sub>.</p><p>① $ \\dfrac{{v}_{1}}{{v}_{2}} {\\rm =} $ <u>　　　　</u>;&nbsp;</p><p>②从上午8:00开始计时,时长记为<i>t</i>分钟(如:上午9:15,则<i>t</i>=75),已知<i>v</i><sub>1</sub>=240千米<i>/</i>小时(可换算为4千米<i>/</i>分钟),在G1002次列车的行驶过程中 $ {\\rm (25\\leqslant \\mathit{t}\\leqslant 150)} $ ,若|<i>d</i><sub>1</sub>-<i>d</i><sub>2</sub>|=60,求<i>t</i>的值.</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训4 方程(组)及其应用", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "571879421377814528", "questionArticle": "<p>5．某商场购进A,B两种商品,已知购进3件A商品比购进4件B商品费用多60元;购进5件A商品和2件B商品总费用为620元.</p><p>(1)求A,B两种商品每件进价各为多少元.</p><p>(2)该商场计划购进A,B两种商品共60件,且购进B商品的件数不少于A商品件数的2倍.若A商品按每件150元销售,B商品按每件80元销售,为满足销售完A,B两种商品后获得的总利润不低于1 770元,则购进A商品的件数最多为多少?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训4 方程(组)及其应用", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "571879420564119552", "questionArticle": "<p>6．乡村振兴战略实施以来,很多外出人员返乡创业.某村有部分返乡青年承包了一些田地.采用新技术种植A,B两种农作物.种植这两种农作物每公顷所需人数和投入资金如下表:</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 68.05pt;\"><p>农作物品种</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96.4pt;\"><p>每公顷所需人数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 141.75pt;\"><p>每公顷所需投入资金(万元)</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 68.05pt;\"><p>A</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96.4pt;\"><p>4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 141.75pt;\"><p>8</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 68.05pt;\"><p>B</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96.4pt;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 141.75pt;\"><p>9</p></td></tr></table><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>&nbsp;</p><p>已知农作物种植人员共24位,且每人只参与一种农作物种植,投入资金共60万元.问A,B这两种农作物的种植面积各多少公顷?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训4 方程(组)及其应用", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "571879417858793472", "questionArticle": "<p>7．国家“双减”政策实施后,某班开展了主题为“书香满校园”的读书活动.班级决定为在活动中表现突出的同学购买笔记本和碳素笔进行奖励(两种奖品都买).其中笔记本每本3元,碳素笔每支2元,共花费28元,则共有几种购买方案&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．5B．4C．3D．2</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训4 方程(组)及其应用", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "571879417082847232", "questionArticle": "<p>8．解方程组: $ \\begin{cases}x+2y=3,\\\\ x-2y=1.\\end{cases} $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训4 方程(组)及其应用", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "571879416323678208", "questionArticle": "<p>9．关于<i>x</i>,<i>y</i>的方程组 $ \\begin{cases}3x+y=2m-1,\\\\ x-y=n\\end{cases} $ 的解满足<i>x</i>+<i>y</i>=1,则4<i><sup>m</sup></i>÷2<i><sup>n</sup></i>的值是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）&nbsp;</p><p>A．1B．2C．4D．8</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16320|16322|16424", "keyPointNames": "幂的乘方|同底数幂的除法|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训4 方程(组)及其应用", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "571879415346405376", "questionArticle": "<p>10．对于二元一次方程组 $ \\begin{cases}y=x-1,①\\\\ x+2y=7,②\\end{cases} $ 将①式代入②式,消去<i>y</i>可以得到&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．<i>x</i>+2<i>x</i>−1=7B．<i>x</i>+2<i>x</i>−2=7\t</p><p>C．<i>x</i>+<i>x</i>−1=7D．<i>x</i>+2<i>x</i>+2=7</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025全国 · 专题模块", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-28", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第一部分 第二章 方程(组)与不等式(组) 分类集训4 方程(组)及其应用", "paperCategory": 2}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 108, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 108, "timestamp": "2025-07-01T02:13:34.738Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}