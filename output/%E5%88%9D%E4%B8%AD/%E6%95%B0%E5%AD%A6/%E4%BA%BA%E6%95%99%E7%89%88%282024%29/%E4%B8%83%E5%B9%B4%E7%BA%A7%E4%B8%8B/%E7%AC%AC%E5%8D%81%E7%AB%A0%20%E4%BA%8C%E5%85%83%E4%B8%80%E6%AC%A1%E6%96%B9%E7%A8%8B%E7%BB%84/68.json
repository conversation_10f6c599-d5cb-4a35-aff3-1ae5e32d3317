{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 67, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "578358645001334784", "questionArticle": "<p>1．下列方程中，是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x+y=1 $ B． $ x{^{2}}+y{^{2}}=1 $ C． $ xy=1 $ D． $ x+\\dfrac { 1 } { y }=1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578358636025524224", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578358636025524224", "title": "河北省石家庄市第41中教育集团2024−2025学年下学期阶段性学业质量评价七年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578361001294209024", "questionArticle": "<p>2．刀鱼馄饨是江苏江阴的特色美食，被誉为“初春第一鲜”．清明节前后是刀鱼馄饨销售的高峰，某电商平台推出 $ \\mathrm{ A } $ ， $ B $ 两种型号的刀鱼馄饨礼盒，第一天售出 $ \\mathrm{ A } $ 礼盒8个、 $ B $ 礼盒5个，总计收入1400元，第二天售出 $ \\mathrm{ A } $ 礼盒6个、 $ B $ 礼盒10个，总计收入1800元；</p><p>（1） $ \\mathrm{ A } $ ， $ B $ 两种型号的刀鱼馄饨礼盒每盒的售价分别是多少元？</p><p>（2）李叔叔在澄务工，清明假期计划同时购买这两种礼盒赠予亲朋（ $ \\mathrm{ A } $ ， $ B $ 都需要购买），预算为1300元．请你帮助他设计预算资金恰好用完时的购买方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏天一 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16420|16437", "keyPointNames": "二元一次方程的解|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578360968788353024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578360968788353024", "title": "江苏省无锡市天一中学2024−2025学年下学期七年级数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578360996579811328", "questionArticle": "<p>3．解方程：</p><p>（1） $ \\begin{cases} x=1-2y \\\\ 2x+3y=\\dfrac { 5 } { 2 } \\end{cases}  $ </p><p>（2） $ \\begin{cases} 3x+4y=19 \\\\ x-y=4 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏天一 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578360968788353024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578360968788353024", "title": "江苏省无锡市天一中学2024−2025学年下学期七年级数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578360994742706176", "questionArticle": "<p>4．九宫格，一款数字游戏，起源于《河图洛书》，是中国古代流传下来的图案，被誉为“宇宙魔方”．在如图所示的九宫格中，横向、纵向及对角线上的实数之和相等，则 $ x= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/15/2/1/0/0/0/578360932973191169/images/img_29.png\" style=\"vertical-align:middle;\" width=\"103\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏天一 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578360968788353024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578360968788353024", "title": "江苏省无锡市天一中学2024−2025学年下学期七年级数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578360987939545088", "questionArticle": "<p>5．关于<i>x</i>．<i>y</i>的方程组 $ \\begin{cases} ax+by=c \\\\ mx+ny=d \\end{cases}  $ 的解为 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ ，则方程组 $ \\begin{cases} a\\left ( { x-1 } \\right ) -3by=3c \\\\ m\\left ( { x-1 } \\right ) -3ny=3d \\end{cases}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=4 \\\\ y=2 \\end{cases}  $ B． $ \\begin{cases} x=4 \\\\ y=-2 \\end{cases}  $ C． $ \\begin{cases} x=2 \\\\ y=-\\dfrac { 2 } { 3 } \\end{cases}  $ D． $ \\begin{cases} x=2 \\\\ y=\\dfrac { 2 } { 3 } \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏天一 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578360968788353024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578360968788353024", "title": "江苏省无锡市天一中学2024−2025学年下学期七年级数学期中试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578360984626044928", "questionArticle": "<p>6．明代《算法统宗》有一首饮酒数学诗：“肆中饮客乱纷纷，薄酒名醨厚酒醇．醇酒一瓶醉三客，薄酒三瓶醉一人．共同饮了一十九，三十三客醉颜生．试问高明能算士，几多醨酒几多醇？”设有醇酒 $ x $ 瓶，薄酒 $ y $ 瓶．根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=19 \\\\ 3x+\\dfrac { 1 } { 3 }y=33 \\end{cases}  $ B． $ \\begin{cases} x+y=19 \\\\ x+3y=33 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=19 \\\\ \\dfrac { 1 } { 3 }x+3y=33 \\end{cases}  $ D． $ \\begin{cases} x+y=19 \\\\ 3x+y=33 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏天一 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578360968788353024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578360968788353024", "title": "江苏省无锡市天一中学2024−2025学年下学期七年级数学期中试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578365858503766016", "questionArticle": "<p>7．某水果店用3450元购进甲、乙两种水果共 $ { { 2 } }{ { 0 } }{ { 0 } }{ \\rm{ k } }{ \\rm{ g } } $ ，每种水果的成本价与利润率如表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>类别</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92pt;\"><p>成本价（元/ $ { \\rm{ k } }{ \\rm{ g } } $ ）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>利润率</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>甲</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92pt;\"><p>20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p> $ 25％ $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>乙</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92pt;\"><p>15</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p> $ 20％ $ </p></td></tr></table><p>全部售完后，求该水果店获得的总利润．[注：利润 $ = $ 售价 $ - $ 成本，利润率 $ = $ （售价 $ - $ 成本） $ \\div  $ 成本 $ \\times 100\\% $ ]</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽合肥 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578365826077601792", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "578365826077601792", "title": "2025年安徽省合肥市瑶海区5月中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015013790588928", "questionArticle": "<p>8．对于平面直角坐标系 $ xOy $ 中的任意一点 $ P\\left ( { x,y } \\right )  $ ，给出如下定义：记 $ a=x+y $ ， $ b=-y $ ，将点 $ M\\left ( { a,b } \\right )  $ 与 $ N\\left ( { b,a } \\right )  $ 称为点 $ P $ 的一对“相伴点”．例如：点 $ P\\left ( { 2,3 } \\right )  $ 的一对“相伴点”是点 $ \\left ( { 5,-3 } \\right )  $ 与 $ \\left ( { -3,5 } \\right )  $ ．</p><p>（1）求点 $ Q\\left ( { 4,-1 } \\right )  $ 的一对“相伴点”的坐标；</p><p>（2）若点 $ A\\left ( { 8,y } \\right )  $ 的一对“相伴点”重合，求 $ y $ 的值；</p><p>（3）若点 $ B $ 的一对“相伴点”之一为 $ \\left ( { -1,7 } \\right )  $ ，求点 $ B $ 的坐标．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000|150000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京交通大学附属中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-05-20", "keyPointIds": "16299|16433|16501", "keyPointNames": "实数的运算|数字问题|坐标与图形性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578014982274588672", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "578014982274588672", "title": "北京交通大学附属中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "209628715533246464", "title": "内蒙古自治区鄂尔多斯市康巴什区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578366759901634560", "questionArticle": "<p>9．“滨滨”和“妮妮”是 $ 2025 $ 年第九届亚洲冬季运动会的吉祥物．某亚冬会官方特许商品零售店购进了一批同一型号的“滨滨”和“妮妮”手办，连续两个月的销售情况如下表：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/15/2/1/0/0/0/578366695380660229/images/img_19.png\" style=\"vertical-align:middle;\" width=\"163\" alt=\"试题资源网 https://stzy.com\"></p><table style=\"border: solid 1px;border-collapse: collapse; width:212.25pt;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50.25pt;\"><p>月份</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100.5pt;\"><p>销售量/个</p></td><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 53.25pt;\"><p>销售额/元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50.25pt;\"><p>滨滨</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 47.25pt;\"><p>妮妮</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50.25pt;\"><p> $ 1 $ 月</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 47.25pt;\"><p> $ 80 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 53.25pt;\"><p> $ 50 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 61.5pt;\"><p> $ 6800 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50.25pt;\"><p> $ 2 $ 月</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 47.25pt;\"><p> $ 100 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 53.25pt;\"><p> $ 60 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 61.5pt;\"><p> $ 8400 $ </p></td></tr></table><p>（1）求该店“滨滨”和“妮妮”手办的单价；</p><p>（2）为了扩大销量，增加盈利，该店对两种手办进行降价促销，其中“滨滨”手办八折销售，“妮妮”手办七五折销售，某学校欲购买若干个“滨滨”和“妮妮”手办作为亚冬会知识竞赛活动的奖品，且“滨滨”手办的数量恰好是“妮妮”手办数量的 $ 2 $ 倍，若总费用不超过 $ 1300 $ 元，那么该校最多可购买多少个“滨滨”手办？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东东莞 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16438|16440|16486", "keyPointNames": "和差倍分问题|表格或图示问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578366730277265408", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "578366730277265408", "title": "2025年广东省东莞市大朗第一中学　九年级数学第二次模拟测试", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578367183295651840", "questionArticle": "<p>10．如图，两灯泡 $ L{{}_{ 1 } } $ 与 $ L{{}_{ 2 } } $ 的电阻之和为 $ 12Ω $ ，闭合开关<i>S</i>后，测得灯泡 $ L{{}_{ 1 } } $ 与 $ L{{}_{ 2 } } $ 两端的电压分别为2V、4V，则灯泡 $ L{{}_{ 1 } } $ 与 $ L{{}_{ 2 } } $ 的电阻 $ R{{}_{ 1 } } $ 与 $ R{{}_{ 2 } } $ 分别是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/15/2/1/0/0/0/578367143751757834/images/img_10.png\" style=\"vertical-align:middle;\" width=\"155\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} R{{}_{ 1 } }=8{ \\rm{ Ω } } \\\\ R{{}_{ 2 } }=4{ \\rm{ Ω } } \\end{cases}  $ B． $ \\begin{cases} R{{}_{ 1 } }=4{ \\rm{ Ω } } \\\\ R{{}_{ 2 } }=8{ \\rm{ Ω } } \\end{cases}  $ C． $ \\begin{cases} R{{}_{ 1 } }=10{ \\rm{ Ω } } \\\\ R{{}_{ 2 } }=2{ \\rm{ Ω } } \\end{cases}  $ D． $ \\begin{cases} R{{}_{ 1 } }=2{ \\rm{ Ω } } \\\\ R{{}_{ 2 } }=10{ \\rm{ Ω } } \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东韶关 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-20", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578367164291260416", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578367164291260416", "title": "2025年广东省韶关市中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 68, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 68, "timestamp": "2025-07-01T02:08:47.979Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}