{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 34, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "588078205405278208", "questionArticle": "<p>1．《九章算术》中有一道“甲乙持钱”问题，大意如下：甲、乙两人各有钱，但数目未知．若甲得到乙钱的一半，则甲有50钱；若乙得到甲钱的三分之二，则乙也有50钱，问甲、乙原有多少钱？设甲原有 $ x $ 钱，乙原有 $ y $ 钱，则（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+\\dfrac { y } { 2 }=50 \\\\ \\dfrac { 2 } { 3 }x+y=50 \\end{cases}  $ B． $ \\begin{cases} x+\\dfrac { y } { 2 }=50 \\\\ x+\\dfrac { 2 } { 3 }y=50 \\end{cases}  $ C． $ \\begin{cases} \\dfrac { x } { 2 }+y=50 \\\\ \\dfrac { 2 } { 3 }x+y=50 \\end{cases}  $ D． $ \\begin{cases} \\dfrac { x } { 2 }+y=50 \\\\ x+\\dfrac { 2 } { 3 }y=50 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东中山市华侨中学 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-06-13", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588078191014621184", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "588078191014621184", "title": "2025年广东省中山市华侨中学中考数学第三次模测试", "paperCategory": 1}, {"id": "586983848795217920", "title": "山东省日照市东港区日照港中学2024-2025学年九年级下学期三模考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "586668204463792128", "questionArticle": "<p>2．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} 2x+y=1-3m \\\\ x+2y=1+m \\end{cases}  $ 的解满足 $ x+y &gt; 0 $ ，求 $ m $ 的取值范围．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-13", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586668182045237248", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "586668182045237248", "title": "陕西省榆林市第六中学2024—2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586668202245005312", "questionArticle": "<p>3．已知 $ \\begin{cases} x=2 \\\\ y=3 \\end{cases}  $ 是关于 $ x $ ， $ y $ 的二元一次方程 $ 2x+ay=16 $ 的一组解，求 $ -2a $ 的立方根．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-13", "keyPointIds": "16290|16402|16420", "keyPointNames": "立方根|解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586668182045237248", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "586668182045237248", "title": "陕西省榆林市第六中学2024—2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586668201032851456", "questionArticle": "<p>4．解方程组： $ \\begin{cases} y+3=2x \\\\ 3x+2y=8 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-13", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586668182045237248", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "586668182045237248", "title": "陕西省榆林市第六中学2024—2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586668199019585536", "questionArticle": "<p>5．《孙子算经》记载：今有3人共车，二车空；二人共车，九人步，问人与车各几何？译文：今有若干人乘车，若每三人共乘一辆车，最终剩余2辆车；若每2人共乘一辆车，最终剩余9人无车可乘．问共有多少人？多少辆车？若设有x辆车，有y人，则可列方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-13", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586668182045237248", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "586668182045237248", "title": "陕西省榆林市第六中学2024—2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586668194611372032", "questionArticle": "<p>6．已知关于 $ x $ 、 $ y $ 的二元一次方程组满足 $ \\begin{cases} 2x+y+k=3 \\\\ x-2y-k=2 \\end{cases}  $ ，则代数式 $ 3x-y $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．5B．7C．4D．6</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-13", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586668182045237248", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "586668182045237248", "title": "陕西省榆林市第六中学2024—2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "586668192577134592", "questionArticle": "<p>7．若关于 $ x $ ， $ y $ 的方程 $ 2x{^{\\left  | { m } \\right  | }}+y=3 $ 是二元一次方程，则 $ m $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0B． $ \\pm 1 $ C．1D． $ -1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西榆林 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-13", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586668182045237248", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "586668182045237248", "title": "陕西省榆林市第六中学2024—2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "586984506667610112", "questionArticle": "<p>8．根据国家相关规定，新建小区的绿地率不得低于 $ 30{ \\rm{ \\% } } $ ，旧小区改造的绿地率不得低于 $ 25\\% $ ，一般地，绿地率可以看做是绿地面积（包括覆土绿地和实土绿地）与小区总面积的比，其中实土绿地是指绿化层下面为真实的土地，其面积应占总绿地面积的 $ 50\\% $ 以上，覆土绿地是在人工铺设的土层上进行绿化，当覆土高度小于 $ 1.5{ \\rm{ m } } $ 时，不算绿地面积；当覆土高度在 $ 1.5{ \\rm{ m } } $ 至 $ 3{ \\rm{ m } } $ 时，覆土面积的 $ 50{ \\rm{ \\% } } $ 计入绿地面积；只有当覆土高度超过 $ 3{ \\rm{ m } } $ 时，覆土面积才全部计入绿地面积．</p><p>某旧小区总面积为 $ 24000{ \\rm{ m } }{^{2}} $ ，绿地率只有 $ 15{ \\rm{ \\% } } $ ，且其中覆土绿地的覆土高度都约为 $ 2{ \\rm{ m } } $ ．现有一种改造方案，计划把原有覆土绿地的覆土高度都增加到 $ 3{ \\rm{ m } } $ 以上，并增加 $ 1400{ \\rm{ m } }{^{2}} $ 实土绿地，从而使实土绿地的面积达到总绿地面积的 $ 60\\% $ ．请判断按照该方案改造后，该小区的绿地率能否合格，并说明理由．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京朝阳 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-13", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586984471775195136", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586984471775195136", "title": "2025年北京市朝阳区九年级中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586984829259919360", "questionArticle": "<p>9．为设计一类推理型模型，已知购进2片 $ \\mathrm{ A } $ 型芯片和1片 $ B $ 型芯片共需7万元，购进1片 $ \\mathrm{ A } $ 型芯片和2片 $ B $ 型芯片共需5万元．若某公司计划投入205万元购进 $ A、B $ 两种型号的芯片共100片，求 $ \\mathrm{ A } $ 型芯片最多购进多少片？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建福州 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-13", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586984804509331456", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "586984804509331456", "title": "2025年福建省福州屏东中学中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586985055228043264", "questionArticle": "<p>10．下列选项中，最适合使代入消元法解方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y=x+3 \\\\ 7x+5y=9 \\end{cases}  $ B． $ \\begin{cases} 2x+y=3 \\\\ 3x+y=4 \\end{cases}  $ </p><p>C． $ \\begin{cases} 3x+2y=7 \\\\ 6x-2y=11 \\end{cases}  $ D． $ \\begin{cases} 2x-5y=7 \\\\ 4x-3y=7 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-13", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586985030783639552", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "586985030783639552", "title": "2025年福建省厦门市思明区中考模拟数学试卷（5月）", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 35, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 35, "timestamp": "2025-07-01T02:04:56.551Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}