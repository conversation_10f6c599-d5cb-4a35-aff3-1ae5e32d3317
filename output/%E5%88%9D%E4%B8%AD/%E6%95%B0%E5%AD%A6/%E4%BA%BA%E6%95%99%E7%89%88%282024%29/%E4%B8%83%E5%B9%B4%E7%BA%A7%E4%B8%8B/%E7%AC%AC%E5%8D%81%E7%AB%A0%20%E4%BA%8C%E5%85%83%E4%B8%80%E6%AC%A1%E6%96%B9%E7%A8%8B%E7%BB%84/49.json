{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 48, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "580622835950858240", "questionArticle": "<p>1．计算</p><p>（1）解方程组： $ \\begin{cases} 3x+4y=7 \\\\ 5x-y=4 \\end{cases}  $ </p><p>（2）计算： $ {\\left( { -\\dfrac { 1 } { 2 } } \\right) ^ {2}}-2{^{-2}}+{\\left( { 2-π } \\right) ^ {0}}+{\\left( { -1 } \\right) ^ {2025}} $ </p><p>（3）计算： $ 2025{^{2}}-2027\\times 2023+9{^{7}}\\times {\\left( { -\\dfrac { 1 } { 9 } } \\right) ^ {7}} $ （应用简便方法，要有必要变形过程）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-04", "keyPointIds": "16278|16321|16331|16424", "keyPointNames": "有理数的混合运算|积的乘方|平方差公式|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622816157937664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622816157937664", "title": "河北省石家庄第九中学2024−2025学年级七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580622832247287808", "questionArticle": "<p>2．已知关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} x+3y=4-a \\\\ x-5y=3a \\end{cases}  $ ，给出下列结论：① $ \\begin{cases} x=5 \\\\ y=-1 \\end{cases}  $ 是方程组的解；②无论 $ a $ 取何值， $ x $ 、 $ y $ 的值都不可能互为相反数；③当 $ a=1 $ 时，方程组的解也是方程 $ x+y=4-a $ 的解：④ $ x、y $ 的都为自然数的解有4对．其中正确的为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．①②③B．②③C．③④D．②③④</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-06-04", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622816157937664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622816157937664", "title": "河北省石家庄第九中学2024−2025学年级七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580622826907938816", "questionArticle": "<p>3．利用加减消元法解方程组 $ \\begin{cases} 2x+5y=-10① \\\\ 5x-3y=6② \\end{cases}  $ 下列做法正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．要消去 $ x $ ，可以将① $ \\times \\left ( { -5 } \\right ) + $ ② $ \\times 2 $ B．要消去 $ x $ ，可以将① $ \\times 3+ $ ② $ \\times \\left ( { -5 } \\right )  $ </p><p>C．要消去 $ y $ ，可以将① $ \\times 5+ $ ② $ \\times 3 $ D．要消去 $ y $ ，可以将① $ \\times 5+ $ ② $ \\times 2 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-04", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622816157937664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622816157937664", "title": "河北省石家庄第九中学2024−2025学年级七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580622823212756992", "questionArticle": "<p>4．下列方程中，不是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ xy=1 $ B． $ 2x=3y $ C． $ x+y=0 $ D． $ \\dfrac { x } { 4 }=3y-1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-04", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622816157937664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622816157937664", "title": "河北省石家庄第九中学2024−2025学年级七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "583057011304083456", "questionArticle": "<p>5．我们知道方程组的解与方程组中每个方程的系数和常数项有联系，系数和常数项经过一系列变形、运算就可以求出方程组的解．因此，在现代数学的高等代数学科将系数和常数项排成一个表的形式，规定：关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ 可以写成矩阵 $ \\begin{pmatrix} a{{}_{ 1 } } & b{{}_{ 1 } } & c{{}_{ 1 } } \\\\ a{{}_{ 2 } } & b{{}_{ 2 } } & c{{}_{ 2 } } \\end{pmatrix}  $ 的形式．例如： $ \\begin{cases} 3x+4y=16 \\\\ 5x-6y=33 \\end{cases}  $ 可以写成矩阵 $ \\begin{pmatrix} 3 & 4 & 16 \\\\ 5 & -6 & 33 \\end{pmatrix}  $ 的形式．</p><p>（1）填空：将 $ \\begin{cases} y-5=4x \\\\ 3x-2y-3=0 \\end{cases}  $ 写成矩阵形式为： $ \\begin{pmatrix} \\_ & \\_ & \\_ \\\\ \\_ & \\_ & \\_ \\end{pmatrix}  $ ；</p><p>（2）若矩阵 $ \\begin{pmatrix} a & -5 & -3 \\\\ -4 & b & -3 \\end{pmatrix}  $ 所对应的方程组的解为 $ \\begin{cases} x=1 \\\\ y=1 \\end{cases}  $ ，求<i>a</i>与<i>b</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东江门 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-06-04", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583056986691907584", "questionFeatureName": "阅读材料题", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583056986691907584", "title": "广东省江门市广雅中学2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583057015657771008", "questionArticle": "<p>6．项目化学习</p><p>项目主题：确定最省钱的租车方案．</p><p>项目背景：为传承启超文化，弘扬“少年强则国强”的理念，江门广雅中学计划在六月下旬组织本校优秀学生代表前往梁启超故居参观学习．</p><p>数据收集：</p><p>①计划参加活动的优秀学生代表及教师共485人．</p><p>②某出租车公司有<i>A</i>，<i>B</i>两种型号的客车可供选择，<i>A</i>型客车每辆有25个座位，<i>B</i>型客车每辆有55个座位．</p><p>③下表是该公司租车记录单上的部分信息：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.2pt;\"><p style=\"text-align:center;\">租用<i>A</i>型客车数量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.2pt;\"><p style=\"text-align:center;\">租用<i>B</i>型客车数量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">租金总费用</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.2pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.2pt;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">3800</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.2pt;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 97.2pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">3600</p></td></tr></table><p>问题解决：利用以上数据完成下列问题．</p><p>（1）根据公司租车记录单上的信息，确定<i>A</i>，<i>B</i>两种型号每辆客车的租金分别是多少元．</p><p>（2）学校本次研学准备租用该租车公司的客车．若每辆客车恰好都坐满，求出所有满足条件的租车方案．</p><p>（3）在（2）问求出的方案中，应选择哪种方案，才能使租车费用最少，并说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东江门 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-04", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583056986691907584", "questionFeatureName": "项目化学习题", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583056986691907584", "title": "广东省江门市广雅中学2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585253230285729792", "questionArticle": "<p>7．小亮坚持体育锻炼，并用某种健身软件进行记录．小亮周六进行了两组运动，第一组安排30个深蹲，20个开合跳，健身软件显示消耗热量34千卡；第二组安排20个深蹲，40个开合跳，健身软件显示消耗热量36千卡．</p><p>（1）小亮每做一个深蹲和一个开合跳分别消耗多少热量？</p><p>（2）小亮想设计一个10分钟的锻炼组合，只进行深蹲和开合跳两个动作，且深蹲的数量不少于开合跳的数量．每个深蹲用时4秒，每个开合跳用时2秒，</p><p>①假设安排 $ m $ 个深蹲，则安排<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>个开合跳；（用含 $ m $ 的代数式填空．）</p><p>②小亮安排多少个深蹲使消耗的热量最多？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西西大附中 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-06-04", "keyPointIds": "16441|16486|16547", "keyPointNames": "其他问题|一元一次不等式的应用|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585253200250318848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "585253200250318848", "title": "广西大学附属中学2025年九年级5月阶段性（第八次）测试数学试题", "paperCategory": 1}, {"id": "578358524809355264", "title": "广东省深圳市南山区第二外国语学校（集团）2024−2025学年九年级中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583586140243927040", "questionArticle": "<p>8．小明租用共享单车从家出发，匀速骑行到相距2 400米的图书馆还书.小明出发的同时，他的爸爸以每分钟96米的速度从图书馆沿同一条道路步行回家，小明在图书馆停留了3分钟后沿原路按原速骑车返回.设他们出发后经过 $ t $ （分）时，小明与家之间的距离为 $ {s}_{1} $ （米），小明爸爸与家之间的距离为 $ {s}_{2} $ （米），图中折线 $ O-A-B-D $ 和线段 $ EF $ 分别表示 $ {s}_{1} $ , $ {s}_{2} $ 与 $ t $ 之间的函数关系的图象.小明从家出发，经过<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>分钟在返回途中追上爸爸.</p><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/30/2/1/0/0/0/583585881929326598/images/img_21.jpg\" style=\"vertical-align:middle;\" width=\"186\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "八年级 · 单元测试", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-04", "keyPointIds": "16414|16430|16466|16545|26247", "keyPointNames": "行程问题|行程问题|行程问题|行程问题|待定系数法求一次函数解析式", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第5章 对点上分（类题推送）《2025秋 初中上分卷 数学八年级上册 苏科版》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "584431705517236224", "questionArticle": "<p>9．为了贯彻落实市委政府提出的“精准扶贫”精神，某校特制定了一系列帮扶A、B两贫困村的计划，现决定从某地运送152箱鱼苗到A、B两村养殖，若用大小货车共15辆，则恰好能一次性运完这批鱼苗，已知这两种大小货车的载货能力分别为12箱/辆和8箱/辆，其运往A、B两村的运费如表：</p><table style=\"border: solid 1px;border-collapse: collapse; width:411pt;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92.25pt;\"><p style=\"text-align:center;\">车型</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 318.75pt;\"><p style=\"text-align:center;\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;目的地</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">A村（元/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">B村（元/辆）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">大货车</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">800</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">900</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">小货车</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">400</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">600</p></td></tr></table><p>（1）求这15辆车中大小货车各多少辆？</p><p>（2）现安排其中10辆货车前往<i>A</i>村，其余货车前往<i>B</i>村，设前往<i>A</i>村的大货车为<i>x</i>辆，前往<i>A</i>、<i>B</i>两村总费用为<i>y</i>元，试求出<i>y</i>与<i>x</i>的函数解析式．</p><p>（3）在（2）的条件下，若运往<i>A</i>村的鱼苗不少于100箱，请你写出使总费用最少的货车调配方案，并求出最少费用．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南郴州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-06-03", "keyPointIds": "16440|16486|16543", "keyPointNames": "表格或图示问题|一元一次不等式的应用|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584431670117310464", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "584431670117310464", "title": "湖南省郴州市2024−2025学年八年级下学期5月期中考试数学试题", "paperCategory": 1}, {"id": "221602738823536640", "title": "河南省商丘市柘城县2021-2022学年八年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584433585597227008", "questionArticle": "<p>10．我国古代经典著作《九章算术》中有一问题：“今有黄金7枚，白银9枚，称之重适等．交易其一，金轻十二两．问金、银一枚各重几何？”．意思是：甲袋中装有黄金7枚（每枚黄金重量相同），乙袋中装有白银9枚（每枚白银重量相同），称重两袋相等．两袋互相交换1枚后，甲袋比乙袋轻了12两（袋子重量忽略不计）．问黄金、白银每枚各重多少两？设每枚黄金重 $ x $ 两，每枚白银重 $ y $ 两，根据题意得（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 9x=7y \\\\ \\left ( { 8y+x } \\right ) -\\left ( { 6x+y } \\right ) =12 \\end{cases}  $ B． $ \\begin{cases} 7x=9y \\\\ 8y+x=6x+y \\end{cases}  $ </p><p>C． $ \\begin{cases} 7x=9y \\\\ \\left ( { 6x+y } \\right ) -\\left ( { 8y+x } \\right ) =12 \\end{cases}  $ D． $ \\begin{cases} 7x=9y \\\\ \\left ( { 8y+x } \\right ) -\\left ( { 6x+y } \\right ) =12 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北江夏区第一中学 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-06-03", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584433570166382592", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "584433570166382592", "title": "2025年湖北省武汉市江夏区第一中学九年级中考三模数学试题", "paperCategory": 1}, {"id": "562772831911583744", "title": "江苏省无锡市江南中学2024−2025学年九年级下学期数学3月月考试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 49, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 49, "timestamp": "2025-07-01T02:06:37.348Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}