{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 183, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "552661913701752832", "questionArticle": "<p>1．我国古代《算法统宗》里有这样一首诗：“我问开店李三公，众客都来到店中，一房七客多七客，一房九客一房空．”诗中后面两句的意思是：如果一间客房住7人，那么有7人无房可住；如果一间客房住9人，那么就空出一间客房，若设该店有客房<i>x</i>间，房客<i>y</i>人，则列出关于<i>x</i>、<i>y</i>的二元一次方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 7x-7=y \\\\ 9\\left ( { x-1 } \\right ) =y \\end{cases}  $ B． $ \\begin{cases} 7x+7=y \\\\ 9\\left ( { x-1 } \\right ) =y \\end{cases}  $ C． $ \\begin{cases} 7x+7=y \\\\ 9x-1=y \\end{cases}  $ D． $ \\begin{cases} 7x-7=y \\\\ 9x-1=y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|520000|340000|330000|450000|430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2022江苏宿迁 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 7, "createTime": "2025-03-06", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "207135501949640704", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "207135501949640704", "title": "江苏省宿迁市2022年中考数学真题", "paperCategory": 1}, {"id": "491398471565484032", "title": "湖南省长沙市2024−2025学年八年级上学期开学考试数学试题", "paperCategory": 1}, {"id": "433397407218442240", "title": "2024年安徽省马鞍山市第八中学中考一模数学试题", "paperCategory": 1}, {"id": "552661899944435712", "title": "2024年贵州省贵阳市云岩区贵阳市第二十八中学九年级中考二模数学试题", "paperCategory": 1}, {"id": "471053411481329664", "title": "浙江省绍兴市越城区2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "350046747991777280", "title": "2023年湖南省岳阳市中考一模数学试题", "paperCategory": 1}, {"id": "476808671021801472", "title": "广西壮族自治区南宁市青秀区第十四中学2023−2024学年八年级上学期开学数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "552326863793725440", "questionArticle": "<p>2．“冰雪同梦 亚洲同心”．随着时间推移，冰雪之约渐近，亚冬盛会将启．阳光社区决定举办一次社区冬季徒步活动，活动结束后，为参加活动的居民准备了<i>A</i>，<i>B</i>两种食品作为午餐．这两种食品每包质量均为60克，营养成分表如下．</p><table style=\"border: solid 1px;border-collapse: collapse; width:288pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>A</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>B</i></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>项目</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>营养成分表（每60克）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>营养成分表（每60克）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>热量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>700千焦</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>900千焦</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>蛋白质</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>10克</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>15克</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>脂肪</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 5.3 $ 克</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 18.2 $ 克</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>碳水化合物</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>28克</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>6克</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>纳</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>205毫克</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>236毫克</p></td></tr></table><p>(1)若要从这两种食品中摄入3900千焦热量和60克蛋白质，应选用<i>A</i>，<i>B</i>两种食品各多少包？</p><p>(2)对于减肥的人群来说，对碳水化合物的摄入量要加以控制．若每份午餐选用这两种食品共7包，要使每份午餐中的碳水化合物含量不高于108克，则最多选择<i>A</i>种食物多少包？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈尔滨 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-06", "keyPointIds": "16440|16486", "keyPointNames": "表格或图示问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552326836300062720", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "552326836300062720", "title": "黑龙江省哈尔滨市第四十九中学2024−2025学年九年级下学期开学测试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552326851311476736", "questionArticle": "<p>3．已知张华的家、画社、文化广场依次在同一条直线上，张华从家出发匀速骑行到画社，在画社停留了一段时间，之后匀速骑行到文化广场，在文化广场停留了一段时间后，再匀速步行返回家，如图所示的图象反映了这个过程中张华离家的距离 $ y $ （单位： $ { \\rm{ k } }{ \\rm{ m } } $ ）与时间 $ x $ （单位： $ { \\rm{ m } }{ \\rm{ i } }{ \\rm{ n } } $ ）之间的对应关系．根据提供信息得出以下四个结论,</p><p> $ ① $ 张华在画社停留 $ 19 $ 分钟；</p><p> $ ② $ 张华从家出发匀速骑行到画社的速度与从画社匀速骑行到文化广场的速度相同；</p><p> $ ③ $ 张华步行返回家的速度为 $ 75 { \\rm{ m } }/{ \\rm{ m } }{ \\rm{ i } }{ \\rm{ n } } $ ；</p><p> $ { \\rm{ ④ } } $ 张华离家的距离为 $ 300 { \\rm{ m } } $ 时，张华离家的时间为 $ 47 { \\rm{ m } }{ \\rm{ i } }{ \\rm{ n } } $ ．</p><p>以上四个结论正确的有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）个</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/07/2/20/0/0/0/553205655697399809/images/img_1.png\" style='vertical-align:middle;' width=\"315\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ 1 $　　　　B． $ 2 $　　　　C． $ 3 $　　　　D． $ 4 $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈尔滨 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-06", "keyPointIds": "16430|16519", "keyPointNames": "行程问题|从函数的图象获取信息", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552326836300062720", "questionMethodName": "分类讨论思想", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "552326836300062720", "title": "黑龙江省哈尔滨市第四十九中学2024−2025学年九年级下学期开学测试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "552332319236857856", "questionArticle": "<p>4．中国古代数学著作《张邱建算经》中有一道题：“今有甲、乙怀钱，各不知其数，甲得乙十钱，多乙余钱五倍．乙得甲十钱，适等．问甲、乙怀钱各几何？”问题大意：甲、乙两人各有钱币若干枚．若乙给甲10枚钱，此时甲的钱币数比乙的钱币数多出5倍，即甲的钱币数是乙钱币数的6倍；若甲给乙10枚钱，此时两人的钱币数相等．问甲、乙原来各有多少枚钱币？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林吉林第二实验学校 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-05", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552332299309719552", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "552332299309719552", "title": "吉林省第二实验学校2024一2025学年下学期九年级开学考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552332025056763904", "questionArticle": "<p>5．解方程（组）：</p><p>(1) $ \\dfrac { 2x-1 } { 3 }-\\dfrac { 6x+1 } { 6 }=1 $ ；</p><p>(2) $ \\begin{cases} 2x+3y=-4 \\\\ 6x-5y=16 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南株洲二中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-03-05", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552332004940881920", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "552332004940881920", "title": "湖南省株洲市第二中学2024−2025学年七年级下学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552332021030232064", "questionArticle": "<p>6．已知 $ 2x{^{n-3}}-\\dfrac { 1 } { 3 }y{^{2m+1}}=0 $ 是关于 $ x $ ， $ y $ 的二元一次方程，则 $ n+m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南株洲二中 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-05", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552332004940881920", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "552332004940881920", "title": "湖南省株洲市第二中学2024−2025学年七年级下学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549854705960132608", "questionArticle": "<p>7．李老师逛超市时看中一套碗，她将碗叠成一列（如图），测量后发现：用2个碗叠放时总高度为 $ 7.5{ \\rm{ c } }{ \\rm{ m } } $ ，用4个碗叠放时总高度为 $ 11.5{ \\rm{ c } }{ \\rm{ m } } $ ．若将8个碗叠成一列能放入消毒柜，则这个消毒柜的内置高度至少有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/06/2/20/0/0/0/552834293426331649/images/img_1.png\" style='vertical-align:middle;' width=\"131\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ 15.5{ \\rm{ c } }{ \\rm{ m } } $　　　　B． $ 17.5{ \\rm{ c } }{ \\rm{ m } } $　　　　C． $ 19.5{ \\rm{ c } }{ \\rm{ m } } $　　　　D． $ 21.5{ \\rm{ c } }{ \\rm{ m } } $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西西大附中 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-05", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549854691951157248", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549854691951157248", "title": "广西大学附属中学2024−2025学年下学期九年级开学考试数学", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "550040086320029696", "questionArticle": "<p>8．昨天，一蔬菜经营户用114元从蔬菜批发市场购进黄瓜和西红柿共 $ 40{ \\rm{ k } }{ \\rm{ g } } $ 到菜市场去卖，黄瓜和西红柿的批发价和零售价如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92.25pt;\"><p>品   名</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>黄瓜</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>西红柿</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92.25pt;\"><p>批发价（元 $ /{ \\rm{ k } }{ \\rm{ g } } $ ）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>2.4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>3</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92.25pt;\"><p>零售价（元 $ /{ \\rm{ k } }{ \\rm{ g } } $ ）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p>4</p></td></tr></table><p>（1）他昨天购进黄瓜和西红柿各多少 $ { \\rm{ k } }{ \\rm{ g } } $ ？</p><p>（2）今天他又按照批发价买入 $ 10{ \\rm{ k } }{ \\rm{ g } } $ 黄瓜和 $ 50{ \\rm{ k } }{ \\rm{ g } } $ 西红柿，黄瓜仍然按照3元 $ /{ \\rm{ k } }{ \\rm{ g } } $ 销售，但运输过程中西红柿损坏了20%，要使这两天的利润率为 $ \\dfrac { 1 } { 3 } $ ，今天的西红柿售价应为多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南郑州外国语学校分校 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-05", "keyPointIds": "16406|16437", "keyPointNames": "销售盈亏问题|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "550040067827343360", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "550040067827343360", "title": "河南省郑州外国语中学2024−2025学年七年级下学期开学作业反馈数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552330452171792384", "questionArticle": "<p>9．重百江津商场销售<i>AB</i>两种商品，售出1件<i>A</i>种商品和4件<i>B</i>种商品所得利润为600元，售出3件<i>A</i>商品和5件<i>B</i>种商品所得利润为1100元．</p><p>（1）求每件<i>A</i>种商品和每件<i>B</i>种商品售出后所得利润分别为多少元？</p><p>（2）由于需求量大<i>A</i>，<i>B</i>两种商品很快售完，重百江津商场决定再次购进<i>A</i>，<i>B</i>两种商品共34件，如果将这34件商品全部售完后所得利润不低于4000元，那么重百江津商场至少购进多少件<i>A</i>种商品？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|230000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈尔滨 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 4, "createTime": "2025-03-05", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552330413542252544", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "552330413542252544", "title": "黑龙江省哈尔滨市萧红中学2024−2025学年九年级下学期数学开学测试卷", "paperCategory": 1}, {"id": "294108471829504000", "title": "黑龙江省哈尔滨市风华中学校2022-2023学年九年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "1051256818372608", "title": "山东省聊城市城区2019年中考二模数学试题", "paperCategory": 1}, {"id": "173935853323984896", "title": "广东省佛山市高明区高明实验中学2017-2018学年九年级第二阶段联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551570439983636480", "questionArticle": "<p>10．快递员把货物送到客户手中称为送件，帮客户寄出货物称为揽件，快递员的提成取决于送件和揽件数量．某快递公司的快递员小李送件100件和揽件40件，提成为230元；送件120件和揽件20件，提成为220元．求快递员小李送1件货物和揽1件货物的提成分别为多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林长春 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-04", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551570416688472064", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "551570416688472064", "title": "2024年吉林省长春市朝阳区中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 184, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 184, "timestamp": "2025-07-01T02:22:38.177Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}