{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 193, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "544973595031674880", "questionArticle": "<p>1．元旦期间，若干名家长和学生去某景区游玩．请根据景区票价公示栏中的信息及两人的对话，解答下列问题：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/12/2/1/0/0/0/544973560382529537/images/img_23.png\" style=\"vertical-align:middle;\" width=\"454\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)求这次参加游玩的家长和学生各多少人？</p><p>(2)通过计算说明，如果家长和学生一起购买团体票，能否比分别购票更省钱？</p><p>(3)另有9名家长和6名学生也计划去这个景区游玩，请直接写出这15人按照上述景区票价购票，最少需要多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江绥化 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-02-14", "keyPointIds": "16424|16434|16438", "keyPointNames": "加减消元法解二元一次方程组|方案问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544973585732902912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544973585732902912", "title": "黑龙江省绥化市2024—2025学年上学期七年级期末考试数学试卷", "paperCategory": 1}, {"id": "403064434149597184", "title": "黑龙江省联考2023-2024学年七年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544973590812205056", "questionArticle": "<p>2．某车间有60名工人生产太阳镜，1名工人每天可生产镜片200片或镜架50个．每副太阳镜需要2片镜片和1个镜架配成一套，应如何分配工人生产镜片和镜架，才能使产品配套？设安排<i>x</i>名工人生产镜片，<i>y</i>名工人生产镜架，则可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=60 \\\\ 2\\times 200x=50y \\end{cases}  $ B． $ \\begin{cases} x+y=60 \\\\ 200x=50y \\end{cases}  $ C． $ \\begin{cases} x+y=60 \\\\ 2\\times 50x=200y \\end{cases}  $ D． $ \\begin{cases} x+y=60 \\\\ 200x=2\\times 50y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江绥化 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-02-14", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544973585732902912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544973585732902912", "title": "黑龙江省绥化市2024—2025学年上学期七年级期末考试数学试卷", "paperCategory": 1}, {"id": "519615561984155648", "title": "广西南宁市第二中学2024−2025学年八年级上学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "545280614573842432", "questionArticle": "<p>3． 方程 $ A:kx-y+1=0 $ ，其中 $ k &gt; 0 $ ，对 $ x $ 的系数 $ k $ 作变化：得到方程 $ A{{}_{ 1 } }:k{{}_{ 1 } }x-y+1=0 $ ，其中 $ k{{}_{ 1 } }=\\dfrac { 1 } { \\dfrac { 1 } { k }+1 } $ ，称为对方程 $ \\mathrm{ A } $ 进行一次“偏移变化”，再对方程 $ A{{}_{ 1 } } $ 中 $ x $ 的系数 $ k{{}_{ 1 } } $ 作变化：得到方程 $ A{{}_{ 2 } }:k{{}_{ 2 } }x-y+1=0 $ ，其中 $ k{{}_{ 2 } }=\\dfrac { 1 } { \\dfrac { 1 } { k{{}_{ 1 } } }+1 } $ ，称为对方程 $ \\mathrm{ A } $ 进行二次“偏移变化”……，在变化过程中，记 $ d{{}_{ n } }=\\dfrac { 1 } { k }\\left  | { k{{}_{ n } }-k } \\right  |  $ 为偏移距离（ $ n $ 为正整数）， $ M{{}_{ n } }=\\dfrac { 1 } { 1-d{{}_{ 1 } } }+\\dfrac { 1 } { 1-d{{}_{ 2 } } }+\\cdots +\\dfrac { 1 } { 1-d{{}_{ n } } } $ ，则以下说法中，正确的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>①当 $ k=2 $ 时， $ \\begin{cases} x=14 \\\\ y=5 \\end{cases}  $ 是对方程 $ \\mathrm{ A } $ 进行三次“偏移变化”后得到方程 $ A{{}_{ 3 } } $ 的一组解；</p><p>②存在一个 $ k $ 值，使得对方程 $ \\mathrm{ A } $ 进行偏移变化，偏移距离为 $ \\dfrac { 15 } { 16 } $ ；</p><p>③满足使 $ \\dfrac { M{{}_{ 8 } } } { M{{}_{ 3 } } } $ 为整数的 $ k $ 的最小值为 $ \\dfrac { 1 } { 18 } $ </p><p>A．0B．1C．2D．3</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025重庆重庆市巴蜀中学校 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-14", "keyPointIds": "16258|16311|16424", "keyPointNames": "绝对值方程|规律型：数与式的变化类|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545280610144657408", "questionFeatureName": "新定义问题|规律探究题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "545280610144657408", "title": "重庆市巴蜀中学2024−2025学年八年级上学期期末数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544972350552317952", "questionArticle": "<p>4．解方程组 $ \\begin{cases} 2x+y=3 \\\\ 3x-2y=8 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽淮北 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-13", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544972343086456832", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "544972343086456832", "title": "安徽省淮北市2024-2025学年七年级上学期1月期末考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544972349298221056", "questionArticle": "<p>5．已知关于 $ x,y $ 的二元一次方程组 $ \\begin{cases} -x+2y=-2m \\\\ 2x-y=2m+3 \\end{cases}  $ ，下列结论正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>①当 $ m=1 $ 时，方程组的解也是 $ x+y=2m+1 $ 的解；</p><p>② $ x,y $ 均为正整数的解只有1对；</p><p>③无论 $ m $ 取何值， $ x $ 、 $ y $ 的值不可能互为相反数；</p><p>④若方程组的解满足 $ x-y=1 $ ，则 $ m=0 $ ．</p><p>A．①③④B．②③④C．①②④D．①②③</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽淮北 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-13", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544972343086456832", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "544972343086456832", "title": "安徽省淮北市2024-2025学年七年级上学期1月期末考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544972551300096000", "questionArticle": "<p>6．对于实数<i>x</i>、<i>y</i>我们定义一种新运算 $ H\\left ( { x、y } \\right ) =mx+ny $ （其中<i>m</i>，<i>n</i>均为非零常数），等式右边是通常的四则运算，由这种运算得到的数我们称之为线性数，记为 $ H\\left ( { x、y } \\right )  $ ，其中<i>x</i>、<i>y</i>叫做线性数的一个数对．若实数<i>x</i>、<i>y</i>都取正整数，我们称这样的线性数为正格线性数，这时的<i>x</i>、<i>y</i>叫做正格线性数的正格数对．</p><p>（1）若 $ H\\left ( { x、y } \\right ) =2x+3y $ ，则 $ H\\left ( { 3、1 } \\right ) = $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>（2）已知 $ H\\left ( { 1、3 } \\right ) =9 $ ， $ H\\left ( { 3、1 } \\right ) =11 $ ，请回答问题： $ m+n= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ m-n= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京昌平 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-13", "keyPointIds": "16278|16424", "keyPointNames": "有理数的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544972538863984640", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "544972538863984640", "title": "北京市昌平区2024−2025学年上学期七年级期末质量抽测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544972546359205888", "questionArticle": "<p>7．某次知识竞赛共出了25道试题，评分标准如下：答对1道题加4分，答错1道题扣1分，不答记0分．已知李刚不答的题比答错的题多2道，他的总分为74分，则他答对了（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．18道B．19道C．20道D．21道</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京昌平 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-13", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544972538863984640", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "544972538863984640", "title": "北京市昌平区2024−2025学年上学期七年级期末质量抽测数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544972769609424896", "questionArticle": "<p>8．《九章算术》第七章“盈不足”中有一题：“今有共买物，人出八，盈三钱；人出七，不足四，问人数、物价各几何”</p><p>译文：现有一些人买一件物品，每人出8钱，则结余3钱；若每人出7钱，则还差4钱．问购买物品的人数是多少？这件物品的价格是多少？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|350000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建漳州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 4, "createTime": "2025-02-13", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544972754077917184", "questionFeatureName": "数学文化题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544972754077917184", "title": "福建省漳州市2024−2025学年七年级上学期期末考试数学试题（北师大版A卷）", "paperCategory": 1}, {"id": "261115710453096448", "title": "山西省大同市第十二中学校2022-2023学年七年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "400809584535838720", "title": "广东省东莞市2022-2023学年七年级上学期期末数学试题", "paperCategory": 1}, {"id": "255408031277555712", "title": "福建省厦门市思明区莲花中学2021-2022学年七年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "542803378633482240", "questionArticle": "<p>9．［核心素养］【阅读材料】若分式 $ \\mathrm{ A } $ 与分式 $ B $ 的差等于它们的积，即 $ A-B=A\\cdot B $ ，则称分式 $ B $ 是分式 $ \\mathrm{ A } $ 的“关联分式”．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 174.6pt;\"><p style=\"text-align:center;\">例如 $ \\dfrac { 1 } { x+1 } $ 与 $ \\dfrac { 1 } { x+2 } $ ．</p><p style=\"text-align:center;\">解： $ \\because \\dfrac { 1 } { x+1 }-\\dfrac { 1 } { x+2 }=\\dfrac { 1 } { \\left ( { x+1 } \\right ) \\left ( { x+2 } \\right )  } $ ，</p><p style=\"text-align:center;\"> $ \\dfrac { 1 } { x+1 }\\times \\dfrac { 1 } { x+2 }=\\dfrac { 1 } { \\left ( { x+1 } \\right ) \\left ( { x+2 } \\right )  } $ ，</p><p style=\"text-align:center;\"> $ \\therefore \\dfrac { 1 } { x+2 } $ 是 $ \\dfrac { 1 } { x+1 } $ 的“关联分式”．</p></td></tr></table><p>【解决问题】</p><p>（1）已知分式 $ \\dfrac { 2 } { a{^{2}}-1 } $ ，则 $ \\dfrac { 2 } { a{^{2}}+1 } $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ \\dfrac { 2 } { a{^{2}}-1 } $ 的“关联分式”（填“是”或“不是”）；</p><p>（2）小明在求分式 $ \\dfrac { 1 } { x{^{2}}+y{^{2}} } $ 的“关联分式”时，用了以下方法．</p><p>解：设分式 $ \\dfrac { 1 } { x{^{2}}+y{^{2}} } $ 的“关联分式”为 $ B $ ，</p><p>则 $ \\dfrac { 1 } { x{^{2}}+y{^{2}} }-B=\\dfrac { 1 } { x{^{2}}+y{^{2}} }\\times B $ ，</p><p> $ \\therefore \\left ( { \\dfrac { 1 } { x{^{2}}+y{^{2}} }+1 } \\right ) B=\\dfrac { 1 } { x{^{2}}+y{^{2}} } $ ，</p><p> $ \\therefore B=\\dfrac { 1 } { x{^{2}}+y{^{2}}+1 } $ ．</p><p>请你仿照小明的方法求分式 $ \\dfrac { a-b } { 2a+3b } $ 的“关联分式”；</p><p>【拓展延伸】</p><p>（3）①观察（1）和（2）的结果，寻找规律，直接写出分式 $ \\dfrac { y } { x } $ 的“关联分式”<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>②用发现的规律解决问题：若 $ \\dfrac { 4n-2 } { mx+m } $ 是 $ \\dfrac { 4m+2 } { mx+n } $ 的“关联分式”，求实数 $ m $ ， $ n $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江西南昌二中 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-12", "keyPointIds": "16370|16426", "keyPointNames": "分式的混合运算|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "542803370064519168", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "542803370064519168", "title": "江西省南昌二中集团校初中部2024−2025学年八年级上学期期末数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544821340726927360", "questionArticle": "<p>10．某面馆向食客推出经典特色小面，顾客可到店食用（简称“堂食”小面），也可购买搭配佐料的袋装生面（简称“生食”小面），已知3份“堂食”小面和2份“生食”小面的总售价为31元，4份“堂食”小面和1份“生食”小面的总售价为33元．</p><p>(1)求每份“堂食”小面和“生食”小面的价格分别是多少元？</p><p>(2)该面馆在4月共卖出“堂食”小面2500份，“生食”小面1500份．为回馈广大食客，该面馆从5月1日起每份“堂食”小面的价格保持不变，每份“生食”小面的价格降低1元，统计5月的销量和销售额发现：“堂食”小面的销量与4月相同，“生食”小面的销量在4月的基础上增长 $ \\dfrac { 20 } { 3 }a\\% $ ，这两种小面的总销售额在：4月的基础上增加 $ a\\% $ ，求 $ a $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西梧州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-12", "keyPointIds": "16416|16441", "keyPointNames": "其他问题|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544821331822419968", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "544821331822419968", "title": "广西梧州市2024−2025学年上学期期末考试七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 194, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 194, "timestamp": "2025-07-01T02:23:45.634Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}