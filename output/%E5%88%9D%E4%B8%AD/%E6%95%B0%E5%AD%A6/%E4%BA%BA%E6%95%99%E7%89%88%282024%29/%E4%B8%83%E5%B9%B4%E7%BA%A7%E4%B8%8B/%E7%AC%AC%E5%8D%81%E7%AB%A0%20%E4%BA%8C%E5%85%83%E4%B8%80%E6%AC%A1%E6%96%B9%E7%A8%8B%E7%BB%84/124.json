{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 123, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "568255115573895168", "questionArticle": "<p>1．由方程组 $ \\begin{cases}2x+m=1,\\\\ m=y-3\\end{cases} $ 可得<i>x</i>与<i>y</i>的关系是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2<i>x</i>+<i>y</i>=4B．2<i>x</i>+<i>y</i>=−4\t</p><p>C．2<i>x</i>-<i>y</i>=4D．2<i>x</i>-<i>y</i>=−4</p><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "期末综合测试《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "569704907516715008", "questionArticle": "<p>2．已知方程组 $ \\begin{cases} x+y=-7-m \\\\ x-y=1+3m \\end{cases}  $ 的解满足<i>x</i>为非正数，<i>y</i>为负数．</p><p>(1)求<i>m</i>得取值范围．</p><p>(2)在<i>m</i>的取值范围内，当<i>m</i>为何整数时，不等式 $ 2mx+x  &lt;  2m+1 $ 的解为 $ x &gt; 1 $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|320000|110000|340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东枣庄 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 13, "referenceNum": 5, "createTime": "2025-04-22", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569704886910099456", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569704886910099456", "title": "山东省枣庄市第十五中学2024—2025学年下学期八年级数学期中考试模拟试题", "paperCategory": 1}, {"id": "562993285075083264", "title": "安徽省宿州市2024−2025学年八年级下学期数学调研测试卷", "paperCategory": 1}, {"id": "564578309738110976", "title": "北京市第一七一中学2023—2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "440723071424569344", "title": "安徽省合肥市2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "202431812177534976", "title": "江苏省扬州市邗江区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569705470518140928", "questionArticle": "<p>3．中国古代数学著作《算法统宗》中记载了这样一个题目：九百九十九文钱，甜果苦果买一千，四文钱买苦果七，十一文钱九个甜，甜苦两果各几个？其大意是，用九百九十九文钱共买了一千个苦果和甜果，其中四文钱可以买苦果七个，十一文钱可以买甜果九个．问：苦、甜果各有几个？设苦果有 $ x $ 个，甜果有 $ y $ 个，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=1000 \\\\ \\dfrac { 4 } { 7 }x+\\dfrac { 11 } { 9 }y=999 \\end{cases}  $　　　　B． $ \\begin{cases} x+y=1000 \\\\ \\dfrac { 7 } { 4 }x+\\dfrac { 9 } { 11 }y=999 \\end{cases}  $</p><p>C． $ \\begin{cases} x+y=1000 \\\\ 7x+9y=999 \\end{cases}  $　　　　D． $ \\begin{cases} x+y=1000 \\\\ 4x+11y=999 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|370000|320000|-1|510000|410000|460000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2022四川成都 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 19, "createTime": "2025-04-22", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "207189926722445312", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "207189926722445312", "title": "四川省成都市2022年中考数学真题", "paperCategory": 1}, {"id": "569705456421085184", "title": "四川省宜宾市第六中学校2024−2025学年下学期九年级数学半期试题", "paperCategory": 1}, {"id": "542798256880787456", "title": "重庆市大渡口区2024−2025学年八年级上学期期末考试数学试题", "paperCategory": 1}, {"id": "450070552725725184", "title": "河南省新乡市2023-2024学年九年级下学期期中数学试题", "paperCategory": 1}, {"id": "446276831190228992", "title": "河南省新乡市牧野区河南师范大学附属中学2023-2024学年九年级下学期期中数学试题", "paperCategory": 1}, {"id": "447523239935385600", "title": "2024年河南省新乡市九年级中考二模数学试题", "paperCategory": 1}, {"id": "471055470448386048", "title": "海南省海口市2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "491756895230795776", "title": "山东省日照市实验中学2024−2025学年八年级上学期9月数学试题", "paperCategory": 1}, {"id": "531853656490024960", "title": "湖南省娄底市第二中学2024-−2025学年七年级上学期期末数学模拟考试卷", "paperCategory": 1}, {"id": "425633584252231680", "title": "2023-2024学年七年级下册人教版数学第八章8.3实际问题与二元一次方程组（1）课时练习", "paperCategory": 1}, {"id": "274998791832379392", "title": "重庆市九龙坡区育才中学校2022-2023学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "346228883094544384", "title": "重庆市南开中学校2022-2023学年九年级上学期开学考试数学试题", "paperCategory": 1}, {"id": "346778336260038656", "title": "重庆市沙坪坝区南开中学2022-2023学年九年级上学期开学数学试题", "paperCategory": 1}, {"id": "392449440441737216", "title": "重庆市第一中学校2023-2024学年八年级上学期期中数学试题", "paperCategory": 1}, {"id": "399682511570247680", "title": "重庆市第八中学2023-2024学年八年级上学期数学期末模拟试题", "paperCategory": 1}, {"id": "427944144453017600", "title": "四川省成都市高新实验中学2022-2023学年九年级下学期3月份月考数学试题", "paperCategory": 1}, {"id": "430009874690383872", "title": "2023年江苏省苏州工业园区中考一模数学试题", "paperCategory": 1}, {"id": "206707688847220736", "title": "2022年七年级上册沪科版数学第3章3.4二元一次方程组的应用课时练习", "paperCategory": 1}, {"id": "203141884784451584", "title": "江苏省连云港市东海县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "569343542570557440", "questionArticle": "<p>4．中考在即，为了缓减学生的学习压力，某中学组织九年级450名师生前往曾国藩故居开展研学活动，如果租用10辆<i>A</i>型巴士和8辆<i>B</i>型巴士，则有10人没有座位，如果租用8辆<i>A</i>型巴士和10辆<i>B</i>型巴士，则还有10个空座位．</p><p>(1)求每辆<i>A</i>型巴士和每辆<i>B</i>型巴士的座位数；</p><p>(2)学校决定租用两种型号的巴士共20辆，且<i>B</i>型巴士最多租8辆，已知每辆<i>A</i>型巴士和每辆<i>B</i>型巴士的租金分别为750元和900元，若要使参加活动的师生均有座位，则共有多少种租车方案？最低租车费用是多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南娄底 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-21", "keyPointIds": "16438|16543", "keyPointNames": "和差倍分问题|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569343514875568128", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "569343514875568128", "title": "湖南省娄底市2025年初中毕业学业作业（二）数学试题卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568252761109733376", "questionArticle": "<p>5．方程组 $ \\begin{cases}3x-4y=2,\\\\ x+2y=1\\end{cases} $  用代入法消去<i>x</i>,所得关于<i>y</i>的一元一次方程为&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3×2<i>y</i>−1−4<i>y</i>=2B．3(1−2<i>y</i>)−4<i>y</i>=2</p><p>C．3(2<i>y</i>−1)−4<i>y</i>=2D．3−2<i>y</i>−4<i>y</i>=2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-04-21", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "198108311048200192", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "198108311048200192", "title": "2022年七年级下册冀教版数学第六章6.2二元一次方程组的解法课时练习", "paperCategory": 1}, {"id": null, "title": "第3章 3.3 课时2 代入法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568252882656468992", "questionArticle": "<p>6．已知方程组 $ \\begin{cases}3x-y=5-2k,\\\\ x+3y=k,\\end{cases} $ 那么<i>x</i>与<i>y</i>的关系是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．4<i>x</i>+2<i>y</i>=5B．2<i>x</i>−2<i>y</i>=5</p><p>C．<i>x</i>+<i>y</i>=1D．5<i>x</i>+7<i>y</i>=5</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|-1|410000|340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河南南阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 8, "referenceNum": 4, "createTime": "2025-04-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "430351577024602112", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "430351577024602112", "title": "河南省南阳市卧龙区第一完全学校、南阳市第九完全学校联考2023-2024学年七年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "442830833604403200", "title": "河北省石家庄市第四十二中学2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "177579860818173952", "title": "安徽省合肥市蜀山区2021-2022学年七年级上学期期末数学试题", "paperCategory": 1}, {"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568252874473381888", "questionArticle": "<p>7．用加减法解方程组 $ \\begin{cases}3x-6y=8,①\\\\ 3x+2y=3②\\end{cases} $ 时,②-①得&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．−8<i>y</i>=9B．6<i>x</i>−4<i>y</i>=11\t</p><p>C．8<i>y</i>=−5D．−2<i>y</i>=5\t</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2022江苏泰州 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-04-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "213354385077739520", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "213354385077739520", "title": "江苏省泰州市兴化市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "569344385252368384", "questionArticle": "<p>8．智能分拣机器人凭借高效、适应性强、减少错误和优化数据管理等特点，广泛应用于快递、制造、物流仓储及食品行业，显著提升运营效率与成本效益．某物流公司智能分拣中心拟购买<i>A</i>、<i>B</i>两种型号智能机器人进行快递分拣．相关信息如下：</p><p>信息一</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p><i>A</i>型机器人台数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p><i>B</i>型机器人台数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 117pt;\"><p>总费用（单位：万元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p>2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 117pt;\"><p>220</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 117pt;\"><p>260</p></td></tr></table><p>信息二</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 215.3pt;\"><p><i>A</i>型机器人每台每天可以分拣快递22万件；</p><p><i>B</i>型机器人每台每天可以分拣快递18万件．</p></td></tr></table><p>(1)求<i>A</i>、<i>B</i>两种型号智能机器人的单价；</p><p>(2)现该企业准备用不超过740万元购买<i>A</i>、<i>B</i>两种型号智能机器人共10台．则该企业选择哪种购买方案，能使每天分拣快递的件数最多？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南洛阳 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-21", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569344353967054848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569344353967054848", "title": "2025年河南省洛阳市洛龙区中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568252964957102080", "questionArticle": "<p>9．某体育场的环形跑道长400 m,甲、乙从同一起点分别以一定的速度同时练习跑步.如果两人反向而行,那么他们每隔30 s相遇一次.如果两人同向而行,那么每隔90 s乙就追上甲一次.问甲、乙的速度分别是多少?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "八年级 · 课本原题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 8, "referenceNum": 3, "createTime": "2025-04-21", "keyPointIds": "16424|16430", "keyPointNames": "加减消元法解二元一次方程组|行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "456829649068269568", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "456829649068269568", "title": "北师大版课后习题 八年级上册 第五章 第4节5.5 应用二元一次方程组——增收节支", "paperCategory": 1}, {"id": "170287150025449472", "title": "2022年八年级上册北师版数学第五章4应用二元一次方程组——增收节支课时练习", "paperCategory": 1}, {"id": null, "title": "第3章 3.4 课时1 列二元一次方程组解决积分和行程问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253095446093824", "questionArticle": "<p>10．营养对促进中学生机体健康具有重要意义,现对一份学生快餐进行检测,得到以下信息:</p><table style=\"border: solid 1px;border-collapse: collapse; margin: auto;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 256.6pt;\"><p>①快餐总质量为300克.</p><p>②快餐的成分:碳水化合物、脂肪、蛋白质、矿物质.</p><p>③蛋白质和脂肪共占50<i>%</i>;矿物质的含量是蛋白质含量的 $ \\dfrac{1}{3} $ ;蛋白质和碳水化合物含量共占70<i>%.</i></p></td></tr></table><p>根据上述信息回答下列的问题:</p><p>(1)这份快餐中蛋白质和脂肪的质量共<u>　　　　</u>克;&nbsp;</p><p>(2)分别求出这份快餐中脂肪、矿物质的质量.</p><p>(3)学生每餐膳食中主要营养成分“理想比”为碳水化合物∶脂肪∶蛋白质=8∶1∶9,同时三者含量为总质量的90<i>%.</i>试判断这份快餐中此三种成分所占百分比是否符合“理想比”?如果符合,直接写出这份快餐中碳水化合物、脂肪、蛋白质、矿物质的质量比;如果不符合,求出符合“理想比”的四种成分中脂肪、矿物质的质量(总质量仍为300克).</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|520000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2023贵州安顺 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 9, "referenceNum": 2, "createTime": "2025-04-21", "keyPointIds": "16441|16444", "keyPointNames": "其他问题|三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "349316993059692544", "questionFeatureName": "阅读材料题", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "349316993059692544", "title": "贵州省安顺市2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": null, "title": "第3章 3.4 课时2 列二元一次方程组解决百分率、配套和销售问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 124, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 124, "timestamp": "2025-07-01T02:15:30.742Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}