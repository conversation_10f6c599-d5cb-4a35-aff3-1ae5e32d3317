{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 154, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "559853111738671104", "questionArticle": "<p>1．数学课堂上，王老师让大家用加减消元法解方程组 $ \\begin{cases} 2x+5y=-10① \\\\ 5x-3y=2② \\end{cases}  $ ，下面是四位同学的求解过程，其中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．要消去 $ y $ ，可以将 $ ①\\times 5+②\\times 2 $ B．要消去 $ x $ ，可以将 $ ①\\times 3-②\\times 5 $ </p><p>C．要消去 $ y $ ，可以将 $ ①\\times 3+②\\times 2 $ D．要消去 $ x $ ，可以将 $ ①\\times 5-②\\times 2 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东淄博 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-31", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559853104558022656", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559853104558022656", "title": "山东省淄博市张店区第七中学2024—2025学年下学期3月月考七年级数学试题（五四学制）", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "561676299934146560", "questionArticle": "<p>2．幻方的历史悠久，传说最早出现在夏禹时代的“洛书”，把洛书用今天的数学符号翻译出来，就是一个三阶幻方如图1所示，三阶幻方的每行、每列、每条对角线上的三个数之和相等，图2是另一个未完成的三阶幻方，则<i>x</i>与<i>y</i>的和为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/01/2/1/0/0/0/562269609371410433/images/img_1.png\" style='vertical-align:middle;' width=\"431\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ -2 $　　　　B．2　　　　C．4　　　　D． $ -4 $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-31", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "561676287732916224", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "561676287732916224", "title": "2025年广东省深圳市21校九年级联考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "559854280062050304", "questionArticle": "<p>3．（1）解方程组： $ \\begin{cases} 3x=5y \\\\ 5x-y=1 \\end{cases}  $ </p><p>（2）李师傅家的超市今年1月盈利3000元，3月盈利3630元，若从1月到3月，每月盈利的平均增长率相同，则这个平均增长率是多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025新疆乌鲁木齐一中 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-31", "keyPointIds": "16423|16461", "keyPointNames": "代入消元法解二元一次方程组|增长率问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559854259707092992", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "559854259707092992", "title": "2025年新疆维吾尔自治区 乌鲁木齐市第一中学二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "561681113405497344", "questionArticle": "<p>4．列方程组解应用题：</p><p>某博物馆有<i>A</i>，<i>B</i>两种不同的文创纪念品，花费400元可以购买10件<i>A</i>纪念品和4件<i>B</i>纪念品，或者购买5件<i>A</i>纪念品和10件<i>B</i>纪念品，<i>A</i>，<i>B</i>两种纪念品的单价各多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京海淀 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-31", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "561681088331948032", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "561681088331948032", "title": "北京市海淀区师达中学2024—2025学年下学期七年级数学第一次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "561680771661996032", "questionArticle": "<p>5．（1）解一元一次不等式组 $ \\begin{cases} 4x &gt; 2x-6 \\\\ \\dfrac { x-1 } { 3 }\\leqslant  \\dfrac { x+1 } { 9 } \\end{cases}  $ ，并把解集表示在如图所示的数轴上．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/30/2/1/0/0/0/561680727714078721/images/img_21.png\" style=\"vertical-align:middle;\" width=\"330\" alt=\"试题资源网 https://stzy.com\"></p><p> （2）某校计划购买一批篮球和足球，已知购买2个篮球和1个足球共需320元，购买3个篮球和2个足球共需540元，求每个篮球和每个足球的售价?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025新疆吐鲁番 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-31", "keyPointIds": "16438|16489|28266", "keyPointNames": "和差倍分问题|解一元一次不等式组|在数轴上表示不等式的解集", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "561680751990710272", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "561680751990710272", "title": "2025年新疆维吾尔自治区吐鲁番市中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559853550626447360", "questionArticle": "<p>6．某校组织七年级师生共480人参观温州博物馆，学校向租车公司租赁<i>A</i>，<i>B</i>两种车型接送师生往返，若租用<i>A</i>型车3辆，<i>B</i>型车6辆，则空余15个座位；若租用<i>A</i>型车5辆，<i>B</i>型车4辆，则15人没有座位，求<i>A</i>，<i>B</i>两种车型各有多少个座位？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽淮南 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-31", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559853529260662784", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559853529260662784", "title": "2025年安徽省淮南市中考一模数学试题", "paperCategory": 1}, {"id": "409537045092147200", "title": "安徽省宣城市2023-2024学年七年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559853115098308608", "questionArticle": "<p>7．在关于 $ x{ { &nbsp; } }、{ { &nbsp; } }y $ 的二元一次方程组 $ \\begin{cases} 3x+y=a \\\\ x-2y=1 \\end{cases}  $ 中，若 $ 2x+3y=2 $ ，则 $ a $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B． $ -3 $ C．3D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东淄博 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-31", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559853104558022656", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559853104558022656", "title": "山东省淄博市张店区第七中学2024—2025学年下学期3月月考七年级数学试题（五四学制）", "paperCategory": 1}, {"id": "202461030915547136", "title": "陕西省安康市汉阴县2020-2021学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "561681574506307584", "questionArticle": "<p>8．《九章算术》中有这样一道题：今有醇酒一斗，直钱五十；行酒一斗，直钱一十．今将钱三十，得酒二斗，问醇、行酒各得几何？其意思为1斗优质酒价值50钱，1斗劣质酒价值10钱．用30钱恰好买得优质酒和劣质酒共2斗，问优质酒和劣质酒各能买得多少斗？设买优质酒<i>x</i>斗，劣质酒<i>y</i>斗，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=2 \\\\ 50x+10y=30 \\end{cases}  $　　　　B． $ \\begin{cases} x+2=y \\\\ 50x+10y=30 \\end{cases}  $　　　　C． $ \\begin{cases} x+y=2 \\\\ 10x+50y=30 \\end{cases}  $　　　　D． $ \\begin{cases} x+2=y \\\\ 10x+50y=30 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州一中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-03-31", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "561681560778350592", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "561681560778350592", "title": "福建省福州市第一中学2024−2025学年九年级下学期学期3月月考数学试卷", "paperCategory": 1}, {"id": "395678561993007104", "title": "四川省成都市树德实验中学西区2022-2023学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "560570692208467968", "questionArticle": "<p>9．若<i>x</i>，<i>y</i>，<i>z</i>为非负实数，且 $ \\begin{cases} x+2y-z=4 \\\\ x-y+2z=1 \\end{cases}  $ ，则代数式 $ x{^{2}}-3y{^{2}}+z{^{2}} $ 的最大值与最小值的差是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川内江市第二中学 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-28", "keyPointIds": "16444|16556", "keyPointNames": "三元一次方程组的应用|二次函数的最值", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "560570657697734656", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "560570657697734656", "title": "2025年四川省内江市第二中学九年级中考数学一模试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559469512581488640", "questionArticle": "<p>10．若关于 $ x $ 的不等式组 $ \\begin{cases} \\dfrac { 3x-1 } { 2 }  &lt;  x+1 \\\\ 2\\left ( { x+1 } \\right ) \\geqslant  -x+a \\end{cases}  $ 有且仅有4个整数解，且关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} x-ay=1 \\\\ x+2y=5 \\end{cases}  $ 的解为整数，则所有满足条件的整数 $ a $ 的值之和为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆清华 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-28", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585959679529885696", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585959679529885696", "title": "重庆市清华中学校2024−2025学年七年级下学期第二阶段定时作业数学试题", "paperCategory": 1}, {"id": "559469491412836352", "title": "重庆市第二十九中学校2024−2025学年九年级下学期定时作业（一）数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 155, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 155, "timestamp": "2025-07-01T02:19:12.657Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}