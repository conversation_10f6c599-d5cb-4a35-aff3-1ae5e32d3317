{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 150, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "562772475244748800", "questionArticle": "<p>1．下列各方程组中，属于二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} \\dfrac { x } { 3 }-\\dfrac { y } { 2 }=1 \\\\ 3x+4y=2 \\end{cases}  $ B． $ \\begin{cases} 2x+y=1 \\\\ x+z=2 \\end{cases}  $ C． $ \\begin{cases} 3x+2y=7 \\\\ xy=5 \\end{cases}  $ D． $ \\begin{cases} \\dfrac { 5 } { x }+\\dfrac { y } { 3 }=\\dfrac { 1 } { 2 } \\\\ x+2y=3 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025湖南衡阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-04", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562772468013768704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562772468013768704", "title": "湖南省衡阳市四校2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562772043118190592", "questionArticle": "<p>2．现有 $ x $ 辆载重 $ 6 $ 吨的卡车运一批重 $ y $ 吨的货物，若每辆卡车装 $ 5 $ 吨，则剩下 $ 2 $ 吨货物；若每辆卡车装满后，最后一辆卡车只需装 $ 4 $ 吨，即可装满所有货物．根据题意，可列方程（组）（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 5x+2=6\\left ( { x-1 } \\right ) +4 $ B． $ 5x+2=6x-4 $ </p><p>C． $ \\begin{cases} 5x-y=2 \\\\ y-6\\left ( { x-1 } \\right ) =4 \\end{cases}  $ D． $ \\begin{cases} y-5x=2 \\\\ 6x-y=4 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-04", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562772031042789376", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "562772031042789376", "title": "广东省深圳市宝安区新安中学2024−2025学年九年级下学期数学3月月考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562776743246864384", "questionArticle": "<p>3．“三八妇女节”来临之际，花店纷纷搞促销活动，小丽发现某花店有康乃馨、玫瑰两种花束正在参加活动，购买2束康乃馨和3束玫瑰需要290元，购买4束康乃馨和5束玫瑰需要530元．</p><p>(1)求康乃馨花束和玫瑰花束的单价分别为多少元？</p><p>(2)“三八妇女节”当天，花店进行促销活动，将康乃馨花束的单价降低了 $ 2m $ 元，玫瑰花束单价降低了 $ m $ 元，节日当天康乃馨花束的销量是玫瑰花束销量的1.2倍，且康乃馨花束的销售额为1200元，玫瑰花束的销售额为750元，求 $ m $ 的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市江津中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-03", "keyPointIds": "16426|16438", "keyPointNames": "二元一次方程组的应用|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562776716910829568", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562776716910829568", "title": "重庆市江津中学校2024−2025学年九年级下学期第一次定时作业数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562993219694272512", "questionArticle": "<p>4．阅读下列材料：</p><p>解答“已知 $ x-y=2 $ ，且 $ x > 1 $ ， $ y  <  0 $ ，试确定 $ x+y $ 的取值范围”有如下解法：</p><p>解： $ \\because x-y=2 $ ，</p><p> $ \\therefore x=y+2 $ ，</p><p>又 $ \\because x > 1 $ ，</p><p> $ \\therefore y+2 > 1 $ ，</p><p> $ \\therefore y > -1 $ ，</p><p>又 $ \\because y  <  0 $ ，</p><p> $ \\therefore -1  <  y  <  0 $ ， $ \\ldots  $ ①</p><p>同理，可得 $ 1  <  x  <  2 $ ， $ \\ldots  $ ②</p><p>① $ + $ ②得 $ -1+1  <  x+y  <  0+2 $ ，</p><p>即 $ 0  <  x+y  <  2 $ ，</p><p> $ \\therefore x+y $ 的取值范围是 $ 0  <  x+y  <  2 $ ．</p><p>请按照上述方法，完成下列问题：</p><p>(1)已知 $ x-y=4 $ ，且 $ x > 3 $ ， $ y  <  1 $ ，则 $ x+y $ 的取值范围是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)已知 $ a-b=m $ ，且关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} 2x-y=-1 \\\\ x+2y=5a-8 \\end{cases}  $ 中 $ x  <  0 $ ， $ y > 0 $ ，求 $ a+b $ 的取值范围（结果用含 $ m $ 的式子表示）．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽蚌埠 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-04-03", "keyPointIds": "16426|16485|16489", "keyPointNames": "二元一次方程组的应用|解一元一次不等式|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562993196898230272", "questionFeatureName": "阅读材料题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "562993196898230272", "title": "安徽省蚌埠市初中教联体2024−2025学年七年级下学期月考数学试卷", "paperCategory": 1}, {"id": "367049734509338624", "title": "广东省深圳市罗湖区桂园中学2023-2024学年九年级上学期月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562776739094503424", "questionArticle": "<p>5．我们把13的倍数称为“大吉数”，判断一个数 $ m $ 是否是大吉数，可以用 $ m $ 的末三位数减去末三位数以前的数字所组成的数，其差记为 $ F(m) $ ，如果 $ F(m) $ 是“大吉数”，这个数就是“大吉数”．比如：数字253448，这个数末三位是448，末三位以前是253，则 $ F(253448)=448-253=195 $ ，因为 $ 195\\div 13=15 $ ，所以 $ F(253448) $ 是“大吉数”，那么253448也是“大吉数”．若整数 $ m=15n+1 $ （其中 $ 0\\leqslant  n\\leqslant  9 $ ，且 $ n $ 为整数）是“大吉数”，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．若 $ p,q $ 均为“大吉数”，且 $ p=1010+110x $ ， $ q=4060+101y+z $ （ $ 0\\leqslant  x\\leqslant  8,1\\leqslant  y\\leqslant  6,0\\leqslant  z\\leqslant  3 $ ，且 $ x $ 、 $ y $ 、 $ z $ 均为整数），则 $ F(p+q) $ 的最大值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025重庆重庆市江津中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-03", "keyPointIds": "16426|29412", "keyPointNames": "二元一次方程组的应用|数的整除", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562776716910829568", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562776716910829568", "title": "重庆市江津中学校2024−2025学年九年级下学期第一次定时作业数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562403553316216832", "questionArticle": "<p>6．厦门一中数学组为校园“科技节”筹备“数学知识竞赛”活动，计划对获奖的同学给予奖励．现要购买甲、乙两种奖品，已知3件甲种奖品和2件乙种奖品共需70元，2件甲种奖品和3件乙种奖品共需80元．</p><p>(1)求甲、乙两种奖品的单价分别为多少元？</p><p>(2)根据颁奖计划，学校需甲、乙两种奖品共30件，其中甲种奖品的数量不超过乙的3倍，求购买两种奖品的总费用的最小值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建厦门 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 10, "referenceNum": 3, "createTime": "2025-04-03", "keyPointIds": "16435|16543", "keyPointNames": "分配问题|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562403530423705600", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "562403530423705600", "title": "福建省厦门市第三中学2024−2025学年九年级下学期3月月考数学试卷", "paperCategory": 1}, {"id": "352504074346995712", "title": "2023年福建省厦门第一中学中考模拟数学试题（6月）", "paperCategory": 1}, {"id": "323841462101123072", "title": "2023年福建省厦门第一中学中考模拟数学试题（6月）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562994040553119744", "questionArticle": "<p>7．已知 $ \\begin{cases} 2x+3y=z \\\\ { { 3 } }x+4y=2z+6 \\end{cases}  $ 且<i>x＋y</i>＝3，则<i>z</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．9B．−3C．12D．不确定</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|-1|120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 4, "createTime": "2025-04-03", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562994025243910144", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "562994025243910144", "title": "河北省石家庄市第九中学2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}, {"id": "395680510658256896", "title": "天津市南开翔宇学校2022-2023学年七年级上学期数学期末试题", "paperCategory": 1}, {"id": "170288833400971264", "title": "2022年八年级上册北师版数学第五章8三元一次方程组课时练习", "paperCategory": 1}, {"id": "169381013855969280", "title": "2022年七年级下册人教版数学第八章8.4三元一次方程组的解法课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "560234859362295808", "questionArticle": "<p>8．已知， $ a{{}_{ 1 } } $ ， $ a{{}_{ 2 } } $ ， $ \\ldots  $ ， $ a{{}_{ 100 } } $ 是从 $ 2 $ ， $ 0 $ ， $ -1 $ 这三个数中取值的一列数，若 $ a{{}_{ 1 } }+a{{}_{ 2 } }+\\cdots +a{{}_{ 100 } }=16 $ ， $ {\\left( { a{{}_{ 1 } }+1 } \\right) ^ {2}}+{\\left( { a{{}_{ 2 } }+1 } \\right) ^ {2}}+\\cdots +{\\left( { a{{}_{ 100 } }+1 } \\right) ^ {2}}=332 $ ，则 $ a{{}_{ 1 } } $ ， $ a{{}_{ 2 } } $ ， $ \\cdots  $ ， $ a{{}_{ 100 } } $ 中为2的个数是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-03", "keyPointIds": "16332|16443", "keyPointNames": "完全平方公式|解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "560234840290795520", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "560234840290795520", "title": "江苏省南京市联合体2024−2025学年七年级下学期第一次月考试卷 数学", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562033162911522816", "questionArticle": "<p>9．“寒夜客来茶当酒，竹炉汤沸火初红”茶，作为中国传统文化的重要组成部分，承载着深厚的历史与文化底蕴．在品茶的过程中，茶具的选择对茶汤的口感、香气、色泽以及品饮的体验有显著影响．某茶具厂共有120个工人，每个工人一天能做200个茶杯或50个茶壶，如果8个茶杯和1个茶壶为一套，问如何安排生产可使每天生产的产品配套？设生产茶杯的工人有<i>x</i>人，生产茶壶的工人有<i>y</i>人，则下列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/31/2/1/0/0/0/562033107135668234/images/img_10.jpg\" style=\"vertical-align:middle;\" width=\"140\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} x+y=120 \\\\ 200x=50y \\end{cases}  $ B． $ \\begin{cases} x+y=120 \\\\ 8\\times 200x=50y \\end{cases}  $ C． $ \\begin{cases} x+y=120 \\\\ 200x=8\\times 50y \\end{cases}  $ D． $ \\begin{cases} x+y=120 \\\\ 8\\times 50x=200y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|210000|510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025辽宁辽阳 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 12, "referenceNum": 4, "createTime": "2025-04-03", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562033149284229120", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "562033149284229120", "title": "辽宁省辽阳市部分学校2024—2025学年下学期九年级学情调研二 数学试卷", "paperCategory": 1}, {"id": "559422112185753600", "title": "辽宁省辽阳市部分学校2024—2025学年九年级下学期学情调研二数学试卷", "paperCategory": 11}, {"id": "534493413774761984", "title": "四川大学附属中学2024−2025学年上学期12月考八年级数学试卷", "paperCategory": 1}, {"id": "503319774052524032", "title": "山东省济南市济南外国语学校2024−2025学年八年级上学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562776072237916160", "questionArticle": "<p>10．为了进一步推动垃圾分类，创建绿色社区，某社区计划购置一批可回收垃圾桶用以美化环境．计划购买中型和大型垃圾桶共140个，其中中型垃圾桶每个100元，大型垃圾桶每个160元，预计花费17600元．</p><p>(1)计划购买中型垃圾桶和大型垃圾桶各多少个？</p><p>(2)实际购买时，经与商家协商：中型垃圾桶打九折；大型垃圾桶的单价每降低5元（大型垃圾桶的价格不低于中型的实际销售价格），社区就多购买10个大型垃圾桶．最后，社区购买的中型垃圾桶数量不变，大型垃圾桶的数量增加了，实际付款金额比计划多3600元，求该社区实际购买的大型垃圾桶的数量．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆八中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-03", "keyPointIds": "16438|16463|16486", "keyPointNames": "和差倍分问题|营销、利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562776040432508928", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "562776040432508928", "title": "重庆市第八中学2024−2025学年 九年级下学期第一次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 151, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 151, "timestamp": "2025-07-01T02:18:45.246Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}