{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 143, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "564941627560599552", "questionArticle": "<p>1．已知 $ \\begin{cases} x=-1 \\\\ y=a \\end{cases}  $ 是方程 $ 3x+2y=1 $ 的一个解，则 $ a $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "九年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-04-10", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565493853970796544", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "565493853970796544", "title": "2025年04月10日童麟斐的初中数学考试卷", "paperCategory": 5}, {"id": "564941612041674752", "title": "浙江省温州市实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564941619373318144", "questionArticle": "<p>2．下列各式是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 3x{^{2}}=y-1 $ B． $ 2x-4y $ C． $ \\dfrac { 2 } { x }=2y+1 $ D． $ 3x-y=2 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江温州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564941612041674752", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564941612041674752", "title": "浙江省温州市实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564941087732703232", "questionArticle": "<p>3．解方程（组）：</p><p>(1) $ \\begin{cases} y=2x \\\\ 3x+y=5 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 3x+2y=5 \\\\ 2x-6y=7 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江温州市第二中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564941059341459456", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564941059341459456", "title": "浙江省温州市第二中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564941084435980288", "questionArticle": "<p>4．若关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} 3x+y=k-6 \\\\ x+3y=2 \\end{cases}  $ 的解满足 $ x+y=1 $ ，则 $ k $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江温州市第二中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564941059341459456", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564941059341459456", "title": "浙江省温州市第二中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564941071098093568", "questionArticle": "<p>5．已知 $ \\begin{cases} x=2 \\\\ y=3 \\end{cases}  $ 是方程 $ 3x-ay=b $ 的一个解，那么 $ 3a+b $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2　　　　B．6　　　　C．3　　　　D．8</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江温州市第二中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564941059341459456", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564941059341459456", "title": "浙江省温州市第二中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564941067646181376", "questionArticle": "<p>6．下列方程组中，是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=1 \\\\ y+z=2 \\end{cases}  $　　　　B． $ \\begin{cases} x+y=1 \\\\ x-y=2 \\end{cases}  $</p><p>C． $ \\begin{cases} xy=2 \\\\ x+y=1 \\end{cases}  $　　　　D． $ \\begin{cases} x-\\dfrac { 1 } { y }=2 \\\\ x+2y=1 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024浙江温州市第二中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564941059341459456", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564941059341459456", "title": "浙江省温州市第二中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564940522814480384", "questionArticle": "<p>7．（1）计算： $ {\\left( { -1 } \\right) ^ {2}}+{\\left( { π+1 } \\right) ^ {0}}+2{^{-2}} $ ；&nbsp;&nbsp;&nbsp;</p><p>（2）解方程组： $ \\begin{cases} 2x+y=2 \\\\ 4x-y=10 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江温州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16323|16372|16424", "keyPointNames": "零指数幂|负整数指数幂|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564940502669238272", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564940502669238272", "title": "浙江省温州市瑞安市五校联考2023−2024学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564940520968986624", "questionArticle": "<p>8．若关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ 的解为 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ ，则关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} 2a{{}_{ 1 } }x-b{{}_{ 1 } }y=3c{{}_{ 1 } } \\\\ 2a{{}_{ 2 } }x-b{{}_{ 2 } }y=3c{{}_{ 2 } } \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江温州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564940502669238272", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564940502669238272", "title": "浙江省温州市瑞安市五校联考2023−2024学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564940517802287104", "questionArticle": "<p>9．若方程 $ 3x-ky=17 $ 的一组解为 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ ，则<i>k</i>＝<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024浙江温州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564940502669238272", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564940502669238272", "title": "浙江省温州市瑞安市五校联考2023−2024学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562405792088891392", "questionArticle": "<p>10．我们知道：任意一个有理数与无理数的和为无理数，任意一个不为零的有理数与一个无理数的积为无理数，而零与无理数的积为零，由此可得，如果 $ ax+b=0 $ ，其中 $ a $ 、 $ b $ 为有理数， $ x $ 为无理数，那么 $ a=0 $ ，且 $ b=0 $ ，运用上述知识解决下列问题：</p><p>(1)如果 $ \\sqrt { 2 }\\left ( { a+2 } \\right ) -b+3=0 $ ，其中 $ a $ 、 $ b $ 为有理数，那么 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)如果 $ 2b-a-\\sqrt { 3 }\\left ( { a+b-4 } \\right ) =5 $ ，其中 $ a $ 、 $ b $ 为有理数，求 $ a+8b $ 的算术平方根；</p><p>(3)若 $ a $ 、 $ b $ 都是有理数，且 $ a{^{2}}+2b+\\sqrt { 7 }\\left ( { b+4 } \\right ) =17 $ ，试求 $ a+b $ 的立方根．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025浙江杭州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16287|16290|16424", "keyPointNames": "平方根|立方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562405766553968640", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562405766553968640", "title": "浙江省杭州市拱墅区浙江锦绣育才教育集团2024−2025学年八年级下学期3月数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 144, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 144, "timestamp": "2025-07-01T02:17:54.199Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}