{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 18, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "591003668234674176", "questionArticle": "<p>1．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x+y=-6+m \\\\ x-y=3m-2 \\end{cases}  $ ．</p><p>（1）求方程组的解（用含 $ m $ 的代数式表示）；</p><p>（2）若方程组的解同时满足 $ x $ 为非负数， $ y $ 为负数，求 $ m $ 的取值范围．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川射洪中学 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591003630657904640", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "591003630657904640", "title": "四川省遂宁市射洪市射洪中学校2024−2025学年七年级下学期6月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "591003666334654464", "questionArticle": "<p>2．某超市销售每台进价分别为160元、120元的<i>A</i>，<i>B</i>两种型号的电器，如表是近两周的销售情况：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售时段</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售数量</p></td><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售收入</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>A</i>种型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>B</i>种型号</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第一周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>2台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>3台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>900元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第二周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>3台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>5台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1430元</p></td></tr></table><p>（进价、售价均保持不变，利润 $ = $ 销售收入 $ - $ 进货成本）</p><p>（1）求<i>A</i>，<i>B</i>两种型号的电器的销售单价；</p><p>（2）若超市准备再采购这两种型号的电器共40台，总费用不超过5700元，销售完这40台电器能否实现利润超过1800元的目标？若能，请给出相应的采购方案；若不能，请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川射洪中学 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591003630657904640", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "591003630657904640", "title": "四川省遂宁市射洪市射洪中学校2024−2025学年七年级下学期6月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590999711852568576", "questionArticle": "<p>3．已知关于 $ x $ 的多项式 $ ax+b $ 与 $ 3x{^{2}}-x-2 $ 的乘积展开式中不含 $ x $ 的二次项，且一次项的系数为 $ -7 $ ，则 $ ab $ 的值为<u>&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西安 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16327|16424", "keyPointNames": "多项式乘多项式|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590999694353932288", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590999694353932288", "title": "陕西省西安市高新第三中学2024−2025学年七年级下学期第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "591003662605918208", "questionArticle": "<p>4．计算：</p><p>（1） $ \\dfrac { 2x+1 } { 3 }-\\dfrac { x-1 } { 6 }=12 $ ；</p><p>（2） $ \\begin{cases} 3\\left ( { x+1 } \\right ) =y+8 \\\\ \\dfrac { 3x+1 } { 2 }=\\dfrac { y+12 } { 4 } \\end{cases}  $ ；</p><p>（3） $ -\\dfrac { 2x } { 3 }+1\\geqslant  \\dfrac { x-1 } { 2 } $ ；</p><p>（4） $ \\begin{cases} \\dfrac { 2x-5 } { 3 }  <  3 \\\\ 5-2\\left ( { x-2 } \\right ) \\geqslant  3-x \\end{cases}  $ ，并在数轴上表示解集．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川射洪中学 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16402|16424|16489", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591003630657904640", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "591003630657904640", "title": "四川省遂宁市射洪市射洪中学校2024−2025学年七年级下学期6月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "591003648106209280", "questionArticle": "<p>5．一个三角形的三边长分别是 $ a $ ， $ b $ ， $ c $ ，且 $ a,b $ 满足 $ {\\left( { a+2b-19 } \\right) ^ {2}}+\\left  | { 3a-b-1 } \\right  | =0 $ ，则此三角形的边 $ c $ 的取值范围是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 3  <  c  <  8 $　　　　B． $ 5  <  c  <  11 $　　　　C． $ 3  <  c $　　　　D． $ c  <  11 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川射洪中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16257|16424|16640", "keyPointNames": "绝对值非负性的应用|加减消元法解二元一次方程组|三角形三边关系", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591003630657904640", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "591003630657904640", "title": "四川省遂宁市射洪市射洪中学校2024−2025学年七年级下学期6月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "591003647212822528", "questionArticle": "<p>6．关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} x+5y+t=12 \\\\ 3x-y=4+t \\end{cases}  $ 的解是 $ \\begin{cases} x=a \\\\ y=b \\end{cases}  $ ，其中 $ t $ 是常数，则 $ a+b $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -4 $　　　　B．4　　　　C． $ -2 $　　　　D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川射洪中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591003630657904640", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "591003630657904640", "title": "四川省遂宁市射洪市射洪中学校2024−2025学年七年级下学期6月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "591003644138397696", "questionArticle": "<p>7．解关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} y=2x-3① \\\\ 3x+2y=8② \\end{cases}  $ ，将 $ ① $ 代入 $ ② $ ，消去 $ y $ 后所得到的方程是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 3x+4x-3=8 $　　　　B． $ 3x+4x+3=8 $</p><p>C． $ 3x+4x-6=8 $　　　　D． $ 3x+4x+6=8 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025四川射洪中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591003630657904640", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "591003630657904640", "title": "四川省遂宁市射洪市射洪中学校2024−2025学年七年级下学期6月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592301271811403776", "questionArticle": "<p>8．中江挂面以“细如发丝、清如白玉、耐煮不糊、入口绵软”闻名遐迩，其独特的空心技艺传承千年，从揉面、开条、上筷到拉扯成型，需经十余道古法工序．数学兴趣小组走进某老字号挂面厂进行调研，已知购买2袋<i>A</i>型与2袋<i>B</i>型挂面共需费用100元，购买3袋<i>A</i>型与2袋<i>B</i>型挂面共需费用120元．</p><p>（1）<i>A</i>型、<i>B</i>型挂面的单价分别是多少元？</p><p>（2）为进一步推广此非遗美食，兴趣小组决定购买<i>A</i>，<i>B</i>两种型号挂面共40袋，在单价不变，总费用不超过950元，且<i>B</i>型挂面不少于10袋的条件下，共有几种购买方案？其中最低花费多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川德阳 · 中考真题", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16438|16490|16543", "keyPointNames": "和差倍分问题|一元一次不等式组的应用|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592301241297842176", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "592301241297842176", "title": "2025年四川省德阳市中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590999458164285440", "questionArticle": "<p>9．坪山区某校积极响应《每周半天计划》相关文件精神，计划组织全校师生开展户外研学，该校某数学兴趣小组就租车问题展开了调查研究，取得了如下信息：</p><table style=\"border: solid 1px;border-collapse: collapse; width:416.25pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 45.75pt;\"><p style=\"text-align:center;\">信息1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 369.75pt;\"><p>大型客车载客量为50人，中型客车载客量为30人，此前 $ A $ 校租用6辆大型客车4辆中型客车花费4400元； $ B $ 校租用4辆大型客车，8辆中型客车花费4800元．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 45.75pt;\"><p style=\"text-align:center;\">信息2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 369.75pt;\"><p>该校六年级师生共460人，租车费用的预算为4900元，拟租用10辆车．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 45.75pt;\"><p style=\"text-align:center;\">任务1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 369.75pt;\"><p>一辆大型客车和一辆中型客车的租金分别为多少元？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 45.75pt;\"><p style=\"text-align:center;\">任务2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 369.75pt;\"><p>若要控制租车费用在预算范围内，在保证10辆车一次性将六年级师生全部送达目的地的前提下，请写出所有的租车方案，并求出花费最少的方案比预算节省的费用．</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东日照 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-06-23", "keyPointIds": "16438|16490", "keyPointNames": "和差倍分问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590999429286502400", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590999429286502400", "title": "山东省日照市北京路中学2024−2025学年七年级下学期6月月考数学试卷", "paperCategory": 1}, {"id": "580905721643970560", "title": "2025年广东省深圳市坪山区中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592300502815121408", "questionArticle": "<p>10．我国古代数学著作《九章算术》中记载了这样一道题：“今有牛五、羊二，直金十两；牛二、羊五、直金八两，问牛、羊各直金几何？”意思是假设5头牛、2只羊，共值金10两;2头牛、5只羊，共值金8两，那么每头牛、每只羊各值金多少两？若设每头牛和每只羊分别值金<i>x</i>两和<i>y</i>两，列出方程组应为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+2y=10 \\\\ 2x+5y=8 \\end{cases}  $　　　　B． $ \\begin{cases} 5x+2y=8 \\\\ 2x+5y=10 \\end{cases}  $</p><p>C． $ \\begin{cases} 5x-2y=10 \\\\ 2x+5y=8 \\end{cases}  $　　　　D． $ \\begin{cases} 5x+2y=10 \\\\ 2x-5y=8 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川宜宾 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-06-23", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592300486742548480", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "592300486742548480", "title": "2025年四川省宜宾市中考数学真题", "paperCategory": 1}, {"id": "581949457727528960", "title": "2025年天津市河西区中考一模数学试题（一）", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 19, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 19, "timestamp": "2025-07-01T02:03:03.099Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}