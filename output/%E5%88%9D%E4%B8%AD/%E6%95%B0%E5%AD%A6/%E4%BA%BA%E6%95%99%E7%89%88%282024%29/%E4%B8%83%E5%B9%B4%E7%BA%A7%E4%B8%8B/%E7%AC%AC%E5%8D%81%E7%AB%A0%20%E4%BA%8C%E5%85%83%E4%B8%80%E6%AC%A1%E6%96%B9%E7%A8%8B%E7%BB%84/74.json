{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 73, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "578017450857373696", "questionArticle": "<p>1．已知 $ \\left ( { 2-a } \\right ) x+y{^{\\left  | { a } \\right  | -1}}=3 $ 是关于 $ x $ ， $ y $ 的二元一次方程，则 $ a $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2　　　　B． $ -2 $　　　　C．2或 $ -2 $　　　　D．1</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京市陈经纶中学分校 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16258|16419", "keyPointNames": "绝对值方程|二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017437091667968", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "578017437091667968", "title": "北京市陈经纶中学2024—2025学年下学期七年级期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578017180706447360", "questionArticle": "<p>2．幻方起源于中国，是我国古代数学的杰作之一，是一种将数字安排在正方形格子中，使每一横行、每一竖列以及两条斜对角线上的数字和都相等的方法．如图①就是一个幻方，图②是一个未完成的幻方，则可以列出的方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/21/2/1/0/0/0/591603401361760257/images/img_1.png\" style='vertical-align:middle;' width=\"251\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} 2x+3=-3+4y \\\\ x+2y+4y=2+3 \\end{cases}  $　　　　B． $ \\begin{cases} 2x+3+2=2-3+4y \\\\ 3+x+2y=2-3 \\end{cases}  $</p><p>C． $ \\begin{cases} 2x+x+2y=2-3 \\\\ x+2y-3=2+4y \\end{cases}  $　　　　D． $ \\begin{cases} 3+x+2y=2-3 \\\\ 2-3+4y=2x+x+2y+4y \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津红桥区第五中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-05-18", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934561456336896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589934561456336896", "title": "天津市第五中学2024−2025学年下学期5月月考七年级数学试卷", "paperCategory": 1}, {"id": "578017163383971840", "title": "2025年天津市和平区九年级中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578017056764768256", "questionArticle": "<p>3．定义：关于<i>x</i>，<i>y</i>的二元一次方程 $ ax+by=c $ 与 $ bx+ay=c $ 互为“对称二元一次方程”，其中 $ a\\ne b $ 如二元一次方程 $ 2x+y=3 $ 与二元一次方程 $ x+2y=3 $ 互为“对称二元一次方程”．</p><p>（1）直接写出二元一次方程 $ 4x-y=5 $ 的“对称二元一次方程”_；</p><p>（2）二元一次方程 $ 3x+2y=2025 $ 与它的“对称二元一次方程”的公共解为 $ \\begin{cases} x=m \\\\ y=n \\end{cases}  $ ，求出<i>m</i>，<i>n</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏苏州等地 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017032400056320", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578017032400056320", "title": "江苏省苏州市昆山市、常熟市、太仓市、张家港市2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578017053791006720", "questionArticle": "<p>4．解方程组；</p><p>（1） $ \\begin{cases} x=5+y \\\\ 3x+4y=1 \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} \\dfrac { x } { 2 }-\\dfrac { y+1 } { 3 }=1 \\\\ 3x+2y=10 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏苏州等地 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017032400056320", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578017032400056320", "title": "江苏省苏州市昆山市、常熟市、太仓市、张家港市2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578017052343971840", "questionArticle": "<p>5．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} ax-by=c \\\\ mx-ny=k \\end{cases}  $ 的解为 $ \\begin{cases} x=4 \\\\ y=3 \\end{cases}  $ ，则关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} 2ax+3by=-5c \\\\ 2mx+3ny=-5k \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏苏州等地 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017032400056320", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578017032400056320", "title": "江苏省苏州市昆山市、常熟市、太仓市、张家港市2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578017042931953664", "questionArticle": "<p>6．已知 $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ 是方程 $ 2x-ay=5 $ 的一个解，那么<i>a</i>的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -3 $ B． $ -1 $ C．1D．3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏苏州等地 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017032400056320", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578017032400056320", "title": "江苏省苏州市昆山市、常熟市、太仓市、张家港市2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577680301960241152", "questionArticle": "<p>7．已知 $ a，b $ 满足如下两个条件：①一个正数 $ x $ 的两个平方根分别是 $ 2a-3 $ 与 $ 1-b $ ；② $ \\sqrt[3] { 3a-2 }+\\sqrt[3] { 2-b }=0 $ </p><p>（1）求 $ a，b，x $ ．</p><p>（2）求 $ x-2ab $ 的平方根．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北武汉 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16287|16290|16424", "keyPointNames": "平方根|立方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577680270779785216", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577680270779785216", "title": "湖北省武汉市武珞路中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577680299921809408", "questionArticle": "<p>8．用指定的方法解下列方程组</p><p>（1） $ \\begin{cases} x=1-y \\\\ 2x+4y=5 \\end{cases}  $ （代入法）</p><p>（2） $ \\begin{cases} 3x-2y=6 \\\\ 2x+3y=17 \\end{cases}  $ （加减法）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北武汉 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577680270779785216", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577680270779785216", "title": "湖北省武汉市武珞路中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577680293340946432", "questionArticle": "<p>9．若关于 $ x $ 、 $ y $ 的二元一次方程 $ 2x+ay=7 $ 有一个解是 $ \\begin{cases} x=3 \\\\ y=-2 \\end{cases}  $ ，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北武汉 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577680270779785216", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577680270779785216", "title": "湖北省武汉市武珞路中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577680287242428416", "questionArticle": "<p>10．已知 $ \\begin{cases} x=1 \\\\ y=-2 \\end{cases}  $ 是二元一次方程 $ ax-by=3 $ 的解，则 $ -2a-4b-3 $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 3 $ B． $ -3 $ C． $ -9 $ D． $ 9 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北武汉 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577680270779785216", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577680270779785216", "title": "湖北省武汉市武珞路中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 74, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 74, "timestamp": "2025-07-01T02:09:32.857Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}