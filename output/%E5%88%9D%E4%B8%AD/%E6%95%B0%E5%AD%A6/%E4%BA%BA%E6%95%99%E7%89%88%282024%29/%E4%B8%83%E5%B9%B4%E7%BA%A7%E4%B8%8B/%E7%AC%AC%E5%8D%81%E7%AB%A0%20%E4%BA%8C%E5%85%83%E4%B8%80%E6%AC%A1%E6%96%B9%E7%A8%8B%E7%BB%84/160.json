{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 159, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "557316571879743488", "questionArticle": "<p>1．已知 $ AB{ \\rm{ /\\mskip-4mu/ } }CD $ ， $ \\angle ABE=\\angle DCE $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/18/2/1/0/0/0/557316513809604608/images/img_17.png\" style=\"vertical-align:middle;\" width=\"554\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)如图1，求证： $ \\angle BEC=2\\angle ABE $ ；</p><p>(2)如图 $ {\\rm 2，} \\angle EBF=\\angle F $ ，且 $ \\angle ABF=45\\mathrm{ ° } $ ，求 $ \\angle FEC $ 的度数；</p><p>(3)如图3，在（2）的条件下，过点<i>B</i>作 $ BM{ \\rm{ /\\mskip-4mu/ } }EF $ 交 $ CE $ 于点<i>M</i>，若 $ EN=6 $ ， $ EF-BM=1 $ ，当 $ △BEF $ 的面积为8时，求 $ BM $ 的长．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈尔滨 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-23", "keyPointIds": "16424|16621|16629", "keyPointNames": "加减消元法解二元一次方程组|平行公理及推论|两直线平行内错角相等", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557316549545074688", "questionMethodName": "分类讨论思想", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "557316549545074688", "title": "黑龙江省年哈尔滨市虹桥初级中学校2024−2025学年七年级下学期开学数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557316568264253440", "questionArticle": "<p>2．如图<i>BE</i><span style=\"font-family: 'SimSun'\"><span style=\"font-family: 'SimSun'\">∥</span></span><i>CF</i>，<i>BC</i>⊥<i>CD</i>，<i>A</i>为<i>CB</i>延长线上一点，若 $ {\\rm ∠\\mathit{ABE}} $  -∠<i>DCF</i>=20°，则 $ {\\rm ∠\\mathit{CBE}=} $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/18/2/1/0/0/0/557316513805410306/images/img_14.png\" style=\"vertical-align:middle;\" width=\"115\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈尔滨 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-23", "keyPointIds": "16424|16613|16630", "keyPointNames": "加减消元法解二元一次方程组|垂线|两直线平行同旁内角互补", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557316549545074688", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "557316549545074688", "title": "黑龙江省年哈尔滨市虹桥初级中学校2024−2025学年七年级下学期开学数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "558040451430785024", "questionArticle": "<p>3．近期，我国国产动画电影“哪吒2魔童闹海”票房突破了 $ 145 $ 亿（含预售及海外票房），商家推出了 $ A，B $ 两种类型的哪吒纪念娃娃．已知购进4件 $ A $ 种娃娃和购进5件 $ B $ 种娃娃的费用相同，购进6件 $ A $ 种娃娃和4件 $ B $ 种娃娃一共需要 $ 92 $ 元．且 $ A $ 种娃娃售价为 $ 15 $ 元/个， $ B $ 种娃娃售价为 $ 10 $ 元/个．</p><p>(1)每个 $ A $ 种娃娃和每个 $ B $ 种娃娃的进价分别是多少元？</p><p>(2)根据网上预约的情况，该商家计划用不超过 $ 1700 $ 元的资金购进 $ A $ ， $ B $ 两种娃娃共 $ 200 $ 个，应如何设计进货方案才能获得最大利润，最大利润是多少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000|410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江西南昌二中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 3, "createTime": "2025-03-22", "keyPointIds": "16438|16486|16544", "keyPointNames": "和差倍分问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575778010244100096", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "575778010244100096", "title": "江西省南昌市第二中学2024−2025学年八年级下学期4月期中数学试题", "paperCategory": 1}, {"id": "558040427993014272", "title": "河南省实验中学2024−2025学年八年级下学期学业诊断数学试卷", "paperCategory": 1}, {"id": "557200176558940160", "title": "河南省实验中学2024−2025学年八年级下学期学业诊断数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "558040267053375488", "questionArticle": "<p>4．某草莓采摘园有如下消费场景：顾客采摘 $ 2{ \\rm{ k } }{ \\rm{ g } } $ 甲种草莓和 $ 3{ \\rm{ k } }{ \\rm{ g } } $ 乙种草莓，共花费230元，采摘 $ 1{ \\rm{ k } }{ \\rm{ g } } $ 甲种草莓和 $ 4{ \\rm{ k } }{ \\rm{ g } } $ 乙种草莓，共花费240元．</p><p>(1)求甲、乙两种草莓的售价分别是每千克多少元;</p><p>(2)为吸引顾客，该采摘园推出以下优惠方案：采摘甲种草莓按原价的八折销售, 采摘乙种草莓超过 $ 4{ \\rm{ k } }{ \\rm{ g } } $ ，超出部分按原价的六折销售,设采摘 $ x{ \\rm{ k } }{ \\rm{ g } } $ 甲种草莓, $ x{ \\rm{ k } }{ \\rm{ g } } $ 乙种草莓的费用分别为 $ y{{}_{ 1 } } $ 元, $ y{{}_{ 2 } } $ 元，请写出 $ y{{}_{ 1 } } $ ， $ y{{}_{ 2 } } $ 关于<i>x</i>的函数表达式;</p><p>(3)某公司为准备团建活动，准备采摘同一品种草莓不少于 $ 40{ \\rm{ k } }{ \\rm{ g } } $ ，请通过计算说明采摘哪种草莓更划算.</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南鹤壁 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-22", "keyPointIds": "16438|16543", "keyPointNames": "和差倍分问题|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "558040241459732480", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "558040241459732480", "title": "河南省鹤壁市2024−2025学年九年级下学期第一次联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "558039848969347072", "questionArticle": "<p>5．某公司有新员工和老员工若干名．已知1名新员工每天制造的零件个数比1名老员工少30，1名新员工与2名老员工每天共可制造180个零件，则1名新员工与1名老员工每天各能制造多少个零件？设1名新员工每天能制造 $ x $ 个零件，1名老员工每天能制造 $ y $ 个零件．根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y-x=30 \\\\ x+2y=180 \\end{cases}  $ B． $ \\begin{cases} x-y=30 \\\\ x-2y=180 \\end{cases}  $ C． $ \\begin{cases} y-x=30 \\\\ 2x+y=180 \\end{cases}  $ D． $ \\begin{cases} x-y=30 \\\\ 2x+y=180 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建集中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-22", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "558039836847808512", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "558039836847808512", "title": "福建省厦门集美中学2024—2025学年下学期九年级数学3月月考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "558039631696011264", "questionArticle": "<p>6．定义：如果一元二次方程 $ ax{^{2}}+bx+c=0 $ （ $ a\\ne 0 $ ）满足 $ a-b+c=0 $ ，那么称这个方程为“联合方程”．</p><p>(1)判断一元二次方程 $ 2x{^{2}}+9x+7=0 $ 是否为“联合方程”，说明理由；</p><p>(2)已知 $ 3x{^{2}}-mx+n=0 $ 是关于 $ x $ 的“联合方程”，若 $ -2 $ 是此“联合方程”的一个根，求 $ m $ 和 $ n $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽淮北 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-22", "keyPointIds": "16424|16448|16449", "keyPointNames": "加减消元法解二元一次方程组|一元二次方程的一般形式|一元二次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "558039610497998848", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "558039610497998848", "title": "安徽省淮北五校联考2024−2025学年八年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "558039547830902784", "questionArticle": "<p>7．随着新能源汽车的销售越来越多，小区新能源汽车充电也越来越困难，某小区为了解决业主新能源汽车充电难的问题，准备在小区内修建10个充电桩，已知新建2个地下充电桩和1个地上充电桩需要1万元；新建1个地下充电桩和3个地上充电桩也需要1万元．</p><p>(1)该小区新建一个地上充电桩和一个地下充电桩各需多少钱？</p><p>(2)若该小区计划用不超过2.6万元的资金新建充电桩，问共有几种建造方案？并列出所有方案.</p><p>(3)在第(2)问的条件下哪种方案投资最少？请求出最少投资金额．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽淮北 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 10, "referenceNum": 1, "createTime": "2025-03-22", "keyPointIds": "16441|16486", "keyPointNames": "其他问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "558039523457802240", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "558039523457802240", "title": "安徽省淮北市五校联考2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "558039536535642112", "questionArticle": "<p>8．关于 $ x、y $ 的方程组 $ \\begin{cases} 3x-y=2k-4 \\\\ x-3y=k \\end{cases}  $ 的解中 $ x-y\\geqslant  5 $ ，则 $ k $ 的取值范围为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ k\\geqslant  3 $</p><p>B． $ k\\leqslant  3 $</p><p>C． $ k\\geqslant  8 $</p><p>D． $ k\\geqslant  9 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽淮北 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-22", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "558039523457802240", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "558039523457802240", "title": "安徽省淮北市五校联考2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "558039359078834176", "questionArticle": "<p>9．我国古代数学专著《九章算术》中有一道关于“分钱”的问题：甲、乙二人有钱若干，若甲给乙10钱，则甲的钱是乙的2倍；若乙给甲5钱，则乙的钱是甲的 $ \\dfrac { 1 } { 3 } $ ．若设甲原有 $ x $ 钱，乙原有 $ y $ 钱，则可列方程（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-10=2\\left ( { y+10 } \\right )  \\\\ \\dfrac { 1 } { 3 }\\left ( { x+5 } \\right ) =y-5 \\end{cases}  $ B． $ \\begin{cases} 2\\left ( { x-10 } \\right ) =y+10 \\\\ \\dfrac { 1 } { 3 }\\left ( { x+5 } \\right ) =y-5 \\end{cases}  $ </p><p>C． $ \\begin{cases} x-10=2\\left ( { y+10 } \\right )  \\\\ x+5=\\dfrac { 1 } { 3 }\\left ( { y-5 } \\right )  \\end{cases}  $ D． $ \\begin{cases} 2\\left ( { x-10 } \\right ) =y+10 \\\\ x+5=\\dfrac { 1 } { 3 }\\left ( { y-5 } \\right )  \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川绵阳 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 3, "createTime": "2025-03-22", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576954052371591168", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "576954052371591168", "title": "四川省绵阳市2025年毕业生学业水平检测试卷数学", "paperCategory": 1}, {"id": "558039346445590528", "title": "2025年浙江省TZ−8共同体初中学校中考数学第一次模拟试题", "paperCategory": 1}, {"id": "557524491447869440", "title": "2025年浙江省初中学校TZ−8共同体九年级下学期一模数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557699603849584640", "questionArticle": "<p>10．2024年巴黎奥运会顺利举行，奥运纪念品深受喜爱，某商场两次购进 $ A $ ， $ B $ 两款纪念品．第一次购进 $ A $ 款纪念品100件， $ B $ 款纪念品80件，共6200元，第二次购进 $ A $ 款纪念品150件， $ B $ 款纪念品40件，共6100元．</p><p>(1)求 $ A $ ， $ B $ 两款纪念品的进价各是多少元？</p><p>(2)商场为了尽快将 $ A $ 款纪念品销售完，决定对 $ A $ 款纪念品进行降价销售，当销售单价为每个60元时，每周可以卖出50个，每降10元，每周就可以多卖100个，请问商场将每个 $ A $ 款纪念品降价多少元时，每周销售 $ A $ 款纪念品的利润为2340元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市长寿中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-21", "keyPointIds": "16437|16463", "keyPointNames": "销售利润问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557699578759258112", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "557699578759258112", "title": "重庆市长寿中学校2024−2025学年九年级下学期第一次素养测评数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 160, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 160, "timestamp": "2025-07-01T02:19:46.914Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}