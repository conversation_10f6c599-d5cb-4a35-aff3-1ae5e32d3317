{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 124, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "568253323653980160", "questionArticle": "<p>1．解方程(组):</p><p>(1) $ \\dfrac{x-2}{3} {\\rm =1-} $  $ \\dfrac{1-3x}{6} {\\rm \\mathit{.}} $ </p><p>(2) $ \\begin{cases}3a-2b=10,①\\\\ 4a-3b=13.②\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2022安徽合肥 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-04-21", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "177579860818173952", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "177579860818173952", "title": "安徽省合肥市蜀山区2021-2022学年七年级上学期期末数学试题", "paperCategory": 1}, {"id": null, "title": "第3章 一次方程与方程组 全章综合训练《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253317249277952", "questionArticle": "<p>2．端午节前夕,某食品加工厂准备将生产的粽子装入A、B两种食品盒中,A种食品盒每盒装8个粽子,B种食品盒每盒装10个粽子.若现将200个粽子分别装入A、B两种食品盒中(两种食品盒均要使用并且装满),则不同的分装方式有&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2种B．3种C．4种D．5种\t</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2022黑龙江齐齐哈尔 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-04-21", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "207079168529440768", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "207079168529440768", "title": "黑龙江省齐齐哈尔市2022年中考数学真题", "paperCategory": 1}, {"id": null, "title": "第3章 一次方程与方程组 全章综合训练《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "569344471273349120", "questionArticle": "<p>3．要把一张面值10元的人民币换成零钱，现有足够的面值为1元、5元的人民币，则换法有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1种B．2种C．3种D．4种</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江佳木斯第五中学 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-21", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569344457167904768", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "569344457167904768", "title": "2025年黑龙江省佳木斯市第五中学一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "568254252595847168", "questionArticle": "<p>4．甲、乙两地相距360千米,一轮船往返于甲、乙两地之间,顺水航行用18小时,逆水航行用24小时.若设轮船在静水中的速度为<i>x</i>千米<i>/</i>时,水流速度为<i>y</i>千米<i>/</i>时,则下列方程组中正确的是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}18(x+y)=360,\\\\ 24(x-y)=360\\end{cases} $ B． $ \\begin{cases}18(x+y)=360,\\\\ 24(x+y)=360\\end{cases} $ </p><p>C． $ \\begin{cases}18(x-y)=360,\\\\ 24(x-y)=360\\end{cases} $ D． $ \\begin{cases}18(x-y)=360,\\\\ 24(x+y)=360\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|-1|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2023广东广州大学附中 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 4, "createTime": "2025-04-21", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "344944008362237952", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "344944008362237952", "title": "广东省广州市越秀区广州大学附中2022-2023学年八年级上学期开学数学试卷", "paperCategory": 1}, {"id": "333917747045244928", "title": "山西省大同市平城区大同市第一中学校2022-2023学年七年级下学期5月月考数学试题", "paperCategory": 1}, {"id": "397789674817757184", "title": "广东省深圳市福田区莲花中学2022-2023学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": null, "title": "第3章 3.3 课时1 认识二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568254249588531200", "questionArticle": "<p>5．下列方程组是二元一次方程组的是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}3x-2y=1,\\\\ y=4z-3\\end{cases} $ B． $ \\begin{cases}2a+b=7,\\\\ 2b-3a=2\\end{cases} $ </p><p>C． $ \\begin{cases}\\dfrac{1}{x}+y=3,\\\\ \\dfrac{1}{y}-2x=4\\end{cases} $ D． $ \\begin{cases}mn=-1,\\\\ m+n=3\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-21", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 一次方程与方程组3.4 二元一次方程组及其解法课时1 认识二元一次方程组《初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "569344122336616448", "questionArticle": "<p>6．我国南宋数学家杨辉在其所著《续古摘奇算法》中的攒九图一节中提出了“幻圆”的概念．如图是一个二阶幻圆模型，其内外两个圆周上四个数字之和以及外圆两直径上的四个数字之和都相等，则 $ b-a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/22/2/1/0/0/0/569905818214113281/images/img_1.png\" style='vertical-align:middle;' width=\"161\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东莞中 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-21", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569344105517457408", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "569344105517457408", "title": "2025年广东省东莞市东莞中学初中部九年级中考数学一模测试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569343660807987200", "questionArticle": "<p>7．某文创商店举办非遗展演，民俗体验等特色活动，共花费 $ 925 $ 元购进“泥塑“和“团扇“共 $ 80 $ 件．其中两种产品的成本价和销售价如下表：</p><table style=\"border: solid 1px;border-collapse: collapse; width:204.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>成本价（元/件）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>销售价（元/件）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>泥塑</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 15 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 25 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>团扇</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 10 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 17.5 $ </p></td></tr></table><p>(1)该文创产品店购进泥塑和团扇各多少件？</p><p>(2)因销售火爆，全部售完后该文创店第二次购进两种产品共 $ 100 $ 件．若此次购进泥塑的数量不超过团扇数量的 $ 1.5 $ 倍，且全部售完．则第二次如何进货，才能使获利最大？最大利润是多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南湖南师大附中博才中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-21", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569343634497118208", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "569343634497118208", "title": "湖南省长沙市博才实验中学联考2024−2025学年九年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569343423032893440", "questionArticle": "<p>8．方程组 $ \\begin{cases} 3x+y=5, \\\\ x+3y=7 \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南郑州 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569343407086149632", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569343407086149632", "title": "河南省新郑市2024−2025学年九年级下学期第二次联考数学试题试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567841037147742208", "questionArticle": "<p>9．若 $ \\left  | { 2a-3b+5 } \\right  |  $ 与 $ \\sqrt { 3a-2b-1 } $ 互为相反数，则 $ a+b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川泸州 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-21", "keyPointIds": "16252|16257|16426|28421", "keyPointNames": "相反数的应用|绝对值非负性的应用|二元一次方程组的应用|算术平方根非负性的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567841015496744960", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567841015496744960", "title": "2025年四川省泸州市泸县第五中学中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567476564939546624", "questionArticle": "<p>10．为了锻炼身体，小明计划购买毽子和跳绳两种体育用品（两种都买），共花费15元，毽子单价2元，跳绳单价3元，则购买方案有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．5种B．3种C．2种D．无数种</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江佳木斯 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-21", "keyPointIds": "16420|16434", "keyPointNames": "二元一次方程的解|方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567476553174523904", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567476553174523904", "title": "2025年黑龙江省佳木斯市富锦市四校联考中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 125, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 125, "timestamp": "2025-07-01T02:15:38.669Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}