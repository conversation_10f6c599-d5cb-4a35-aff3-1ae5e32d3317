{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 54, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "580621224285347840", "questionArticle": "<p>1．随着科技的发展，许多家庭都使用了智能家居设备．某些智能设备通常安装了两块电池：主电池和辅助电池．由于主电池负责主要的电力供应，其损耗速度比辅助电池快．如果主电池耗尽后直接更换新电池，而辅助电池继续使用旧电池，设备的续航时间和稳定性会显著下降；如果同时更换两块电池，使用成本又会增加．为了解决这个问题，设备制造问商建议定期对两块电池进行轮换使用．</p><p>已知：主电池在设备使用达到400小时后需要更换，辅助电池在设备使用达到600小时后需要更换．</p><p>（1）设每个电池的总消耗量为1，则主电池每使用1小时的消耗量为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，辅助电池每使用1小时的消耗量为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）如果在电池的使用周期内只交换一次主电池和辅助电池，那么应在设备使用时间达到多少小时后进行交换，才能使两块电池同时耗尽？并求出电池耗尽时设备的总使用时间．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京北京13中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16429", "keyPointNames": "实际问题与二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580621194862305280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580621194862305280", "title": "北京市第十三中学分校2024—2025学年下学期七年级期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580621218602065920", "questionArticle": "<p>2．解方程（组）：</p><p>（1） $ x{^{3}}-3=\\dfrac { 3 } { 8 } $ </p><p>（2） $ {\\left( { x+1 } \\right) ^ {2}}=81 $ </p><p>（3） $ \\begin{cases} x=y+3 \\\\ 2x-y=5 \\end{cases}  $ </p><p>（4） $ \\begin{cases} 2m-n=3 \\\\ 3m+1=2+2n \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京13中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16287|16290|16423|16424", "keyPointNames": "平方根|立方根|代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580621194862305280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580621194862305280", "title": "北京市第十三中学分校2024—2025学年下学期七年级期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580621216358113280", "questionArticle": "<p>3．已知方程组 $ \\begin{cases} 3x-4y=2a \\\\ 2x+9y=a-18 \\end{cases}  $ 的解满足条件 $ x+y=3 $ ，则 $ a $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京13中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580621194862305280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580621194862305280", "title": "北京市第十三中学分校2024—2025学年下学期七年级期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580621210154737664", "questionArticle": "<p>4．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} x+3y=4-a \\\\ x-y=3a \\end{cases}  $ ，给出下列结论：</p><p>①当这个方程组的解 $ x $ ， $ y $ 的值互为相反数时， $ a=-2 $ ；</p><p>②当 $ a=1 $ 时，方程组的解也是方程 $ x+y=1+2a $ 的解；</p><p>③无论 $ a $ 取什么实数， $ x+2y $ 的值始终不变．</p><p>其中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．①②B．②③C．①③D．①②③</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京北京13中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580621194862305280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580621194862305280", "title": "北京市第十三中学分校2024—2025学年下学期七年级期中数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580621578704035840", "questionArticle": "<p>5．在平面直角坐标系中，直线 $ y=x+4 $ 和直线 $ y=-x+2 $ 的交点位于（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．第一象限B．第二象限C．第三象限D．第四象限</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京人大附中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16424|16535", "keyPointNames": "加减消元法解二元一次方程组|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580621561528360960", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580621561528360960", "title": "北京市中国人民大学附属中学2024−2025学年八年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "581950712059305984", "questionArticle": "<p>6．近年来新能源汽车产业及市场迅猛增长，为了缓解新能源汽车充电难的问题，某小区计划新建地上和地下两类充电桩，每个充电桩的占地面积分别为 $ { { 3 } }{ \\rm{ m } }{^{2}} $ 和 $ { { 1 } }{ \\rm{ m } }{^{2}} $ ，已知新建1个地上充电桩和2个地下充电桩需要0.8万元，新建2个地上充电桩和1个地下充电桩需要0.7万元．</p><p>（1）该小区新建一个地上充电桩和一个地下充电桩各需多少万元？</p><p>（2）若该小区计划用不超过16.3万元的资金新建60个充电桩，且地下充电桩的数量不少于40个，则共有几种建造方案？并列出所有方案；</p><p>（3）现考虑到充电设备对小区居住环境的影响，要求充电桩的总占地面积不得超过 $ a{ \\rm{ m } }{^{2}} $ ，在（2）的前提下，若仅有两种方案可供选择，直接写出<i>a</i>的取值范围．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川内江六中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16441|16490", "keyPointNames": "其他问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950674998435840", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "581950674998435840", "title": "四川省内江市第六中学2025年九年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580622457570111488", "questionArticle": "<p>7．已知方程组 $ \\{\\hspace{-0.5em}  \\begin{array} {} x+y=-7-m \\\\ x-y=1+3m \\end{array} \\hspace{-0.5em}  $ 的解满足<i>x</i>为非正数，<i>y</i>为负数．</p><p>（1）求<i>m</i>的取值范围；</p><p>（2）化简： $ \\left  | { m-3\\left  | { - } \\right  | m+2 } \\right  |  $ </p><p>（3）在<i>m</i>的取值范围内，当<i>m</i>取何整数时，不等式 $ 2mx+x &gt; 2m+1 $ 的解集为 $ x  &lt;  1 $ ？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建泉州五中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 8, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16256|16424|16489", "keyPointNames": "化简绝对值|加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622432030994432", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622432030994432", "title": "福建省泉州第五中学2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580622454596349952", "questionArticle": "<p>8．解二元一次方程组： $ \\begin{cases} 4x-3y=5 \\\\ 2x+y=5 \\end{cases}  $ ；</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建泉州五中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622432030994432", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622432030994432", "title": "福建省泉州第五中学2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580622448522997760", "questionArticle": "<p>9．已知 $ x $ 、 $ y $ 、 $ z $ 是三个非负实数，满足 $ 3x+2y+z=5 $ ， $ x+y-z=2 $ ，若 $ s=2x+y-z $ ，则 $ s $ 的最大值与最小值的差为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．2C．3D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建泉州五中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16305|16424", "keyPointNames": "代数式求值|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622432030994432", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622432030994432", "title": "福建省泉州第五中学2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580622447600250880", "questionArticle": "<p>10．若方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ 的解是 $ \\begin{cases} x=-4 \\\\ y=+2 \\end{cases}  $ ，则方程组 $ \\begin{cases} 3a{{}_{ 1 } }x+2b{{}_{ 1 } }y=a{{}_{ 1 } }+c{{}_{ 1 } } \\\\ 3a{{}_{ 2 } }x+2b{{}_{ 2 } }y=a{{}_{ 2 } }+c{{}_{ 2 } } \\end{cases}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=-1 \\\\ y=1 \\end{cases}  $ B． $ \\begin{cases} x=-1 \\\\ y=-1 \\end{cases}  $ C． $ \\begin{cases} x=\\dfrac { 5 } { 3 } \\\\ y=1 \\end{cases}  $ D． $ \\begin{cases} x=\\dfrac { 5 } { 3 } \\\\ y=-1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建泉州五中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16420|16427", "keyPointNames": "二元一次方程的解|同解方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622432030994432", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622432030994432", "title": "福建省泉州第五中学2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 55, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 55, "timestamp": "2025-07-01T02:07:17.950Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}