{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 78, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "576951593876107264", "questionArticle": "<p>1．把 $ 1-9 $ 这 $ 9 $ 个数填入 $ 3\\times 3 $ 方格中，使其任意一行，任意一列及两条对角线上的数之和都相等，这样便构成了一个“九宫格”．它源于我国古代的“洛書”（图 $ ① $ ），是世界上最早的“幻方”．图 $ ② $ 是仅可以看到部分数值的“九宫格”，则其中 $ x $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>&nbsp;&nbsp;&nbsp;&nbsp;<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/12/2/1/0/0/0/576951554114101252/images/img_13.png\" style=\"vertical-align:middle;\" width=\"208\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ 1 $ B． $ 3 $ C． $ 4 $ D． $ 6 $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北咸宁 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576951577879031808", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "576951577879031808", "title": "2025年湖北省咸宁市中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "575479343180197888", "questionArticle": "<p>2．（1）解方程组 $ \\begin{cases} 2x-y=5 \\\\ 4x+3y=-10 \\end{cases}  $ </p><p>（2）解不等式组 $ \\begin{cases} 2\\left ( { x-1 } \\right ) +1 &gt; -3 \\\\ x-1\\leqslant  \\dfrac { 1+x } { 3 } \\end{cases}  $ 并把它的解集在数轴上表示出来．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/08/2/1/0/0/0/575479280995442691/images/img_14.png\" style=\"vertical-align:middle;\" width=\"221\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州市杨桥中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16424|16489|28266", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组|在数轴上表示不等式的解集", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575479321046855680", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575479321046855680", "title": "福建省福州杨桥中学2024−2025学年下学期七年级数学半期考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575479337098457088", "questionArticle": "<p>3．小明和小亮在一起探究一个数学活动．首先小亮站立在箱子上，小明站立在地面上（如图1），然后交换位置（如图2），测量的数据如图所示，想要探究的问题有：①小明的身高；②小亮的身高；③箱子的高度；④小明与小亮的身高和．根据图上信息，你认为可以计算出的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/08/2/1/0/0/0/575479280991248387/images/img_8.png\" style=\"vertical-align:middle;\" alt=\"试题资源网 https://stzy.com\"></p><p>A．①B．②C．③D．④</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州市杨桥中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575479321046855680", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575479321046855680", "title": "福建省福州杨桥中学2024−2025学年下学期七年级数学半期考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "575479335584313344", "questionArticle": "<p>4．如图，在长为20、宽为15的长方形中，有形状、大小完全相同的5个小长方形，则图中阴影部分的面积为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/08/2/1/0/0/0/575479280991248386/images/img_7.jpg\" style=\"vertical-align:middle;\" width=\"143\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州市杨桥中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575479321046855680", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575479321046855680", "title": "福建省福州杨桥中学2024−2025学年下学期七年级数学半期考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575479333868843008", "questionArticle": "<p>5．已知 $ \\begin{cases} x=2 \\\\ y=a \\end{cases}  $ 是方程 $ 2x+y=7 $ 的一个解，则 $ a $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ a=-1 $ B． $ a=1 $ C． $ a=-3 $ D． $ a=3 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州市杨桥中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575479321046855680", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575479321046855680", "title": "福建省福州杨桥中学2024−2025学年下学期七年级数学半期考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578014650903601152", "questionArticle": "<p>6．为响应“全民植树增绿，共建美丽中国”的号召，学校组织学生到郊外参加义务植树活动，并准备了 $ A，B $ 两种食品作为午餐．这两种食品每包质量均为 $ 50{ \\rm{ g } } $ ，营养成分表如下．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/15/2/1/0/0/0/578014570721095691/images/img_22.png\" style=\"vertical-align:middle;\" width=\"384\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）若要从这两种食品中摄入 $ 5000 { \\rm{ k } }{ \\rm{ J } } $ 热量和 $ 80 { \\rm{ g } } $ 蛋白质，应选用 $ A， B $ 两种食品各多少包？</p><p>（2）运动量大的人或青少年对蛋白质的摄入量应更多．若每份午餐选用这两种食品共8包，要使每份午餐中的蛋白质含量不低于 $ { { 9 } }{ { 0 } } { \\rm{ g } } $ ，且热量最低，应如何选用这两种食品？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东济南 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578014607454806016", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578014607454806016", "title": "2025年山东省济南市东南片区中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "5"}, {"questionId": "576951450049224704", "questionArticle": "<p>7．《九章算术》是中国古代数学著作之一，书中有这样一个问题：五只雀、六只燕的质量共一斤；雀重燕轻，互换其中一只，恰好质量相等．问：每只雀、燕的质量各为多少？设一只雀的质量为<i>x</i>斤，一只燕的质量为<i>y</i>斤（注：1斤＝500克），则根据题意列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+6y=1 \\\\ 4x+y=5y+x \\end{cases}  $ B． $ \\begin{cases} 5x+6y=1 \\\\ 5x-y=6y-x \\end{cases}  $ </p><p>C． $ \\begin{cases} 6x+5y=1 \\\\ 4x+y=5y+x \\end{cases}  $ D． $ \\begin{cases} 6x+5y=1 \\\\ 5x-y=6y-x \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北十堰 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576951435444658176", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "576951435444658176", "title": "2025年湖北省十堰市九年级数学中考二模试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578014788090900480", "questionArticle": "<p>8．合川桃片有香甜味和椒盐味两种类型，五一将至，小新打算购买若干袋香甜味桃片和椒盐味桃片．</p><p>（1）小新花费4300元购买了40袋香甜味桃片和50袋椒盐味桃片，已知10袋香甜味桃片和9袋椒盐味桃片的售价相同，求每袋香甜味桃片和椒盐味桃片的售价分别是多少元？</p><p>（2）由于市场供不应求，香甜味和椒盐味桃片的价格均有上涨，其中每袋香甜味桃片的售价是每袋椒盐味桃片售价的1.2倍，小新分别花费了2400元、3600元购买香甜味桃片和椒盐味桃片，一共购买了100袋，求每袋椒盐味桃片的售价．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆八中 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16438|16476", "keyPointNames": "和差倍分问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578014755102699520", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578014755102699520", "title": "2025年重庆市第八中学校中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577296100299677696", "questionArticle": "<p>9．小明在解方程组 $ \\begin{cases} ax+by=2 \\\\ cx-3y=-2 \\end{cases}  $ 时，得到的解是 $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $ ，小英同样解这个方程组，由于把<i>c</i>抄错而得到的解是 $ \\begin{cases} x=2 \\\\ y=-6 \\end{cases}  $ ，求<i>a</i>，<i>b</i>，<i>c</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东聊城 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577296077239394304", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577296077239394304", "title": "山东省聊城市2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577296095316844544", "questionArticle": "<p>10．已知关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} x+2y=5m \\\\ 6x-y=4m \\end{cases}  $ 的解满足 $ x-2y+6=0 $ ，则<i>m</i>的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东聊城 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577296077239394304", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577296077239394304", "title": "山东省聊城市2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 79, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 79, "timestamp": "2025-07-01T02:10:08.755Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}