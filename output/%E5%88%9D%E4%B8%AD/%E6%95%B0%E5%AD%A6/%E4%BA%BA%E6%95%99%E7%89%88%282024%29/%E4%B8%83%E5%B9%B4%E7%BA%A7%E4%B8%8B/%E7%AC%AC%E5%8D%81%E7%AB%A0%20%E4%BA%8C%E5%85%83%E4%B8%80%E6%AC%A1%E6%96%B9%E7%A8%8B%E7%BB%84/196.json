{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 195, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "544317375010611200", "questionArticle": "<p>1．解方程组 $ \\begin{cases}ax+by=2,\\\\ cx+7y=8\\end{cases} $ 时，甲同学正确解得 $ \\begin{cases}x=2,\\\\ y=2,\\end{cases} $ 乙同学因把 $ c $ 写错而得到 $ \\begin{cases}x=-1,\\\\ y=3,\\end{cases} $ 则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ c= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317372452085760", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317372452085760", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.2 加减消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317374624735232", "questionArticle": "<p>2．已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases}ax+2(a-1)y=a,\\\\ 2x+2y=3,\\end{cases} $ 有下列几种说法：①一定有唯一解；②可能有无数个解；③当 $ a=2 $ 时方程组无解；④若方程组的一个解中 $ y $ 的值为0，则 $ a=0 $ .其中正确的说法有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0种B．1种C．2种D．3种</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 8, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317372452085760", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317372452085760", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.2 加减消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317061687713792", "questionArticle": "<p>3．为扩大粮食生产规模，某粮食生产基地计划投入一笔资金购进甲、乙两种农机具.已知购进2件甲种农机具和1件乙种农机具共需4.5万元，购进1件甲种农机具和3件乙种农机具共需5万元.</p><p>（1） 购进1件甲种农机具和1件乙种农机具各需多少元？</p><p>（2） 若该粮食生产基地计划购进甲、乙两种农机具共20件，且投入资金不少于22.8万元又不超过25万元，设购进甲种农机具 $ m $ 件，求该基地投入总资金的最小值.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317057917034496", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317057917034496", "title": "2024—2025学年七年级下册人教版（2024）数学第十一章11.3 一元一次不等式组 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317375199354880", "questionArticle": "<p>4．已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases}x+2y-6=0,\\\\ x-2y+mx+5=0,\\end{cases} $ 若方程组的解中 $ x $ 恰为整数， $ m $ 也为整数，则 $ m $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317372452085760", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317372452085760", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.2 加减消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317374817673216", "questionArticle": "<p>5．解方程组 $ \\begin{cases}4x-5y=2,\\\\ x+5y=9\\end{cases} $ 适合用<u>&nbsp;&nbsp;&nbsp;&nbsp;</u>消元法，解方程组 $ \\begin{cases}x=4y,\\\\ x+5y=9\\end{cases} $ 适合用<u>&nbsp;&nbsp;&nbsp;&nbsp;</u>消元法.（填“代入”或“加减”）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317372452085760", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317372452085760", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.2 加减消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317374427602944", "questionArticle": "<p>6．已知 $ x $ ， $ y $ 满足方程组 $ \\begin{cases}3x-y=5-2m,\\\\ x+3y=m,\\end{cases} $ 则无论 $ m $ 取何值， $ x $ ， $ y $ 恒有关系式是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 4x+2y=5 $ B． $ 2x-2y=5 $ C． $ x+y=1 $ D． $ 5x+7y=5 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 10, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317372452085760", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317372452085760", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.2 加减消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317374238859264", "questionArticle": "<p>7．若 $ |x-3y+3|+(3x+y-5)^{2}=0 $ ，则（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x $ , $ y $ 的值都是正数B． $ x $ , $ y $ 的值都是负数</p><p>C． $ x $ 是正数， $ y $ 是负数D． $ x $ 是负数， $ y $ 是正数</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317372452085760", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317372452085760", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.2 加减消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317374045921280", "questionArticle": "<p>8．用加减消元法解方程组 $ \\begin{cases}3x-2y=3,\\mathrm{①}\\\\ 4x+y=15\\mathrm{②}\\end{cases} $ 时，如果消去 $ y $ ，最简便的方法是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\mathrm{②}×2-\\mathrm{①} $ B． $ \\mathrm{②}×2+\\mathrm{①} $ </p><p>C． $ \\mathrm{①}×4-\\mathrm{②}×3 $ D． $ \\mathrm{①}×4+\\mathrm{②}×3 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317372452085760", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317372452085760", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.2 加减消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317373760708608", "questionArticle": "<p>9．用加减消元法解方程组 $ \\begin{cases}5x-2y=3,\\mathrm{①}\\\\ x+2y=-19,\\mathrm{②}\\end{cases} $ 最简便的做法是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\mathrm{①}+\\mathrm{②} $ B． $ \\mathrm{①}-\\mathrm{②} $ C． $ \\mathrm{①}+\\mathrm{②}×5 $ D． $ \\mathrm{①}×5-\\mathrm{②} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317372452085760", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317372452085760", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.2 加减消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "544317289534889984", "questionArticle": "<p>10．甲、乙两位同学在解关于 $ x $ , $ y $ 的方程组 $ \\begin{cases}2ax-by=-2,\\mathrm{①}\\\\ ax+by=7\\mathrm{②}\\end{cases} $ 时，甲看错了方程①，解得 $ \\begin{cases}x=1,\\\\ y=-1；\\end{cases} $ 乙看错了②，解得 $ \\begin{cases}x=-2,\\\\ y=3,\\end{cases} $ 求 $ a $ , $ b $ 的值.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317286896672768", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317286896672768", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.1 代入消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 196, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 196, "timestamp": "2025-07-01T02:24:00.912Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}