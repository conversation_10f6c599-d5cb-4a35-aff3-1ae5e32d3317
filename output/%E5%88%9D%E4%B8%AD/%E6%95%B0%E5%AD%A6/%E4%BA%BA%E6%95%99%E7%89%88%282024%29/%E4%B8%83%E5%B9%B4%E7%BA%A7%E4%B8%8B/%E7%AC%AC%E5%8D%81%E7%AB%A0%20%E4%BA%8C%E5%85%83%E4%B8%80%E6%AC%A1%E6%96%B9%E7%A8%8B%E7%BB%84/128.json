{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 127, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "568162651085250560", "questionArticle": "<p>1．下列叙述正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．方程组 $ \\begin{cases} x=5 \\\\ y=6 \\end{cases}  $ 不是二元一次方程组</p><p>B．方程 $ xy=1 $ 不是二元一次方程</p><p>C． $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ 既是方程 $ 2x-3y=1 $ 的解，也是方程 $ \\dfrac { { { 1 } } } { { { 2 } } }x-2y=1 $ 的解</p><p>D．任何一个二元一次方程组的解都是唯一存在的</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16419|16420|16421", "keyPointNames": "二元一次方程的定义|二元一次方程的解|二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568162643246096384", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "568162643246096384", "title": "江苏省南京市第五十中学2024—2025学年下学期3月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "568163704237236224", "questionArticle": "<p>2．解方程组： $ \\begin{cases} 2x+3y=9 \\\\ x-2y=1 \\end{cases}  $ .</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568163682905006080", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "568163682905006080", "title": "浙江省J12共同体联盟学校2024−2025学年九年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568163699778691072", "questionArticle": "<p>3．已知 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是方程 $ x+ky=9 $ 的一个解，则<i>k</i>的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-19", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568163682905006080", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "568163682905006080", "title": "浙江省J12共同体联盟学校2024−2025学年九年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568158499303104512", "questionArticle": "<p>4．待定系数法是确定函数解析式的常用方法，也可用于化学方程式的配平．以黄铜矿为主要原料的火法炼铜的化学反应方程式为 $ x{ \\rm{ C } }{ \\rm{ u } }{ \\rm{ F } }{ \\rm{ e } }{ \\rm{ S } }{{}_{ 2 } }+y{ \\rm{ O } }{{}_{ { { 2 } } } }{\\mathop { = } \\limits_{  } ^{{ {   } }高温{ {   } }}}x{ \\rm{ C } }{ \\rm{ u } }{ { + } }{ { 4 } }{ \\rm{ F } }{ \\rm{ e } }{ \\rm{ O } }{ { + } }{ { 2 } }{ \\rm{ F } }{ \\rm{ e } }{{}_{ { { 2 } } } }{ \\rm{ O } }{{}_{ { { 3 } } } }{ { + } }{ { 2 } }x{ \\rm{ S } }{ \\rm{ O } }{{}_{ { { 2 } } } }\\uparrow  $ ，其中 $ x，y $ 为常数，则 $ x+y $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-19", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568158478050566144", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "568158478050566144", "title": "福建省福州延安中学 2024−2025 学年下学期九年级期中考 数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568158277990653952", "questionArticle": "<p>5．欣欣农贸公司将收购的农产品加工成甲、乙两种礼盒进行销售，每件农产品的单价和体积如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse; width:350.25pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 51.75pt;\"><p>品种</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 149.25pt;\"><p>每件的单价（单位：元）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 149.25pt;\"><p>每件的体积（单位：立方米）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 51.75pt;\"><p>甲</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 149.25pt;\"><p>80</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 149.25pt;\"><p>0.075</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 51.75pt;\"><p>乙</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 149.25pt;\"><p>60</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 149.25pt;\"><p>0.06</p></td></tr></table><p>经营户张老板有一辆车箱体积为13.2立方米的箱式小货车，用13600元购进甲、乙两种礼盒正好堆满了车箱．求他购进的两种礼盒各多少件？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000|340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025甘肃陇南 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-04-19", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577815898280472576", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "577815898280472576", "title": "甘肃省陇南市武都区部分学校2024−2025学年九年级下学期4月教学质量检测", "paperCategory": 11}, {"id": "568158245409300480", "title": "2025年安徽省合肥市包河区九年级下学期中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568158101209128960", "questionArticle": "<p>6．我县在创建全国文明城市过程中，决定购买<i>A</i>，<i>B</i>两种树苗对某路段道路进行绿化改造，已知购买<i>A</i>种树苗8棵，<i>B</i>种树苗3棵，要950元；若购买<i>A</i>种树苗5棵，<i>B</i>种树苗6棵，则需要800元．</p><p>(1)求购买<i>A</i>，<i>B</i>两种树苗每棵各需多少元？</p><p>(2)考虑到绿化效果和资金周转，购进<i>A</i>种树苗要多于<i>B</i>种树苗，且用于购买这两种树苗的资金不能超过7650元，若购进这两种树苗共100棵，则有哪几种购买方案？</p><p>(3)在(2)的条件下，哪种方案最省钱？最少费用是多少？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽安庆 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-04-18", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568158079243558912", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "568158079243558912", "title": "安徽省安庆市20校联考2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "202459557972451328", "title": "山东省德州市宁津县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567099704250507264", "questionArticle": "<p>7．综合与探究</p><p>某商场计划购进一批甲、乙两种类型的玩具，已知一件甲种玩具的进价与一件乙种玩具的进价的和为40元，用1200元购进甲种玩具的件数与用2000元购进乙种玩具的件数相同．</p><p>(1)甲、乙两种玩具每件的进价分别是多少元？</p><p>(2)商场计划购进甲、乙两种玩具共50件，其中甲种玩具的件数不多于24．若商场决定此次进货的总资金不超过1050元，求商场共有几种进货方案．</p><p>(3)在（2）的条件下，若每件甲种玩具的售价为40元，每件乙种玩具的售价为55元，商场为扩大销量，推出“买一赠一”活动．顾客从这两种玩具中任购一件，就可以从这两种玩具中任选一件作为赠品．若这批玩具在活动期间全部售出后并恰好获利235元，求商场的进货方案．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山西临汾 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-18", "keyPointIds": "16420|16476|16490", "keyPointNames": "二元一次方程的解|分式方程的实际应用|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567099674416422912", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567099674416422912", "title": "山西省临汾市霍州市部分多校联考2024−2025学年下学期八年级3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568163796302209024", "questionArticle": "<p>8．某广告公司要利用长为240cm、宽为40cm的 $ \\mathrm{ K }\\mathrm{ T } $ 板裁切甲、乙两种广告牌，已知甲广告牌尺寸为 $ { { 4 } }{ { 0 } }{ \\rm{ c } }{ \\rm{ m } }\\times { { 1 } }{ { 5 } }{ \\rm{ c } }{ \\rm{ m } } $ ，乙广告牌尺寸为 $ { { 4 } }{ { 0 } }{ \\rm{ c } }{ \\rm{ m } }\\times 3{ { 5 } }{ \\rm{ c } }{ \\rm{ m } } $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/20/2/1/0/0/0/569166407931305984/images/img_1.png\" style='vertical-align:middle;' width=\"126\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)若该广告公司用1块 $ \\mathrm{ K }\\mathrm{ T } $ 板裁切出的甲广告牌的数量是乙广告牌的数量的3倍，在不造成板材浪费的前提下，求此时裁切出的甲、乙广告牌的数量；</p><p>(2)求1块 $ \\mathrm{ K }\\mathrm{ T } $ 板的所有无浪费裁切方案；</p><p>(3)现需要甲、乙两种广告牌各500块，该公司仓库已有488块乙广告牌，还需要购买该型号板材多少块（恰好全部用完）？写出购买数量，并说明如何裁切．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025浙江 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-18", "keyPointIds": "16432|16438", "keyPointNames": "配套问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568163772403064832", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "568163772403064832", "title": "浙江省J12共同体联盟学校2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568163792061767680", "questionArticle": "<p>9．解下列二元一次方程组：</p><p>(1) $ \\begin{cases} y=3x+2 \\\\ x+2y=11 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 2a-5b=12 \\\\ 4a+3b=-2 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-18", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568163772403064832", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "568163772403064832", "title": "浙江省J12共同体联盟学校2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568163789775872000", "questionArticle": "<p>10．小张计划花20元购买了铅笔和记号笔，铅笔每支3元，记号笔每支2元，并且购买的记号笔数量超过了铅笔的数量，若剩余3元，则小张购买的铅笔可能有<u>&nbsp;&nbsp;&nbsp;&nbsp;</u>支．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-18", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568163772403064832", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "568163772403064832", "title": "浙江省J12共同体联盟学校2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 128, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 128, "timestamp": "2025-07-01T02:15:59.815Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}