{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 60, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "580899998826536960", "questionArticle": "<p>1．解方程（组）：</p><p>（1） $ {\\left( { 3x+1 } \\right) ^ {2}}-8=1 $ ；</p><p>（2） $ \\begin{cases} 2\\left ( { x+y } \\right ) =7+3y \\\\ \\dfrac { x+2 } { 3 }=\\dfrac { y+1 } { 2 } \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16287|16424", "keyPointNames": "平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580899964739428352", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580899964739428352", "title": "重庆实验外国语学校2024−2025学年七年级下学期半期考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580899995315904512", "questionArticle": "<p>2．如图，用15块形状和大小完全相同的小长方形纸片拼成一个宽是10厘米的大长方形，则大长方形的面积是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>平方厘米．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/22/2/1/0/0/0/580899935563853833/images/img_9.png\" style=\"vertical-align:middle;\" width=\"182\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580899964739428352", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580899964739428352", "title": "重庆实验外国语学校2024−2025学年七年级下学期半期考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580899398357397504", "questionArticle": "<p>3．现有大量的沙石需要运输．“益安”车队原来有载重量为8吨、10吨的卡车共12辆，全部车辆运输一次能运输110吨沙石．求“益安”车队载重量为8吨、10吨的卡车各有多少辆？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580899372625342464", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580899372625342464", "title": "重庆市长寿实验中学校2024-2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580899992077901824", "questionArticle": "<p>4．已知 $ M{{}_{ n } } $ 为整式，且 $ M{{}_{ n } }=a{{}_{ n } }x{^{n}}+a{{}_{ n-1 } }x{^{n-1}}+\\cdots +a{{}_{ 1 } }x+a{{}_{ 0 } } $ ，其中 $ a{{}_{ n } }，a{{}_{ n-1 } }，\\cdots ，a{{}_{ 1 } }，a{{}_{ 0 } } $ 为正整数， $ n $ 为自然数，令 $ F\\left ( { M{{}_{ n } } } \\right ) =a{{}_{ n } }+a{{}_{ n-1 } }+\\cdots +a{{}_{ 1 } }+a{{}_{ 0 } }+n $ ．下列说法：</p><p>①若 $ n=1 $ 时， $ a{{}_{ 1 } } $ 和 $ a{{}_{ 0 } } $ 满足 $ \\begin{cases} a{{}_{ 1 } }+2a{{}_{ 0 } }=11 \\\\ 3a{{}_{ 1 } }-a{{}_{ 0 } }=5 \\end{cases}  $ ，则 $ F\\left ( { M{{}_{ n } } } \\right ) =8 $ ；</p><p>②不存在 $ x $ 和 $ n $ 的值，使 $ M{{}_{ n } }=F\\left ( { M{{}_{ n } } } \\right )  $ ；</p><p>③若 $ n\\mathrm{ \\geqslant  }2 $ 时， $ a{{}_{ 2 } }+a{{}_{ 1 } }=a{{}_{ 0 } } $ ， $ F\\left ( { M{{}_{ n } } } \\right ) =8 $ ，则满足条件的所有整式 $ M{{}_{ n } } $ 的和为 $ x{^{3}}+4x{^{2}}+4x+5 $ ．其中正确的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3B．2C．1D．0</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580899964739428352", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580899964739428352", "title": "重庆实验外国语学校2024−2025学年七年级下学期半期考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580899396117639168", "questionArticle": "<p>5．解方程或方程组</p><p>（1） $ 9{\\left( { 2x-1 } \\right) ^ {2}}=225 $ </p><p>（2） $ \\begin{cases} 2x-y=6 \\\\ 2y+x=8 \\end{cases}  $ </p><p>（3） $ \\begin{cases} 7x+3y=24 \\\\ 5x-2y=13 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16287|16424", "keyPointNames": "平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580899372625342464", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580899372625342464", "title": "重庆市长寿实验中学校2024-2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580899990815416320", "questionArticle": "<p>6．已知关于 $ x，y $ 的二元一次方程组 $ \\begin{cases} kx+3y=2 \\\\ 2x+y=-2 \\end{cases}  $ 的解 $ x，y $ 均为整数，则符合条件的整数 $ k $ 的值有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）个．</p><p>A．4B．5C．6D．8</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580899964739428352", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580899964739428352", "title": "重庆实验外国语学校2024−2025学年七年级下学期半期考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580899981898326016", "questionArticle": "<p>7．《天工开物》中记载：“凡扎花灯，需竹篾八分，彩绢三尺．”某非遗工坊用竹篾和彩绢制作传统花灯，每盏大灯用竹篾 $ 1.2 $ 米、彩绢 $ 5 $ 米，每盏小灯用竹篾 $ 0.5 $ 米、彩绢 $ 2 $ 米．若工坊恰好用完了 $ 120 $ 米竹篾和 $ 490 $ 米彩绢，设制作大灯 $ x $ 盏，小灯 $ y $ 盏，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 1.2x+0.5y=120 \\\\ 5x+2y=490 \\end{cases}  $ B． $ \\begin{cases} 1.2x+0.5y=490 \\\\ 5x+2y=120 \\end{cases}  $ </p><p>C． $ \\begin{cases} 1.2x+5y=120 \\\\ 0.5x+2y=490 \\end{cases}  $ D． $ \\begin{cases} 1.2x+5y=490 \\\\ 0.5x+2y=120 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580899964739428352", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580899964739428352", "title": "重庆实验外国语学校2024−2025学年七年级下学期半期考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580899393676554240", "questionArticle": "<p>8．已知关于 $ x, y $ 的二元一次方程组 $ \\begin{cases} 2x+y=7 \\\\ x+2y=m-3 \\end{cases}  $ 的解也是方程 $ x-2y=6 $ 的解，则<i>m</i>值为<u>&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580899372625342464", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580899372625342464", "title": "重庆市长寿实验中学校2024-2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580899980128329728", "questionArticle": "<p>9．已知 $ \\begin{cases} x=3 \\\\ y=1 \\end{cases}  $ 是方程组 $ \\begin{cases} mx+y=4 \\\\ 2x+ny=5 \\end{cases}  $ 的解，则 $ 3m+n $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -2 $ B．2C．5D． $ -3 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580899964739428352", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580899964739428352", "title": "重庆实验外国语学校2024−2025学年七年级下学期半期考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580899384558137344", "questionArticle": "<p>10．方程组 $ \\begin{cases} 4x-3y=k \\\\ 2x+3y=5 \\end{cases}  $ 的解 $ x $ 与 $ -y $ 的值相等，则 $ k= $ （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．15或 $ -15 $ B．25C．35D． $ -35 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16420|16423", "keyPointNames": "二元一次方程的解|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580899372625342464", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580899372625342464", "title": "重庆市长寿实验中学校2024-2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 61, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 61, "timestamp": "2025-07-01T02:08:00.826Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}