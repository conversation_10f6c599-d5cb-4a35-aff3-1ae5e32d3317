{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 147, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "562404822852345856", "questionArticle": "<p>1．某小区计划购买10台健身器材供小区居民锻炼使用，了解到购买1台<i>B</i>型健身器材比购买1台<i>A</i>型健身器材贵200元，购买2台<i>A</i>型健身器材和5台<i>B</i>型健身器材共花8000元．</p><p>(1)<i>A</i>型健身器材和<i>B</i>型健身器材的单价各是多少元？</p><p>(2)该小区计划购买<i>B</i>型健身器材的数量不得超过<i>A</i>型健身器材，购买资金不低于10800元．请通过计算说明共有几种购买方案？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西十中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-07", "keyPointIds": "16435|16490", "keyPointNames": "分配问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562404798760263680", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562404798760263680", "title": "山西省实验中学2024−2025学年下学期3月月考八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562773246048772096", "questionArticle": "<p>2．在“元旦”期间，七（1）班小明，小亮等同学随家长一同到某公园游玩，小明与他爸爸的对话：</p><p>票价：成人：每张40元；学生：按成人票价的5折优惠；团体票（16人以上含16人）：按成人票价的6折优惠.</p><p>爸爸：大人门票是每张40元，学生门票是5折优惠，我们一共12人，共需400元．</p><p>小明：爸爸，等一下，让我算一算，另一种方式买票是否可以省钱？</p><p>(1)小明他们一共去了几个成人，几个学生？</p><p>(2)请你帮助小明算一算，用哪种方式购票省钱？请说明理由．</p><p>(3)正要购票时，小明发现七（2）班的小张等10名同学和他们的7名家长共17人也来购票，为了节省费用，经协商，他们决定一起购票，请你为他们设计最省钱的购票方案，并求出此时的费用．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025山西临汾 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-06", "keyPointIds": "16435|16438", "keyPointNames": "分配问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562773223684743168", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562773223684743168", "title": "山西省临汾一中第一附属学校2024−2025 学年七年级下学期第一次月质量监测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562773245365100544", "questionArticle": "<p>3．定义：二元一次方程 $ y=ax+b $ 与二元一次方程 $ y=bx+a $ 互为“反对称二元一次方程”．如：二元一次方程 $ y=2x+1 $ 与二元一次方程 $ y=x+2 $ 互为“反对称二元一次方程”．</p><p>(1)直接写出二元一次方程 $ y=-x+4 $ 的“反对称二元一次方程”<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)二元一次方程 $ y=4x+3 $ 的解 $ \\begin{cases} x=m \\\\ y=n \\end{cases}  $ 又是它的“反对称二元一次方程”的解，求出 $ m $ 、 $ n $ 的值；</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山西临汾 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-06", "keyPointIds": "16425|16426", "keyPointNames": "二元一次方程组的特殊解法|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562773223684743168", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562773223684743168", "title": "山西省临汾一中第一附属学校2024−2025 学年七年级下学期第一次月质量监测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562773241812525056", "questionArticle": "<p>4．计算：</p><p>(1)解方程： $ \\dfrac { x-2 } { 3 }=\\dfrac { 2x-4 } { 5 } $ ；</p><p>(2)解方程组： $ \\begin{cases} x-2y=-3 \\\\ 3x-y=-4 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西临汾 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-06", "keyPointIds": "16402|16426", "keyPointNames": "解一元一次方程|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562773223684743168", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562773223684743168", "title": "山西省临汾一中第一附属学校2024−2025 学年七年级下学期第一次月质量监测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562773241095299072", "questionArticle": "<p>5．若关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} ax-by=3 \\\\ 2ax-3by=10 \\end{cases}  $ 的解为 $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ ，则关于 $ m $ ， $ n $ 二元一次方程组 $ \\begin{cases} a\\left ( { m+1 } \\right ) -b\\left ( { n-2 } \\right ) =3 \\\\ 2a\\left ( { m+1 } \\right ) -3b\\left ( { n-2 } \\right ) =10 \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西临汾 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-06", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562773223684743168", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562773223684743168", "title": "山西省临汾一中第一附属学校2024−2025 学年七年级下学期第一次月质量监测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562773235269410816", "questionArticle": "<p>6．如图，9个大小，形状完全相同的小长方形，组成了一个周长为46的大长方形 $ ABCD $ ，若设小长方形的长为 $ x $ ，宽为 $ y $ ，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/02/2/1/0/0/0/562773188859437058/images/img_2.png\" style=\"vertical-align:middle;\" width=\"176\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} 2x=7y \\\\ 2\\left ( { 7y+x+y } \\right ) =46 \\end{cases}  $ B． $ \\begin{cases} 2x=7y \\\\ 7y+x+y=46 \\end{cases}  $ </p><p>C． $ \\begin{cases} 7x=2y \\\\ 7y+x+y=46 \\end{cases}  $ D． $ \\begin{cases} 2x=7y \\\\ 2\\left ( { 7y+x+x } \\right ) =46 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西临汾 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-06", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562773223684743168", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562773223684743168", "title": "山西省临汾一中第一附属学校2024−2025 学年七年级下学期第一次月质量监测数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562773234493464576", "questionArticle": "<p>7．今有三人共车，二车空；二人共车，九人步．问：人与车各几何？（选自《孙子算经》）题目大意：有若干人要坐车，若每3人坐一辆车，则有2辆空车；若每2人坐一辆车，则有9人需要步行，问人与车各多少？设共有<i>x</i>辆车，<i>y</i>个人，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3\\left ( { x-2 } \\right ) =y \\\\ 2x+9=y \\end{cases}  $ B． $ \\begin{cases} 3x-2=y \\\\ 2x+9=y \\end{cases}  $ </p><p>C． $ \\begin{cases} 3\\left ( { x+2 } \\right ) =y \\\\ 2x-9=y \\end{cases}  $ D． $ \\begin{cases} 3x+2=y \\\\ 2x-9=y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-04-06", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584852359609622528", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "584852359609622528", "title": "浙江省杭州市十三中教育集团2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}, {"id": "562773223684743168", "title": "山西省临汾一中第一附属学校2024−2025 学年七年级下学期第一次月质量监测数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562773233075789824", "questionArticle": "<p>8．下列各方程组中，属于二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3x+2y=7 \\\\ xy=5 \\end{cases}  $ B． $ \\begin{cases} 2x+y=1 \\\\ x+z=2 \\end{cases}  $ C． $ \\begin{cases} y=2x \\\\ x+4y=2 \\end{cases}  $ D． $ \\begin{cases} \\dfrac { 5 } { x }+\\dfrac { y } { 3 }=\\dfrac { 1 } { 2 } \\\\ x+2y=3 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西临汾 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-06", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562773223684743168", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562773223684743168", "title": "山西省临汾一中第一附属学校2024−2025 学年七年级下学期第一次月质量监测数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562403678679769088", "questionArticle": "<p>9．在平面内，对于 $ ∠P $ 和 $ \\angle Q $ ，给出如下定义：若存在一个常数 $ t\\left ( { t &gt; 0 } \\right )  $ ，使得 $ \\angle P+t\\angle Q=180{}\\degree  $ ，则称 $ {\\rm ∠\\mathit{Q}} $ 是 $ {\\rm ∠\\mathit{P}} $ 的“<i>t</i>系数补角”．例如， $ \\angle P=80{}\\degree ，\\angle Q=20{}\\degree  $ ，有 $ \\angle P+5\\angle Q=180{}\\degree  $ ，则 $ \\angle Q $ 是 $ ∠P $ 的“5系数补角”．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/01/2/1/0/0/0/562403608106410009/images/img_25.png\" style=\"vertical-align:middle;\" width=\"355\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;</p><p>(1)若 $ \\angle P=90{}\\degree  $ ，在 $ \\angle 1=60{}\\degree ，\\angle 2=45{}\\degree ，\\angle 3=30{}\\degree  $ 中， $ ∠P $ 的“3系数补角”是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(2)在平面内， $ AB{ \\rm{ /\\mskip-4mu/ } }CD $ ，点<i>E</i>为直线 $ AB $ 上一点，点<i>F</i>为直线 $ CD $ 上一点．</p><p>①如图1，点<i>G</i>为平面内一点，连接 $ GE，GF $ ， $ \\angle DFG=50{}\\degree  $ ，若 $ \\angle BEG $ 是 $ ∠EGF $ 的“6系数补角”，求 $ \\angle BEG $ 的大小．</p><p>②如图2，连接 $ EF $ ．若<i>H</i>为平面内一动点（点<i>H</i>不在直线 $ AB，CD，EF $ 上）， $ \\angle EFH $ 与 $ \\angle FEH $ 两个角的平分线交于点<i>M</i>．若 $ ∠BEH=α $ ， $ \\angle DFH=β $ ， $ \\angle N $ 是 $ \\angle EMF $ 的“2系数补角”，直接写出 $ \\angle N $ 的大小的所有情况（用含 $ α $ 和 $ β $ 的代数式表示），并写出其中一种情况的求解过程．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025福建福建省厦门双十中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-06", "keyPointIds": "16424|16607|16628", "keyPointNames": "加减消元法解二元一次方程组|角平分线|两直线平行同位角相等", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562403651781697536", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562403651781697536", "title": "福建省厦门双十中学2024−2025学年下学期3月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562404053323390976", "questionArticle": "<p>10．为了响应“足球进校园”的号召，更好地开展足球运动，某学校计划购买一批足球．已知购买3个 $ \\mathrm{ A } $ 品牌足球和2个 $ B $ 品牌足球共需360元；购买2个 $ \\mathrm{ A } $ 品牌足球和1个 $ B $ 品牌足球共需220元．</p><p>(1)求 $ A，B $ 两种品牌足球的单价．</p><p>(2)该学校计划购买 $ A，B $ 两种品牌的足球共75个，且<i>A</i>品牌足球的数量不少于 $ B $ 品牌足球数量的2倍，实际购买时，商家对<i>A</i>品牌足球售价下调<i>m</i>（ $ 0  &lt;  m\\leqslant  30 $ ）元，且限定学校最多购进<i>A</i>品牌足球60个．请你设计出最省钱的购买方案，并说明理由．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南南阳市第十三中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-06", "keyPointIds": "16438|16486|16535", "keyPointNames": "和差倍分问题|一元一次不等式的应用|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562404026416930816", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562404026416930816", "title": "河南省 南阳市第十三中学校2024—2025学年下学期3月月考九年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 148, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 148, "timestamp": "2025-07-01T02:18:23.932Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}