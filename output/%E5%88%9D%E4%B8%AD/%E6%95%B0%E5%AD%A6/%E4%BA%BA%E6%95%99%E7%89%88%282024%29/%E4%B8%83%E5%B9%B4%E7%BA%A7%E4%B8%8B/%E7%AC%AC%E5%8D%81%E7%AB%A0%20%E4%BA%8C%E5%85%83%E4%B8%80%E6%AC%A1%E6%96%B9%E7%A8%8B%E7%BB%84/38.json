{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 37, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "587396075050278912", "questionArticle": "<p>1．某水果商收购了120吨水果打算运往外省售卖，现有甲、乙、丙三种车型供选择，且要求每辆车均满载，每辆车的运载量和运费如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 91.35pt;\"><p>车型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>甲</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>乙</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>丙</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 91.35pt;\"><p>运载量/（吨/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>10</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 91.35pt;\"><p>运费/（元/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>300</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>400</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>500</p></td></tr></table><p>（1）若全部水果都用甲、乙两种车型车辆来运送，所需运费为6400元，则需甲、乙两种车型各多少辆？</p><p>（2）该水果商决定从甲、乙、丙三种车型中至少选择两种车型来运送，已知它们的总辆数为18辆，请通过列方程的方法求出符合题意的运送方案．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "520000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025贵州遵义 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-11", "keyPointIds": "16420|16440", "keyPointNames": "二元一次方程的解|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587396047439175680", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "587396047439175680", "title": "2025年贵州省遵义市三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587750660046630912", "questionArticle": "<p>2．列方程或方程组解决问题：某校组织学生到郊外参加义务植树活动，并准备了<i>A</i>，<i>B</i>两种食品作为师生的午餐，这两种食品每包的营养成分表如下：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/10/2/1/0/0/0/587750599338270723/images/img_12.png\" style=\"vertical-align:middle;\" width=\"391\" alt=\"试题资源网 https://stzy.com\"></p><p>若要从这两种食品中摄入 $ 3000{ \\rm{ K } }{ \\rm{ J } } $ 热量和45g蛋白质，应选取<i>A</i>，<i>B</i>两种食品各多少包？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏盐城 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-06-11", "keyPointIds": "16438|16440", "keyPointNames": "和差倍分问题|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587750621991710720", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "587750621991710720", "title": "2025年江苏省盐城市三校联考中考一模数学试题", "paperCategory": 1}, {"id": "579859021131194368", "title": "北京市第五十中学2024−2025学年下学期4月九年级数学检测试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587750771694804992", "questionArticle": "<p>3．5<i>G</i>时代的到来，将给人类生活带来巨大改变．现有<i>A</i>、<i>B</i>两种型号的5<i>G</i>手机，进价和售价如表所示：型号价格</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 18.45pt;\"><p style=\"text-align:center;\">&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">进价（元/部）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">售价（元/部）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 18.45pt;\"><p style=\"text-align:center;\"><i>A</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">3000</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">3400</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 18.45pt;\"><p style=\"text-align:center;\"><i>B</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">3500</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">4000</p></td></tr></table><p>某营业厅购进<i>A</i>、<i>B</i>两种型号手机共花费32000元，手机销售完成后共获得利润4400元．</p><p>（1）营业厅购进<i>A</i>、<i>B</i>两种型号手机各多少部？</p><p>（2）若营业厅再次购进<i>A</i>、<i>B</i>两种型号手机共30部，其中<i>B</i>型手机的数量不多于<i>A</i>型手机数量的2倍，请设计一个方案：营业厅购进两种型号手机各多少部时获得最大利润，最大利润是多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西新余四中 · 临考冲刺", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-06-11", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587750739222503424", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "587750739222503424", "title": "2025年江西省新余市第四中学中考模拟数学试卷", "paperCategory": 1}, {"id": "425586665488424960", "title": "重庆市南岸区南坪中学校2022-2023学年八年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585961168516198400", "questionArticle": "<p>4．如图，由 $ 8 $ 个大小相同的小长方形无缝拼接成一个大长方形，已知大长方形的周长为 $ 40\\mathrm{ c }\\mathrm{ m } $ ，则小长方形的周长为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ \\mathrm{ c }\\mathrm{ m } $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/11/2/1/0/0/0/587979397161857025/images/img_1.png\" style='vertical-align:middle;' width=\"152\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北黄石 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961147863445504", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585961147863445504", "title": "湖北省黄石十四中教联体2024—2025学年下学期八年级数学培优测试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585961165479522304", "questionArticle": "<p>5．当实数<i>m</i>，<i>n</i>满足<i>m</i>−2<i>n</i>=1，则称点<i>P</i>(<i>m</i>+2， $ \\dfrac { n+2 } { 3 } $ ）为创新点，若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+3y=4 \\\\ 2x-3y=4a \\end{cases}  $ 的解点<i>Q</i>（<i>x</i>，<i>y</i>）为创新点，则<i>a</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -\\dfrac { 2 } { 5 } $</p><p>B． $ \\dfrac { 2 } { 5 } $</p><p>C． $ -\\dfrac { 2 } { 3 } $</p><p>D． $ \\dfrac { 2 } { 3 } $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北黄石 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961147863445504", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585961147863445504", "title": "湖北省黄石十四中教联体2024—2025学年下学期八年级数学培优测试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "587003785693667328", "questionArticle": "<p>6．解方程组： $ \\begin{cases} 3x+2y=2 \\\\ x+4y=-1 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东广州市铁一中学 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587003761807106048", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "587003761807106048", "title": "广东省广州市铁一中学2024−2025学年下学期中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587003683130351616", "questionArticle": "<p>7．樱桃是安徽特产水果，每年 $ 5\\sim 6 $ 月成熟上市，这种水果圆润香甜，富含维生素<i>C</i>，具有生津止渴功效．某果农将采摘的樱桃分装为大箱和小箱销售，已知2个大箱和3个小箱共装樱桃 $ 17 $ 千克，4个大箱和1个小箱共装樱桃 $ 19 $ 千克，求每个大箱和每个小箱各装多少千克的樱桃．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽合肥 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587003656739790848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "587003656739790848", "title": "安徽省合肥市百校联赢2025安徽名校大联考最后一卷数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587003572119711744", "questionArticle": "<p>8．列方程（组）解应用题：</p><p>端午节食粽是我国的传统习俗，某超市根据顾客口味，上架了蜜枣粽和咸肉粽两种类型的粽子．已知顾客购买 $ 5 $ 个蜜枣粽和 $ 6 $ 个咸肉粽需要 $ 122 $ 元，购买 $ 10 $ 个蜜枣粽和 $ 15 $ 个咸肉粽需要 $ 280 $ 元．</p><p>（1）求超市蜜枣粽和咸肉粽的单价分别是多少元？</p><p>（2）为了吸引顾客，超市对两种粽子降价销售．降价后，蜜枣粽单价是咸肉粽单价的 $ 0.8 $ 倍，小北花了 $ 120 $ 元购买蜜枣粽， $ 180 $ 元购买咸肉粽，并且购买的蜜枣粽比咸肉粽少 $ 3 $ 个，则蜜枣粽的单价降低了多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆南开 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16437|16476", "keyPointNames": "销售利润问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587003538527531008", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "587003538527531008", "title": "2025年重庆市南开中学九年级下学期中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585960595251306496", "questionArticle": "<p>9．在本册的数学活动中，我们探究了“以方程 $ x-y=0 $ 的解为坐标（ $ x $ 的值为横坐标， $ y $ 的值为纵坐标）的点的特性”，了解了二元一次方程的解与其图象上点的坐标的关系．</p><p>规定：以方程 $ x-y=0 $ 的解为坐标的点的全体叫做方程 $ x-y=0 $ 的图象；</p><p>结论：一般地，在平面直角坐标系中，任何一个二元一次方程的图象都是一条直线．</p><p>示例：如图1，我们画方程 $ x-y=0 $ 的图象时，可以取点 $ A(-1,-1) $ 和 $ B(2,2) $ ，作出直线 $ AB $ ．</p><p>【解决问题】</p><p>（1）请在图2中所给的平面直角坐标系中画出二元一次方程组 $ \\begin{cases} 2x+y=4 \\\\ x-y=-1 \\end{cases}  $ 中的两个二元一次方程的图象（提示：依据“两点确定一条直线”，画出图象，无需写过程）．</p><p>（2）观察图象，两条直线的交点坐标为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，由此得出这个二元一次方程组的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>【拓展延伸】</p><p>（3）已知二元一次方程 $ ax+by=6 $ 的图象经过两点 $ A(-1,3) $ 和 $ B(2,0) $ ，试求 $ a $ ， $ b $ 的值．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/12/2/1/0/0/0/588345549373353985/images/img_1.png\" style='vertical-align:middle;' width=\"445\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广西广西壮族自治区南宁市第三中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16426|16540", "keyPointNames": "二元一次方程组的应用|一次函数与二元一次方程（组）", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585960564838408192", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585960564838408192", "title": "广西南宁市第三中学2024−2025学年七年级下学期5月数学月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585960594206924800", "questionArticle": "10．<table style=\"border: solid 1px;border-collapse: collapse; width:600pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138pt;\"><p>背景</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 462pt;\"><p>校体艺文化周期间，小艾所在的班级也开展各种竞赛活动，需要去商店购买<i>A</i>，<i>B</i>两种款式的运动徽章作为奖品．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138pt;\"><p>素材1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 279.75pt;\"><p>某商店在无促销活动时，若买15枚<i>A</i>款徽章、10枚<i>B</i>款徽章，共需230元；若买25枚<i>A</i>款徽章、25枚<i>B</i>款徽章，共需450元．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/12/2/1/0/0/0/588344058596073473/images/img_1.png\" style='vertical-align:middle;' width=\"99\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138pt;\"><p>素材2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 462pt;\"><p>该商店搞促销活动：用35元购买会员卡成为会员后，凭会员卡购买商店内任何商品一律按商品价格的8折出售（已知小艾在此之前不是该商店的会员）；线上淘宝店促销活动：购买商店内任何商品，一律按商品价格的9折出售且包邮．</p></td></tr><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 600pt;\"><p>问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138pt;\"><p>任务1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 462pt;\"><p>某商店在无促销活动时，求<i>A</i>款徽章和<i>B</i>款徽章的销售单价各是多少元？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138pt;\"><p>任务2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 462pt;\"><p>小艾计划在促销期间购买<i>A</i>，<i>B</i>两款徽章共40枚，其中<i>A</i>款徽章 $ t $ 枚 $ \\left ( { 0  <  t  <  40 } \\right )  $ ，</p><p>若在线下商店购买，共需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元；</p><p>若在线上淘宝店购买，共需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．（均用含 $ t $ 的代数式表示）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138pt;\"><p>任务3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 462pt;\"><p>请你帮小艾算一算，在任务2的条件下，两种购买方式只能选一种，请问选择哪种则买方式更合算？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广西广西壮族自治区南宁市第三中学 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585960564838408192", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585960564838408192", "title": "广西南宁市第三中学2024−2025学年七年级下学期5月数学月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 38, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 38, "timestamp": "2025-07-01T02:05:17.802Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}