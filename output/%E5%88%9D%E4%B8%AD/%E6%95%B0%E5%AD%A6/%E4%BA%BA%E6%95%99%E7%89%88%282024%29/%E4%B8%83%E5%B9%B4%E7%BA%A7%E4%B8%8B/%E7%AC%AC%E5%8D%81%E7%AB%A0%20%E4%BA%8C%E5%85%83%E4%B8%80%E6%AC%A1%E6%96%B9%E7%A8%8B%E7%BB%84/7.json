{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 6, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "589573241414393856", "questionArticle": "<p>1．若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+y=4 \\\\ x+2y=-3m+2 \\end{cases}  $ 的解满足 $ x-y &gt; 5 $ ，则<i>m</i>的值可能是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -3 $ B．0C．1D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西大同 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16426|16485", "keyPointNames": "二元一次方程组的应用|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589573228693069824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589573228693069824", "title": "山西省大同六中集团校2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589573239971553280", "questionArticle": "<p>2．对于方程 $ x+3y=2 $ ，用含 $ x $ 的代数式表示 $ y $ ，正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x=3y-2 $ B． $ x=2-3y $ </p><p>C． $ y=\\dfrac { x-2 } { 3 } $ D． $ y=\\dfrac { 2-x } { 3 } $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西大同 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589573228693069824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589573228693069824", "title": "山西省大同六中集团校2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589573238281248768", "questionArticle": "<p>3．下列方程是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 2x{^{2}}+y=0 $ B． $ 3x+y=5 $ </p><p>C． $ xy=5 $ D． $ 2x+\\dfrac { 3 } { y }=0 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025山西大同 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589573228693069824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589573228693069824", "title": "山西省大同六中集团校2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590998413539319808", "questionArticle": "<p>4．一支部队第一天行军4h，第二天行军5h，两天共行军98km，且第一天比第二天少走2km．第一天的平均速度为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建泉州七中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998393041756160", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590998393041756160", "title": "福建省泉州市第七中学2024−2025学年七年级下学期第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590998411932901376", "questionArticle": "<p>5．已知 $ \\begin{cases} 4x-y=1 \\\\ -x+4y=4 \\end{cases}  $ ，则 $ \\left ( { x+y } \\right ) \\left ( { x-y } \\right )  $ 的值等于<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建泉州七中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16305|16424", "keyPointNames": "代数式求值|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998393041756160", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590998393041756160", "title": "福建省泉州市第七中学2024−2025学年七年级下学期第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590998406757130240", "questionArticle": "<p>6．下列说法正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．方程3<i>x</i>−4<i>y</i>＝1只有两个解，这两个解分别是 $ \\begin{cases} x=1, \\\\ y=\\dfrac { 1 } { 2 } \\end{cases}  $ 和 $ \\begin{cases} x=-1 \\\\ y=-1 \\end{cases}  $ </p><p>B．方程3<i>x</i>−4<i>y</i>＝1中，<i>x</i>、<i>y</i>可以取任何数值</p><p>C． $ \\begin{cases} x=3 \\\\ y=2 \\end{cases}  $ 是方程3<i>x</i>−4<i>y</i>＝1的一个解</p><p>D．方程3<i>x</i>−4<i>y</i>＝1可能无解</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建泉州七中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998393041756160", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590998393041756160", "title": "福建省泉州市第七中学2024−2025学年七年级下学期第二次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590998663117189120", "questionArticle": "<p>7．体育已经作为中考重点考查项目，分过程性评价和终结性评价，其中足球、篮球也是主要考查对象．为了增强学生体育素养，某校准备花费15000元购买这两种球，第一种方案恰好可以购买篮球100个，足球100个；第二种方案恰好可以购买篮球120个，足球60个．</p><p>（1）求足球、篮球的单价；</p><p>（2）因学生参与积极性高，参加人数多，现决定再以同样的单价购买足球和篮球共100个，其中足球数量不超过篮球数量的 $ \\dfrac { 1 } { 4 } $ ，如何设计购买方案，才能使花费最少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16438|16486|16535", "keyPointNames": "和差倍分问题|一元一次不等式的应用|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590998634847580160", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590998634847580160", "title": "湖南省永州市冷水滩区京华中学2024−2025学年八年级下学期第三次检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592868563876098048", "questionArticle": "<p>8．某学校为丰富图书馆藏书，提高学生的阅读兴趣，计划购买一批新书．经过调研，学校决定购买科普类和文学类两种书籍．已知购买5本科普类书籍和3本文学类书籍共需270元，购买7本科普类书籍和5本文学类书籍共需410元．</p><p>（1）求科普类和文学类书籍的单价各是多少元？</p><p>（2）若学校计划购买这两种书籍共100本，在预算不超过3600元的前提下，学校至少要购买多少本科普类书籍？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024北京密云 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592868534297866240", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592868534297866240", "title": "北京市密云区2023−2024学年下学期七年级数学期末 试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592868559262363648", "questionArticle": "<p>9．解方程组： $ \\begin{cases} x-y=3 \\\\ 2x+3y=1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024北京密云 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592868534297866240", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592868534297866240", "title": "北京市密云区2023−2024学年下学期七年级数学期末 试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592868767320813568", "questionArticle": "<p>10．如果某个二元一次方程组的解中两个未知数的值互为相反数，那么我们称这个方程组为“奇妙方程组”．</p><p>（1）判断关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x-2y=3 \\\\ 2x-y=3 \\end{cases}  $ ，是“奇妙方程组”，并说明理由；</p><p>（2）如果关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x+3y=4-a \\\\ x-y=3a \\end{cases}  $ ，是“奇妙方程组”，求<i>a</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024安徽阜阳 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16252|16424", "keyPointNames": "相反数的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592868735515406337", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592868735515406337", "title": "安徽省阜阳实验中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 7, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 7, "timestamp": "2025-07-01T02:01:36.196Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}