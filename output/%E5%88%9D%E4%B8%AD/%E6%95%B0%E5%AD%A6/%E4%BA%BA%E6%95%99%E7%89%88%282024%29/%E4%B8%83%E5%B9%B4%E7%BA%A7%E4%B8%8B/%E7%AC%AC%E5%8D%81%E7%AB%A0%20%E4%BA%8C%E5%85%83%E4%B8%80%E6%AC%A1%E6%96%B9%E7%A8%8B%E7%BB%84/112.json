{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 111, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "570807216996917248", "questionArticle": "<p>1．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ 的解是 $ \\begin{cases} x=2.1 \\\\ y=4.5 \\end{cases}  $ ，则关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} a{{}_{ 1 } }\\left ( { x-2 } \\right ) +5b{{}_{ 1 } }y=2c{{}_{ 1 } } \\\\ a{{}_{ 2 } }\\left ( { x-2 } \\right ) +5b{{}_{ 2 } }y=2c{{}_{ 2 } } \\end{cases}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=4.1 \\\\ y=1.8 \\end{cases}  $ B． $ \\begin{cases} x=4.2 \\\\ y=4.5 \\end{cases}  $ C． $ \\begin{cases} x=6.2 \\\\ y=1.8 \\end{cases}  $ D． $ \\begin{cases} x=6.2 \\\\ y=3 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏南京 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 9, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16424|16425", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的特殊解法", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570807201557684224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "570807201557684224", "title": "江苏省南京市鼓楼区2024−2025学年七年级下学期期中考试数学卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570807215407276032", "questionArticle": "<p>2．如图，用8块相同的小长方形地砖拼成一个宽为 $ 80{ \\rm{ c } }{ \\rm{ m } } $ 的大长方形，则每个小长方形的面积为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/25/2/1/0/0/0/570807174672195587/images/img_3.png\" style=\"vertical-align:middle;\" width=\"182\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;</p><p>A． $ 2000{ \\rm{ c } }{ \\rm{ m } }{^{2}} $ B． $ 1200{ \\rm{ c } }{ \\rm{ m } }{^{2}} $ C． $ 4800{ \\rm{ c } }{ \\rm{ m } }{^{2}} $ D． $ 1600{ \\rm{ c } }{ \\rm{ m } }{^{2}} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570807201557684224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "570807201557684224", "title": "江苏省南京市鼓楼区2024−2025学年七年级下学期期中考试数学卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570807213792468992", "questionArticle": "<p>3．下列各组数是方程 $ x+2y=4 $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=-1 \\\\ y=3 \\end{cases}  $ B． $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ C． $ \\begin{cases} x=0 \\\\ y=2 \\end{cases}  $ D． $ \\begin{cases} x=4 \\\\ y=1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570807201557684224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "570807201557684224", "title": "江苏省南京市鼓楼区2024−2025学年七年级下学期期中考试数学卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570807212190244864", "questionArticle": "<p>4．已知 $ \\begin{cases} x=-2 \\\\ y=1 \\end{cases}  $ 是关于 $ x $ 和 $ y $ 的二元一次方程 $ ax+by=0 $ 的解，则 $ \\dfrac { a } { b } $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2B． $ \\dfrac { 1 } { 2 } $ C． $ -2 $ D． $ -\\dfrac { 1 } { 2 } $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-27", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570807201557684224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "570807201557684224", "title": "江苏省南京市鼓楼区2024−2025学年七年级下学期期中考试数学卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570267125601837056", "questionArticle": "<p>5．在方程 $ \\dfrac { 1 } { 2 }x=x+1 $ ， $ 2x+3y=5 $ ， $ 2y-1=x $ ， $ x-y+z=0 $ 中二元一次方程的个数为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1个B．2个C．3个D．4个</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|320000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025江苏镇江 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-04-27", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267118328913920", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267118328913920", "title": "江苏省镇江市2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}, {"id": "203904614445391872", "title": "重庆市万州区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570805687392968704", "questionArticle": "<p>6．解方程（组）</p><p>(1) $ 49x{^{2}}=25 $ ；</p><p>(2) $ \\begin{cases} 4x-3y=4 \\\\ 2x+y=12 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东广州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-26", "keyPointIds": "16287|16424", "keyPointNames": "平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570805668409548800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "570805668409548800", "title": "广东省广州市第一一三中学2024~2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570805679671255040", "questionArticle": "<p>7．若 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ 是关于<i>x</i>，<i>y</i>的方程 $ x-ay=3 $ 的一个解，则<i>a</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．5C． $ -1 $ D． $ -5 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东广州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-26", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570805668409548800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "570805668409548800", "title": "广东省广州市第一一三中学2024~2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "569704681800245248", "questionArticle": "<p>8．若关于 $ x,y $ 的二元一次方程组 $ \\begin{cases} 3x+y=1+a \\\\ x+3y=-3 \\end{cases}  $ 的解满足 $ x-y=-4 $ ，则 $ a $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏南京市第二十九中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-04-25", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569704667619303424", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "569704667619303424", "title": "江苏省南京市第二十九中学2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570806853564669952", "questionArticle": "<p>9．《算法统宗》中有如下问题：“哑巴来买肉，难言钱数目，一斤少二十五，八两多十五，试问能算者，合与多少肉”，意思是一个哑巴来买肉，说不出钱的数目，买一斤（ $ 16 $ 两）还差二十五文钱，买八两多十五文钱，问肉数和肉价各是多少？设肉价为 $ x $ 文/两，哑巴所带的钱数为 $ y $ 文，则可建立方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 16x=y-25 \\\\ 8x=y+15 \\end{cases}  $</p><p>B． $ \\begin{cases} 16x=y+25 \\\\ 8x=y-15 \\end{cases}  $</p><p>C． $ \\begin{cases} 8x=y-25 \\\\ 16x=y+15 \\end{cases}  $</p><p>D． $ \\begin{cases} 8x=y+25 \\\\ 16x=y-15 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|-1|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙市雅礼教育集团 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 5, "createTime": "2025-04-25", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570806839450836992", "questionFeatureName": "数学文化题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570806839450836992", "title": "湖南省长沙市雅礼教育集团2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "400430318396678144", "title": "重庆市大渡口区巴渝学校2023-2024学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "399682707456827392", "title": "重庆市大渡口区巴渝学校2023-2024学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "171921238570344448", "title": "2022年七年级下册苏科版数学第十章10.5用二元一次方程解决问题课时练习", "paperCategory": 1}, {"id": "206707688847220736", "title": "2022年七年级上册沪科版数学第3章3.4二元一次方程组的应用课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570806852742586368", "questionArticle": "<p>10．已知方程组 $ \\begin{cases} x+2y=k, \\\\ 2x+y=1 \\end{cases}  $ 的解满足 $ x-y=3 $ ，则 $ k $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 2 $</p><p>B． $ -2 $</p><p>C． $ 1 $</p><p>D． $ -1 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙市雅礼教育集团 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-04-25", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570806839450836992", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "570806839450836992", "title": "湖南省长沙市雅礼教育集团2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "196595204857569280", "title": "广东省珠海市香洲区文园中学2021-2022学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 112, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 112, "timestamp": "2025-07-01T02:14:02.261Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}