{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 36, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "587393849883930624", "questionArticle": "<p>1．2025年4月23日是第30个世界读书日．主题为“通往未来的桥梁”，为了感受阅读的幸福，体味生命的真谛，分享读书的乐趣，某学校特为每个班级订购了一批新的图书．七年级订购《骆驼祥子》10套和《平凡的世界》8套，总费用为900元；八年级订购《骆驼祥子》14套和《平凡的世界》6套，总费用为870元．</p><p>（1）求《骆驼祥子》和《平凡的世界》每套各是多少元？</p><p>（2）学校准备再购买《骆驼祥子》和《平凡的世界》共32套，购买《骆驼祥子》的数量不超过《平凡的世界》的2倍，请你设计出最省钱的购买方案，并求出该方案所需的费用．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南永州 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-12", "keyPointIds": "16438|16486|16535", "keyPointNames": "和差倍分问题|一元一次不等式的应用|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587393819626221568", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "587393819626221568", "title": "2025年湖南省永州市中考适应性考试二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587394855522840576", "questionArticle": "<p>2．（1）解方程组： $ \\begin{cases} 3x+\\dfrac { 1 } { 2 }y=8 \\\\ 2x-\\dfrac { 1 } { 2 }y=2 \\end{cases}  $ </p><p>（2）某工程队计划修建一条长为360米的地下管道．甲工程队单独施工需要12天完成，乙工程队单独施工需要18天完成．现计划由甲、乙两队合作施工，但实际施工时发现，甲队每天比原计划少修10米，乙队每天比原计划多修5米．问：两队合作实际需要多少天完成任务？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025新疆喀什地区 · 临考冲刺", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-12", "keyPointIds": "16405|16424", "keyPointNames": "工程问题|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587394834597457920", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "587394834597457920", "title": "2025年新疆维吾尔自治区喀什地区九年级中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587395095336361984", "questionArticle": "<p>3．《中国居民膳食指南（2022）》推荐每人烹调油摄入量为 $ 25-30 $ 克/天，烹调盐摄入量低于5克/天．2000年该地区居民的烹调油和盐人均摄入总量为65克/天，2025年的人均摄入总量为50.5克/天．2025年与2000年相比，平均每人每天烹调油的摄入量降低了 $ 20{ \\rm{ \\% } } $ ，烹调盐的摄入量降低了 $ 30{ \\rm{ \\% } } $ ．请判断2025年该地区居民的平均每人每天烹调油摄入量是否符合标准，并说明理由．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京顺义 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-12", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587395058585870336", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "587395058585870336", "title": "2025年北京市顺义中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585613623235489792", "questionArticle": "<p>4．得益于“互联网 $ + $ ”和人工智能的发展，无人配送服务行业已经进入人们的生活．某大学校园内使用了无人配送车和无人机配送快递．已知一架无人机一次可运送3千克货物，一辆无人配送车一趟可运送120千克货物．快递公司提供了无人机和无人配送车共30台运送2430千克货物，求运送物资使用的无人机和无人配送车各有几台．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏淮安 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585613597734121472", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585613597734121472", "title": "2025年江苏省淮安市中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585613929583259648", "questionArticle": "<p>5．“砀山梨”是安徽名特产，果农为了便于销售，将采摘的砀山梨分装为大箱和小箱两种规格，已知2个大箱和3个小箱能装 $ 16 $ 公斤砀山梨，4个大箱和1个小箱能装 $ 22 $ 公斤砀山梨．求每个大箱和小箱各装多少公斤砀山梨．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽合肥 · 临考冲刺", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-11", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585613892128124928", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585613892128124928", "title": "2025年安徽省合肥市名校九年级联合教研大联考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587394463288303616", "questionArticle": "<p>6．（1）计算： $ -2\\cdot {\\cos}45{}\\degree +{\\left( { π-3.14 } \\right) ^ {0}}+\\left  | { 1-\\sqrt { 2 } } \\right  | +{\\left( { \\dfrac { 1 } { 4 } } \\right) ^ {-1}} $ ；</p><p>（2）解方程组： $ \\begin{cases} 2x-y=5 \\\\ 4x+3y=-10 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东枣庄 · 临考冲刺", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-11", "keyPointIds": "16299|16372|16424|16834", "keyPointNames": "实数的运算|负整数指数幂|加减消元法解二元一次方程组|特殊角的三角函数值", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587394433898815488", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "587394433898815488", "title": "2025年山东省枣庄市台儿庄区九年级中考第三次调研考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585920743348674560", "questionArticle": "<p>7．为落实“双减”政策，刘老师把班级里50名学生分成若干小组进行小组互助学习，每小组只能是4人或6人，则分组方案有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．4种　　　　B．3种　　　　C．2种　　　　D．1种</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽阜阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-06-11", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575796445982720", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "589575796445982720", "title": "安徽省阜阳市2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 1}, {"id": "585920729167732736", "title": "2025年山东省枣庄市第十五中学中考三模数学测试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "585920853717590016", "questionArticle": "<p>8．如图是一张长为 $ 40{ \\rm{ c } }{ \\rm{ m } } $ ，宽为 $ 20{ \\rm{ c } }{ \\rm{ m } } $ 的长方形硬纸板，在四个直角处分别剪去边长为 $ x{ \\rm{ c } }{ \\rm{ m } } $ 的正方形和中间的一个正方形 $ ABCD $ ，剩余部分（阴影部分）可制作两个大小完全相等的底面是正方形的无盖长方体纸盒（接头处忽略不计），则 $ x $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/05/2/1/0/0/0/585920815562010645/images/img_22.png\" style=\"vertical-align:middle;\" width=\"227\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东青岛 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-11", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585920832754458624", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "585920832754458624", "title": "2025年山东省青岛市市北区九年级中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585921295042256896", "questionArticle": "<p>9．某小区为了绿化环境，计划分两次购进 $ A,B $ 两种花草，第一次分别购进 $ A,B $ 两种花草30棵和15棵，共花费675元；第二次分别购进 $ A,B $ 两种花草12棵和5棵，两次共花费940元（两次购进的 $ A,B $ 两种花草价格均分别相同）．求 $ A,B $ 两种花草每棵的价格分别是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁沈阳 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-11", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585921272132968448", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "585921272132968448", "title": "2025年辽宁省沈阳市皇姑区九年级数学中考二模试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587396723107999744", "questionArticle": "<p>10．长江比黄河长836千米，黄河长度的6倍比长江长度的5倍多1284千米，如果设长江长为<i>x</i>千米，黄河长为<i>y</i>千米，那么所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=836 \\\\ 5x-6y=1284 \\end{cases}  $ B． $ \\begin{cases} x-y=836 \\\\ 6x-5y=1284 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=836 \\\\ 6x-5y=1284 \\end{cases}  $ D． $ \\begin{cases} x-y=836 \\\\ 6y-5x=1284 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北孝感 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-11", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587396709111607296", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "587396709111607296", "title": "2025年湖北省孝感市汉川市九年级5月学业水平调研考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 37, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 37, "timestamp": "2025-07-01T02:05:10.176Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}