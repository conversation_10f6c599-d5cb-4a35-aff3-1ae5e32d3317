{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 56, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "581950090849333248", "questionArticle": "<p>1．《算法统宗》是我国明代数学家程大位（1533−1606）所著，文中记录了“二果问价”问题：四百五十文钱，甜果苦果买四百八．苦果七个四文钱，甜果九个十一文，苦甜果各几何？设苦果有 $ x $ 个，甜果有 $ y $ 个，则可列二元一次方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=480 \\\\ 7x+9y=450 \\end{cases}  $ B． $ \\begin{cases} x+y=480 \\\\ \\dfrac { 4 } { 7 }x+\\dfrac { 11 } { 9 }y=450 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=480 \\\\ \\dfrac { 7 } { 4 }x+\\dfrac { 9 } { 11 }y=450 \\end{cases}  $ D． $ \\begin{cases} x+y=480 \\\\ 4x+11y=450 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-30", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950078060900352", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950078060900352", "title": "广东省深圳市南实南山实验教育集团2024−2025学年九年级下学期第二次学业质量监测数学试卷（二模）", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "583055717646508032", "questionArticle": "<p>2．端午节前夕，某商铺用620元购进50个肉粽和30个蜜枣粽，肉粽的进货单价比蜜枣粽的进货单价多6元．</p><p>（1）肉粽和蜜枣粽的进货单价分别是多少元？</p><p>（2）由于粽子畅销，商铺决定再购进这两种粽子共300个，其中肉粽数量不多于蜜枣粽数量的2倍，且每种粽子的进货单价保持不变，若肉粽的销售单价为14元，蜜枣粽的销售单价为6元，试问第二批购进肉粽多少个时，全部售完后，第二批粽子获得利润最大？第二批粽子的最大利润是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2020广东深圳 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-05-30", "keyPointIds": "16438|16565", "keyPointNames": "和差倍分问题|销售问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "128775252214063104", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "128775252214063104", "title": "广东省深圳市2020年中考数学试题", "paperCategory": 1}, {"id": "583055687619485696", "title": "2025年湖南省长沙市立信中学中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583057002936446976", "questionArticle": "<p>3．已知 $ \\begin{cases} x=2 \\\\ y&nbsp;=1 \\end{cases}  $ 是二元一次方程 $ ax+2y=6 $ 的一个解，则<i>a</i>＝<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东江门 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-05-29", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583056986691907584", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583056986691907584", "title": "广东省江门市广雅中学2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "555175874812747776", "title": "黑龙江省哈尔滨市松雷中学2024—2025学年七年级下学期数学开学考试试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580243549469323264", "questionArticle": "<p>4．列方程（组）解实际问题</p><p>为防治污染，保护和改善生态环境，自2023年7月1日起，我国全面实施汽车国六排放标准6b阶段（以下简称“标准”）．对某型号汽车，“标准”要求A类物质排放量不超过 $ 35{ \\rm{ m } }{ \\rm{ g } }/{ \\rm{ k } }{ \\rm{ m } } {\\rm ，\\mathit{A}} $ 、 $ B $ 两类物质排放量之和不超过 $ 50{ \\rm{ m } }{ \\rm{ g } }/{ \\rm{ k } }{ \\rm{ m } } $ ．</p><p>已知该型号某汽车的<i>A</i>、 $ B $ 两类物质排放量之和原为 $ 84{ \\rm{ m } }{ \\rm{ g } }/{ \\rm{ k } }{ \\rm{ m } } $ ．经过一次技术改进后，该汽车的 $ \\mathrm{ A } $ 类物质排放量降低了 $ \\dfrac { 1 } { 3 } $ ， $ B $ 类物质排放量降低了60%，<i>A</i>、 $ B $ 两类物质排放量之和为 $ 48{ \\rm{ m } }{ \\rm{ g } }/{ \\rm{ k } }{ \\rm{ m } } $ ．判断这一次技术改进后该汽车的 $ \\mathrm{ A } $ 类物质排放量是否符合“标准”，并说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京二中 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-05-29", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593321389827858432", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "593321389827858432", "title": "北京二中教育集团 2024—2025学年下学期 九年级数学保温训练试卷", "paperCategory": 1}, {"id": "580243518544719872", "title": "北京师范大学附属实验中学2024−2025学年下学期七年级期中", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580243538182451200", "questionArticle": "<p>5．方程组 $ \\begin{cases} 2x+3y=-2 \\\\ x-5y=12 \\end{cases}  $ 的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京师范大学附属实验中学分校 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-29", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580243518544719872", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580243518544719872", "title": "北京师范大学附属实验中学2024−2025学年下学期七年级期中", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580243531643531264", "questionArticle": "<p>6．用代入法解方程组 $ \\begin{cases} x-y=-3 & ① \\\\ 5x+2y=6 & ② \\end{cases}  $ 时，由①用 $ x $ 表示 $ y $ ，再代入到②中，所得到的一元一次方程是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 5x+2\\left ( { x+3 } \\right ) =6 $ B． $ 5x+2\\left ( { -x+3 } \\right ) =6 $ </p><p>C． $ 5x+2\\left ( { x-3 } \\right ) =6 $ D． $ 5x+2\\left ( { -x-3 } \\right ) =6 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京师范大学附属实验中学分校 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-29", "keyPointIds": "16402|16423", "keyPointNames": "解一元一次方程|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580243518544719872", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580243518544719872", "title": "北京师范大学附属实验中学2024−2025学年下学期七年级期中", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580243530779504640", "questionArticle": "<p>7．已知 $ \\begin{cases} x=-2 \\\\ y=1 \\end{cases}  $ 是方程 $ x+my=4 $ 的解，则 $ m $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -2 $ B．2C． $ -6 $ D．6</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京师范大学附属实验中学分校 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-29", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580243518544719872", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580243518544719872", "title": "北京师范大学附属实验中学2024−2025学年下学期七年级期中", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580243953330466816", "questionArticle": "<p>8．菜农王大叔在蔬菜批发市场上了解到以下信息内容：</p><table style=\"border: solid 1px;border-collapse: collapse; width:325.5pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 102pt;\"><p style=\"text-align:center;\">蔬菜品种</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 52.5pt;\"><p style=\"text-align:center;\">辣椒</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\">黄瓜</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\">西红柿</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\">茄子</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 102pt;\"><p style=\"text-align:center;\">批发价（元/公斤）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 52.5pt;\"><p style=\"text-align:center;\"> $ 4.0 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\"> $ 3.2 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\"> $ 1.6 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\"> $ 2.1 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 102pt;\"><p style=\"text-align:center;\">零售价（元/公斤）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 52.5pt;\"><p style=\"text-align:center;\"> $ 8.0 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\"> $ 8.4 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\"> $ 7.0 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 57pt;\"><p style=\"text-align:center;\"> $ 6.3 $ </p></td></tr></table><p>他共用116元钱从市场上批发了辣椒和西红柿共44公斤到菜市场去卖，当天卖完，请你计算出王大叔一天能赚多少钱？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京西城 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-29", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580243921529253888", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580243921529253888", "title": "北京市西城区第三十九中学2024−2025学年下学期七年级期中考试数学试题卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581952533662314496", "questionArticle": "<p>9．“一盔一带，安全行动”是全国公安部门启动的一项重要安全措施，旨在打造文明城市，提升市民文明素质，此行动要求电动自行车驾驶者及其乘客必须佩戴安全头盔，某商场计划采购一批头盔以响应此倡议．已知购进3个<i>A</i>型头盔和2个<i>B</i>型头盔需要225元，购进2个<i>A</i>型头盔和3个<i>B</i>型头盔需要245元．</p><p>（1）购进1个<i>A</i>型头盔和1个<i>B</i>型头盔分别需要多少元？</p><p>（2）如果该商场准备购进60个这两种型号的头盔，总费用不超过2600元，则至少购进<i>A</i>型头盔多少个？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西抚州 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-29", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581952509247270912", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581952509247270912", "title": "2025年江西省抚州市中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580243938159669248", "questionArticle": "<p>10．在方程 $ 2x-y=3 $ 中，用含有<i>x</i>的代数式表示<i>y</i>为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025北京西城 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-05-29", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580243921529253888", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580243921529253888", "title": "北京市西城区第三十九中学2024−2025学年下学期七年级期中考试数学试题卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 57, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 57, "timestamp": "2025-07-01T02:07:32.770Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}