{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 145, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "564581030230794240", "questionArticle": "<p>1．用加减消元法解方程组： $ \\begin{cases} x+y=4① \\\\ x-2y=1② \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581012056875008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564581012056875008", "title": "吉林省吉林市第九中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564581029681340416", "questionArticle": "<p>2．用代入消元法解方程组： $ \\begin{cases} 2x+y=5① \\\\ y=x-3② \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581012056875008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564581012056875008", "title": "吉林省吉林市第九中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564581028603404288", "questionArticle": "<p>3．已知方程组 $ \\begin{cases} 2x+y=● \\\\ 2x-y=10 \\end{cases}  $ 的解为 $ \\begin{cases} x=4 \\\\ y=▲ \\end{cases}  $ 由于不小心，滴上了两滴墨水，刚好遮住了两个数●和▲，则 $ ▲+●= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581012056875008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564581012056875008", "title": "吉林省吉林市第九中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564581026325897216", "questionArticle": "<p>4．已知 $ \\begin{cases} x=3 \\\\ y=-1 \\end{cases}  $ 是二元一次方程 $ mx-2y=3 $ 的一个解，则<i>m</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581012056875008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564581012056875008", "title": "吉林省吉林市第九中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564581246858207232", "questionArticle": "<p>5．对于 $ x $ ， $ y $ 定义一种新运算，规定 $ T\\left ( { x,y } \\right ) =\\left ( { ax-by } \\right ) \\left ( { x+3y } \\right )  $ (其中 $ a $ ， $ b $ 均为非零常数)，例如： $ T\\left ( { 1,1 } \\right ) =\\left ( { a\\times 1-b\\times 1 } \\right ) \\times \\left ( { 1+3\\times 1 } \\right ) =4a-4b $ ．</p><p>(1) $ T\\left ( { 1,2 } \\right ) = $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>(用含有 $ a $ ， $ b $ 的代数式表示)．</p><p>(2)已知 $ T\\left ( { 0,1 } \\right ) =-3 $ ，且 $ T\\left ( { -2,1 } \\right ) =-3 $ ．</p><p>①求 $ a $ ， $ b $ 的值；</p><p>②直接写出 $ T\\left ( { 2,3 } \\right )  $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024吉林长春外国语学校 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581225806995456", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564581225806995456", "title": "吉林省长春市朝阳区长春外国语学校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564581239992131584", "questionArticle": "<p>6．若关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} 2x+4y=a+7 \\\\ 7x+5y=2a-5 \\end{cases}  $ 的解<i>x</i>，<i>y</i>满足 $ x+y  <  1 $ ，则满足题意的最大整数<i>a</i>是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林长春外国语学校 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581225806995456", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564581225806995456", "title": "吉林省长春市朝阳区长春外国语学校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564581243297243136", "questionArticle": "<p>7．解方程组： $ \\begin{cases} x-3y=-20 \\\\ 2x+7y=90 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林长春外国语学校 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581225806995456", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564581225806995456", "title": "吉林省长春市朝阳区长春外国语学校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564581141052694528", "questionArticle": "<p>8．解关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} ax+by=9 \\\\ 3x-cy=-2 \\end{cases}  $ 时，甲正确地解出 $ \\begin{cases} x=2 \\\\ y=4 \\end{cases}  $ ，乙因为把 $ c $ 抄错了，误解为 $ \\begin{cases} x=4 \\\\ y=-1 \\end{cases}  $ ，求 $ 2a+b-c $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581119414280192", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564581119414280192", "title": "吉林省吉林市吉化第九中学校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564581139572105216", "questionArticle": "<p>9．机械厂加工车间有85名工人，平均每人每天加工大齿轮16个或小齿轮10个，2个大齿轮和3个小齿轮配成一套，问需分别安排多少名工人加工大、小齿轮，才能使每天加工的大小齿轮刚好配套？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581119414280192", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564581119414280192", "title": "吉林省吉林市吉化第九中学校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564581138292842496", "questionArticle": "<p>10．解方程组： $ \\begin{cases} \\dfrac { x } { 3 }-\\dfrac { y+1 } { 2 }=1 \\\\ 4x-\\left ( { 2y-5 } \\right ) =11 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581119414280192", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564581119414280192", "title": "吉林省吉林市吉化第九中学校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 146, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 146, "timestamp": "2025-07-01T02:18:09.246Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}