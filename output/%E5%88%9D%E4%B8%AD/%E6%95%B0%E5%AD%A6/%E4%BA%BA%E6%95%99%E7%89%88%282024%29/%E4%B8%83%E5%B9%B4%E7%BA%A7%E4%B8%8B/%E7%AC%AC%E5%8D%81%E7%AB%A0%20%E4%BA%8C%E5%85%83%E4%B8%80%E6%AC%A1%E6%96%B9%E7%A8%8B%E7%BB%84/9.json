{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 8, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "592869554704920576", "questionArticle": "<p>1．若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+y=4a-9 \\\\ x-4y=2a+3 \\end{cases}  $ 的解满足 $ x-y=2 $ ，则<i>a</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．2C． $ -1 $ D． $ -2 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592869537717989376", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592869537717989376", "title": "重庆实验外国语学校2024−2025学年七年级下学期数学期末复习试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592870573371338752", "questionArticle": "<p>2．注意：为了使同学们更好地解答本题，我们提供了一种解题思路，你可以依照这个思路按下面的要求填空，完成本题的解答．也可以选用其他的解题方案，此时不必填空，只需按照解答题的一般要求进行解答．我国古代数学名著《孙子算经》中记载：“今有木，不知长短，引绳度之，余绳四尺五寸；屈绳量之，不足一尺，木长几何？”意思是：用一根绳子去量一根木条，绳子还剩余4.5尺；将绳子对折再量木条，木条剩余1尺，问木条长多少尺？解题方案：设木条长<i>x</i>尺，绳子长<i>y</i>尺．</p><p>（1）根据题意，列出方程组 $ \\left \\{\\hspace{-0.5em}  \\begin{array} {l}   {\\underline{\\hspace{-0.5em}   \\begin{array} {} \\; & \\; & \\; & \\; \\end{array} \\hspace{-0.5em}  }}   \\\\   {\\underline{\\hspace{-0.5em}   \\begin{array} {} \\; & \\; & \\; & \\; \\end{array} \\hspace{-0.5em}  }}   \\end{array} \\hspace{-0.5em} \\right.  $ </p><p>（2）解这个方程组，得 $ \\begin{cases} x=\\_\\_\\_\\_\\_\\_ \\\\ y=\\_\\_\\_\\_\\_\\_ \\end{cases}  $ </p><p>答：木条长<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>尺．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024天津西青 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870530685906944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592870530685906944", "title": "天津市西青区2023～2024 学年下学期期末测试七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592870838891753472", "questionArticle": "<p>3．已知关于<i>a</i>、<i>b</i>的方程组 $ \\begin{cases} a-b=1+3m \\\\ a+b=-7-m \\end{cases}  $ 中，<i>a</i>为负数，<i>b</i>为非正数．</p><p>（1）求<i>m</i>的取值范围；</p><p>（2）化简： $ |m-3|+|m+2| $ ；</p><p>（3）在<i>m</i>的取值范围内，当<i>m</i>为何整数值时，不等式 $ 2mx-3 &gt; 2m-3x $ 的解集为 $ x  &lt;  1 $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024天津 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870802313228288", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592870802313228288", "title": "天津市建华中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592870564131287040", "questionArticle": "<p>4．小林在某商场先后三次购买商品<i>A</i>和<i>B</i>，只有其中一次购买时遇到商场有优惠活动，商品<i>A</i>和<i>B</i>同时按标价打折出售，其余两次均按标价购买，三次购买<i>A</i>，<i>B</i>商品的数量和总费用如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\"></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p style=\"text-align:center;\"><i>A</i>商品（个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p style=\"text-align:center;\"><i>B</i>商品（个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">总费用（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\">第1次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p style=\"text-align:center;\">6</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p style=\"text-align:center;\">5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">1140</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\">第2次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p style=\"text-align:center;\">7</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">1110</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 43.5pt;\"><p style=\"text-align:center;\">第3次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p style=\"text-align:center;\">9</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.55pt;\"><p style=\"text-align:center;\">8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p style=\"text-align:center;\">1047</p></td></tr></table><p>（1）在这三次购物中，第<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>次购物享受了优惠；</p><p>（2）若在本次优惠活动中商品<i>B</i>打五折优惠，则商品<i>A</i>享受打<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>折出售的优惠政策．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024天津西青 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16406|16438", "keyPointNames": "销售盈亏问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870530685906944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592870530685906944", "title": "天津市西青区2023～2024 学年下学期期末测试七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592870837969006592", "questionArticle": "5．<table style=\"border: solid 1px;border-collapse: collapse; width:726.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 726.75pt;\"><p>设计最优订餐方案</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 217.5pt;\"><p>素材一：</p><p>天天中餐厅推出了没有代言费的酸菜鱼，25元一份．若购买2个鸡腿，2个狮子头，3份素菜，2份饭需要46元；若购买1份酸菜鱼，2个鸡腿，4个狮子头，2份素菜，3份饭需要75元．</p><p>注：米饭2元一份，素菜8元一份．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/25/2/1/0/0/0/592870623006732306/images/img_19.png\" style=\"vertical-align:middle;\" width=\"139\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 217.5pt;\"><p>素材二：</p><p>天天中餐厅推出多款优惠套餐：</p><p>小鸡腿套餐：2个鸡腿，1个狮子头，1份素菜，1份饭，共20元；</p><p>狮子头套餐：2个狮子头，1个鸡腿，1份素菜，1份饭，共21元；</p><p>酸菜鱼套餐：1份酸菜鱼，1份素菜，1份饭，共32元．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 217.5pt;\"><p>素材三：</p><p>美团外卖：菜品单点价格比到店购买价格高3元．</p><p>美团套餐价：小鸡腿套餐28元，狮子头套餐29元，酸菜鱼套餐39元．</p><p>现活动推出每月可购买外卖通用券4张，每张2元，每单只能用一张券，券至少可以抵扣5元（无门槛），最多可以免费膨胀到20元，券不用可以退．外卖每个订单需要打包费3元，满20起送，活动期间减免配送费．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/25/2/1/0/0/0/592870623006732307/images/img_20.png\" style=\"vertical-align:middle;\" width=\"139\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 726.75pt;\"><p>问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 217.5pt;\"><p>任务一：</p><p>店内鸡腿和狮子头的销售单价各是多少元？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 217.5pt;\"><p>任务二：</p><p>小明到店购买晚餐，单点1份酸菜鱼，4份素菜，4份饭，若干个鸡腿和狮子头（每样都要有）预计花费105元，求其中鸡腿和狮子头各几个？在相同菜品量的基础上，如何购买最优惠，求最少费用是多少？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 217.5pt;\"><p>任务三：</p><p>小明到店来回还需打车费20元，若选择美团外卖，按任务二相同菜品量，购买外卖券后，点了免费膨胀，前2个券分别膨胀到7元，8元，最后两个券膨胀结果未知．请结合膨胀情况，比较美团外卖和到店购买哪种方案更省钱？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024天津 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16420|16437|16486", "keyPointNames": "二元一次方程的解|销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870802313228288", "questionMethodName": "分类讨论思想", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592870802313228288", "title": "天津市建华中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592870565649625088", "questionArticle": "<p>6．解下列方程组：</p><p>（1） $ \\begin{cases} y=2x-3 \\\\ 3x+2y=8 \\end{cases}  $ </p><p>（2） $ \\begin{cases} 2\\left ( { x-1 } \\right ) -3\\left ( { y+1 } \\right ) =12 \\\\ \\dfrac { x } { 2 }+\\dfrac { y } { 3 }=1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024天津西青 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870530685906944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592870530685906944", "title": "天津市西青区2023～2024 学年下学期期末测试七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592869698556960768", "questionArticle": "<p>7．已知关于 $ x，y $ 的二元一次方程组 $ \\begin{cases} ax-by=13 \\\\ cx-y=4 \\end{cases}  $ 的解为 $ \\begin{cases} x=-5 \\\\ y=-14 \\end{cases}  $ ，小强因看错了系数 $ c $ ，得到的解为 $ \\begin{cases} x=5 \\\\ y=1 \\end{cases}  $ ，则 $ 5a-b-c= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江蛟川书院 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592869675421179904", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592869675421179904", "title": "浙江省宁波市镇海区蛟川书院2023−2024学年下学期七年级期末数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592870552240435200", "questionArticle": "<p>8．在长为 $ 21{ \\rm{ m } } $ ，宽为 $ 15{ \\rm{ m } } $ 的长方形空地上，沿平行于长方形各边的方向分别割出3个大小完全一样的小长方形花圃，其示意图如图所示，则其中每个小长方形花圃的面积为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/25/2/1/0/0/0/592870511689900032/images/img_6.png\" style=\"vertical-align:middle;\" width=\"120\" alt=\"试题资源网 https://stzy.com\"></p><p>A．30B．27C．21D．15</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024天津西青 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870530685906944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592870530685906944", "title": "天津市西青区2023～2024 学年下学期期末测试七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592869685097439232", "questionArticle": "<p>9．下列方程中，属于二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 2+xy=15 $ B． $ 2x-y{^{2}}=5 $ C． $ 2x-\\dfrac { 1 } { y }=1 $ D． $ x+3y=1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024浙江蛟川书院 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592869675421179904", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592869675421179904", "title": "浙江省宁波市镇海区蛟川书院2023−2024学年下学期七年级期末数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592870816653553664", "questionArticle": "<p>10．若关于 $ x $ 和 $ y $ 的方程组 $ \\begin{cases} 5x+4y=a \\\\ ax+by=c \\end{cases}  $ 无解，则（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 5a=4c $ B． $ 4a=5b $ C． $ 4a=5c $ D． $ 5a=4b $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024天津 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592870802313228288", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592870802313228288", "title": "天津市建华中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 9, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 9, "timestamp": "2025-07-01T02:01:51.057Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}