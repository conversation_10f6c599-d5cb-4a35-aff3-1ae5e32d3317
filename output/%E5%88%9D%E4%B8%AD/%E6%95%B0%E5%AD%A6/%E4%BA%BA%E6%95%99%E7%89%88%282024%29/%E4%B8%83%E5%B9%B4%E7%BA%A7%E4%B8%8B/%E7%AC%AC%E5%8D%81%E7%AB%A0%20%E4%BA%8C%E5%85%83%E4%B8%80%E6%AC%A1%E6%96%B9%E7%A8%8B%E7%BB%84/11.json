{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 10, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "588453236211752960", "questionArticle": "<p>1．已知关于<i>x</i><i>、</i><i>y</i>的二元一次方程组 $ \\begin{cases} x+2y=5p \\\\ 2x+y=4p+3 \\end{cases}  $ （<i>p</i>为实数）．</p><p>（1） $ x+y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>（用含<i>p</i>的式子表示）；</p><p>（2）若方程组的解也是方程 $ qx+3y=1 $ （<i>q</i>为整数，且<i>q</i>不等于0或 $ -6 $ ）的解，<i>p</i>也是整数，则<i>q</i>的最小值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025浙江杭州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588453213898055680", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588453213898055680", "title": "浙江省杭州市锦绣育才教育集团2024−2025学年七年级下学期5月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588453241576267776", "questionArticle": "<p>2．在纸盒制作的劳动实践课上，对规格是 $ 150{ \\rm{ c } }{ \\rm{ m } }\\times 90{ \\rm{ c } }{ \\rm{ m } } $ 的原材料板材进行裁剪得到 $ \\mathrm{ A } $ 型长方形纸板和 $ B $ 型正方形纸板 $ ． $ 为了避免材料浪费，每张原材料板材先裁得 $ 3 $ 张 $ 150{ \\rm{ c } }{ \\rm{ m } }\\times 30{ \\rm{ c } }{ \\rm{ m } } $ 的纸板条，每张纸板条又恰好可以裁得 $ 3 $ 张 $ \\mathrm{ A } $ 型长方形纸板或 $ 5 $ 张 $ B $ 型正方形纸板，如图 $ 1 $ 所示．（单位： $ { \\rm{ c } }{ \\rm{ m } } $ ） </p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/12/2/1/0/0/0/588453172571582471/images/img_7.png\" style=\"vertical-align:middle;\" width=\"479\" alt=\"试题资源网 https://stzy.com\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/12/2/1/0/0/0/588453172571582472/images/img_8.png\" style=\"vertical-align:middle;\" width=\"95\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）每张原材料板材可以裁得 $ \\mathrm{ A } $ 型纸板<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>张或裁得 $ B $ 型纸板<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>张；</p><p>（2）现有 $ 260 $ 张原材料板材全部裁剪 $ ( $ 每张原材料板材只能一种裁法 $ ) $ 得到 $ \\mathrm{ A } $ 型与 $ B $ 型纸板当侧面和底面，做成如图 $ 2 $ 所示的竖式无盖长方体纸盒和横式无盖长方体纸盒，若横式无盖长方体纸盒个数为竖式无盖长方体纸盒个数的两倍，问：怎样裁剪才能使剪出的 $ \\mathrm{ A } $ ， $ B $ 型纸板恰好用完，两种纸盒各做多少个？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025浙江杭州 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16432|16439", "keyPointNames": "配套问题|几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588453213898055680", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588453213898055680", "title": "浙江省杭州市锦绣育才教育集团2024−2025学年七年级下学期5月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588453237788811264", "questionArticle": "<p>3．解方程(组)：</p><p>（1） $ \\dfrac { x } { x-2 }=\\dfrac { x+1 } { x-2 }-1 $ </p><p>（2） $ \\begin{cases} 3x-y=15 \\\\ 5x+2y=14 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16424|16471", "keyPointNames": "加减消元法解二元一次方程组|解分式方程", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588453213898055680", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588453213898055680", "title": "浙江省杭州市锦绣育才教育集团2024−2025学年七年级下学期5月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588453229362454528", "questionArticle": "<p>4．若 $ \\left ( { x{^{2}}+px+q } \\right ) \\left ( { x{^{2}}-3x+2 } \\right )  $ 的展开式中不含 $ x{^{2}} $ 项和<i>x</i>项，则<i>p</i>、<i>q</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ p=0 $ ， $ q=0 $ B． $ p=-3 $ ， $ q=-9 $ </p><p>C． $ p=\\dfrac { 6 } { 7 } $ ， $ q=\\dfrac { 4 } { 7 } $ D． $ p=-3 $ ， $ q=1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025浙江杭州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16327|16424", "keyPointNames": "多项式乘多项式|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588453213898055680", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588453213898055680", "title": "浙江省杭州市锦绣育才教育集团2024−2025学年七年级下学期5月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "588455185413877760", "questionArticle": "<p>5．蓝天白云下，青山绿水间，支一顶帐篷，邀亲朋好友，听蝉鸣，闻清风，话家常，好不惬意．某景区为响应文化和旅游部《关于推动露营旅游休闲健康有序发展的指导意见》精神，需要购买 $ A、B $ 两种型号的帐篷．若购买 $ \\mathrm{ A } $ 种型号帐篷2顶和 $ B $ 种型号帐篷4顶，则需5200元；若购买 $ \\mathrm{ A } $ 种型号帐篷3顶和 $ B $ 种型号帐篷1顶，则需2800元．</p><p>（1）求每顶 $ \\mathrm{ A } $ 种型号帐篷和每顶 $ B $ 种型号帐篷的价格；</p><p>（2）若该景区需要购买 $ A、B $ 两种型号的帐篷共20顶（两种型号的帐篷均需购买），购买 $ \\mathrm{ A } $ 种型号帐篷数量不超过购买 $ B $ 种型号帐篷数量的 $ \\dfrac { { { 1 } } } { { { 3 } } } $ ，为使购买帐篷的总费用最低，应购买 $ \\mathrm{ A } $ 种型号帐篷和 $ B $ 种型号帐篷各多少顶？购买帐篷的总费用最低为多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙一中 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588455156552871936", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588455156552871936", "title": "湖南省长沙市一中初级中学2024−2025学年七年级下学期第三次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588455181051801600", "questionArticle": "<p>6．解下列方程组和不等式组：</p><p>（1） $ \\begin{cases} 2x-y=4 \\\\ 3x+2y=-1 \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} 4x-3  &lt;  5 \\\\ \\dfrac { -2x+1 } { 3 } &gt; 2+x \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙一中 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588455156552871936", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588455156552871936", "title": "湖南省长沙市一中初级中学2024−2025学年七年级下学期第三次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588455311997968384", "questionArticle": "<p>7．2023年9月23日至10月8日第十九届亚运会将在杭州举办．某商场用25000元购进亚运吉祥物的摆件和挂件，售完后共获利11700元．其中摆件每件进价40元，售价58元；挂件每件进价30元，售价45元．</p><p>（1）请分别求出该商场购进摆件和挂件的数量．（用二元一次方程组解决问题）</p><p>（2）618促销期间，商场第二次以原进价购进摆件和挂件，购进摆件的件数不变，而购进挂件的件数是第一次的2倍，摆件按原售价出售，而挂件打折销售．若摆件和挂件销售完毕，要使第二次经营活动获利不少于10800元，则挂件最低可以打几折？（用一元一次不等式解决问题）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588455286018449408", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "588455286018449408", "title": "江苏省南京市第五十中学（南京工业大学附属中学）2024−2025学年七年级下学期6月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588455308617359360", "questionArticle": "<p>8．（1）解方程组： $ \\begin{cases} x+2y=0 \\\\ 3x+4y=6 \\end{cases}  $ ；</p><p>（2）解不等式组 $ \\begin{cases} 5+3x  &lt;  13 \\\\ \\dfrac { x+2 } { 3 }-\\dfrac { x-1 } { 2 }\\leqslant  2 \\end{cases}  $ ，并写出正整数解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588455286018449408", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "588455286018449408", "title": "江苏省南京市第五十中学（南京工业大学附属中学）2024−2025学年七年级下学期6月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588455304112676864", "questionArticle": "<p>9．已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} x-y=4a \\\\ x+y=a+6 \\end{cases}  $ 的解满足 $ x+3y=18 $ ，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588455286018449408", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "588455286018449408", "title": "江苏省南京市第五十中学（南京工业大学附属中学）2024−2025学年七年级下学期6月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588455682581504000", "questionArticle": "<p>10．如图，长方形的长为 $ a $ ，宽为 $ b\\left ( { a &gt; b &gt; 1 } \\right )  $ ，将原长方形的长和宽各增加3，得到的新长方形的面积记为 $ S{{}_{ 1 } } $ ；将原长方形的长和宽各减少1，得到的新长方形的面积记为 $ S{{}_{ 2 } } $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/12/2/1/0/0/0/588455617393635344/images/img_16.png\" style=\"vertical-align:middle;\" width=\"249\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）若 $ S{{}_{ 1 } }=S{{}_{ 2 } }+26 $ ，求原长方形的周长；</p><p>（2）当 $ 2S{{}_{ 1 } }-S{{}_{ 2 } }=35 $ 时，求将原长方形的长和宽各增加7后得到的新长方形的面积；</p><p>（3）如果用一个面积为 $ S{{}_{ 1 } } $ 的长方形和三个面积为 $ S{{}_{ 2 } } $ 的长方形恰好能拼成一个没有缝隙没有重叠的正方形，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁沈阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-26", "keyPointIds": "16327|16439", "keyPointNames": "多项式乘多项式|几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588455652638367744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588455652638367744", "title": "辽宁省 沈阳市私立联合体2024−2025学年七年级下学期阶段练习（二）数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 11, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 11, "timestamp": "2025-07-01T02:02:05.353Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}