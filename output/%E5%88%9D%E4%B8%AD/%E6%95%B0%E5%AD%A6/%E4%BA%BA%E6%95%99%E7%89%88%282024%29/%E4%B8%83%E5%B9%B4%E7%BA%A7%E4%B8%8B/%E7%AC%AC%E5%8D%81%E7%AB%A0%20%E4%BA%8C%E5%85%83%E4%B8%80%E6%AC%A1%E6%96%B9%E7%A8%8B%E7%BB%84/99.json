{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 98, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "571886530886672384", "questionArticle": "<p>1．已知方程组 $ \\begin{cases} 2x+y=◯ \\\\ x+y=3 \\end{cases}  $ 的解为 $ \\begin{cases} x=2 \\\\ y=\\vartriangle  \\end{cases}  $ 则被“○”和“△”遮盖的两个数的和为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南河南师大附中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886512666615808", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886512666615808", "title": "河南省新乡市河南师范大学附属中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886528479141888", "questionArticle": "<p>2．题目：“已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 3x+5y=4k-2① \\\\ x-3y=2② \\end{cases}  $ 的解满足 $ 2x+y=3 $ ，求<i>k</i>的值．”</p><p>如下有嘉嘉和淇淇两种解题思路和部分步骤：</p><p>嘉嘉：将方程组中的①式和②式相加并整理，可得到 $ 2x+y\\mathrm{ = }2k $ ，再求<i>k</i>的值；</p><p>淇淇：解方程组 $ \\begin{cases} 2x+y=3 \\\\ x-3y=2 \\end{cases}  $ 得 $ \\begin{cases} x=\\dfrac { 11 } { 7 } \\\\ y=-\\dfrac { 1 } { 7 } \\end{cases}  $ ，将结果代入 $ 3x+5y\\mathrm{ = }4k-2 $ ，再求<i>k</i>的值．</p><p>下列判断正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．只有嘉嘉的解题思路正确B．只有淇淇的解题思路正确</p><p>C．嘉嘉和祺淇的解题思路都正确D．嘉嘉和淇淇的解题思路都不正确</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南河南师大附中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886512666615808", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886512666615808", "title": "河南省新乡市河南师范大学附属中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571886522795859968", "questionArticle": "<p>3．下列方程组中，属于二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=2 \\\\ y-\\dfrac { 1 } { x }=2 \\end{cases}  $ B． $ \\begin{cases} x+y=5 \\\\ y=2 \\end{cases}  $ C． $ \\begin{cases} xy=4 \\\\ y=1 \\end{cases}  $ D． $ \\begin{cases} x+y=5 \\\\ x{^{2}}-1=0 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025河南河南师大附中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886512666615808", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886512666615808", "title": "河南省新乡市河南师范大学附属中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571886756833828864", "questionArticle": "<p>4．在平面直角坐标系中，我们能把二元一次方程 $ x-y=0 $ 的一个解用一个点表示出来，标出一些以方程 $ x-y=0 $ 的解为坐标的点，过这些点中的任意两点作直线，在这条直线上任取一点，这个点的坐标就是方程 $ x-y=0 $ 的解，这条直线也被称为二元一次方程的“图象”．</p><p>规定：以方程 $ x-y=0 $ 的解为坐标的所有点的全体叫做方程 $ x-y=0 $ 的图象．</p><p>结论：一般地，在平面直角坐标系中，任何一个二元一次方程的图象都是一条直线．</p><p>示例：如图1，我们在画方程 $ x-y=0 $ 的图象时，可以取点 $ A\\left ( { -1,-1 } \\right )  $ 和 $ B\\left ( { 2,2 } \\right )  $ ，然后作出直线 $ AB $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/28/2/1/0/0/0/571886689611718666/images/img_10.png\" style=\"vertical-align:middle;\" width=\"554\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)请你判断在方程 $ 2x-y=-1 $ 的图象上的点有<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>（填序号）；</p><p>① $ \\left ( { -2,-2 } \\right )  $ ；② $ \\left ( { -1,-1 } \\right )  $ ；③ $ \\left ( { 1,2 } \\right )  $ ；④ $ \\left ( { 2,5 } \\right )  $ ．</p><p>(2)请你在图2所给的平面直角坐标系中画出二元一次方程组 $ \\begin{cases} 2x-y=-1 \\\\ x+y=4 \\end{cases}  $ 中的两个二元一次方程的图象；观察图象，两条直线的交点坐标为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，由此你得出这个二元一次方程组的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>(3)已知以关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} 6x+5y=16-21m \\\\ 5x+6y=25+10m \\end{cases}  $ 的解为坐标的点在方程 $ x+y=3 $ 的图象上，当 $ t &gt; m $ 时，化简 $ \\left  | { 11t-7 } \\right  | -\\sqrt { {\\left( { -2018-11t } \\right) ^ {2}} } $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙一中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16379|16420|16540", "keyPointNames": "二次根式的性质和化简|二元一次方程的解|一次函数与二元一次方程（组）", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886727800856576", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886727800856576", "title": "湖南省长沙市一中教育集团联考2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886751595143168", "questionArticle": "<p>5．解下列方程组</p><p>(1) $ \\begin{cases} 3x+2y=-1① \\\\ 5x+2y=1② \\end{cases}  $ </p><p>(2) $ \\begin{cases} y=3x+7① \\\\ 2x-3y=-14② \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙一中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886727800856576", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886727800856576", "title": "湖南省长沙市一中教育集团联考2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886750273937408", "questionArticle": "<p>6．甲、乙两人共同解方程组 $ \\begin{cases} ax+by=2① \\\\ cx-3y=4② \\end{cases}  $ ，甲将①中的 $ b $ 看成了它的相反数解得 $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $ ，乙抄错②中的 $ c $ 解得 $ \\begin{cases} x=2 \\\\ y=4 \\end{cases}  $ ，则 $ a-b+c= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙一中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886727800856576", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886727800856576", "title": "湖南省长沙市一中教育集团联考2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886746448732160", "questionArticle": "<p>7．若 $ 2x{^{m}}+3y{^{n}}=5 $ 为二元一次方程，则 $ m+n= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025湖南长沙一中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886727800856576", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886727800856576", "title": "湖南省长沙市一中教育集团联考2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571886743890206720", "questionArticle": "<p>8．长沙市一中为提倡校园垃圾分类，需制作宣传海报．已知制作2张 $ \\mathrm{ A } $ 类海报和3张 $ B $ 类海报共需130元，制作4张 $ \\mathrm{ A } $ 类海报和1张 $ B $ 类海报共需110元．设 $ \\mathrm{ A } $ 类海报单价为 $ x $ 元， $ B $ 类海报单价为 $ y $ 元，则可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2x+3y=110 \\\\ 4x+y=130 \\end{cases}  $ B． $ \\begin{cases} 2x+3y=130 \\\\ 4x+y=110 \\end{cases}  $ </p><p>C． $ \\begin{cases} 3x+2y=110 \\\\ x+4y=130 \\end{cases}  $ D． $ \\begin{cases} 3x+2y=130 \\\\ x+4y=110 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西大同 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-05-02", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589573228693069824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589573228693069824", "title": "山西省大同六中集团校2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}, {"id": "571886727800856576", "title": "湖南省长沙市一中教育集团联考2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571886739498770432", "questionArticle": "<p>9．“天宫课堂”第四课航天员演示了“水球变向实验”，水球的运动轨迹可表示为二元一次方程 $ 2x+y=6 $ ．下列哪组解是这个二元一次方程的解（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=1 \\\\ y=3 \\end{cases}  $ B． $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ C． $ \\begin{cases} x=3 \\\\ y=0 \\end{cases}  $ D． $ \\begin{cases} x=1 \\\\ y=6 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025湖南长沙一中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571886727800856576", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571886727800856576", "title": "湖南省长沙市一中教育集团联考2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "572224299081179136", "questionArticle": "<p>10．春季是传染病高发的季节，同学们要勤通风常洗手，为了同学们的身体健康，李老师为全年级师生购买洗手液，根据市场调研，李老师发现某品牌的洗手液的大瓶装 $ \\left ( { { { 5 } }{ { 0 } }{ { 0 } }{ \\rm{ g } } } \\right )  $ 和小瓶装 $ \\left ( { { { 2 } }{ { 5 } }{ { 0 } }{ \\rm{ g } } } \\right )  $ 两种产品的销售数量（按瓶计算）比为 $ 2:5 $ ，某厂每天生产这种洗手液22.5吨，请同学们利用二元一次方程组的数学思想，帮助李老师估计一下这些洗手液应该分装多少个大瓶，多少个小瓶才是最合理的？（请同学们注意单位换算）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津耀华 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-02", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572224272225050624", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572224272225050624", "title": "天津市和平区天津市耀华中学2024-2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 99, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 99, "timestamp": "2025-07-01T02:12:30.472Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}