{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 59, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "580620481012736000", "questionArticle": "<p>1．《孙子算经》是中国传统数学的重要著作，其中有一道题，原文是：“今有木，不知长短，引绳度之，余绳四尺五寸；屈绳量之，不足一尺．木长几何？”意思是：用一根绳子去量一根木头的长，绳子还剩余4.5尺；将绳子对折再量木头，则木头还剩余1尺，问木头长多少尺？可设木头长为<i>x</i>尺，绳子长为<i>y</i>尺，则所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y-x=4.5 \\\\ 2x-y=1 \\end{cases}  $ B． $ \\begin{cases} x-y=4.5 \\\\ 2x-y=1 \\end{cases}  $ </p><p>C． $ \\begin{cases} x-y=4.5 \\\\ \\dfrac { y } { 2 }-x=1 \\end{cases}  $ D． $ \\begin{cases} y-x=4.5 \\\\ x-\\dfrac { y } { 2 }=1 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-05-27", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580620467360276480", "questionFeatureName": "数学文化题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580620467360276480", "title": "2025年广东省深圳市初中学业水平测试数学模拟练习试卷（二）", "paperCategory": 1}, {"id": "430352103485251584", "title": "山东省东营市胜利第一初级中学2023-2024学年九年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580620605231247360", "questionArticle": "<p>2．为了传承中华优秀传统文化，增强文化自信，爱知中学举办了以“争做时代先锋少年”为主题的演讲比赛，并为获奖的同学颁发奖品．张老师去商店购买甲、乙两种笔记本作为奖品，若买甲种笔记本 $ 20 $ 个，乙种笔记本 $ 30 $ 个，共用 $ 190 $ 元，且买 $ 10 $ 个甲种笔记本比买 $ 20 $ 个乙种笔记本少花 $ 10 $ 元．</p><p>（1）求甲、乙两种笔记本的单价各是多少元？</p><p>（2）张老师准备购买甲乙两种笔记本共 $ 100 $ 个，且甲种笔记本的数量不少于乙种笔记本数量的 $ 3 $ 倍，因张老师购买的数量多，实际付款时按原价的九折付款．为了使所花费用最低，应如何购买？最低费用是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南驻马店 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 2, "createTime": "2025-05-27", "keyPointIds": "16434|16543", "keyPointNames": "方案问题|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580620572998021120", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580620572998021120", "title": "2025年河南省驻马店市驿城区部分初中联考一模数学试题", "paperCategory": 1}, {"id": "461661834258456576", "title": "河南省郑州市航空港区2023-2024学年八年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580620971180072960", "questionArticle": "<p>3．被历代数学家尊为“算经之首”的《九章算术》是中国古代算法的扛鼎之作．书中有一道题的大意为：“现在有5只雀、6只燕，分别集中放在天平上称重，聚在一起的雀重燕轻．将一只雀一只燕交换位置而放，重量相等，5只雀和6只燕共重1斤，问雀和燕各重多少？”设雀每只<i>x</i>斤，燕每只<i>y</i>斤，则可列出方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+6y=1 \\\\ 4x+y=5y+x \\end{cases}  $ B． $ \\begin{cases} 5x+6y=1 \\\\ 5x+y=6y+x \\end{cases}  $ </p><p>C． $ \\begin{cases} 6x+5y=1 \\\\ 5x+y=4y+x \\end{cases}  $ D． $ \\begin{cases} 6x+5y=1 \\\\ 6x+y=5y+x \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京八一学校 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-05-27", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580620953886957568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580620953886957568", "title": "北京市八一学校2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "533729701627469824", "title": "重庆市长寿中学校2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580622719928016896", "questionArticle": "<p>4．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x-y=7 \\\\ 2ax-by=4 \\end{cases}  $ 和 $ \\begin{cases} x+2y=1 \\\\ ax+2by=7 \\end{cases}  $ 有相同的解．</p><p>（1）求出它们的相同解；</p><p>（2）求 $ {\\left( { a+b } \\right) ^ {2023}} $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东惠州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-05-27", "keyPointIds": "16420|16424|30400", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组|有理数的乘方", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622697090031616", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622697090031616", "title": "广东省惠阳市高级中学2024−2025学年下学期七年级数学期中质量监测试题", "paperCategory": 1}, {"id": "450067356301172736", "title": "广东省惠州市第五中学教育集团2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580622708653727744", "questionArticle": "<p>5．方程组 $ \\{\\hspace{-0.5em}  \\begin{array} {l} x+y=1， \\\\ 2x-y=5 \\end{array} \\hspace{-0.5em}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）.</p><p>A． $ \\{\\hspace{-0.5em}  \\begin{array} {l} x=-1， \\\\ y=2. \\end{array} \\hspace{-0.5em}  $ B． $ \\{\\hspace{-0.5em}  \\begin{array} {l} x=-2， \\\\ y=3. \\end{array} \\hspace{-0.5em}  $ C． $ \\{\\hspace{-0.5em}  \\begin{array} {l} x=2， \\\\ y=1. \\end{array} \\hspace{-0.5em}  $ D． $ \\{\\hspace{-0.5em}  \\begin{array} {l} x=2， \\\\ y=-1. \\end{array} \\hspace{-0.5em}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东惠州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-05-27", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622697090031616", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622697090031616", "title": "广东省惠阳市高级中学2024−2025学年下学期七年级数学期中质量监测试题", "paperCategory": 1}, {"id": "554780097598758912", "title": "湖南省永州市第十二中学2023−2024学年七年级下学期第一次月考数学模拟试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579858213429878784", "questionArticle": "<p>6．马面裙作为汉服的重要组成部分，承载着我国深厚的历史文化底蕴．在某网店中，销量最高的 $ A,B $ 两款马面裙备受消费者青睐， $ A,B $ 两款马面裙的售价分别为150元/件和200元/件，两款马面裙3月份的总销量为600件，销售总额为110000元．</p><p>（1）求3月份 $ A,B $ 两款马面裙的销量分别为多少件？</p><p>（2）为满足店铺的日常运营需求，该网店决定从服装厂预定 $ A,B $ 两款马面裙共2400件，且 $ \\mathrm{ A } $ 款马面裙数量不超过 $ B $ 款马面裙数量的 $ \\dfrac { 1 } { 2 } $ ，已知 $ \\mathrm{ A } $ 款马面裙进价为100元/件， $ B $ 款马面裙进价160元/件，请你设计一种方案，使得这批马面裙全部售出后获利最大，并求出最大利润．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579858180668170240", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "579858180668170240", "title": "2025年福建省厦门市第十中学九年级下学期数学模拟考试", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579858597892370432", "questionArticle": "<p>7．在阳光明媚的一天，小颖和小亮同学想用所学的数学知识测量小区门口小广场上5<i>G</i>微基站信号塔 $ AB $ 的高度．信号塔固定在一个高为1米的平台上．测量时，小颖调整自己位置到 $ CD $ ，使得信号塔 $ AB $ 在地面上的影子 $ MG $ 和自己的影子 $ CG $ 重合，小颗转过身蹲下来，在 $ CM $ 上的点<i>E</i>处放置一小块平面镜，使得此刻小颖的眼睛<i>F</i>通过平面镜<i>E</i>恰好能看到信号塔顶部<i>B</i>，此时，<i>D</i>，<i>C</i>，<i>F</i>三点共线．</p><p>已知：四边形 $ OPMN $ 为矩形，<i>B</i>，<i>A</i>，<i>T</i>三点共线，<i>P</i>，<i>T</i>，<i>M</i>，<i>C</i>共线， $ AB\\bot ON $ ， $ CD\\bot CM $ ，测得 $ CD=1.8{ \\rm{ m } } $ ， $ CF=1.35{ \\rm{ m } } $ ， $ EC=1{ \\rm{ m } } $ ， $ CG=2{ \\rm{ m } } $ ，测量示意图如图所示．请根据相关测量信息，求信号塔 $ AB $ 的高度．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/20/2/1/0/0/0/579858538077401108/images/img_20.png\" style=\"vertical-align:middle;\" width=\"192\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西安铁一中学 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16424|16823", "keyPointNames": "加减消元法解二元一次方程组|相似三角形的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579858564342132736", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579858564342132736", "title": "2025年陕西省西安市铁一中学中考五模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579858454459752448", "questionArticle": "<p>8．解方程组： $ \\begin{cases} x+2y=-5 \\\\ 7x-2y=13 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南郴州 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579858429834993664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579858429834993664", "title": "2025年湖南省郴州市初中学业水平考试第二次监测数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579858925278769152", "questionArticle": "<p>9．某学校为了改善办学条件，计划购置一批电子白板和一批笔记本电脑．经投标，购买一块电子白板比3台笔记版电脑多3000元，购买4块电子白板和5台笔记本电脑共需80000元．</p><p>（1）求购买1块白板和一台笔记本电脑各需多少元；</p><p>（2）根据该校实际情况，需购买电子白板和笔记本电脑的总数为396，要求购买的总费用不超过2700000元，并购买笔记本电脑的台数不超过购买电子白板数量的3倍，该校有几种购买方案；</p><p>（3）经销商根据发改环资（2025）13号补贴要求决定笔记本电脑按八五折销售．上面的哪种购买方案最省钱？按最省钱方案购买需要多少钱．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川内江 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16437|16490|16544", "keyPointNames": "销售利润问题|一元一次不等式组的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579858889606213632", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579858889606213632", "title": "2025年四川省内江市第一中学九年级中考二模考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580900002366529536", "questionArticle": "<p>10．四月春风和煦，气温适宜，正是放风筝的好时节．某景区提前购买了 $ \\mathrm{ A } $ 、 $ B $ 两种型号风筝进行销售，已知2只 $ \\mathrm{ A } $ 型风筝和1只 $ B $ 型风筝共需18元，3只 $ \\mathrm{ A } $ 型风筝和2只 $ B $ 型风筝共需31元．</p><p>（1）求 $ \\mathrm{ A } $ 、 $ B $ 两种型号风筝的进价各多少元？</p><p>（2）该景区将 $ \\mathrm{ A } $ 型风筝的售价定为每只12元， $ B $ 型风筝的售价定为每只20元．该景区第一天售出 $ \\mathrm{ A } $ 型风筝200只， $ B $ 型风筝150只，第二天该景区决定对 $ B $ 型风筝打折， $ \\mathrm{ A } $ 型风筝售价不变，结果第二天 $ \\mathrm{ A } $ 型风筝售出的数量比第一天少了 $ 20{ \\rm{ \\% } }，B $ 型风筝售出的数量比第一天多了 $ 20{ \\rm{ \\% } } $ ．若第二天的销售利润比第一天的销售利润少了640元，请问 $ B $ 型风筝打了几折？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-26", "keyPointIds": "16406|16437", "keyPointNames": "销售盈亏问题|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580899964739428352", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580899964739428352", "title": "重庆实验外国语学校2024−2025学年七年级下学期半期考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 60, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 60, "timestamp": "2025-07-01T02:07:53.044Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}