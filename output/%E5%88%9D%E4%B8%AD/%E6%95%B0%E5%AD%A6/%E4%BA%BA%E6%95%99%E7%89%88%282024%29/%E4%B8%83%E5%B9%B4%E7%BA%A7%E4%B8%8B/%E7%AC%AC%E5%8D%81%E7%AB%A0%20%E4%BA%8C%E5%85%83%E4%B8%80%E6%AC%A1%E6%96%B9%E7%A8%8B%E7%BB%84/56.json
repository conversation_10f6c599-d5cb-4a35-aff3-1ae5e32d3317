{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 55, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "580622441472372736", "questionArticle": "<p>1．方程2<i>x</i>﹣ $ \\dfrac { 1 } { y } {\\rm =0，3\\mathit{x}+\\mathit{y}=0，2\\mathit{x}+\\mathit{xy}=1，3\\mathit{x}+\\mathit{y}} $ ﹣2<i>x</i>=0，<i>x</i><sup>2</sup>﹣<i>x</i>+1=0中，二元一次方程的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1个B．2个C．3个D．4个</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建泉州五中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622432030994432", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622432030994432", "title": "福建省泉州第五中学2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580622355950510080", "questionArticle": "<p>2．根据以下素材，思考并完成任务：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>如何设计购买方案？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材1</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>展览馆分为 $ A{ \\rm{ ， } }B{ \\rm{ ， } }C $ 三个场馆，已知 $ \\mathrm{ A } $ 场馆门票为每张50元， $ B $ 场馆门票为每张40元， $ C $ 场馆门票为每张15元．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材2</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>某校40名同学要去参观航天展览馆，由于场地原因，要求到 $ \\mathrm{ A } $ 场馆参观的人数要少于到 $ B $ 场馆参观的人数，且每位同学只能选择一个场馆参观．参观当天刚好有优惠活动：每购买1张 $ \\mathrm{ A } $ 场馆门票就赠送1张 $ C $ 场馆门票</p></td></tr><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>探究经费的使用</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>若购买 $ \\mathrm{ A } $ 场馆门票赠送的 $ C $ 场馆门票刚好够参观 $ C $ 场馆的同学使用，设购买 $ \\mathrm{ A } $ 场馆门票 $ m $ 张，则购买 $ B $ 场馆门票<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>张，求购买门票所需总金额的最值．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>拟定购买方案</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>若购买 $ a $ 张 $ \\mathrm{ A } $ 场馆门票，参观 $ C $ 场馆的同学除了使用掉赠送的门票外，还需购买 $ c $ 张门票，最终购买三种门票共花费了1100元，为让去 $ \\mathrm{ A } $ 场馆的人数尽量的多，请你通过计算写出 $ a $ 的最大值．</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建泉州五中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-31", "keyPointIds": "16420|16486", "keyPointNames": "二元一次方程的解|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622316607938560", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580622316607938560", "title": "福建省泉州第五中学2024−2025学年九年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581950461898432512", "questionArticle": "<p>3．规定：关于 $ x,y $ 的二元一次方程 $ ax+by=c\\left ( { a\\ne 0,b\\ne 0 } \\right )  $ 的解记为有序数对 $ M\\left ( { x,y } \\right )  $ ，将这些有序数对在平面直角坐标系下描点，连接得到一条直线，这些有序数对对应的点称为直线 $ ax+by=c\\left ( { a\\ne 0,b\\ne 0 } \\right )  $ 的“解点”，回答下列问题：</p><p>（1）已知 $ P{{}_{ 1 } }\\left ( { -1,0 } \\right )  $ ， $ P{{}_{ 2 } }\\left ( { 0,-1 } \\right )  $ ， $ P{{}_{ 3 } }\\left ( { 1,2 } \\right )  $ ，是直线 $ x-y=1 $ 的“解点”的是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>（2）若 $ A\\left ( { 1,1 } \\right )  $ ， $ B\\left ( { 2,-1 } \\right )  $ 是直线 $ \\left ( { m+1 } \\right ) x+ny=3 $ 的“解点”，求 $ m,n $ 的值．</p><p>（3）已知实数 $ h $ ， $ t $ 满足 $ -2\\sqrt { h }+t=2 $ ，若点 $ M\\left ( { \\sqrt { h },t } \\right )  $ 是直线 $ 3x-y=s $ 一个“解点”，用等式表示 $ s $ 与 $ h $ 之间的关系，并直接写出 $ s $ 的最小值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京理工大学附中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-05-30", "keyPointIds": "16424|16540", "keyPointNames": "加减消元法解二元一次方程组|一次函数与二元一次方程（组）", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950428130091008", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950428130091008", "title": "级重点 北京理工大学附属中学2024—2025学年下学期七年级期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581950458178084864", "questionArticle": "<p>4．已知正实数<i>x</i>的两个平方根分别是 $ 2m $ 和 $ m+a $ ．</p><p>（1）若 $ a=6 $ ，求 $ m $ 的值；</p><p>（2）若 $ a-m=4 $ ，求<i>x</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京理工大学附中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-30", "keyPointIds": "16287|16424", "keyPointNames": "平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950428130091008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950428130091008", "title": "级重点 北京理工大学附属中学2024—2025学年下学期七年级期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581950456756215808", "questionArticle": "<p>5．解方程（组）</p><p>（1） $ 9x{^{2}}=16 $ </p><p>（2） $ \\begin{cases} x+y=2 \\\\ 2x-3y=4 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京理工大学附中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-30", "keyPointIds": "16287|16424", "keyPointNames": "平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950428130091008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950428130091008", "title": "级重点 北京理工大学附属中学2024—2025学年下学期七年级期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581950455380484096", "questionArticle": "<p>6．我校 $ { \\rm{ π } } $ 节举办数学素养大赛，比赛共设四个项目：七巧拼图、三阶幻方、连环解锁和数独比拼，每个项目得分都按照一定百分比折算后计入总分（每个项目得分的折算百分比之和为1），并规定总分在85分以上（含85分）设为一等奖，下表为甲、乙、丙三位同学的得分情况（单位：分）．</p><table style=\"border: solid 1px;border-collapse: collapse; width:318.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>项目</p><p>项目得分</p><p>学生</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>七巧拼图</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>三阶幻方</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>连环解锁</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>数独比拼</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>折算后总分</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>甲</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>66</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>95</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/25/2/1/0/0/0/581950384886820864/images/img_18.png\" style=\"vertical-align:middle;\" width=\"53\" alt=\"试题资源网 https://stzy.com\"></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>68</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;&nbsp;<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/25/2/1/0/0/0/581950384886820865/images/img_19.png\" style=\"vertical-align:middle;\" width=\"53\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>乙</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>66</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>80</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>60</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>68</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>70</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>丙</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>66</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>90</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>80</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>68</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>80</p></td></tr></table><p>若甲、乙、丙三位同学的七巧拼图和数独比拼两项得分折算后的分数之和均为<i>a</i>分，设三阶幻方和连环解锁两个项目的折算百分比分别为 $ x $ 和 $ y $ ，以下说法正确的是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>①三阶幻方和连环解锁的折算百分比满足关系式 $ x+2y=1 $ ；</p><p>②若七巧拼图和数独比拼两项折算后分数之和 $ a=20 $ ，则它们的折算百分比之和为0.25；</p><p>③在②的基础上，若甲在连环解锁得到90分，就一定能得到一等奖．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京理工大学附中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-30", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950428130091008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950428130091008", "title": "级重点 北京理工大学附属中学2024—2025学年下学期七年级期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581950448006897664", "questionArticle": "<p>7．若 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ 是二元一次方程 $ x-my=1 $ 的一个解，则<i>m</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025北京北京理工大学附中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-30", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950428130091008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950428130091008", "title": "级重点 北京理工大学附属中学2024—2025学年下学期七年级期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581950442210369536", "questionArticle": "<p>8．我国古代数学著作《增删算法统宗》记载“绳索量竿”问题：“一条竿子一条索，索比竿子长一托．折回索子来量竿，却比竿子短一托．”其大意为：现有一根竿和一条绳索，用绳索去量竿，绳索比竿长5尺；如果将绳索对半折后再去量竿，就比竿短5尺．设绳索长 $ x $ 尺，竿长 $ y $ 尺，则符合题意的方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y-x=5 \\\\ \\dfrac { 1 } { 2 }x=y+5 \\end{cases}  $ B． $ \\begin{cases} x-y=5 \\\\ \\dfrac { 1 } { 2 }x=y-5 \\end{cases}  $ C． $ \\begin{cases} x-y=5 \\\\ 2x=y-5 \\end{cases}  $ D． $ \\begin{cases} x=y-5 \\\\ 2x=y+5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京理工大学附中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-30", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950428130091008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950428130091008", "title": "级重点 北京理工大学附属中学2024—2025学年下学期七年级期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580622935032901632", "questionArticle": "<p>9．我国古代《四元玉鉴》中记载二果问价问题，其内容如下：九百九十九文钱，甜果苦果买千，甜果九个十一文，苦果七个四文钱，试问甜苦果几个，又问各该几个钱？其意思为：九百九十九文钱买了甜果和苦果共一千个．已知十一文钱可买九个甜果，四文钱可买七个苦果，那么甜果、苦果各买了多少个？买甜果和苦果各需要多少文钱？若设买甜果<i>x</i>个，买苦果<i>y</i>个，根据题意所列方程组是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南鹤壁 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-05-30", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580622915088986112", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580622915088986112", "title": "河南省鹤壁市2024−2025学年九年级下学期第三次联考数学试卷", "paperCategory": 1}, {"id": "593322677009428480", "title": "江苏省南京师范大学附属中学树人学校2024—2025学年下学期6月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580623082831781888", "questionArticle": "<p>10．某企业准备将购买的100t蔬菜运往某市，现有甲、乙两种货车可以租用，已知2辆甲种货车和3辆乙种货车一次可运44t蔬菜；3辆甲种货车和2辆乙种货车一次可运46t蔬菜．</p><p>（1）每辆甲种货车和每辆乙种货车一次分别能运多少吨蔬菜？</p><p>（2）已知甲种货车每辆租金为500元，乙种货车每辆租金为450元，该企业共租用甲、乙两种货车12辆，设租用甲种货车<i>a</i>辆．求租车总费用<i>w</i>（单位：元）与<i>a</i>之间的函数关系式，并求出自变量<i>a</i>的取值范围；</p><p>（3）在（2）的条件下，请你为该企业设计出费用最少的方案，并求出最少的租车费用．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南商丘 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-30", "keyPointIds": "16441|16490|16543", "keyPointNames": "其他问题|一元一次不等式组的应用|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580623036941901824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580623036941901824", "title": "河南省商丘市2024−2025学年下学期五月九年级学业水平考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 56, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 56, "timestamp": "2025-07-01T02:07:25.230Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}