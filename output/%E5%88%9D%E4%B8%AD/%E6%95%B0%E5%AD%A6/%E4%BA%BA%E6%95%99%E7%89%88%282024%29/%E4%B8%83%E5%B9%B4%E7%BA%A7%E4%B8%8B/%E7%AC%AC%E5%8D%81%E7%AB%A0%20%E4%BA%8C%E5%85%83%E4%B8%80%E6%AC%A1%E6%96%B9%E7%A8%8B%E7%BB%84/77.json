{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 76, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "578015966593855488", "questionArticle": "<p>1．解下列方程组：</p><p>（1） $ \\begin{cases} y=2x-5 \\\\ 3x+4y=2 \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} 4m+2n=13 \\\\ 3n-4m=-3 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京人大附中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015941700661248", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015941700661248", "title": "北京市中国人民大学附属中学2024−2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015957605462016", "questionArticle": "<p>2．《九章算术》中记载了一道古代数学名题：今有善行者行一百步，不善行者行六十步．今不善行者先行一百步，善行者追之，问几何步及之．意思是：同样时间内，走路快的人能走100步，走路慢的人只能走60步．走路慢的人先走100步，走路快的人走多少步才能追上走路慢的人（两人每步长相等）？为解决此问题，设走路快的人走 $ x $ 步才能追上走路慢的人，此时走路慢的人又走了 $ y $ 步，则可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-y=100 \\\\ x=\\dfrac { 100 } { 60 }y \\end{cases}  $ B． $ \\begin{cases} x-y=100 \\\\ y=\\dfrac { 100 } { 60 }x \\end{cases}  $ C． $ \\begin{cases} x=y-100 \\\\ x=\\dfrac { 100 } { 60 }y \\end{cases}  $ D． $ \\begin{cases} x=y-100 \\\\ y=\\dfrac { 100 } { 60 }x \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京人大附中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015941700661248", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015941700661248", "title": "北京市中国人民大学附属中学2024−2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578015960042352640", "questionArticle": "<p>3．已知 $ x $ ， $ y $ 满足方程组 $ \\begin{cases} 2x+y=1 \\\\ x+2y=2 \\end{cases}  $ ，则 $ x-y $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京人大附中 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-05-17", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585254077530943488", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "585254077530943488", "title": "2025年北京市中国人民大学附属中学中考数学二模模拟试卷", "paperCategory": 1}, {"id": "578015941700661248", "title": "北京市中国人民大学附属中学2024−2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015953583124480", "questionArticle": "<p>4．如果方程 $ x-y=1 $ 与下面方程中的一个组成的方程组的解为 $ \\begin{cases} x=3 \\\\ y=2 \\end{cases}  $ ，那么这个方程可以是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ y=2x-1 $ B． $ x+\\dfrac { y } { 2 }=4 $ C． $ x=2y+1 $ D． $ \\dfrac { x } { 2 }+y=4 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京人大附中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015941700661248", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015941700661248", "title": "北京市中国人民大学附属中学2024−2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578015814718107648", "questionArticle": "<p>5．某中学为了贯彻落实北京市委办公厅《关于全面加强和改进新时代学校体育工作的行动方案》，切实加强和改进新时代学校体育工作，决定购进一批篮球和足球．已知购买2个篮球和3个足球共需费用430元；购买3个篮球和5个足球共需费用690元．</p><p>（1）求篮球和足球的单价分别是多少元？</p><p>（2）学校计划采购一批篮球和足球（两种球都要买），恰好花费1600元，请问有哪几种购买方案？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16420|16437", "keyPointNames": "二元一次方程的解|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015784682696704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015784682696704", "title": "北京市燕山地区2024−2025学年下学期七年级期中练习 数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015809374564352", "questionArticle": "<p>6．解下列方程组：</p><p>（1） $ \\begin{cases} x=5+y \\\\ x-2y=2 \\end{cases}  $ </p><p>（2） $ \\begin{cases} 5x-2y=17 \\\\ 3x+4y=5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015784682696704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015784682696704", "title": "北京市燕山地区2024−2025学年下学期七年级期中练习 数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015802453962752", "questionArticle": "<p>7．二元一次方程组 $ { \\rm{ \\{ } }\\hspace{-0.5em}  \\begin{array} {} x+y=5 \\\\ 2x-y=1 \\end{array} \\hspace{-0.5em}  $ 的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015784682696704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015784682696704", "title": "北京市燕山地区2024−2025学年下学期七年级期中练习 数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015798024777728", "questionArticle": "<p>8．《算法统宗》是我国古代数学著作，书中记载了这样一个问题，大意是：100个和尚分100个馒头，大和尚一人分3个馒头，小和尚3人分一个馒头．问大小和尚各有多少人？设大和尚有<i>x</i>人，小和尚有<i>y</i>人，那么下面列出的方程组中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/15/2/1/0/0/0/578015759793692675/images/img_4.png\" style=\"vertical-align:middle;\" width=\"148\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} x+y=100 \\\\ 3x+3y=100 \\end{cases}  $ B． $ \\begin{cases} x+y=100 \\\\ \\dfrac { x } { 3 }+3y=100 \\end{cases}  $ C． $ \\begin{cases} x+y=100 \\\\ 3x+\\dfrac { y } { 3 }=100 \\end{cases}  $ D． $ \\begin{cases} x+y=100 \\\\ \\dfrac { x } { 3 }+\\dfrac { y } { 3 }=100 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015784682696704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015784682696704", "title": "北京市燕山地区2024−2025学年下学期七年级期中练习 数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578015794514145280", "questionArticle": "<p>9．若 $ \\begin{cases} x=1 \\\\ y=-2 \\end{cases}  $ 是关于 $ x $ 和 $ y $ 的二元一次方程 $ ax+y=1 $ 的解，则 $ a $ 的值等于（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3B．1C． $ -1 $ D． $ -3 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015784682696704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015784682696704", "title": "北京市燕山地区2024−2025学年下学期七年级期中练习 数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578015684262670336", "questionArticle": "<p>10．阅读材料：关于 $ x，y $ 的二元一次方程 $ ax+by=c $ 有一组整数解 $ \\begin{cases} x=x{{}_{ 0 } } \\\\ y=y{{}_{ 0 } } \\end{cases}  $ 则方程 $ ax+by=c $ 的全部整数解可表示为 $ \\left \\{\\hspace{-0.5em}  \\begin{array} {l} x=x{{}_{ 0 } }-bt \\\\ y=y{{}_{ 0 } }+at \\end{array} \\hspace{-0.5em} { { &nbsp; } }\\right.  $ （<i>t</i>为整数）．</p><p>问题：求方程 $ 7x+19y=213 $ 的所有正整数解．小明参考阅读材料，解决该问题如下：</p><p>解：该方程一组整数解为 $ \\begin{cases} x{{}_{ 0 } }=6 \\\\ y{{}_{ 0 } }=9 \\end{cases}  $ 则全部整数解可表示为 $ \\begin{cases} x=6-19t \\\\ y=9+7t \\end{cases}  $ （ $ t $ 为整数）．</p><p>因为 $ \\begin{cases} 6-19t &gt; 0 \\\\ 9+7t &gt; 0 \\end{cases}  $ 解得 $ -\\dfrac { 9 } { 7 }  &lt;  t  &lt;  \\dfrac { 6 } { 19 } $ ．因为 $ t $ 为整数，所以 $ t=0 $ 或 $ -1 $ ．</p><p>所以该方程的正整数解为 $ \\begin{cases} x=6 \\\\ y=9 \\end{cases}  $ 和 $ \\begin{cases} x=25 \\\\ y=2 \\end{cases}  $ ．</p><p>请你参考小明的解题方法，完成下面的问题：</p><p>（1）方程 $ 5x-3y=1 $ 的全部整数解表示为： $ \\begin{cases} x=2+3t \\\\ y=θ+5t \\end{cases}  $ （ $ t $ 为整数）．则 $ θ $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>（2）请你参照小明的方法，求 $ 3x+4y=48 $ 的全部正整数解；</p><p>（3）方程 $ 5x+19y=2025 $ 的正整数解有<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>组．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京北京市十一学校 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-17", "keyPointIds": "16420|16489", "keyPointNames": "二元一次方程的解|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015653266763776", "questionFeatureName": "阅读材料题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015653266763776", "title": "北京市十一学校2024-−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 77, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 77, "timestamp": "2025-07-01T02:09:56.173Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}