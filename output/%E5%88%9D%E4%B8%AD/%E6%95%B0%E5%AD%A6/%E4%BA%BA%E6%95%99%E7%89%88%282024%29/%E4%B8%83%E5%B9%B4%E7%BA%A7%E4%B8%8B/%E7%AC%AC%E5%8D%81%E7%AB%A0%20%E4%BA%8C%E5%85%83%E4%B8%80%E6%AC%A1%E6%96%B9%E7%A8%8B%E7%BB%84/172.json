{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 171, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "554779787283177472", "questionArticle": "<p>1．关于<i>x</i>,<i>y</i>的方程组 $ \\begin{cases} x+2y=3m \\\\ x-y=9m \\end{cases}  $ 的解是方程3<i>x</i>+2<i>y</i>=34的一组解，那么<i>m</i>的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2</p><p>B．−1</p><p>C．1</p><p>D．−2</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈工大附中 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 3, "createTime": "2025-03-14", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779775455240192", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}, {"id": "1028824092512256", "title": "河北省张家口市2019年中考一模数学试卷", "paperCategory": 1}, {"id": "996640270716928", "title": "黑龙江省佳木斯市第二十一中学2019年人教版七年级下册期末复习检测数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "553366833899806720", "questionArticle": "<p>2．定义新运算：</p><p>①在平面直角坐标系中， $ \\left \\{a,b\\right \\}  $ 表示动点从原点出发，沿着 $ x $ 轴正方向 $ \\left ( { a\\geqslant  0 } \\right )  $ 或负方向 $ \\left ( { a  &lt;  0 } \\right )  $ ．平移 $ \\left  | { a } \\right  |  $ 个单位长度，再沿着 $ y $ 轴正方向 $ \\left ( { b\\geqslant  0 } \\right )  $ 或负方向 $ \\left ( { b  &lt;  0 } \\right )  $ 平移 $ \\left  | { b } \\right  |  $ 个单位长度．例如，动点从原点出发，沿着 $ x $ 轴负方向平移 $ 2 $ 个单位长度，再沿着 $ y $ 轴正方向平移 $ 1 $ 个单位长度，记作 $ \\left \\{-2,1\\right \\}  $ ．</p><p>②加法运算法则： $ \\left \\{a,b\\right \\} +\\left \\{c,d\\right \\} =\\left \\{a+c,b+d\\right \\}  $ ，其中 $ a $ ， $ b $ ， $ c $ ， $ d $ 为实数．</p><p>若 $ \\left \\{4,6\\right \\} +\\left \\{m+n,m-n\\right \\} =\\left \\{7,10\\right \\}  $ ，则 $ mn= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河北秦皇岛 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16424|16775", "keyPointNames": "加减消元法解二元一次方程组|平移的性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553366814635368448", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "553366814635368448", "title": "2025年河北省秦皇岛市九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553366660826046464", "questionArticle": "<p>3．（1）解方程： $ \\begin{cases} 3x+4y=11 \\\\ x+2y=5 \\end{cases}  $ ；</p><p>（2）解不等式组： $ \\begin{cases} -4x-5  &lt;  2x+13 \\\\ \\dfrac { 3x-2 } { 5 }\\leqslant  \\dfrac { x } { 2 } \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东揭阳 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553366631893737472", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "553366631893737472", "title": "2025年广东省揭阳市部分学校九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553367462361735168", "questionArticle": "<p>4．“双减”政策颁布后，学校开展了延时服务，并增加体育锻炼时间．某体育用品商店抓住商机，购进一批乒乓球拍和羽毛球拍进行销售，其进价和售价如表所示．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 103pt;\"><p style=\"text-align:center;\"></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">进价</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">售价</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 103pt;\"><p style=\"text-align:center;\">乒乓球拍（元 $ / $ 套）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\"> $ 35 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\"> $ a $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 103pt;\"><p style=\"text-align:center;\">羽毛球拍（元 $ / $ 套）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\"> $ 40 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\"> $ b $ </p></td></tr></table><p>某班甲体育小组购买 $ 2 $ 套乒乓球拍和 $ 1 $ 套羽毛球拍共花费 $ 160 $ 元，乙体育小组购买 $ 1 $ 套乒乓球拍和 $ 2 $ 套羽毛球拍共花费 $ 170 $ 元．</p><p>(1)求出 $ a $ ， $ b $ 的值；</p><p>(2)根据销售情况，商店决定再次购进 $ 300 $ 套球拍，且购进的乒乓球拍套数不少于羽毛球拍套数的一半，若这批球拍的进价和售价均不变，且能够全部售完，如何购货才能获利最大？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西安 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16438|16535|16544", "keyPointNames": "和差倍分问题|一次函数的图象和性质|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553367434306035712", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "553367434306035712", "title": "2025年陕西省西安市经开区九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554779784280055808", "questionArticle": "<p>5．用加减法解方程组 $ \\begin{cases} 2x-3y=5① \\\\ 3x-2y=7② \\end{cases}  $ ,下列解法错误的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．①×3－②×2，消去<i>x</i></p><p>B．①×2－②×3，消去<i>y</i></p><p>C．①×(−3)＋②×2，消去<i>x</i></p><p>D．①×2－②×(−3)，消去<i>y</i></p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|460000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈工大附中 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-03-14", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779775455240192", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "554779775455240192", "title": "黑龙江省哈尔滨工业大学附属中学校2024−2025学年下学期八年级数学开门考试卷", "paperCategory": 1}, {"id": "440724656502382592", "title": "海南省海口市2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555513378103402496", "questionArticle": "<p>6．已知二元一次方程 $ 2x-y=0 $ 的一组解为 $ \\begin{cases} x=1 \\\\ y=n \\end{cases}  $ ，正比例函数 $ y=kx $ 经过点 $ A\\left ( { 2,n+4 } \\right )  $ ，则<i>k</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．3C． $ -1 $ D． $ -3 $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西安 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16420|16533", "keyPointNames": "二元一次方程的解|正比例函数图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555513367273709568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555513367273709568", "title": "2025年陕西省西安市莲湖区中考数学一模试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555512891337646080", "questionArticle": "<p>7．解方程或方程组</p><p>(1) $ \\begin{cases} 5\\left ( { x+y } \\right ) -2x=0 \\\\ 3x-10\\left ( { x+y } \\right ) =2 \\end{cases}  $ ；</p><p>(2) $ \\dfrac { x } { x-3 }-2=\\dfrac { -3 } { 3-x } $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江杭州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16424|16471", "keyPointNames": "加减消元法解二元一次方程组|解分式方程", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512867790823424", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555512867790823424", "title": "浙江省杭州市萧山区2023−2024学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512885994102784", "questionArticle": "<p>8．下表中每一对<i>x</i>，<i>y</i>的值都是二元一次方程 $ ax-by=2 $ 的一个解，则<i>t</i>的值为 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><table style=\"border: solid 1px;border-collapse: collapse; width:134.25pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 25.5pt;\"><p style=\"text-align:center;\"><i>x</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24pt;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 30.75pt;\"><p style=\"text-align:center;\"> $ -2 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24.75pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 29.25pt;\"><p style=\"text-align:center;\">…</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 25.5pt;\"><p style=\"text-align:center;\"><i>y</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24pt;\"><p style=\"text-align:center;\">0</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 30.75pt;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 24.75pt;\"><p style=\"text-align:center;\"><i>t</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 29.25pt;\"><p style=\"text-align:center;\">…</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江杭州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512867790823424", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555512867790823424", "title": "浙江省杭州市萧山区2023−2024学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512777428738048", "questionArticle": "<p>9．根据以下素材，探索解决任务．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">确定什锦糖的销售量</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">素材1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">某商店有甲，乙两种糖果，单价分别为15元/千克，20元/千克．</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/13/2/1/0/0/0/555512724324655108/images/img_15.png\" style=\"vertical-align:middle;\" width=\"138\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">素材2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">商店将两种糖果混合形成<i>A</i>型什锦糖如图所示．</p><p style=\"text-align:center;\">小温根据个人需要，另外混合配制成<i>B</i>型什锦糖，每份重5千克，价格80元．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">素材3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">小温恰好用870元各买了若干份<i>A</i>，<i>B</i>型什锦糖．</p></td></tr><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">任务1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">确定<i>A</i>型单价</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">每份什锦糖<i>A</i>需要多少元？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">任务2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">确定<i>B</i>型配比</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">每份什锦糖<i>B</i>中甲，乙两种糖果的质量分别是多少千克？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">任务3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">确定销售量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">本次买卖中，商家卖出甲，乙糖果各多少千克？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江杭州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512752124502016", "questionFeatureName": "阅读材料题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555512752124502016", "title": "浙江省杭州市采荷中学2023−2024学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512880738639872", "questionArticle": "<p>10．北魏数学家张丘建被称“算圣”，他所著的《张丘建算经》中记载了各种计算，其中有一题：今有鸡翁一值钱五，鸡母一值钱三，鸡雏三值钱一．百钱买百鸡，问鸡翁、鸡母、鸡雏各几何？译：一只公鸡值5钱，一只母鸡值3钱，三只小鸡值1钱．现用100钱买100只鸡，请问能买公鸡、母鸡、小鸡各多少只？设公鸡有 $ x $ 只，则下列各值中 $ x $ 不能取的数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．4B．8C．12D．16</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江杭州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16420|16438", "keyPointNames": "二元一次方程的解|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512867790823424", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555512867790823424", "title": "浙江省杭州市萧山区2023−2024学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 172, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 172, "timestamp": "2025-07-01T02:21:12.509Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}