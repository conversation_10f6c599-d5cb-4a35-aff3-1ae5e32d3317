{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 70, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "579482512830799872", "questionArticle": "<p>1．下列4组数中，不是二元一次方程 $ 2x-3y=0 $ 的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=1 \\\\ y=\\dfrac { 2 } { 3 } \\end{cases}  $ B． $ \\begin{cases} x=\\dfrac { 3 } { 2 } \\\\ y=1 \\end{cases}  $ C． $ \\begin{cases} x=\\dfrac { 5 } { 2 } \\\\ y=\\dfrac { 4 } { 3 } \\end{cases}  $ D． $ \\begin{cases} x=3 \\\\ y=2 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏省锡中 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579482498683412480", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579482498683412480", "title": "江苏省无锡市锡山高级中学2024−2025学年下学期中考二模数学模拟试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579477049410101248", "questionArticle": "<p>2．对于有理数<i>x</i>，<i>y</i>，定义新运算： $ x*y=ax+by $ ， $ x\\otimes y=ax-by $ ，其中<i>a</i>，<i>b</i>是常数．例如， $ 3*2=3a+2b $ ， $ 2\\otimes 1=2a-b， $ </p><p>已知 $ 3*2=-1 $ ， $ 2\\otimes 1=4 $ ，则根据定义可以得到： $ \\begin{cases} 3a+2b=-1 \\\\ 2a-b=4 \\end{cases}  $ </p><p>（1） $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）若 $ x*2y+x\\otimes y=10 $ ，求 $ x-y $ 的值；</p><p>（3）若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x*y=8+m \\\\ x\\otimes y=5m \\end{cases}  $ 的解也满足方程 $ x-y=9 $ ，求<i>m</i>的值；</p><p>（4）若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} a{{}_{ 1 } }x*b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ a{{}_{ 2 } }x\\otimes b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ 的解为 $ \\begin{cases} x=12 \\\\ y=5 \\end{cases}  $ ，则关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 4a{{}_{ 1 } }\\left ( { x+y } \\right ) *5b{{}_{ 1 } }\\left ( { x-y } \\right ) =c{{}_{ 1 } } \\\\ 4a{{}_{ 2 } }\\left ( { x+y } \\right ) \\otimes 5b{{}_{ 2 } }\\left ( { x-y } \\right ) =c{{}_{ 2 } } \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东华南师大附中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16424|16425|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的特殊解法|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579477020821725184", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579477020821725184", "title": "广东省广州市华南师范大学附属中学2024~2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579477044372742144", "questionArticle": "<p>3．解方程组 $ \\begin{cases} x-3y=4 \\\\ 2x+3y=-1 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东华南师大附中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579477020821725184", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579477020821725184", "title": "广东省广州市华南师范大学附属中学2024~2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579477041969405952", "questionArticle": "<p>4．若实数<i>x</i>，<i>y</i>满足 $ {\\left( { 4x+3y-1 } \\right) ^ {2}}+\\sqrt { 2x-y+7 }=0 $ ，则 $ x{^{y}}= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东华南师大附中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16424|28421", "keyPointNames": "加减消元法解二元一次方程组|算术平方根非负性的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579477020821725184", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579477020821725184", "title": "广东省广州市华南师范大学附属中学2024~2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579477035392737280", "questionArticle": "<p>5．用代入法解方程组 $ \\begin{cases} 2x+3y-2=0① \\\\ { { 4 } }x+1=9y② \\end{cases}  $ ，正确的解法是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．先将 $ ① $ 变形为 $ x=\\dfrac { 3y-2 } { 2 } $ ，再代入 $ ② $ </p><p>B．先将 $ ① $ 变形为 $ y=\\dfrac { 2-2x } { 3 } $ ，再代入 $ ② $ </p><p>C．先将 $ ② $ 变形为 $ x=\\dfrac { 9 } { 4 }y-1 $ ，再代入 $ ① $ </p><p>D．先将 $ ② $ 变形为 $ y=9(4x-1) $ ，再代入 $ ① $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东华南师大附中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579477020821725184", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579477020821725184", "title": "广东省广州市华南师范大学附属中学2024~2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579477032796463104", "questionArticle": "<p>6．若 $ \\begin{cases} x=-1 \\\\ y=2 \\end{cases}  $ 是方程 $ 2x+my=4 $ 的解，则 $ m $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -1 $ B．1C． $ -3 $ D．3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东华南师大附中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579477020821725184", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579477020821725184", "title": "广东省广州市华南师范大学附属中学2024~2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579476685881384960", "questionArticle": "<p>7．对实数 $ x $ ， $ y $ 定义一种新运算 $ f $ ，规定<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/19/2/1/0/0/0/579476625936396312/images/img_24.png\" style=\"vertical-align:middle;\" class=\"latextool_math_img\" width=\"156\" alt=\"试题资源网 https://stzy.com\">（其中 $ a $ ， $ b $ 均为常数），例如： $ f\\left ( { 1,0 } \\right ) =1 $ ， $ f\\left ( { 2,1 } \\right ) =5 $ ．</p><p>（1）求 $ a $ ， $ b $ 的值；</p><p>（2）求关于 $ m $ ， $ n $ 的方程 $ f\\left ( { 2,m } \\right ) +f\\left ( { 3,n } \\right ) =0 $ 的正整数解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东广大附中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579476659159474176", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579476659159474176", "title": "广东省广州市广州大学附属中学2024−2025学年 七年级数学下学期期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579476686988681216", "questionArticle": "<p>8．小明的数学研学作业单上有这样一道题：已知 $ -x+y=2 $ ，且 $ x  &lt;  3 $ ， $ y\\geqslant  0 $ ，设 $ w=x+y-2 $ ，那么<i>w</i>的取值范围是什么？</p><p>（1）小明的做法：由 $ -x+y=2 $ 得 $ y=2+x $ ，则 $ w=x+y-2=x+2+x-2=2x $ ，由 $ x  &lt;  3,y\\geqslant  0 $ ，得关于<i>x</i>的一元一次不等式组_，</p><p>解该不等式组得到<i>x</i>的取值范围为_，</p><p>则<i>w</i>的取值范围是_．（直接填写答案，不用写过程）</p><p>（2）已知 $ a-b=n $ （<i>n</i>是大于0的常数），且 $ a &gt; 1,b\\leqslant  1 $ ，求 $ 2a+b $ 的最大值．（用含<i>n</i>的代数式表示）；</p><p>（3）若 $ 3x=6y+12=2z $ ，且 $ x &gt; 0,y\\geqslant  -4,z\\leqslant  9 $ ，设 $ m=2x-2y-z $ ，且<i>m</i>为整数，求<i>m</i>所有可能的值的和．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025广东广大附中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16426|16489", "keyPointNames": "二元一次方程组的应用|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579476659159474176", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579476659159474176", "title": "广东省广州市广州大学附属中学2024−2025学年 七年级数学下学期期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579476680781111296", "questionArticle": "<p>9．在长方形 $ ABCD $ 中放入六个相同的小长方形，尺寸如图所标示，则小长方形的长为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/19/2/1/0/0/0/579476625936396306/images/img_18.png\" style=\"vertical-align:middle;\" width=\"225\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东广大附中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579476659159474176", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579476659159474176", "title": "广东省广州市广州大学附属中学2024−2025学年 七年级数学下学期期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579476673524965376", "questionArticle": "<p>10．《九章算术》是中国古代第一部数学专著，它对我国古代后世的数学家产生了深远的影响，该书中记载了一个问题，原文如下：“今有人共买物，人出八，盈三；人出七，不足四．问人数，物价各几何？”大意是：有几个人一起去买一件物品，每人出8元，多3元；每人出7元，少4元，求有几个人及该物品的价格，用二元一次方程组解答该问题，若已经列出一个方程7<i>x</i>+4=<i>y</i>，则符合题意的另一个方程是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 8x-3=y $ B． $ 8x+3=y $ C． $ \\dfrac { y } { 8 }+3=x $ D． $ \\dfrac { y } { 8 }-3=x $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东广大附中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-19", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579476659159474176", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579476659159474176", "title": "广东省广州市广州大学附属中学2024−2025学年 七年级数学下学期期中考试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 71, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 71, "timestamp": "2025-07-01T02:09:10.324Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}