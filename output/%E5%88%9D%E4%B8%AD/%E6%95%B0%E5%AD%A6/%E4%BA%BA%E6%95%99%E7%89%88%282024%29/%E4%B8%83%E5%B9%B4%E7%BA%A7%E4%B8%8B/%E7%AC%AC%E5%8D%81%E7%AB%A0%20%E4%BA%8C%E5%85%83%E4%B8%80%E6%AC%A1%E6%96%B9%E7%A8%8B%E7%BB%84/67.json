{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 66, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "578015005276151808", "questionArticle": "<p>1．已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} x+2y=k \\\\ 2x+y=1 \\end{cases}  $ 的解满足 $ x+y=3 $ ，则 $ k= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京交通大学附属中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578014982274588672", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578014982274588672", "title": "北京交通大学附属中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015003090919424", "questionArticle": "<p>2．写出一个以 $ \\begin{cases} x=3 \\\\ y=2 \\end{cases}  $ 为解的二元一次方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京交通大学附属中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578014982274588672", "questionFeatureName": "开放性试题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578014982274588672", "title": "北京交通大学附属中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578014997562826752", "questionArticle": "<p>3．请你阅读下面的诗句：“栖树一群鸦，鸦树不知数，三只栖一树，五只没去处，五只栖一树，闲了一棵树，请你仔细数，鸦树各几何？”假设树有 $ x $ 棵，鸦有 $ y $ 只，根据题意，以下方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3x+5=y \\\\ 5x-5=y \\end{cases}  $ B． $ \\begin{cases} 3x+5=y \\\\ 5x+5=y \\end{cases}  $ </p><p>C． $ \\begin{cases} 3x-5=y \\\\ 5x-5=y \\end{cases}  $ D． $ \\begin{cases} 3x-5=y \\\\ 5x+5=y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京交通大学附属中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578014982274588672", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578014982274588672", "title": "北京交通大学附属中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579857507843092480", "questionArticle": "<p>4．列二元一次方程组解决问题：某商店决定购进甲、乙两种文创产品．若购进甲种文创产品7件，乙种文创产品3件，则费用是285元；若购进甲种文创产品2件，乙种文创产品6件，则费用是210元．求购进的甲、乙两种文创产品每件的费用各是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000|340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽淮南 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-05-21", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579857480680779776", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579857480680779776", "title": "2025年安徽省淮南市九年级中考学情调研（三）数学试题", "paperCategory": 1}, {"id": "578015436006010880", "title": "北京市第十八中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015468088242176", "questionArticle": "<p>5．解方程组： $ \\begin{cases} x+y=3 \\\\ 3x+2y=8 \\end{cases}  $  </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京18中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015436006010880", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015436006010880", "title": "北京市第十八中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015455467581440", "questionArticle": "<p>6．已知 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是关于<i>x</i>，<i>y</i>的二元一次方程 $ ax-y=1 $ 的一个解，那么<i>a</i>的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京18中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578015436006010880", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578015436006010880", "title": "北京市第十八中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578358672578883584", "questionArticle": "<p>7．某水果店计划进<i>A</i>，<i>B</i>两种水果共100千克，这两种水果的进价和售价如表所示．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 52.55pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>进价（元/千克）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>售价（元/千克）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 52.55pt;\"><p><i>A</i>种水果</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>8</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 52.55pt;\"><p><i>B</i>种水果</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>9</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>13</p></td></tr></table><p>（1）若该水果店购进这两种水果共花费740元，求该水果店分别购进<i>A</i>，<i>B</i>两种水果各多少千克？</p><p>（2）在（1）的基础上，为了促销，水果店老板决定把<i>A</i>种水果全部八折出售，<i>B</i>种水果全部降价10%出售，那么售完后共获利多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578358636025524224", "questionMethodName": "函数与方程思想", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578358636025524224", "title": "河北省石家庄市第41中教育集团2024−2025学年下学期阶段性学业质量评价七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578358664655843328", "questionArticle": "<p>8．解方程组：</p><p>（1） $ \\begin{cases} 3x+4y=7 \\\\ 5x-y=4 \\end{cases}  $ </p><p>（2） $ \\begin{cases} x+y=2 \\\\ 3x+2y=4 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578358636025524224", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578358636025524224", "title": "河北省石家庄市第41中教育集团2024−2025学年下学期阶段性学业质量评价七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578358660780306432", "questionArticle": "<p>9．已知关于 $ x $ ， $ y $ 的二元一次方程 $ x+ay=5 $ 的解是 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ ，则 $ a $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578358636025524224", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578358636025524224", "title": "河北省石家庄市第41中教育集团2024−2025学年下学期阶段性学业质量评价七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578358653784207360", "questionArticle": "<p>10．若<i>x</i>，<i>y</i>满足方程组 $ \\begin{cases} x+4y=4 \\\\ 2x-2y=13 \\end{cases}  $ ，则 $ 3x+2y $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．17B．9C．21D．7</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578358636025524224", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578358636025524224", "title": "河北省石家庄市第41中教育集团2024−2025学年下学期阶段性学业质量评价七年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 67, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 67, "timestamp": "2025-07-01T02:08:41.753Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}