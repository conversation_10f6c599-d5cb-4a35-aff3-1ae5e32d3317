{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 118, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "568252881335263232", "questionArticle": "<p>1．解方程组:</p><p>(1) $ \\begin{cases}2x-3y=4,\\\\ 7x+6y=25.\\end{cases} $ </p><p>(2) $ \\begin{cases}\\dfrac{x}{2}-\\dfrac{y+1}{3}=1,\\\\ 3x+2y=40.\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252758131777536", "questionArticle": "<p>2．下列各组数值是二元一次方程<i>x</i>−3<i>y</i>=4的解的是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>　　　　　　　　　　　　　　　</p><p>A． $ \\begin{cases}x=1,\\\\ y=-1\\end{cases} $ B． $ \\begin{cases}x=2,\\\\ y=1\\end{cases} $ </p><p>C． $ \\begin{cases}x=-1,\\\\ y=-2\\end{cases} $ D． $ \\begin{cases}x=4,\\\\ y=-1\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时2 代入法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568252880676757504", "questionArticle": "<p>3．表一</p><table class=\"editor-gen-table\" data-edited=\"true\" style=\"width: auto;\"><tbody><tr><td style=\"width: 55.65pt; border-collapse: collapse; border: 1px solid; text-align: left;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p><i>x</i></p></td><td style=\"width:55.75pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>3</p></td><td style=\"width:55.75pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p><i>a</i></p></td><td style=\"width:89.45pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>9</p></td></tr><tr><td style=\"width:55.65pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p><i>y</i></p></td><td style=\"width:55.75pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>0</p></td><td style=\"width:55.75pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>2</p></td><td style=\"width:89.45pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p><i>b</i></p></td></tr></tbody></table><p>表二</p><table class=\"editor-gen-table\" data-edited=\"true\" style=\"width: auto;\"><tbody><tr><td style=\"width: 55.65pt; border-collapse: collapse; border: 1px solid; text-align: left;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p><i>x</i></p></td><td style=\"width:55.75pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>9</p></td><td style=\"width:55.75pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>1</p></td><td style=\"width:89.45pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p><i>c</i></p></td></tr><tr><td style=\"width:55.65pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p><i>y</i></p></td><td style=\"width:55.75pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>4</p></td><td style=\"width:55.75pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>36</p></td><td style=\"width:89.45pt;border-collapse:collapse;border:solid 1px;\" colspan=\"1\" rowspan=\"1\" width=\"auto\"><p>12</p></td></tr></tbody></table><p>(1)关于<i>x</i>,<i>y</i>二元一次方程2<i>x</i>−3<i>y</i>=6和<i>mx</i>+<i>ny</i>=40的三组解分别如表一、表二所示,则<i>a</i>=<span style=\"font-family: &quot;Courier New&quot;;\"><u> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</u></span>,<i>b</i>=<span style=\"font-family: &quot;Courier New&quot;;\"><u> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</u></span>,<i>c</i>=<span style=\"font-family: &quot;Courier New&quot;;\"><u> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</u></span><i>. </i></p><p>(2)关于<i>x</i>,<i>y</i>的二元一次方程组 $\\begin{cases}mx+ny=40,\\\\ 2x-3y=6\\end{cases}$ 的解是<span style=\"font-family: &quot;Courier New&quot;;\"><u> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</u></span><i>. </i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253100827385856", "questionArticle": "<p>4．用如图(1)中的长方形和正方形纸板作侧面和底面,做成如图(2)的竖式和横式两种无盖纸盒,现在仓库里有1 000张正方形纸板和2 000张长方形纸板.</p><p>(1)根据题意完成下表格.</p><table style=\"border: solid 1px;border-collapse: collapse; margin: auto;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 45.35pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.7pt;\"><p><i>x</i>只竖式纸盒中</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.7pt;\"><p><i>y</i>只横式纸盒中</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 63.85pt;\"><p>合计</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 45.35pt;\"><p>正方形纸</p><p>板的张数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.7pt;\"><p><u>　　</u><u><i>&nbsp;</i></u></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.7pt;\"><p><u>　　</u><u><i>&nbsp;</i></u></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 63.85pt;\"><p>1 000</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 45.35pt;\"><p>长方形纸</p><p>板的张数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.7pt;\"><p><u>　　</u><u><i>&nbsp;</i></u></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 73.7pt;\"><p><u>　　</u><u><i>&nbsp;</i></u></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 63.85pt;\"><p>2 000</p></td></tr></table><p>(2)问两种纸盒各做多少个,恰好将库存的纸板用完?</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/18/2/1/0/0/0/568253069118447618/images/img_2.png\" style=\"vertical-align:middle;\" width=\"87\" alt=\"试题资源网 https://stzy.com\"></p><p>图(1)</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/18/2/1/0/0/0/568253069118447619/images/img_3.png\" style=\"vertical-align:middle;\" width=\"151\" alt=\"试题资源网 https://stzy.com\"></p><p>图(2)</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16432|16440", "keyPointNames": "配套问题|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时2 列二元一次方程组解决百分率、配套和销售问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252877371645952", "questionArticle": "<p>5．若 $ {\\rm (2\\mathit{x}-\\mathit{y})^{2}} $ 与|<i>x</i>+2<i>y</i>−5|互为相反数,则(<i>x</i>-<i>y</i>)<sup>2 021</sup>=<u>　　　　</u><i>.&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252876717334528", "questionArticle": "<p>6．已知二元一次方程组 $ \\begin{cases}2x-y=5,\\\\ x-2y=1,\\end{cases} $ 则<i>x</i>-<i>y</i>的值为&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2B．6\t</p><p>C．−2D．−6</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568252875924611072", "questionArticle": "<p>7．小丽在用“加减消元法”解二元一次方程组 $ \\begin{cases}5x-2y=4,①\\\\ 2x+3y=9②\\end{cases} $ 时,利用① $ {\\rm ×\\mathit{a}+} $ ② $ {\\rm ×\\mathit{b}} $ 消去<i>x</i>,则<i>a</i>,<i>b</i>的值可能是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．<i>a</i>=2,<i>b</i>=5B．<i>a</i>=3,<i>b</i>=2</p><p>C．<i>a</i>=−3,<i>b</i>=2D．<i>a</i>=2,<i>b</i>=−5</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568253102928732160", "questionArticle": "<p>8．合肥某商场从厂家批发电视机进行零售,批发价格与零售价格如下表:</p><table style=\"border: solid 1px;border-collapse: collapse; margin: auto;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100pt;\"><p>电视机型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>甲</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96.6pt;\"><p>乙</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100pt;\"><p>批发价(元<i>/</i>台)</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>1 500</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96.6pt;\"><p>2 500</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 100pt;\"><p>零售价(元<i>/</i>台)</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 60pt;\"><p>2 000</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96.6pt;\"><p>3 600</p></td></tr></table><p>若商场购进甲、乙两种型号的电视机共50台,用去9万元.</p><p>(1)求商场购进甲、乙型号的电视机各多少台.</p><p>(2)商场决定两种型号电视机均打折销售:以零售价的七五折销售乙种型号电视机,两种电视机销售完毕,商场共获利15<i>%</i>,求甲种型号电视机打几折销售.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16406|16438", "keyPointNames": "销售盈亏问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时2 列二元一次方程组解决百分率、配套和销售问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252875198996480", "questionArticle": "<p>9．用加减法解方程组 $ \\begin{cases}4x+3y=7,①\\\\ 6x-5y=-1②\\end{cases} $ 时,若要求消去<i>y</i>,则应&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．①×3+②×2B．①×3-②×2\t</p><p>C．①×5+②×3D．①×5-②×3\t</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568252873747767296", "questionArticle": "<p>10．用加减消元法解二元一次方程组时,必须使这两个方程中&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．某个未知数的系数化为1</p><p>B．同一个未知数的系数相等</p><p>C．同一个未知数的系数互为相反数</p><p>D．同一个未知数的系数的绝对值相等</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时3 加减法解二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 119, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 119, "timestamp": "2025-07-01T02:14:51.641Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}