{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 137, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "564940142848286720", "questionArticle": "<p>1．我国古代数学经典著作《九章算术》中有这样一题，原文是：“今有共买物，人出八，盈三；人出七，不足四．问人数、物价各几何？”意思是：今有人合伙购物，每人出八钱，会多三钱；每人出七钱，又差四钱．问人数、物价各多少？设人数为 $ x $ 人，物价为 $ y $ 钱，下列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y=8x-3 \\\\ y=7x+4 \\end{cases}  $ B． $ \\begin{cases} y=8x+3 \\\\ y=7x+4 \\end{cases}  $ C． $ \\begin{cases} y=8x-3 \\\\ y=7x-4 \\end{cases}  $ D． $ \\begin{cases} y=8x+3 \\\\ y=7x-4 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|330000|420000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2021湖北宜昌 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 4, "createTime": "2025-04-14", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "202120079605014528", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "202120079605014528", "title": "湖北省宜昌市2021年中考数学真题", "paperCategory": 1}, {"id": "564940131838238720", "title": "浙江省杭州市拱墅区文澜中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "359449917780172800", "title": "湖南省长沙市麓山国际学校2023-2024学年八年级上学期开学考试数学试题", "paperCategory": 1}, {"id": "220537633352491008", "title": "江苏省扬州市宝应县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "566752119727890432", "questionArticle": "<p>2．我国明代数学家程大位编撰的《算法统宗》记载了“绳索量竿”问题：“一条竿子一条索，索比竿子长一托，折回索子来量竿，却比竿子短一托，问索、竿各长几何？”译文为“有一根竿和一条绳，若用绳去量竿，则绳比竿长5尺；若将绳对折后再去量竿，则绳比竿短5尺，问绳和竿各有多长？”设绳长<i>x</i>尺，竿长<i>y</i>尺，根据题意得（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）（注：“托”和“尺”为古代的长度单位，1托 $ =5 $ 尺）</p><p>A． $ \\begin{cases} x-y=5 \\\\ y-\\dfrac { 1 } { 2 }x=5 \\end{cases}  $　　　　B． $ \\begin{cases} y-x=5 \\\\ \\dfrac { 1 } { 2 }x-y=5 \\end{cases}  $　　　　C． $ \\begin{cases} x-y=5 \\\\ 2x=y+5 \\end{cases}  $　　　　D． $ \\begin{cases} x-y=5 \\\\ y-2x=5 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|210000|510000|450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁阜新实中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 5, "createTime": "2025-04-14", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "566752102124396544", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "566752102124396544", "title": "辽宁省阜新市实验中学2024−2025学年九年级下学期限时作业数学试卷", "paperCategory": 1}, {"id": "550039905293869056", "title": "广西壮族自治区南宁市第四十七中学2024−2025学年下学期九年级数学开学考试题", "paperCategory": 1}, {"id": "531850990758174720", "title": "重庆市第十一中学校2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "527503474591834112", "title": "重庆市第十一中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 11}, {"id": "467873325852696576", "title": "四川省凉山彝族自治州2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "565668271501910016", "questionArticle": "<p>3．为响应“全民植树增绿，共建美丽中国”的号召，学校组织学生到郊外参加义务植树活动，并准备了 $ A $ ， $ B $ 两种食品作为午餐．这两种食品每包质量均为 $ 50{ \\rm{ g } } $ ，营养成分如表所示．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/14/2/1/0/0/0/566987140925530113/images/img_1.png\" style='vertical-align:middle;' width=\"434\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)若要从这两种食品中摄入 $ 4600{ \\rm{ K } }{ \\rm{ J } } $ 热量和 $ 70{ \\rm{ g } } $ 蛋白质，应选用 $ A $ ， $ B $ 两种食品各多少包？</p><p>(2)运动量大的人或青少年对蛋白质的摄入量应更多，若每份午餐选用这两种食品共7包，要使每份午餐中的蛋白质含量不低于 $ 90{ \\rm{ g } } $ ，最多能选用几包 $ A $ 种食品？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春 · 模拟", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-13", "keyPointIds": "16435|16486", "keyPointNames": "分配问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565668249502785536", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "565668249502785536", "title": "吉林省长春市七校2024−2025学年九年级下学期联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "565667352827699200", "questionArticle": "<p>4．已知 $ x+2y=3 $ 且满足 $ x\\geqslant  0 $ ， $ y\\geqslant  0 $ ，设 $ m=2x+3y $ ，则 $ m $ 的取值范围是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 0\\leqslant  m\\leqslant  \\dfrac { 21 } { 2 } $</p><p>B． $ 0\\leqslant  m\\leqslant  \\dfrac { 9 } { 2 } $</p><p>C． $ \\dfrac { 9 } { 2 }\\leqslant  m\\leqslant  6 $</p><p>D． $ \\dfrac { 9 } { 2 }\\leqslant  m\\leqslant  \\dfrac { 21 } { 2 } $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽滁州 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-04-13", "keyPointIds": "16426|16489", "keyPointNames": "二元一次方程组的应用|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565667338705477632", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "565667338705477632", "title": "2025年安徽省滁州市九年级中考第一次模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564579222041174016", "questionArticle": "<p>5．若 $ \\begin{cases} x=-2 \\\\ y=1 \\end{cases}  $ 是方程 $ 2x-my=1 $ 的一个解，则 $ m $ 的值（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -1 $ B． $ -5 $ C．1D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-13", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564579207507910656", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564579207507910656", "title": "河北省石家庄市复兴中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564579430531637248", "questionArticle": "<p>6．方程 $ x+2y=7 $ 在自然数范围内的解（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．有无数组B．有两组</p><p>C．有三组D．有四组</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北唐山 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-13", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564579414748471296", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564579414748471296", "title": "河北省唐山地区2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564579435032125440", "questionArticle": "<p>7．用适当的方法解下列方程</p><p>(1) $ \\begin{cases} x-3y=5 \\\\ 2x+y=5 \\end{cases}  $ </p><p>(2) $ \\begin{cases} y=x-3 \\\\ y-2x=5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北唐山 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-13", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564579414748471296", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564579414748471296", "title": "河北省唐山地区2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564579972737703936", "questionArticle": "<p>8．<b>综合与运用</b></p><p>已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} x-y=3 \\\\ ax+2by=4 \\end{cases}  $ 与 $ \\begin{cases} bx+（a-1）y=3 \\\\ x+y=1 \\end{cases}  $ 有相同的解．</p><p>(1)求这个相同的解；</p><p>(2)求 $ a $ ， $ b $ 的值；</p><p>(3)小明同学说，无论 $ m $ 取何值，（1）中的解都是关于 $ x $ ， $ y $ 的方程 $ \\left ( { 3+m } \\right ) x+\\left ( { 2m+1 } \\right ) y=5 $ 的解，这句话对吗？请你说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16424|16427", "keyPointNames": "加减消元法解二元一次方程组|同解方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564579950881185792", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564579950881185792", "title": "山西省晋城市多校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564579969059299328", "questionArticle": "<p>9．解方程或方程组：</p><p>(1)解方程： $ \\dfrac { x-1 } { 2 }-\\dfrac { 2x+3 } { 6 }=1 $ ．</p><p>(2)解方程组： $ \\begin{cases} x-2y=1 \\\\ 3x+4y=23 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564579950881185792", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564579950881185792", "title": "山西省晋城市多校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564579969604558848", "questionArticle": "<p>10．某社区积极响应市委市政府“创建文明城市，构建美好家园”的精神．为提高居民的垃圾分类意识，该社区决定采购<i>A</i>，<i>B</i>两种型号的新型垃圾桶．若购买4个<i>A</i>型垃圾桶和5个<i>B</i>型垃圾桶共需要740元，购买3个<i>A</i>型垃圾桶和10个<i>B</i>型垃圾桶共需要1180元．求两种型号垃圾桶的单价．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564579950881185792", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564579950881185792", "title": "山西省晋城市多校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 138, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 138, "timestamp": "2025-07-01T02:17:12.558Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}