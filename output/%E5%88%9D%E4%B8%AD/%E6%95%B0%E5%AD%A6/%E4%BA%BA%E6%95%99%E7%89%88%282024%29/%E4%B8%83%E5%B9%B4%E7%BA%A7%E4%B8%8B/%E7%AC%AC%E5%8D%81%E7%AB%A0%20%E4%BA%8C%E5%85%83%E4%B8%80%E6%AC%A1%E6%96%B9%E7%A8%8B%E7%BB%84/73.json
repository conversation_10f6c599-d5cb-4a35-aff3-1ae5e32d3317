{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 72, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "578017955721555968", "questionArticle": "<p>1．解下列方程及方程组：</p><p>（1） $ {\\left( { x+2 } \\right) ^ {2}}=9 $ ；</p><p>（2） $ \\begin{cases} x-3y=4 \\\\ 2x+3y=-1 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州一中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16287|16424", "keyPointNames": "平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017930094358528", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "578017930094358528", "title": "福建省福州第一中学2024−2025学年下学期七年级期中考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578018132603744256", "questionArticle": "<p>2．解下列方程组：</p><p>（1） $ \\begin{cases} y=x+3① \\\\ 2x+y=6② \\end{cases}  $ </p><p>（2） $ \\begin{cases} 3x+2y=7① \\\\ 6x-2y=11② \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福建师范大学附属小学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578018110046777344", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578018110046777344", "title": "福建省福州市福建师范大学附属中学2024−2025学年下学期七年级期中考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578018131156709376", "questionArticle": "<p>3．盲盒为消费市场注入了活力，既能够营造消费者购物过程中的趣味体验，也为商家实现销售额提升拓展了途径．某商家将蓝牙耳机、多接口优盘、迷你音箱共22个，搭配为<i>A</i>，<i>B</i>，<i>C</i>三种盲盒各一个，其中<i>A</i>盒中有2个蓝牙耳机，3个多接口优盘，1个迷你音箱；<i>B</i>盒中蓝牙耳机与迷你音箱的数量之和等于多接口优盘的数量，蓝牙耳机与迷你音箱的数量之比为3：2；<i>C</i>盒中有1个蓝牙耳机，3个多接口优盘，2个迷你音箱．经核算，<i>A</i>盒的成本为145元，<i>B</i>盒的成本为245元（每种盲盒的成本为该盒中蓝牙耳机、多接口优盘、迷你音箱的成本之和），则<i>C</i>盒的成本为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福建师范大学附属小学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16416|16444", "keyPointNames": "其他问题|三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578018110046777344", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578018110046777344", "title": "福建省福州市福建师范大学附属中学2024−2025学年下学期七年级期中考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578018130288488448", "questionArticle": "<p>4．关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+y=5a+1 \\\\ x+2y=4a+2 \\end{cases}  $ 的解满足 $ x-y=12 $ ，则<i>a</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州第十四中学附属学校 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-05-18", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591265343122747392", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "591265343122747392", "title": "2024−2025学年浙江省杭州十四中附中七年级（下）月考数学试卷（6月份）", "paperCategory": 11}, {"id": "578018110046777344", "title": "福建省福州市福建师范大学附属中学2024−2025学年下学期七年级期中考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578017946875768832", "questionArticle": "<p>5．《九章算术》是我国古代的数学著作，它的代数成就主要包括开方术、正负术和方程术，其中方程术是《九章算术》最高的数学成就．《九章算术》中记载：“今有二马、一牛价过一万，如半马之价，一马、二牛价不满一万，如半牛之价，问牛、马价各几何？”其大意为：现有两匹马加一头牛的价钱超过一万，超过的部分正好是半匹马的价钱；一匹马加二头牛的价钱则不到一万，不足的部分正好是半头牛的价钱，求一匹马、一头牛各多少钱？设一匹马值<i>x</i>钱，一头牛值<i>y</i>钱，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）．</p><p>A． $ \\begin{cases} \\left ( { 2x+y } \\right ) +\\dfrac { 1 } { 2 }x=10000 \\\\ \\left ( { x+2y } \\right ) +\\dfrac { 1 } { 2 }y=10000 \\end{cases}  $ B． $ \\begin{cases} \\left ( { 2x+y } \\right ) +\\dfrac { 1 } { 2 }x=10000 \\\\ \\left ( { x+2y } \\right ) -\\dfrac { 1 } { 2 }y=10000 \\end{cases}  $ </p><p>C． $ \\begin{cases} \\left ( { 2x+y } \\right ) -\\dfrac { 1 } { 2 }x=10000 \\\\ \\left ( { x+2y } \\right ) +\\dfrac { 1 } { 2 }y=10000 \\end{cases}  $ D． $ \\begin{cases} \\left ( { 2x+y } \\right ) -\\dfrac { 1 } { 2 }x=10000 \\\\ \\left ( { x+2y } \\right ) -\\dfrac { 1 } { 2 }y=10000 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州一中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017930094358528", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "578017930094358528", "title": "福建省福州第一中学2024−2025学年下学期七年级期中考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578017941003743232", "questionArticle": "<p>6．下列是二元一次方程 $ x+2y=4 $ 的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）．</p><p>A． $ \\begin{cases} x=-2 \\\\ y=2 \\end{cases}  $　　　　B． $ \\begin{cases} x=0 \\\\ y=0 \\end{cases}  $　　　　C． $ \\begin{cases} x=0 \\\\ y=1 \\end{cases}  $　　　　D． $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州一中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017930094358528", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "578017930094358528", "title": "福建省福州第一中学2024−2025学年下学期七年级期中考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578018119978889216", "questionArticle": "<p>7．已知 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是方程 $ x-ay=3 $ 的一个解，那么<i>a</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -3 $ B． $ -1 $ C．1D．3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福建师范大学附属小学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578018110046777344", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578018110046777344", "title": "福建省福州市福建师范大学附属中学2024−2025学年下学期七年级期中考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "578017468058214400", "questionArticle": "<p>8．规定：若点 $ P\\left ( { x,y } \\right )  $ 的横纵坐标是以 $ x,y $ 为未知数的二元一次方程 $ ax+by=c $ 的整数解，则称点 $ P $ 为二元一次方程 $ ax+by=c $ 的“理想点”．请回答以下问题．</p><p>（1）在点 $ A\\left ( { -2,1 } \\right ) ,B\\left ( { 2,-1 } \\right ) ,C\\left ( { 1,2 } \\right ) ,D\\left ( { 5,0 } \\right )  $ 中，哪些是方程 $ 3x+y=5 $ 的“理想点”？</p><p>（2）已知 $ m,n $ 为正整数，若点 $ P\\left ( { m,n } \\right )  $ 是方程 $ x+2y=4 $ 的“理想点”，直接写出 $ m,n $ 的值；</p><p>（3）已知 $ k $ 是整数，且 $ P\\left ( { x,y } \\right )  $ 是方程 $ 2x+y=1 $ 和 $ kx+y=2 $ 的“理想点”，求 $ k $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京北京市陈经纶中学分校 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16420|16424|16497", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组|点的坐标", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017437091667968", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "578017437091667968", "title": "北京市陈经纶中学2024—2025学年下学期七年级期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578017465210281984", "questionArticle": "<p>9．解下列方程（组）</p><p>（1） $ \\dfrac { 1 } { 2 }{\\left( { x-3 } \\right) ^ {2}}=8 $ ；</p><p>（2） $ \\begin{cases} 2x+3y=\\dfrac { 7 } { 2 } \\\\ x-6y=-2 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京市陈经纶中学分校 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16287|16424", "keyPointNames": "平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017437091667968", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "578017437091667968", "title": "北京市陈经纶中学2024—2025学年下学期七年级期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578017462777585664", "questionArticle": "<p>10．在平面直角坐标系<i>xOy</i>中，对点<i>P</i>进行如下操作：把点<i>P</i>的横、纵坐标乘同一个实数<i>a</i>，将得到的点先向左平移<i>m</i>个单位长度，再向上平移<i>n</i>个单位长度，得到点<i>P</i>的对应点<i>P</i>′．如图，点<i>A</i>，<i>B</i>经过上述操作后得到的对应点分别是点<i>A</i>′，<i>B</i>′．</p><p>（1）如果点<i>C</i>（6，﹣2）经过上述操作后得到的对应点是点<i>C</i>′，那么点<i>C</i>′的坐标为<u>&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>（2）如果点<i>D</i>经过上述操作后得到的对应点<i>D</i>′与点<i>D</i>重合，那么点<i>D</i>的坐标为<u>&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/19/2/1/0/0/0/579764031998701569/images/img_1.png\" style='vertical-align:middle;' width=\"544\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京市陈经纶中学分校 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-18", "keyPointIds": "16443|16777", "keyPointNames": "解三元一次方程组|坐标与图形变化——平移", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578017437091667968", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "578017437091667968", "title": "北京市陈经纶中学2024—2025学年下学期七年级期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 73, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 73, "timestamp": "2025-07-01T02:09:24.835Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}