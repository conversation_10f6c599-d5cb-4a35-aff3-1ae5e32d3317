{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 80, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "577679587682856960", "questionArticle": "<p>1．已知关于 $ x $ 、 $ y $ 的二元一次方程组 $ \\begin{cases} 2x+y=3k+1① \\\\ x-y=5② \\end{cases}  $ ．</p><p>（1）若方程组的解 $ x $ 、 $ y $ 互为相反数．求 $ k $ 的值；</p><p>（2）若方程组的解满足 $ -1  &lt;  x+y  &lt;  1 $ ，求 $ k $ 的取值范围．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16402|16424|16489", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679561179049984", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577679561179049984", "title": "福建省福州延安中学2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577679580296687616", "questionArticle": "<p>2．已知 $ \\begin{cases} x=2 \\\\ y=3 \\end{cases}  $ 是二元一次方程 $ x+ky=5 $ 的一个解，则<i>k</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679561179049984", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577679561179049984", "title": "福建省福州延安中学2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577679577327120384", "questionArticle": "<p>3．北魏数学家张丘建被称“算圣”，他所著的《张丘建算经》中记载了各种计算，其中有一题：今有鸡翁一值钱五，鸡母一值钱三，鸡雏三值钱一，百钱买百鸡，问鸡翁、鸡母、鸡雏各几何？译：一只公鸡值5钱，一只母鸡值3钱，三只小鸡值1钱．现用100钱买100只鸡（三种鸡都要买），请问能买公鸡、母鸡、小鸡各多少只？设公鸡有 $ x $ 只，母鸡有 $ y $ 只，小鸡有 $ z $ 只，则下列不符合题意的选项是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=4 \\\\ y=18 \\\\ z=78 \\end{cases}  $ B． $ \\begin{cases} x=8 \\\\ y=11 \\\\ z=81 \\end{cases}  $ </p><p>C． $ \\begin{cases} x=12 \\\\ y=4 \\\\ z=84 \\end{cases}  $ D． $ \\begin{cases} x=13 \\\\ y=5 \\\\ z=82 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679561179049984", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577679561179049984", "title": "福建省福州延安中学2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577679574613405696", "questionArticle": "<p>4．若关于 $ x $ ， $ y $ 的方程 $ 2x{^{\\left  | { m } \\right  | }}+\\left ( { m-1 } \\right ) y=3 $ 是二元一次方程，则 $ m $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\pm 1 $ B． $ -1 $ C．1D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679561179049984", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577679561179049984", "title": "福建省福州延安中学2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577679858379042816", "questionArticle": "<p>5．阅读下列材料：</p><p>问题：已知 $ x-y=2 $ ，且 $ x &gt; 1 $ ， $ y  &lt;  0 $ ，试确定 $ x+y $ 的取值范围．</p><p>解：∵ $ x-y=2 $ ，∴ $ x=y+2 $ ，</p><p>又∵ $ x &gt; 1 $ ，∴ $ y+2 &gt; 1 $ ，∴ $ y &gt; -1 $ ，</p><p>又∵ $ y  &lt;  0 $ ，∴ $ -1  &lt;  y  &lt;  0① $ </p><p>∴ $ -1+2  &lt;  y+2  &lt;  0+2 $ ，</p><p>即 $ 1  &lt;  x  &lt;  2② $ ，</p><p> $ ①+② $ 得 $ -1+1  &lt;  x+y  &lt;  0+2 $ ，</p><p>∴ $ x+y $ 的取值范围是 $ 0  &lt;  x+y  &lt;  2 $ ．</p><p>请按照上述方法，完成下列问题：</p><p>（1）已知 $ x-y=5 $ ，且 $ x &gt; -2 $ ， $ y  &lt;  0 $ ，</p><p> $ ① $ 试确定 $ y $ 的取值范围；</p><p> $ ② $ 试确定 $ x+y $ 的取值范围</p><p>（2）已知 $ x-y=a+1 $ ，且 $ x  &lt;  -b $ ， $ y &gt; 2b $ ，若根据上述做法得到 $ 3x-5y $ 的取值范围是 $ -10  &lt;  3x-5y  &lt;  26 $ ，请求出 $ a、b $ 的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河北保定 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679832177225728", "questionFeatureName": "阅读材料题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577679832177225728", "title": "河北省保定市保定师范附属学校2024−2025学年八年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577679963156951040", "questionArticle": "<p>6．某铁件加工厂用如图1所示的长方形和正方形铁片（长方形的宽与正方形的边长相等）．加工成如图2所示的竖式与横式两种无盖的长方体铁容器．（加工时接缝材料不计）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/14/2/1/0/0/0/577679915820032004/images/img_20.png\" style=\"vertical-align:middle;\" width=\"372\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）如果加工竖式铁容器与横式铁容器各1个，则共需要长方形铁片<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>张，正方形铁片<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>张；</p><p>（2）现有长方形铁片100张，正方形铁片50张，如果加工成这两种铁容器，刚好铁片全部用完，那加工的竖式铁容器、横式铁容器各有多少个？</p><p>（3）把长方体铁容器加盖则可以加工成为铁盒．现准备用33张铁板先做成长方形铁片和正方形铁片，再加工成铁盒，每张铁板有两种裁法：</p><p>方法1：可以裁出3个长方形铁片；</p><p>方法2：可以裁出4个正方形铁片．</p><p>若充分利用这些铁板加工成铁盒，则可以加工成多少个铁盒？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北邢台 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679937009659904", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577679937009659904", "title": "河北省邢台市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577679959533072384", "questionArticle": "<p>7．下面是两个同学解方程组 $ \\begin{cases} -4x+7y=-19① \\\\ -4x-5y=17② \\end{cases}  $ 时，不完整的解题过程：</p><p>甲同学：① $ - $ ②得 $ 2y=-36 $ ， $ \\therefore y=-18 $ ．</p><p>乙同学：由①得 $ 4x=7y+19 $ ③，将③代入②得 $ -7y+19-5y=17 $ ， $ \\therefore -12y=-2 $ ， $ \\therefore y=\\dfrac { 1 } { 6 } $ ．</p><p>（1）甲和乙两位同学的解题过程中，出现错误的同学是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）请你对一个同学的错误解题过程改正并完善．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北邢台 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679937009659904", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "577679937009659904", "title": "河北省邢台市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577679957289119744", "questionArticle": "<p>8．已知 $ \\begin{cases} x=2 \\\\ y=a \\end{cases}  $ 是关于<i>x</i>，<i>y</i>的二元一次方程 $ \\begin{cases} x+y=3 \\\\ 2x+y=b \\end{cases}  $ 的解，求 $ a+b $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北邢台 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16420|16423", "keyPointNames": "二元一次方程的解|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679937009659904", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577679937009659904", "title": "河北省邢台市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577679955900805120", "questionArticle": "<p>9．某校初二年级组织数学编题比赛，其中同学甲创编了如下问题：若3人坐一辆车，则8人需要步行，若“……”．问：人与车各多少？如果假设有<i>x</i>辆车，人数为<i>y</i>，根据题意可列方程组为 $ \\begin{cases} y=3x+8 \\\\ y=4(x-1) \\end{cases}  $ ，题中用“……”表示的缺失条件应补充为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北邢台 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679937009659904", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577679937009659904", "title": "河北省邢台市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "577679951148658688", "questionArticle": "<p>10．利用加减消元法解方程组 $ \\begin{cases} 2x+3y=-10① \\\\ 3x-5y=-6② \\end{cases}  $ 下列做法正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．要消去<i>y</i>，可以将 $ ①\\times 5-②\\times 3 $ B．要消去<i>x</i>，可以将 $ ①\\times 3+②\\times 2 $ </p><p>C．要消去<i>y</i>，可以将 $ ①\\times 5+②\\times 3 $ D．要消去<i>x</i>，可以将 $ ①\\times \\left ( { -2 } \\right ) -②\\times 3 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北邢台 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-16", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577679937009659904", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "577679937009659904", "title": "河北省邢台市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 81, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 81, "timestamp": "2025-07-01T02:10:22.353Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}