{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 149, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "562772987058888704", "questionArticle": "<p>1．河南旅游资源丰富，其中龙门石窟是中国三大石窟之一，拥有97000余尊佛像；清明上河园是以《清明上河图》为蓝本而建造的大型宋代文化实景主题公园．某文旅店拟推出龙门石窟（用<i>A</i>表示）和清明上河园（用<i>B</i>表示）明信片组合套装．已知买2张<i>A</i>明信片和1张<i>B</i>明信片共需花费14元，3张<i>B</i>明信片的价格比2张<i>A</i>明信片的价格多2元．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/02/2/1/0/0/0/562772927696904214/images/img_22.png\" style=\"vertical-align:middle;\" width=\"554\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)分别求<i>A</i>、<i>B</i>两种明信片的单价；</p><p>(2)现有40人的旅行团需要定制40套相同套装，要求每套明信片包含<i>A</i>、<i>B</i>两种共15张，且<i>A</i>明信片的数量不少于6张．设购买所有的明信片所需费用为 $ w $ 元，每套明信片中有 $ m $ 张<i>B</i>明信片，求 $ w $ 与 $ m $ 之间的函数关系式，并求出最少购买费用．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江西吉安 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-05", "keyPointIds": "16438|16490|16542", "keyPointNames": "和差倍分问题|一元一次不等式组的应用|一次函数的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562772966741680128", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562772966741680128", "title": "江西省吉安市2024−2025学年八年级下学期第一次阶段练习数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562772984915599360", "questionArticle": "<p>2．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} x+2y=m+4 \\\\ 2x+y=2m-1 \\end{cases}  $ ．若方程组的解满足 $ \\begin{cases} x+y  &lt;  2 \\\\ x-y  &lt;  4 \\end{cases}  $ 求 $ m $ 的取值范围．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江西吉安 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-05", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562772966741680128", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562772966741680128", "title": "江西省吉安市2024−2025学年八年级下学期第一次阶段练习数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562406088886231040", "questionArticle": "<p>3．《九章算术》是古代中国第一部自成体系的数学专著，其中《卷第八方程》记载：“今有甲乙二人持钱不知其数，甲得乙半而钱五十，乙得甲太半而亦钱五十，问甲、乙持钱各几何？”译文是：今有甲、乙两人持钱不知道各有多少，甲若得到乙所有钱的 $ \\dfrac { 1 } { 2 } $ ，则甲有50钱，乙若得到甲所有钱的 $ \\dfrac { 2 } { 3 } $ ，则乙也有50钱．问甲、乙各持钱多少？设甲持钱数为 $ x $ 钱，乙持钱数为 $ y $ 钱，列出关于 $ x $ 、 $ y $ 的二元一次方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} \\dfrac { 2 } { 3 }x+y=50 \\\\ x+2y=50 \\end{cases}  $ B． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=50 \\\\ \\dfrac { 2 } { 3 }x+y=50 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=50 \\\\ \\dfrac { 3 } { 2 }x+y=50 \\end{cases}  $ D． $ \\begin{cases} x+\\dfrac { 2 } { 3 }y=50 \\\\ \\dfrac { 1 } { 2 }x+y=50 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆复旦中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-04", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562406077611941888", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562406077611941888", "title": "重庆市复旦中学2024−2025学年九年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562772494261723136", "questionArticle": "<p>4．已知关于 $ x $ ， $ y $ 的方程组 $ \\{\\hspace{-0.5em}  \\begin{array} {l} x+2y=5 \\\\ x-2y+mx+9=0 \\end{array} \\hspace{-0.5em}  $ </p><p>（1）请写出方程 $ x+2y=5 $ 的所有正整数解；</p><p>（2）若方程组的解满足 $ x+y=0 $ ，求 $ m $ 的值；</p><p>（3）无论实数 $ m $ 取何值，方程 $ x-2y+mx+9=0 $ 总有一个公共解，你能把求出这个公共解吗？</p><p>（4）如果方程组有整数解，求整数 $ m $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南衡阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-04", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562772468013768704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562772468013768704", "title": "湖南省衡阳市四校2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562772493401890816", "questionArticle": "<p>5．甲、乙两同学解方程组 $ \\begin{cases} ax+by=2 \\\\ cx-3y=-2 \\end{cases}  $ 时，甲得出正确的解为 $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $ ，乙因抄错<i>c</i>的值，解得 $ \\begin{cases} x=2 \\\\ y=6 \\end{cases}  $ ，求 $ a-b+c $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南衡阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-04", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562772468013768704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562772468013768704", "title": "湖南省衡阳市四校2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562772489861898240", "questionArticle": "<p>6． $ \\begin{cases} x+2y=-2, \\\\ 7x-4y=-41. \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南衡阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-04", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562772468013768704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562772468013768704", "title": "湖南省衡阳市四校2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562772484686127104", "questionArticle": "<p>7．已知方程 $ 2x+y=3 $ ，用含<i>x</i>的代数式表示<i>y</i>，则 $ y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南衡阳 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-04", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562772468013768704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562772468013768704", "title": "湖南省衡阳市四校2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562772479397109760", "questionArticle": "<p>8．已知<i>a</i><i>，</i><i>b</i>满足方程组 $ \\begin{cases} a+2b=8 \\\\ 2a+b=7 \\end{cases}  $ ，则 $ |\\bm{ a }-\\bm{ b }| $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -1 $ B．0C．1D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南衡阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-04", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562772468013768704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562772468013768704", "title": "湖南省衡阳市四校2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562772478608580608", "questionArticle": "<p>9．用代入消元法解二元一次方程组 $ \\begin{cases} 3x+5y=8① \\\\ y=2x-1② \\end{cases}  $ 时，将②代入①，正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 3x+(2x-1)=8 $ B． $ 3x-(2x-1)=8 $ C． $ 3x+5(2x-1)=8 $ D． $ 3x-5(2x-1)=8 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南衡阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-04", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562772468013768704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562772468013768704", "title": "湖南省衡阳市四校2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562772476100386816", "questionArticle": "<p>10．若 $ x{^{\\left  | { m{ { - } }3 } \\right  | }}+\\left ( { m{ { - } }2 } \\right ) y=6 $ 是关于<i>x</i>，<i>y</i>的二元一次方程，则<i>m</i>的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．4B．任何数C．2D．2或4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025湖南衡阳 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-04", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562772468013768704", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562772468013768704", "title": "湖南省衡阳市四校2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 150, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 150, "timestamp": "2025-07-01T02:18:38.114Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}