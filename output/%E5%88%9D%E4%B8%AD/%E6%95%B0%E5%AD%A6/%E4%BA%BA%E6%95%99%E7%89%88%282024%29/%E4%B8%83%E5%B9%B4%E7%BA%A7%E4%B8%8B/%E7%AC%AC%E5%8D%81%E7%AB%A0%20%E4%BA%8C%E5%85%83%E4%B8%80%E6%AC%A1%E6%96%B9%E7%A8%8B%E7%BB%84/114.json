{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 113, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "569703521588649984", "questionArticle": "<p>1．下列方程中，哪个是二元一次方程？（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ xy=3 $ B． $ 2x{^{2}}-y=9 $ C． $ \\dfrac { 1 } { x }=x $ D． $ 8x-y=3 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-25", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703514873569280", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "569703514873569280", "title": "河北省 石家庄市第六中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "569703351102775296", "questionArticle": "<p>2．某校40名同学要去参观航天展览馆，已知展览馆分为<i>A</i>，<i>B</i>，<i>C</i>三个场馆，且购买1张<i>A</i>场馆门票和1张<i>B</i>场馆门票共需90元，购买3张<i>A</i>场馆门票和2张<i>B</i>场馆门票共需230元 $ {\\rm ．\\mathit{C}} $ 场馆门票为每张15元．由于场地原因，要求到<i>A</i>场馆参观的人数要少于到<i>B</i>场馆参观的人数，且每位同学只能选择一个场馆参观．参观当天刚好有优惠活动：每购买1张<i>A</i>场馆门票就赠送1张<i>C</i>场馆门票．</p><p>(1)求<i>A</i>场馆和<i>B</i>场馆的门票价格．</p><p>(2)若购买<i>A</i>场馆的门票赠送的<i>C</i>场馆门票刚好够参观<i>C</i>场馆的同学使用，求此次购买门票所需总金额的最小值．</p><p>(3)若参观<i>C</i>场馆的同学除了使用掉赠送的门票外，还需另外购买部分门票，且最终购买三种门票共花费了1100元，求所有满足条件的购买方案．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东深圳 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-25", "keyPointIds": "16420|16438|16535", "keyPointNames": "二元一次方程的解|和差倍分问题|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703316847894528", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "569703316847894528", "title": "广东省深圳市外国语学校2024−2025学年九年级下学期第五次月考数学试卷（一模）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569704811064500224", "questionArticle": "<p>3．某文体书店销售<i>A</i>，<i>B</i>两种跳绳，购买2条<i>A</i>种跳绳和3条<i>B</i>种跳绳共计35元，购买6条<i>A</i>种跳绳和4条<i>B</i>种跳绳共计80元．</p><p>(1)求<i>A</i>种跳绳和<i>B</i>种跳绳每条的价钱．</p><p>(2)现该文体书店对<i>A</i>，<i>B</i>两种跳绳开展促销活动，活动方案如表（两种促销方案不能同时使用）：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">方案</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 182.35pt;\"><p style=\"text-align:center;\">内容</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">促销方案一</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 182.35pt;\"><p style=\"text-align:center;\">买一条<i>A</i>种跳绳，赠送一条<i>B</i>种跳绳</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p style=\"text-align:center;\">促销方案二</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 182.35pt;\"><p style=\"text-align:center;\">买<i>A</i>种或<i>B</i>种跳绳都打八折</p></td></tr></table><p>某校为了准备跳绳比赛，计划购买<i>A</i>，<i>B</i>两种跳绳，且<i>B</i>种跳绳比<i>A</i>种跳绳多买20条．请根据购买<i>A</i>种跳绳的条数<i>x</i>的不同范围，说明该校选择哪种促销方案合适．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025辽宁沈阳 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-25", "keyPointIds": "16441|16486", "keyPointNames": "其他问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569704786515238912", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "569704786515238912", "title": "辽宁省沈阳铁西区部分学校2024−2025学年八年级下学期3月联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569704686422368256", "questionArticle": "<p>4．解下列方程组：</p><p>(1) $ \\begin{cases} y=2x-3 \\\\ 3x-y=18 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 3x-2y=5 \\\\ x+4y=4 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京市第二十九中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-04-25", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569704667619303424", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "569704667619303424", "title": "江苏省南京市第二十九中学2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "557236839221862400", "title": "湖南省长沙市麓山外国语实验中学2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569703625032769536", "questionArticle": "<p>5．根据图中给出的信息，解答下列问题：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/22/2/1/0/0/0/569703583597240330/images/img_22.png\" style=\"vertical-align:middle;\" width=\"397\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）放入一个小球水面升高<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ { \\rm{ c } }{ \\rm{ m } } $ ，放入一个大球水面升高<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ c } }{ \\rm{ m } } $ ；</p><p>（2）如果要使水面上升到 $ {\\rm 50} { \\rm{ c } }{ \\rm{ m } } $ ，应放入大球、小球各多少个？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|460000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025河北石家庄 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-04-25", "keyPointIds": "16429", "keyPointNames": "实际问题与二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703598805786624", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569703598805786624", "title": "河北省石家庄市第十七中学2024−2025学年七年级下学期4月考试数学试题", "paperCategory": 1}, {"id": "1009457344155648", "title": "海南省海口市九中2020年5月九年级中考模拟数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569703604686200832", "questionArticle": "<p>6．下列选项是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x-3y $ B． $ xy+y=-1 $ C． $ x+y=z-2 $ D． $ \\dfrac { x+1 } { 2 }-y=1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 8, "referenceNum": 2, "createTime": "2025-04-25", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703598805786624", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569703598805786624", "title": "河北省石家庄市第十七中学2024−2025学年七年级下学期4月考试数学试题", "paperCategory": 1}, {"id": "537436963659358208", "title": "湖南省怀化市2024−2025学年七年级上学期数学期末抽测卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "569703540760813568", "questionArticle": "<p>7．放学后，小贤和小艺来到学校附近的地摊上购买一种特殊型号的笔芯和卡通笔记本，这种笔芯每盒10支，如果整盒买比单支买每支可优惠0.5元，小贤要买3支笔芯，2本笔记本需花19元，小艺要买7支笔芯，1本笔记本需花费26元．</p><p>（1）求笔记本的单价和单独购买一支笔芯的价格；</p><p>（2）小贤和小艺都还想再买一件单价为3元的小工艺品，但如果他们各自为要买的文具付款后，只有小贤还剩2元钱，他们要怎样做才能既买到各自的文具，又都买到小工艺品，请通过运算说明．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-04-25", "keyPointIds": "16424|16434", "keyPointNames": "加减消元法解二元一次方程组|方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703514873569280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569703514873569280", "title": "河北省 石家庄市第六中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}, {"id": "202427352294400000", "title": "湖南省怀化市新晃县2021-2022学年七年级下学期期末质量检测数学试题（城区）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569703534934925312", "questionArticle": "<p>8．如图，8块相同的小长方形地砖拼成一个长方形，其中每一个小长方形的面积为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/22/2/1/0/0/0/569703493964963847/images/img_7.png\" style=\"vertical-align:middle;\" width=\"164\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ { { 6 } }{ { 7 } }{ { 5 } }{ \\rm{ c } }{ \\rm{ m } }{^{{ { 2 } }}} $ B． $ { { 6 } }{ { 0 } }{ { 0 } }{ \\rm{ c } }{ \\rm{ m } }{^{{ { 2 } }}} $ C． $ { { 5 } }{ { 0 } }{ { 0 } }{ \\rm{ c } }{ \\rm{ m } }{^{{ { 2 } }}} $ D． $ { { 4 } }{ { 0 } }{ { 0 } }{ \\rm{ c } }{ \\rm{ m } }{^{{ { 2 } }}} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春市第二实验中学 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-04-25", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551913641898975232", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "551913641898975232", "title": "吉林省第二实验（高新远洋朝阳）学校2024−2025学年七年级下学期开学考试数学", "paperCategory": 1}, {"id": "569703514873569280", "title": "河北省 石家庄市第六中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "569703534121230336", "questionArticle": "<p>9．现用190张铁皮做盒子，每张铁皮可做8个盒身，或做22个盒底，一个盒身与两个盒底配成一个盒子．设用<i>x</i>张铁皮做盒身，<i>y</i>张铁皮做盒底正好配套，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=190 \\\\ 2\\times 8x=22y \\end{cases}  $ B． $ \\begin{cases} x+y=190 \\\\ 2\\times 22y=8x \\end{cases}  $ C． $ \\begin{cases} 2y+x=190 \\\\ 8x=22y \\end{cases}  $ D． $ \\begin{cases} 2y+x=190 \\\\ 2\\times 8x=22y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|500000|-1|120000|530000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 12, "referenceNum": 7, "createTime": "2025-04-25", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703514873569280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569703514873569280", "title": "河北省 石家庄市第六中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}, {"id": "362364053220532224", "title": "重庆育才中学数育集团2023-2024学年八年级上学期入学定时练习数学试题", "paperCategory": 1}, {"id": "463902987007598592", "title": "天津市外国语大学附属中学2023-2024学年七年级下期学期末数学试题", "paperCategory": 1}, {"id": "209995271605886976", "title": "河北省唐山市丰润区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "209633640166236160", "title": "云南省玉溪市通海县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "198710777250881536", "title": "2022年七年级下册华师版数学第7章7.3三元一次方程组及其解法课时练习", "paperCategory": 1}, {"id": "174284793256910848", "title": "2022年七年级下册湘教版数学第一章1.1建立二元一次方程组课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "569703533408198656", "questionArticle": "<p>10．已知<i>a</i>，<i>b</i>满足方程组 $ \\begin{cases} a+5b=12 \\\\ 3a-b=4 \\end{cases}  $ 则<i>a</i>+<i>b</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．﹣4B．4C．﹣2D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-04-25", "keyPointIds": "16305|16424", "keyPointNames": "代数式求值|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703514873569280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569703514873569280", "title": "河北省 石家庄市第六中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}, {"id": "202443290158866432", "title": "广东省惠州市惠城区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 114, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 114, "timestamp": "2025-07-01T02:14:16.242Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}