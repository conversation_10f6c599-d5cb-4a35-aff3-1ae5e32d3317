{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 165, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "557232728468398080", "questionArticle": "<p>1．下列方程中，是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ xy=100 $ B． $ \\dfrac { 1 } { x }+\\dfrac { 1 } { y }=2 $ C． $ x=2y+1 $ D． $ x{^{2}}+y=13 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024福建泉州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232720440500224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557232720440500224", "title": "福建省泉州科技中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557232943778799616", "questionArticle": "<p>2．二元一次方程组 $ \\begin{cases} x+2y=5 \\\\ x+y=2 \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南开封 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232925495828480", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "557232925495828480", "title": "河南省开封市2024−2025学年下学期九年级第一次数学试题试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557234044489998336", "questionArticle": "<p>3．对<i>m</i>、<i>n</i>的定义一种新运算“◇”，规定： $ m◇n=am-bn+5 $ （其中<i>a</i>、<i>b</i>均为非零常数），等式右边的运算是通常的四则运算，例如： $ 5◇6=5a-6b+5 $ ．已知 $ 2◇3=1 $ ， $ 3◇\\left ( { -1 } \\right ) =10 $ ．</p><p>(1)求<i>a</i>、<i>b</i>的值；</p><p>(2)若关于<i>x</i>的不等式组 $ \\begin{cases} x◇\\left ( { 2x-3 } \\right )   &lt;  9 \\\\ 3x◇\\left ( { -6 } \\right )   &lt;  t \\end{cases}  $ 有且只有一个整数解，试求字母<i>t</i>的取值范围．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山东枣庄 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557234020397916160", "questionFeatureName": "新定义问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557234020397916160", "title": "山东省枣庄市滕州市育才中学2023−2024学年八年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557233340593512448", "questionArticle": "<p>4．《算法统宗》中记载了这样一个问题，其大意是， $ 100 $ 个和尚分 $ 100 $ 个馒头，大和尚 $ 1 $ 人分 $ 3 $ 个馒头，小和尚 $ 3 $ 人分 $ 1 $ 个馒头．问大、小和尚各有多少人？设大和尚有 $ x $ 人，小和尚有 $ y $ 人，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=100 \\\\ 3x+\\dfrac { 1 } { 3 }y=100 \\end{cases}  $　　　　B． $ \\begin{cases} x+y=100 \\\\ \\dfrac { 1 } { 3 }x+3y=100 \\end{cases}  $</p><p>C． $ \\begin{cases} x+y=100 \\\\ 3x+y=100 \\end{cases}  $　　　　D． $ \\begin{cases} x+y=100 \\\\ x+\\dfrac { 1 } { 3 }y=100 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁鞍山 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557233327771525120", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "557233327771525120", "title": "辽宁省鞍山市铁东区鞍山市第二中学2024—2025学年下学期开学测试九年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557234041113583616", "questionArticle": "<p>5．在北京进行的2022年冬季奥运会和冬季残奥会备受世界人士关注．吉祥物“冰墩墩”、“雪容融”玩具随之大卖，购买8个“冰墩墩”和4个“雪容融”玩具共需960元，购买6个“冰墩墩”和8个“雪容融”玩具共需1020元．</p><p>(1)分别求出“冰墩墩”和“雪容融”玩具的销售单价．</p><p>(2)若每个“冰墩墩”玩具制作成本为60元，每个“雪容融”玩具成本为40元，准备制作两种吉祥物玩具共100个，总成本不超过5000元，且销售完该批次吉祥物玩具，利润不低于2480元，请问有哪几种制作方案？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山东枣庄 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16438|16489|16490", "keyPointNames": "和差倍分问题|解一元一次不等式组|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557234020397916160", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557234020397916160", "title": "山东省枣庄市滕州市育才中学2023−2024学年八年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557234036629872640", "questionArticle": "<p>6．已知关于 $ x，y $ 的方程组 $ \\begin{cases} 2x+3y=3k \\\\ x+2y=2-2k \\end{cases}  $ 的解满足 $ x+y &gt; 7 $ ，则 $ k $ 的取值范围是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山东枣庄 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557234020397916160", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557234020397916160", "title": "山东省枣庄市滕州市育才中学2023−2024学年八年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557233494436388864", "questionArticle": "<p>7．清代康熙年间编辑的算书《御制数理精蕴》（卷九）中记载一题，“设如有甲乙二人入山采果共得三百枚，但云甲数加六百枚乙数加二百枚，则甲数比乙数多二倍，问甲乙各得几何？”其大意是：甲、乙二人入山采果共得三百枚，若甲的采果数加六百，乙的采果数加二百枚，则新得到的甲的采果数比乙的采果数多二倍，问甲、乙原来各采果多少枚？如果设甲原来采果数是 $ x $ 枚，乙原来采果数是 $ y $ 枚，则根据题可列方程为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=300 \\\\ x+600=2\\left ( { y+200 } \\right )  \\end{cases}  $ B． $ \\begin{cases} x+y=300 \\\\ 3\\left ( { x+600 } \\right ) =y+200 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=300 \\\\ 2\\left ( { x+600 } \\right ) =y+200 \\end{cases}  $ D． $ \\begin{cases} x+y=300 \\\\ x+600=3\\left ( { y+200 } \\right )  \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁锦州 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557233479102013440", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557233479102013440", "title": "辽宁省锦州市实验学校2024−2025学年九年级下学期数学开门考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557318286502830080", "questionArticle": "<p>8．灵蛇献瑞，已蛇呈祥．新年之际，探亲访友都会提上新春礼盒，缤纷美食满载幸福与甜蜜．重庆某百货超市计划主推两款礼盒：坚果礼盒“锦然秋鸿”和糖果礼盒“甘饴冬藏”．已知4件坚果礼盒和5件糖果礼盒进价1200元，7件坚果礼盒和2件糖果礼盒进价1290元．</p><p>(1)求每件坚果礼盒和糖果礼盒进价分别是多少元？</p><p>(2)超市决定用不超过66600元资金购进坚果礼盒和糖果礼盒共500盒，其中坚果礼盒的数量不少于糖果礼盒数量的 $ \\dfrac { 2 } { 3 } $ ，且两种礼盒的进价保持不变，在运输过程中，有5件坚果礼盒外包装破损，3件糖果礼盒外包装破损，销售时每件坚果礼盒售价为175元，每件糖果礼盒售价为150元，外包装破损的产品均按售价的六折出售，若本次购进的两种礼盒全部售出，请问坚果礼盒购进多少件时，可使本次销售获得最大利润，最大利润是多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆南开 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-19", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557318256022822912", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "557318256022822912", "title": "重庆市南开中学校2024−2025学年下学期八年级入学数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557232733048578048", "questionArticle": "<p>9．把一根长 $ 9{ \\rm{ m } } $ 的钢管截成 $ 1{ \\rm{ m } } $ 长和 $ 2{ \\rm{ m } } $ 长两种规格均有的短钢管，且没有余料，设某种截法中 $ 1{ \\rm{ m } } $ 长的钢管有 $ a $ 根，则 $ a $ 的值有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3种B．4种C．5种D．6种</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024福建泉州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-03-19", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232720440500224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557232720440500224", "title": "福建省泉州科技中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}, {"id": "299649331673473024", "title": "黑龙江省哈尔滨市萧红中学2022-2023学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557232731375050752", "questionArticle": "<p>10．为加强中小学生安全教育，某校组织了“防溺水”知识竞赛，对表现优异的班级进行奖励，学校购买了若干副乒乓球拍和羽毛球拍，购买 $ 2 $ 副乒乓球拍和 $ 1 $ 副羽毛球拍共需 $ 116 $ 元；购买 $ 3 $ 副乒乓球拍和 $ 2 $ 副羽毛球拍共需 $ 204 $ 元，设购买一副乒乓球拍 $ x $ 元，一副羽毛球拍 $ y $ 元，则根据题意列方程组得（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2x+y=3 \\\\ x-y=0 \\end{cases}  $ B． $ \\begin{cases} 2x+y=116 \\\\ 3x+2y=204 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=3 \\\\ 3x-5y=4 \\end{cases}  $ D． $ \\begin{cases} x+4y=600 \\\\ 3x+5y=1100 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|350000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024福建泉州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-19", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232720440500224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557232720440500224", "title": "福建省泉州科技中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}, {"id": "129965107325476864", "title": "山东省济南市高新区2019届九年级4月一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 166, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 166, "timestamp": "2025-07-01T02:20:30.431Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}