{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 169, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "554780113910407168", "questionArticle": "<p>1．已知方程组 $ \\begin{cases} a-2b=6 \\\\ 3a-b=m \\end{cases}  $ 中， $ a $ ， $ b $ 互为相反数，则 $ m $ 的值是 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16252|16423", "keyPointNames": "相反数的应用|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780097598758912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780097598758912", "title": "湖南省永州市第十二中学2023−2024学年七年级下学期第一次月考数学模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780113117683712", "questionArticle": "<p>2．已知 $ 2a{^{y}}b{^{3x+1}} $ 与 $ -3a{^{x-2}}b{^{2-2y}} $ 是同类项，则 $ x= $ <u>&nbsp;&nbsp;&nbsp;</u>， $ y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16314|16423", "keyPointNames": "同类项|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780097598758912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780097598758912", "title": "湖南省永州市第十二中学2023−2024学年七年级下学期第一次月考数学模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780211880960000", "questionArticle": "<p>3．随着“低碳生活，绿色出行”理念的普及，新能源汽车正逐渐成为人们喜爱的交通工具，某汽车销售公司计划购进一批新能源汽车尝试进行销售，据了解2辆<i>A</i>型汽车、3辆<i>B</i>型汽车的进价共计80万元；3辆<i>A</i>型汽车、2辆<i>B</i>型汽车的进价共计95万元．</p><p>(1)求<i>A</i>、<i>B</i>两种型号的汽车每辆进价分别为多少万元？</p><p>(2)若该公司计划正好用180万元购进以上两种型号的新能源汽车（两种型号的汽车均购买），请你帮助该公司设计购买方案；</p><p>(3)若该汽车销售公司销售1辆<i>A</i>型汽车可获利8000元，销售1辆<i>B</i>型汽车可获利6000元，在(2)中的购买方案中，假如这些新能源汽车全部售出，哪种方案获利最大？最大利润是多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州第十四中学附属学校 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-03-15", "keyPointIds": "16420|16437", "keyPointNames": "二元一次方程的解|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591265343122747392", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "591265343122747392", "title": "2024−2025学年浙江省杭州十四中附中七年级（下）月考数学试卷（6月份）", "paperCategory": 11}, {"id": "554780184961916928", "title": "湖南省永州市冷水滩区京华中学2023−2024学年七年级下学期第一次数学课后练习试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780105391775744", "questionArticle": "<p>4．关于<i>x</i>、<i>y</i>的二元一次方程 $ 2x+11y=50 $ 的正整数解的对数为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．4B．3C．2D．1</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780097598758912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780097598758912", "title": "湖南省永州市第十二中学2023−2024学年七年级下学期第一次月考数学模拟试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "554780103814717440", "questionArticle": "<p>5．若方程 $ \\left ( { m+2n } \\right ) x{^{\\left  | { m } \\right  | +n}}=3y{^{n+2}}+4 $ 是二元一次方程，则 $ mn $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2B． $ -1 $ C．0D． $ -2 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780097598758912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780097598758912", "title": "湖南省永州市第十二中学2023−2024学年七年级下学期第一次月考数学模拟试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "554780103021993984", "questionArticle": "<p>6．下列属于二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2x-y=1 \\\\ \\dfrac { 2 } { 3 }x+2y=2 \\end{cases}  $ B． $ \\begin{cases} \\dfrac { 1 } { x }-y=2 \\\\ 2x+y=4 \\end{cases}  $ C． $ \\left \\{{{\\mathop { xy=3 } \\limits_{  } ^{2x+y=1}}}\\right.  $ D． $ \\left \\{{{\\mathop { y+z=3 } \\limits_{  } ^{x+2y=2}}}\\right.  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780097598758912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780097598758912", "title": "湖南省永州市第十二中学2023−2024学年七年级下学期第一次月考数学模拟试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "554780206273175552", "questionArticle": "<p>7．选用适当的方法解下列方程组</p><p>(1) $ \\begin{cases} y=2x-3 \\\\ 3x+y=7 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 5x+4y=4 \\\\ 3x+2y=3 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780184961916928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780184961916928", "title": "湖南省永州市冷水滩区京华中学2023−2024学年七年级下学期第一次数学课后练习试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780203173584896", "questionArticle": "<p>8．《九章算术》是中国古代数学著作之一，其中“方程”记载：“今有五雀、六燕，集称之衡，雀俱重，燕俱轻．一雀一燕交而处，衡适平．并燕、雀重一斤．问燕、雀一枚各重几何？”译文：“五只雀，六只燕共重一斤，雀重燕轻，互换一只，恰好一样重．问：每只雀、燕的重量各为多少？”设一只雀的重量为 $ x $ 斤，一只燕的重量为 $ y $ 斤，则可列方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780184961916928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780184961916928", "title": "湖南省永州市冷水滩区京华中学2023−2024学年七年级下学期第一次数学课后练习试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780201521029120", "questionArticle": "<p>9．把9个数填入 $ 3\\times 3 $ 的方格中，使其任意一行，任意一列及任意一条对角线上的数之和都相等，这样便构成了一个“九宫格”，它源于我国古代的“洛书”，洛书是世界上最早的“幻方”.如图是仅可以看到部分数值的“九宫格”，则 $ 3b-a $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p><table style=\"border: solid 1px;border-collapse: collapse; width:69.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p> $ a $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p> $ b $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p>6</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p>7</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p>4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p>&nbsp;</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16305|16424", "keyPointNames": "代数式求值|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780184961916928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780184961916928", "title": "湖南省永州市冷水滩区京华中学2023−2024学年七年级下学期第一次数学课后练习试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554780198484353024", "questionArticle": "<p>10．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x+2y=6-3a \\\\ x-y=6a \\end{cases}  $ ，给出下列说法：</p><p>①当 $ a=1 $ 时，方程组的解也是 $ x+y=a+3 $ 的解；</p><p>②若 $ 2x+y=3 $ ，则 $ a=-1 $ ；</p><p>③无论<i>a</i>取何值，<i>x</i>，<i>y</i>的值不可能互为相反数；</p><p>④<i>x</i>，<i>y</i>都为自然数的解有5对．</p><p>以上说法中正确的个数为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．2C．3D．4</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南永州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-15", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554780184961916928", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554780184961916928", "title": "湖南省永州市冷水滩区京华中学2023−2024学年七年级下学期第一次数学课后练习试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 170, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 170, "timestamp": "2025-07-01T02:20:57.950Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}