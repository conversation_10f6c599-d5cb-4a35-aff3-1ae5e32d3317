{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 131, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "565307680161570816", "questionArticle": "<p>1．为拓展学生视野，提升学生综合实践能力，某中学组织全校师生开展研学活动，租用甲，乙两种客车15辆，除一辆甲种客车有3个空座位，其余客车全部满座，且总租金为7600元．甲，乙客车的载客量和租金如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p style=\"text-align:center;\">&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">甲种客车</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">乙种客车</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p style=\"text-align:center;\">载客量（人/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">45</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">60</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p style=\"text-align:center;\">租金（元/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">400</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">600</p></td></tr></table><p>该校一共多少师生参加此次研学活动？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽宣城 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565307654974775296", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "565307654974775296", "title": "2025年安徽省宣城市九年级中考第一次模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "565307789418995712", "questionArticle": "<p>2．某图书馆计划用800元购买经典文学和科普读物两种书籍，经典文学每套50元，科普读物每套35元．若购买经典文学的数量比科普读物的数量多10套，判断能否恰好用完预算？若能，请求出所购买的经典文学和科普读物的套数，若不能，请说明理由．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京延庆 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565307763389145088", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "565307763389145088", "title": "2025年北京市延庆区九年级中考零模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567106643407708160", "questionArticle": "<p>3．随着科技的发展，新能源汽车正逐渐成为人们喜欢的交通工具，其需求量快速增长．为满足客户需求，现某汽车销售公司计划购进一批新能源汽车尝试进行销售，据了解1辆 $ A $ 型汽车、1辆 $ B $ 型汽车的进价共计37万元；若单次购买 $ A $ 型汽车超过15辆，每辆车进价会打九五折，单次购买 $ B $ 型汽车超过15辆，每辆车进价优惠5千元，当购买 $ A $ 型和 $ B $ 型车各20辆时共需支付进价715万元．</p><p>(1)求该汽车销售公司单独购进 $ A $ ， $ B $ 型号汽车各一辆时进价分别为多少万元？</p><p>(2)因资金紧张，该公司计划以不超过260万元购进以上两种型号的新能源汽车共15辆，每辆 $ A $ 型汽车在进价的基础上提高7000元销售，每辆 $ B $ 型汽车在进价的基础上提高 $ 6\\% $ 销售．假如这些新能源汽车全部售出，至少要获利12.5万元，该公司有哪几种购进方案？哪种方案获得的利润最多，最多利润是多少？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆朝高 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-04-15", "keyPointIds": "16441|16490", "keyPointNames": "其他问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579473654632722432", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "579473654632722432", "title": "重庆朝阳中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}, {"id": "567106615091961856", "title": "重庆市育才中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567106640333283328", "questionArticle": "<p>4．解下列方程组：</p><p>(1) $ \\begin{cases} x+2y=0 \\\\ 3x+4y=6 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x } { 2 }-\\dfrac { y+1 } { 3 }=1 \\\\ 9x+4y=4 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市育才中学校 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567106615091961856", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567106615091961856", "title": "重庆市育才中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567106638865276928", "questionArticle": "<p>5．一个四位正整数<i>m</i>，各数位上的数字均不为0，若千位上的数字和百位上的数字之和，等于十位数字与个位数字之差的<i>k</i>倍（<i>k</i>为整数），称<i>m</i>为“<i>k</i>型数”，即例如，4275： $ 4+2=3\\times (7-5) $ ，则4275为“3型数”；3526： $ 3+5=-2\\times (2-6) $ ，则3526为“ $ -2 $ 型数”．</p><p>（1）最小的“2型数”是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>（2）若四位数<i>m</i>是“3型数”， $ m-3 $ 是“ $ -3 $ 型数”，将<i>m</i>的百位数字与十位数字交换位置，得到一个新的四位数 $ m^{′} $ ， $ m^{′} $ 也是“3型数”，求满足条件的<i>m</i>的最大值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市育才中学校 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16444|28551", "keyPointNames": "三元一次方程组的应用|代数推理", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567106615091961856", "questionFeatureName": "阅读材料题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567106615091961856", "title": "重庆市育才中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567106637036560384", "questionArticle": "<p>6．若 $ m $ 使得关于 $ x $ 的不等式组 $ \\begin{cases} 6x-5\\geqslant  m \\\\ \\dfrac { x } { 4 }-\\dfrac { x-1 } { 6 }  <  \\dfrac { 1 } { 2 } \\end{cases}  $ 有至少2个整数解，且关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+y=4 \\\\ x+2y=-3m+2 \\end{cases}  $ 的解满足 $ x-y > 10 $ ，则满足条件的整数 $ m $ 之和是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川内江市第一中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-04-15", "keyPointIds": "16426|16489", "keyPointNames": "二元一次方程组的应用|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575327820416589824", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "575327820416589824", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 11}, {"id": "567106615091961856", "title": "重庆市育才中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567106636201893888", "questionArticle": "<p>7．我市出租车起步价所包含的路程为 $ { { 0 } }\\mathrm{ ∼ }{ { 3 } }{ \\rm{ k } }{ \\rm{ m } } $ ，超过 $ { { 3 } }{ \\rm{ k } }{ \\rm{ m } } $ 的部分按每千米另收费．小江乘坐这种出租车走了 $ { { 7 } }{ \\rm{ k } }{ \\rm{ m } } $ ，付了 $ 18 $ 元；小北乘坐这种出租车走了 $ { { 1 } }{ { 3 } }{ \\rm{ k } }{ \\rm{ m } } $ ，付了 $ 30 $ 元．设这种出租车的起步价为 $ x $ 元，超过 $ { { 3 } }{ \\rm{ k } }{ \\rm{ m } } $ 后每千米收费 $ y $ 元．根据题意，可列方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市育才中学校 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567106615091961856", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567106615091961856", "title": "重庆市育才中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567106625921654784", "questionArticle": "<p>8．下列各式中，为二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 2x+y+3z=4 $　　　　B． $ 4x+3y-9=0 $　　　　C． $ x+\\dfrac { 4 } { y }=6 $　　　　D． $ 3x-xy-20=0 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025重庆重庆市育才中学校 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567106615091961856", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567106615091961856", "title": "重庆市育才中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567105180061179904", "questionArticle": "<p>9．甲乙两地相距480千米，一轮船往返于甲、乙两地之间，顺水行船用20小时，逆水行船用26小时，若设船在静水中的速度为 $ x $ 千米/时，水流速度为 $ y $ 千米/时，则下列方程组中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 20\\left ( { x+y } \\right ) =480 \\\\ 26\\left ( { x-y } \\right ) =480 \\end{cases}  $　　　　B． $ \\begin{cases} 20\\left ( { x+y } \\right ) =480 \\\\ 26\\left ( { x+y } \\right ) =480 \\end{cases}  $</p><p>C． $ \\begin{cases} 20\\left ( { x-y } \\right ) =480 \\\\ 26\\left ( { x-y } \\right ) =480 \\end{cases}  $　　　　D． $ \\begin{cases} 20\\left ( { x-y } \\right ) =480 \\\\ 26\\left ( { x+y } \\right ) =480 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105165871849472", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567105165871849472", "title": "黑龙江省哈尔滨市第十七中学2024−2025学年七年级下学期3月考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567105178375069696", "questionArticle": "<p>10．如果 $ \\begin{cases} x=a， \\\\ y=b \\end{cases}  $ 是方程 $ x-3y=-3 $ 的一组解，那么代数式 $ 5-2a+6b $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．8</p><p>B．5</p><p>C．11</p><p>D．0</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105165871849472", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "567105165871849472", "title": "黑龙江省哈尔滨市第十七中学2024−2025学年七年级下学期3月考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 132, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 132, "timestamp": "2025-07-01T02:16:28.358Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}