{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 161, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "557236852689772544", "questionArticle": "<p>1．为了“践行垃圾分类 $ \\cdot  $ 助力双碳目标”的活动，学校的小亮和小芬一起收集了一些废电池，小亮说：“我比你多收集了5节废电池．”小芬说：“如果你给我6节废电池，此时我的废电池数量就是你的2倍．”如果他们说的都是真的，设小亮收集了 $ m $ 节废电池，小芬收集了 $ n $ 节废电池，根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} m-n=5 \\\\ 2\\left ( { m-6 } \\right ) =n+6 \\end{cases}  $ B． $ \\begin{cases} m-n=5 \\\\ m-6=2\\left ( { n+6 } \\right )  \\end{cases}  $ </p><p>C． $ \\begin{cases} m-n=5 \\\\ 2\\left ( { m-6 } \\right ) =n \\end{cases}  $ D． $ \\begin{cases} m-n=5 \\\\ m+6=2\\left ( { n-6 } \\right )  \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-21", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557236839221862400", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557236839221862400", "title": "湖南省长沙市麓山外国语实验中学2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557236846087938048", "questionArticle": "<p>2．下列算式中，是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x+17=y $ B． $ \\dfrac { 5 } { x{^{2}} }=\\dfrac { 5 } { 9 } $ </p><p>C． $ \\left ( { 2x+1 } \\right ) \\left ( { x-2 } \\right ) =2x{^{2}}+6x-15 $ D． $ x{^{2}}-1=0 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025湖南长沙 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-21", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557236839221862400", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557236839221862400", "title": "湖南省长沙市麓山外国语实验中学2024−2025学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557959336405803008", "questionArticle": "<p>3．一种商品有大、小盒两种包装，3大盒、4小盒共装108瓶，2大盒、3小盒共装76瓶.大盒与小盒各装多少瓶？若设大盒每盒装 $ x $ 瓶 ，小盒每盒装 $ y $ 瓶，则可列方程组得（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;） </p><p>A． $ \\begin{cases} 3x+2y=76 \\\\ 3x+4y=108 \\end{cases}  $ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;B． $ \\begin{cases} 3x+4y=76 \\\\ 2x+3y=108 \\end{cases}  $ </p><p>C． $ \\begin{cases} 3x+4y=108 \\\\ 2x+3y=76 \\end{cases}  $ &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;D． $ \\begin{cases} 3x+2y=76 \\\\ 2x+4y=108 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆七中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-03-20", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557959326305918976", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557959326305918976", "title": "重庆市第七中学校2024−2025年度七年级下学期月考数学试题", "paperCategory": 11}, {"id": "542361230805082112", "title": "四川省成都市青羊区树德实验学校2024−2025学年上学期八年级期末数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557235363871236096", "questionArticle": "<p>4．徽州雪梨产于安徽省歙县，已有数百年的种植历史，皮薄肉厚，汁多味甜，口感细腻，还具有一定的药用价值．某果园现有一批雪梨，计划租用 $ \\mathrm{ A } $ ， $ B $ 两种型号的货车将雪梨运往外地销售，已知满载时，用3辆 $ \\mathrm{ A } $ 型车和2辆 $ B $ 型车一次可运雪梨13吨；用2辆 $ \\mathrm{ A } $ 型车和3辆 $ B $ 型车一次可运雪梨12吨．求1辆 $ \\mathrm{ A } $ 型车和1辆 $ B $ 型车满载时一次分别运雪梨多少吨．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽阜阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-03-20", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557235343059099648", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "557235343059099648", "title": "安徽省阜阳市部分学校2024−2025学年下学期九年级第一次月考数学试题", "paperCategory": 1}, {"id": "554317843698851840", "title": "安徽省阜阳市部分学校2024−2025学年九年级下学期第一次月考数学试题", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "557236197929558016", "questionArticle": "<p>5．春茶是咸丰的支柱产业之一，我县某茶厂清明前生产<i>A</i>、<i>B</i>两种茶叶，若生产10千克<i>A</i>种茶叶和20千克<i>B</i>种茶叶，共需投入成本22000元；若生产20千克<i>A</i>种茶叶和30千克<i>B</i>种茶叶，共需投入成本36000元．</p><p>(1)每千克<i>A</i>，<i>B</i>两种茶叶的生产成本分别是多少元？</p><p>(2)经测算，<i>A</i>种茶叶每千克可获利280元，<i>B</i>种茶叶每千克可获利400元，该厂准备用10万元资金生产这两种茶叶．设生产<i>A</i>种茶叶<i>a</i>千克，总获利为<i>w</i>元，且要求生产<i>A</i>种茶叶量不少于<i>B</i>种茶叶量的2倍，请你设计出总获利最大的生产方案，并求出最大总获利．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建福建省漳州市第三中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16438|16486|16544", "keyPointNames": "和差倍分问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557236172591767552", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "557236172591767552", "title": "福建省漳州市第三中学2024−2025学年九年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557698549045043200", "questionArticle": "<p>6．新年来临，爸爸想送小明一个书包和一双运动鞋作为新年礼物，爸爸对小明说，我在甲商场，乙商场发现同款的运动鞋的单价相同，书包单价也相同，运动鞋和书包单价之和是452元，且运动鞋的单价比书包单价的4倍少8元．</p><p>(1)求运动鞋和书包单价各是多少元？</p><p>(2)恰好两家商场都在搞促销活动，乙商场所有商品打八折销售，甲商场全场购物满100元返购物券30元（不足100元不返券，购物券全场通用），但他们只带了400元，如果只在一家购买两样物品，你能帮助他们选择在哪一家购买吗？若两家都可以选择在哪一家购买更省钱？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557698547396681728", "questionArticle": "<p>7．小明的妈妈在菜市场买回1千克萝卜和0.5千克排骨，准备做萝卜排骨汤，妈妈说，今天买的这两样共花了23.6元，上个星期同等质量的这两样只要17元．爸爸说今天电视新闻上说萝卜每千克上涨了 $ 30\\% $ ，排骨每千克上涨了 $ 40\\% $ ．求今天萝卜和排骨的价格？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557698546675261440", "questionArticle": "<p>8．两个两位数的差是10，在较大的两位数的右边接着写较小的两位数，得到一个四位数；在较大的两位数的左边写上较小的两位数，也得到一个四位数，若这两个四位数的和是5050，求较大的两位数与较小的两位数分别是多少？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557698546016755712", "questionArticle": "<p>9．马四匹牛六头共价白银48两，马三匹牛五头共价白银38两．每匹马，每头牛各价值多少两白银？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557698545291141120", "questionArticle": "<p>10．解方程组：</p><p>(1) $ \\begin{cases} 3x-y=17 \\\\ 2x+3y=4 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 4\\left ( { m-1 } \\right ) =3\\left ( { 2n+3 } \\right )  \\\\ 4m-3n=7 \\end{cases}  $ </p><p>(3)根据不等式的基本性质，把下列不等式化成 $ x $ 大于 $ a $ 或 $ x $ 小于 $ a $ 的形式</p><p>① $ 5x  &lt;  -2 $ &nbsp;&nbsp;&nbsp;&nbsp;</p><p>② $ -5x-4 &gt; 3 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16423|16424|16482", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组|不等式的性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 162, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 162, "timestamp": "2025-07-01T02:20:01.546Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}