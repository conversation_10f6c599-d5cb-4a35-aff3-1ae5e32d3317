{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 194, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "544822029649747968", "questionArticle": "<p>1．为创建“绿色校园”，某校计划分两次购进<i>A</i>，<i>B</i>两种花草，第一次分别购进<i>A</i>，<i>B</i>两种花草30棵和15棵，共花费825元，第二次分别购进<i>A</i>，<i>B</i>两种花草12棵和5棵，共花费325元（两次购进同种花草且价格相同）．</p><p>(1)<i>A</i>，<i>B</i>两种花草每棵的价格分别是多少元；</p><p>(2)若计划购买<i>A</i>，<i>B</i>两种花草共30棵，其中购买<i>A</i>种花草<i>m</i>棵，且 $ m\\geqslant  12 $ ，请你给出一种费用最省的方案，并求该方案所需费用．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西抚州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-12", "keyPointIds": "16437|16544", "keyPointNames": "销售利润问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544822019726024704", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "544822019726024704", "title": "江西省抚州市2024~2025 学年八年级上学期期末数学试题卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544822027389018112", "questionArticle": "<p>2．（1）计算： $ {\\left( { \\sqrt { 2025 }-1 } \\right) ^ {0}}-\\left  | { \\sqrt { 3 }-2 } \\right  | +{\\left( { \\dfrac { 1 } { 5 } } \\right) ^ {-1}}+\\sqrt[3] { -8 } $ ；</p><p>（2）解方程组 $ \\begin{cases} 2x+3y=12 \\\\ 3x+2y=13 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西抚州 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-12", "keyPointIds": "16299|16323|16372|16424", "keyPointNames": "实数的运算|零指数幂|负整数指数幂|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544822019726024704", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "544822019726024704", "title": "江西省抚州市2024~2025 学年八年级上学期期末数学试题卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544316896654434304", "questionArticle": "<p>3．某电器超市销售每台进价分别为160元、120元的A、B两种型号的电风扇，如表是近两周的销售情况：</p><table style=\"border: solid 1px;border-collapse: collapse; width:414.7pt; margin: auto;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 95.75pt; vertical-align: top;\"><p>销售时段</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 190.75pt; vertical-align: top;\"><p>销售数量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 128.15pt; vertical-align: top;\"><p>销售收入</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 95pt; vertical-align: top;\"><p>A种型号</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 95pt; vertical-align: top;\"><p>B种型号</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 95.75pt; vertical-align: top;\"><p>第一周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 95pt; vertical-align: top;\"><p>3台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 95pt; vertical-align: top;\"><p>4台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 128.15pt; vertical-align: top;\"><p>1 200元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 95.75pt; vertical-align: top;\"><p>第二周</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 95pt; vertical-align: top;\"><p>5台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 95pt; vertical-align: top;\"><p>6台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 128.15pt; vertical-align: top;\"><p>1 900元</p></td></tr></table><p>（进价、售价均保持不变，利润 $ = $ 销售收入-进货成本）</p><p>（1） 求A、B两种型号的电风扇的销售单价.</p><p>（2） 若超市准备用不超过7 500元的金额再采购这两种型号的电风扇共50台，求A种型号的电风扇最多能采购多少台.</p><p>（3） 在（2）的条件下，超市销售完这50台电风扇能否实现利润超过1 850元的目标？若能，请给出相应的采购方案；若不能，请说明理由.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544316893173161984", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544316893173161984", "title": "2024−2025学年七年级下册人教版（2024）数学第十一章11.2 一元一次不等式课时2 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544316807403839488", "questionArticle": "<p>4．在方程组 $ \\begin{cases}2x+y=2-3m,\\\\ x+2y=2+m\\end{cases} $ 中，若未知数 $ x $ ， $ y $ 满足 $ x+y &lt; 0 $ ，则 $ m $ 的取值范围是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544316804505575424", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544316804505575424", "title": "2024—2025学年七年级下册人教版（2024）数学第十一章11.2 一元一次不等式课时1 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544316896444719104", "questionArticle": "<p>5．某高速公路施工路段总长为90千米，若甲、乙两工程队合作，6个月可以完成.若甲工程队先做4个月，则剩下的部分乙工程队需要9个月可以完成.已知甲工程队每月施工费用为12万元，乙工程队每月施工费用为9万元.</p><p>（1） 求甲、乙两工程队每月的施工路段长度分别是多少千米.</p><p>（2） 按要求该工程需要在11个月内竣工.由甲工程队先做 $ a $ 个月，剩下的部分由乙工程队来完成.为了保证该工程在要求工期内完成，则甲工程队至少做多少个月？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16431|16486", "keyPointNames": "工程问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544316893173161984", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544316893173161984", "title": "2024−2025学年七年级下册人教版（2024）数学第十一章11.2 一元一次不等式课时2 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544316806812442624", "questionArticle": "<p>6．已知关于 $ x $ , $ y $ 的二元一次方程组 $ \\begin{cases}x+2y=2m+1,\\\\ 2x+y=m+2\\end{cases} $ 的解满足 $ x-y &gt; 2 $ ，则 $ m $ 可取的最大整数值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544316804505575424", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544316804505575424", "title": "2024—2025学年七年级下册人教版（2024）数学第十一章11.2 一元一次不等式课时1 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544316896012705792", "questionArticle": "<p>7．为提升学生身体素质，某校利用课余时间，在八年级开展班级篮球赛，共16个班级参加.</p><p>（1） 比赛积分规定：每场比赛都要分出胜负，胜一场积3分，负一场积1分，某班在15场比赛中获得总积分为39分，求该班胜、负场数分别是多少.</p><p>（2） 投篮评分规则：在三分线外投篮，投中一球可得3分，称为“三分球”;在三分线内（含三分线）投篮，投中一球可得2分,称为“二分球”.某班在其中一场比赛中，共投中27个球（不包括罚球），所得总分不低于58分，求该班在这场比赛中至少投中多少个“三分球”.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16441|16486", "keyPointNames": "其他问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544316893173161984", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544316893173161984", "title": "2024−2025学年七年级下册人教版（2024）数学第十一章11.2 一元一次不等式课时2 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544316895823962112", "questionArticle": "<p>8．刺绣是我国民间传统手工艺.湘绣作为中国四大刺绣之一，闻名中外.在巴黎奥运会倒计时50天之际，某国际旅游公司计划购买A、B两种奥运主题的湘绣作品作为纪念品.已知购买1件A种湘绣作品与2件B种湘绣作品共需要700元，购买2件A种湘绣作品与3件B种湘绣作品共需要1 200元.</p><p>（1） 求A种湘绣作品和B种湘绣作品的单价分别为多少元.</p><p>（2） 该国际旅游公司计划购买A种湘绣作品和B种湘绣作品共200件，总费用不超过50 000元，那么最多能购买A种湘绣作品多少件？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544316893173161984", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544316893173161984", "title": "2024−2025学年七年级下册人教版（2024）数学第十一章11.2 一元一次不等式课时2 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317375589425152", "questionArticle": "<p>9．已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases}x-y=4a-3,\\\\ x+2y=-5a.\\end{cases} $ </p><p>（1） ① 当 $ a=0 $ 时，该方程组的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>②  $ x $ 与 $ y $ 的数量关系是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>（不含字母 $ a $ ）；</p><p>（2） 是否存在有理数 $ a $ ，使得 $ |x+3|+{y}^{2}=0 $ ？请写出你的思考过程．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317372452085760", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317372452085760", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.2 加减消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "544317375400681472", "questionArticle": "<p>10．解方程组： $ \\begin{cases}\\dfrac{x-1}{2}-\\dfrac{y}{3}=1,\\mathrm{①}\\\\ y+3x=5.\\mathrm{②}\\end{cases} $ </p><p>下面是马虎的解答，你认为他的解法正确吗？若不正确，请给出正确解法.</p><p>解：方程①去分母，得 $ 3(x-1)-2y=1 $ ，即 $ 3x-2y=4.\\mathrm{③} $ </p><p> $ \\mathrm{②}-\\mathrm{③} $ ，得 $ 3y=1 $ ，解得 $ y=\\dfrac{1}{3} $ .</p><p>把 $ y=\\dfrac{1}{3} $ 代入②，得 $ \\dfrac{1}{3}+3x=5 $ ，解得 $ x=\\dfrac{14}{9} $ .</p><p>所以原方程组的解为 $ \\begin{cases}x=\\dfrac{14}{9},\\\\ y=\\dfrac{1}{3}.\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-11", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "544317372452085760", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "544317372452085760", "title": "2024—2025学年七年级下册人教版（2024）数学第十章 10.2.2 加减消元法 课时练习", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 195, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 195, "timestamp": "2025-07-01T02:23:53.479Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}