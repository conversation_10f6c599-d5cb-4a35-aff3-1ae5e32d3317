{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 31, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "588078728992825344", "questionArticle": "<p>1．已知二元一次方程组 $ \\begin{cases} 2m-n=2 \\\\ m-2n=-2 \\end{cases}  $ ，则 $ m+n $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南驻马店 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588078705148207104", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588078705148207104", "title": "2025年河南省驻马店市中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588078598134734848", "questionArticle": "<p>2．《九章算术》是中国传统数学最重要的著作，书中记载：“今有牛五、羊二、直金十二两；牛二、羊五，直金九两、问牛、羊各直金几何？”意思是：“假设有5头牛和2只羊共值金12两，2头牛和5只羊共值金9两．问每头牛、每只羊各值金多少两？”如果按书中记载，1头牛和1只羊一共值金<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>两．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南信阳 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588078572696281088", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588078572696281088", "title": "2025年河南省信阳市部分学校中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588078311382757376", "questionArticle": "<p>3．某校九（1）班级部分学生参加社会实践活动，实践基地有宿舍若干间．如果每间宿舍住4人，那么有2人没有宿舍住；如果每间宿舍住6人，那么会空出一间宿舍．设宿舍有 $ x $ 间，学生有<i>y</i>人，则可列出方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 4x=y-2 \\\\ 6\\left ( { x+1 } \\right ) =y \\end{cases}  $ B． $ \\begin{cases} 4x=y+2 \\\\ 6\\left ( { x-1 } \\right ) =y \\end{cases}  $ C． $ \\begin{cases} 4x=y-2 \\\\ 6\\left ( { x-1 } \\right ) =y \\end{cases}  $ D． $ \\begin{cases} 4x=y+2 \\\\ 6\\left ( { x+1 } \\right ) =y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南安阳 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588078296539115520", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588078296539115520", "title": "2025年河南省安阳中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "588078065130971136", "questionArticle": "<p>4．身体每天消耗的热量主要由碳水化合物和脂肪（不考虑蛋白质及其他有机物）提供．碳水化合物和脂肪分解时所消耗的氧气、生成的二氧化碳、释放的热量三个方面的相关数据如下表：</p><table style=\"border: solid 1px;border-collapse: collapse; width:403.5pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.75pt;\"><p>分解的营养物质</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92.25pt;\"><p>氧气消耗量/克</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 113.25pt;\"><p>二氧化碳生成量/克</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92.25pt;\"><p>释放热量/千焦</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.75pt;\"><p>1克碳水化合物</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92.25pt;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 113.25pt;\"><p>1.5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92.25pt;\"><p>15</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 105.75pt;\"><p>1克脂肪</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92.25pt;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 113.25pt;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 92.25pt;\"><p>45</p></td></tr></table><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/11/2/1/0/0/0/588077972705292309/images/img_22.png\" style=\"vertical-align:middle;\" width=\"275\" alt=\"试题资源网 https://stzy.com\"></p><p>请解答下列问题：</p><p>（1）研究人员测出小祺在某次运动中平均每分钟消耗氧气2.5克，产生二氧化碳3克，求小祺的身体平均每分钟分解碳水化合物与脂肪各多少克．</p><p>（2）已知小祺骑脚踏车每分钟消耗热量20千焦，快走每分钟消耗热量27千焦，小祺某天骑脚踏车和快走共1小时，若要消耗完40克碳水化合物与20克脂肪分解后释放的热量，小祺至少需要分配多少分钟进行快走？（精确到1分钟）</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建福建省厦门双十中学 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16440|16486", "keyPointNames": "表格或图示问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588078029760405504", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588078029760405504", "title": "2025年福建省厦门市双十中学中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589794328953794560", "questionArticle": "<p>5．若 $ {\\left( { 3x+2y-19 } \\right) ^ {2}}+\\left  | { 2x+y-11 } \\right  | =0 $ ，则 $ x+y $ 的平方根是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．8</p><p>B． $ \\pm 8 $</p><p>C． $ \\pm 2\\sqrt { 2 } $</p><p>D． $ 2\\sqrt { 2 } $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川凉山彝族自治州 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16257|16287|16424", "keyPointNames": "绝对值非负性的应用|平方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589794313753636864", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "589794313753636864", "title": "2025年四川省凉山州中考数学真题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "588077745269157888", "questionArticle": "<p>6．为增强学生的劳动意识，养成良好的劳动习惯和品质，某校组织学生到劳动基地参加“耕读累德”实践活动，计划组织学生种植甲、乙两种作物．如果种植3亩甲作物和2亩乙作物需要27名学生，种植4亩甲作物和1亩乙作物需要26名学生．问：种植1亩甲作物和1亩乙作物一共需要多少名学生．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京房山 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588077710682927104", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588077710682927104", "title": "2025年北京市房山区九年级中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588077614075523072", "questionArticle": "<p>7．“寒夜客来茶当酒，竹炉汤沸火初红．”茶作为中国传统文化的重要组成部分，承载着深厚的历史与文化底蕴．某茶馆的店主计划购买三种不同类型的茶叶来丰富茶馆的饮品选择，其中包括龙井茶、普洱茶和茉莉花茶．龙井茶的采购价为每千克700元，普洱茶的采购价为每千克300元，茉莉花茶的采购价为每千克200元．店主计划采购这三种茶叶总共50千克，以满足不同顾客的口味需求．</p><p>（1）设采购龙井茶 $ x $ 千克、普洱茶 $ y $ 千克，请用含 $ x $ ， $ y $ 的代数式填表：</p><table style=\"border: solid 1px;border-collapse: collapse; width:411pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">质量/千克</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">采购总价/元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">龙井茶</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ x $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 700x $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">普洱茶</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ y $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 300y $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">茉莉花茶</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p></td></tr></table><p>（2）若店主总共花了15000元，其中采购的普洱茶的质量比龙井茶的2倍多1千克，求店主采购的龙井茶、普洱茶以及茉莉花茶各有多少千克．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽马鞍山 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16304|16438", "keyPointNames": "列代数式|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588077580831469568", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "588077580831469568", "title": "2025年安徽省马鞍山市中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588077233132052480", "questionArticle": "<p>8．已知<i>a</i>，<i>b</i>，<i>c</i>为有理数，且多项式 $ x{^{3}}+ax{^{2}}+bx+c $ 能够写成 $ \\left ( { x{^{2}}+3x-4 } \\right ) \\left ( { x-\\dfrac { c } { 4 } } \\right )  $ 的形式．</p><p>（1）求 $ 4a+c $ 的值．</p><p>（2）若<i>a</i>，<i>b</i>，<i>c</i>为整数，且 $ c\\geqslant  a &gt; 1 $ ，试求<i>a</i>，<i>b</i>，<i>c</i>的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建泉州七中 · 临考冲刺", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-16", "keyPointIds": "16353|16443|16489", "keyPointNames": "因式分解的应用|解三元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588077200202571776", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588077200202571776", "title": "2025年福建省泉州市第七中学中考模拟数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587753397593350144", "questionArticle": "<p>9．某蔬菜种植户有甲、乙两块菜地，甲菜地去年收获 $ x{ \\rm{ k } }{ \\rm{ g } } $ 西蓝花，乙菜地去年收获 $ y{ \\rm{ k } }{ \\rm{ g } } $ 西蓝花，今年在县技术专家的帮助下，甲菜地增收 $ 10\\% $ ，乙菜地增收 $ 15\\% $ ．</p><p>（1）今年两块菜地共收获<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ k } }{ \\rm{ g } } $ 西蓝花；（用含 $ x $ ， $ y $ 的代数式表示）</p><p>（2）若去年两块菜地共收获 $ { { 1 } }{ { 0 } }{ { 0 } }{ { 0 } }{ { 0 } }{ \\rm{ k } }{ \\rm{ g } } $ 西蓝花，今年共收获 $ { { 1 } }{ { 1 } }{ { 2 } }{ { 0 } }{ { 0 } }{ \\rm{ k } }{ \\rm{ g } } $ 西蓝花，求甲、乙两块菜地今年分别收获多少千克西蓝花．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽合肥 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-15", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587753373975224320", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "587753373975224320", "title": "2025年安徽省合肥市第二十九中学中考第三次模拟数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "587753255460970496", "questionArticle": "<p>10．解方程组： $ \\begin{cases} 2x-y=-5 \\\\ 4x+3y=10 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江绍兴 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-15", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587753230488084480", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "587753230488084480", "title": "浙江省绍兴市绍初教育集团2024−2025学年下学期5月九年级大单元教学效果检测数学试题（二模）", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 32, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 32, "timestamp": "2025-07-01T02:04:35.026Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}