{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 49, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "584434058022658048", "questionArticle": "<p>1．小龙和小刚两人玩“打弹珠”游戏，小龙对小刚说：“把你珠子的一半给我，我就有10颗珠子”，小刚却说：“只要把你的 $ \\dfrac { 1 } { 3 } $ 给我，我就有10颗”，如果设小刚的弹珠数为<i>x</i>颗，小龙的弹珠数为<i>y</i>颗，则列出方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+2y=10 \\\\ 3x+y=30 \\end{cases}  $  <img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/01/2/1/0/0/0/584434007972032524/images/img_14.png\" style=\"vertical-align:middle;\" alt=\"试题资源网 https://stzy.com\">B． $ \\begin{cases} x+2y=10 \\\\ 3x+y=10 \\end{cases}  $  C． $ \\begin{cases} x+2y=20 \\\\ 3x+y=10 \\end{cases}  $ D． $ \\begin{cases} x+2y=20 \\\\ 3x+y=30 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-06-03", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584434042499538944", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "584434042499538944", "title": "2025年湖北省中考适应性考试数学试题", "paperCategory": 1}, {"id": "478353162966966272", "title": "广东省湛江市霞山区滨海学校2022−2023学年八年级上学期开学考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "584850587893669888", "questionArticle": "<p>2．若 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ 是关于<i>x</i>，<i>y</i>的二元一次方程 $ ax-y=3 $ 的一个解，则 $ a $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -1 $</p><p>B．1</p><p>C． $ -2 $</p><p>D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京35中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 3, "createTime": "2025-06-03", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584850574408982528", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584850574408982528", "title": "北京市第三十五中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "561681088331948032", "title": "北京市海淀区师达中学2024—2025学年下学期七年级数学第一次月考试卷", "paperCategory": 1}, {"id": "456233675333607424", "title": "北京市海淀区2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "584850707422949376", "questionArticle": "<p>3．解方程组： $ \\begin{cases} x+y=4 \\\\ x-2y=1 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000|350000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东莞中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 3, "createTime": "2025-06-03", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584850683985178624", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584850683985178624", "title": "广东省东莞市东城区东莞中学2024—2025学年下学期七年级数学期中考试试卷", "paperCategory": 1}, {"id": "208357112941944832", "title": "北京市燕山区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "160704610071519232", "title": "福建省建瓯市芝华中学2020年7月九年级中考模拟数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581949772233220096", "questionArticle": "<p>4．《九章算术》是我国古代数学的经典著作，书中有一个问题：“今有黄金九枚，白银一十一枚，称之重适等．交易其一，金轻十三两．问金、银一枚各重几何？”．意思是：甲袋中装有黄金9枚（每枚黄金重量相同），乙袋中装有白银11枚（每枚白银重量相同），称重两袋相等．两袋互相交换1枚后，甲袋比乙袋轻了13两（袋子重量忽略不计）．问黄金、白银每枚各重多少两？设每枚黄金重<i>x</i>两，每枚白银重<i>y</i>两，试求黄金、白银每枚各重多少两？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽合肥 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-03", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581949748992581632", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581949748992581632", "title": "安徽省合肥市庐阳中学2025年 中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "583055310723522560", "questionArticle": "<p>5．2025年春节凸显了我国在机器人领域的强大实力，随着人工智能与物联网等技术的快速发展，人形机器人的应用场景不断拓展，某快递企业为提高工作效率，拟购买 $ A、B $ 两种型号智能机器人进行快递分拣，相关信息如下：</p><p>信息一</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\"><i>A</i>型机器人台数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\"><i>B</i>型机器人台数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 117pt;\"><p style=\"text-align:center;\">总费用（单位：万元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 117pt;\"><p style=\"text-align:center;\">260</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 84.05pt;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 117pt;\"><p style=\"text-align:center;\">360</p></td></tr></table><p>信息二</p><table style=\"border: solid 1px;border-collapse: collapse; width:212.25pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 212.25pt;\"><p><i>A</i>型机器人每台每天可分拣快递33万件；</p><p><i>B</i>型机器人每台每天可分拣快递27万件．</p></td></tr></table><p>（1）求 $ A、B $ 两种型号智能机器人的单价；</p><p>（2）现该企业准备购买 $ A、B $ 两种型号智能机器人共10台．需要每天分拣快递不少于300万件，且购买总费用最少，应如何选用这两种型号机器人？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南洛阳 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-06-03", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583055275147436032", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "583055275147436032", "title": "2025年河南省洛阳市第二次中招模拟考试数学试卷", "paperCategory": 1}, {"id": "590679611270279168", "title": "2025年河南省郑州枫杨外国语学校中考四模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581949766226976768", "questionArticle": "<p>6．若 $ a-b+c=5 $ ， $ a+b+c=-3 $ ，则 $ c{^{2}}-ab $ 的值满足（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．小于0B．小于或等于0</p><p>C．大于0D．大于或等于0</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽合肥 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-03", "keyPointIds": "16332|16424", "keyPointNames": "完全平方公式|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581949748992581632", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581949748992581632", "title": "安徽省合肥市庐阳中学2025年 中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "580620492979085312", "questionArticle": "<p>7．2025年春节档，电影《哪吒之魔童闹海》掀起观影热潮，影片通过粒子水墨技术、动态水墨渲染引擎等技术，将传统水墨画意境融入 $ 3D $ 动画，打造出兼具古典神韵与现代视觉冲击力的场景，形成独特的文化辨识度，向全球展示了“既古老又充满活力的中国形象”． 影片将封神神话中的角色（如哪吒、敖丙）赋予现代价值观，使传统文化符号与当代人民心理形成共振．某文创店果断订购了印有“哪吒”图案和“敖丙”图案的两种书签．经统计，订购30张“哪吒”书签与20张“敖丙”书签，成本共计430元；而订购45张“哪吒”书签和25张“敖丙”书签，则需花费605元．</p><p>（1）求“哪吒”、“敖丙”两种书签每张的进价分别是多少元？</p><p>（2）该文创店计划购进“哪吒”、“敖丙”两种书签共90张， “哪吒”种书签的购进数量不超过“敖丙”种书签数量 $ \\dfrac { 4 } { 5 } $ ，已知“哪吒”、“敖丙”两种书签的销售单价分别为15元和12元，如何规划购买方案，才能使文具店在这批书签全部售出后获得最大利润？最大利润是多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-03", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580620467360276480", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "580620467360276480", "title": "2025年广东省深圳市初中学业水平测试数学模拟练习试卷（二）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580244091994157056", "questionArticle": "<p>8．解方程组：</p><p>（1） $ \\begin{cases} y=x+1 \\\\ x+y=3 \\end{cases}  $ </p><p>（2） $ \\begin{cases} 3x+y=1 \\\\ x-2y=12 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-03", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580244070536097792", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580244070536097792", "title": "福建省厦门市松柏中学2024−2025学年下学期七年级数学期中考卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580244089758593024", "questionArticle": "<p>9．若关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} 3x+2y=7m \\\\ x-y=-m \\end{cases}  $ 的解满足 $ 4x+y=18 $ ，则 $ m $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-06-03", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580244070536097792", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580244070536097792", "title": "福建省厦门市松柏中学2024−2025学年下学期七年级数学期中考卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580244081948798976", "questionArticle": "<p>10．已知 $ \\{\\hspace{-0.5em}  \\begin{array} {} x=-1 \\\\ y=-2 \\end{array} \\hspace{-0.5em} ，\\{\\hspace{-0.5em}  \\begin{array} {} x=1 \\\\ y=2 \\end{array} \\hspace{-0.5em}  $ 是二元一次方程 $ a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } $ 的两个解， $ \\{\\hspace{-0.5em}  \\begin{array} {} x=1 \\\\ y=2 \\end{array} \\hspace{-0.5em} ，\\{\\hspace{-0.5em}  \\begin{array} {} x=-1 \\\\ y=-3 \\end{array} \\hspace{-0.5em}  $ 是二元一次方程 $ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } $ 的两个解，则二元一次方程组 $ \\{\\hspace{-0.5em}  \\begin{array} {} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{array} \\hspace{-0.5em}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ B． $ \\begin{cases} x=-1 \\\\ y=3 \\end{cases}  $ C． $ \\begin{cases} x=-1 \\\\ y=-2 \\end{cases}  $ D． $ \\begin{cases} x=1 \\\\ y=-2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建厦门 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-03", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580244070536097792", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580244070536097792", "title": "福建省厦门市松柏中学2024−2025学年下学期七年级数学期中考卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 50, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 50, "timestamp": "2025-07-01T02:06:45.127Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}