{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 129, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "566750024358469632", "questionArticle": "<p>1．某校开设了内容丰富的社团活动，其中“编织中国结”社团为布置校园环境组织学生编织<i>A</i>、<i>B</i>两种中国结．已知编织1个<i>A</i>种中国结和2个<i>B</i>种中国结需用绳 $ 180{ \\rm{ c } }{ \\rm{ m } } $ ；编织2个<i>A</i>种中国结和3个<i>B</i>种中国结需用绳 $ 310{ \\rm{ c } }{ \\rm{ m } } $ ．</p><p>(1)求编织1个<i>A</i>种、1个<i>B</i>种中国结分别需要绳子的长度．</p><p>(2)为满足校园环境的布置需求，需要编织两种中国结共100个，其中<i>A</i>种中国结的数量不少于<i>B</i>种中国结数量的一半．当编织<i>A</i>种中国结多少个时所需的总用绳量最少？最少用绳量是多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-17", "keyPointIds": "16441|16486|16547", "keyPointNames": "其他问题|一元一次不等式的应用|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "566749994159480832", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "566749994159480832", "title": "福建省厦门市湖里中学2024−2025学年下学期 第一次月考九年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567977054928412672", "questionArticle": "<p>2．为实现乡村振兴战略，解决山区老百姓优质土特产销售问题，某地政府帮助小强家开通了网络商店（简称“网店”），将红枣，小米等土特产迅速销往全国，已知相关的销售信息如下：今年前3个月，该网店销售了红枣和小米共3000kg，获得利润4.2万元．问：这3个月该网店销售红枣和小米多少袋？</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p style=\"text-align:center;\">&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">红枣</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">小米</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p style=\"text-align:center;\">规格／（kg/袋）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">2</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p style=\"text-align:center;\">成本／（元／袋）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">38</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p style=\"text-align:center;\">售价／（元／袋）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">60</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">54</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-17", "keyPointIds": "16437|16438", "keyPointNames": "销售利润问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567977030626615296", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567977030626615296", "title": "2025年吉林省长春市净月区九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567976470091440128", "questionArticle": "<p>3．某工厂准备在开学前生产甲、乙两种型号的开学文具礼盒共 $ 60 $ 万套．甲礼盒的成本为 $ 15 $ 元/套；乙礼盒的成本为 $ 20 $ 元/套．该工厂计划筹集资金 $ 1100 $ 万元，且全部用于生产甲、乙两种礼盒，则这两种礼盒各生产多少万套？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽安庆 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-17", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567976442736189440", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567976442736189440", "title": "2025年安徽省安庆市中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567976612714553344", "questionArticle": "<p>4．《九章算术》中记载了这样一个问题：“今有五雀六燕，集称之衡，雀俱重，燕俱轻．一雀一燕交而处，衡适平．并雀、燕重一斤．”其可译为“有5只麻雀、6只燕子，分别在衡上称量之，麻雀在一起重，燕子在一起轻．将1只麻雀、1只燕子交换，衡恰好平衡．麻雀与燕子合起来共重1斤（1斤等于16两）．”设雀、燕每只各重<i>x</i>，<i>y</i>两，则下列说法<point-tag>错误</point-tag>的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．依题意 $ 5x+6y=16 $</p><p>B．依题意 $ 4x+y=5y+x $</p><p>C．依题意 $ 5x > 6y $</p><p>D．一只燕的重量是 $ 1\\dfrac { 3 } { 19 } $ 两</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北唐山 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-17", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567976599120814080", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567976599120814080", "title": "2025年河北省唐山市九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567845501028048896", "questionArticle": "<p>5．《九章算术》中有一道题目，“今有共买豕，人出一百，盈一百；人出九十，适足，问人数、豕价各几何？”其大意为，几人合资买猪，若每人出 $ 100 $ 钱，则买完猪后，多出 $ 100 $ 钱，若每人出 $ 90 $ 钱，恰好能买到猪．若我们设共 $ x $ 人，猪价为 $ y $ 钱，那么可以列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 100x-y=100 \\\\ y=90x \\end{cases}  $　　　　B． $ \\begin{cases} y-100x=100 \\\\ y=90x \\end{cases}  $　　　　C． $ \\begin{cases} y-90x=100 \\\\ y=100x \\end{cases}  $　　　　D． $ \\begin{cases} 90x-y=100 \\\\ y=100x \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津河北 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-17", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567845486855495680", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567845486855495680", "title": "2025年天津市河北区九年级下学期第一次模拟考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567478194640560128", "questionArticle": "<p>6．若关于 $ x,y $ 的方程组 $ \\begin{cases} 3x+2y=4 \\\\ 2x+y=m-1 \\end{cases}  $ 的解互为相反数，则 $ m $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东济宁 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-16", "keyPointIds": "16252|16424", "keyPointNames": "相反数的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567478176118513664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567478176118513664", "title": "2025年山东省济宁市中考数学多校联考一模试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567478187304722432", "questionArticle": "<p>7．为了奖励进步较大的学生，某班决定购买甲、乙、丙三种钢笔作为奖品，其单价分别为4元、5元、6元，购买这些钢笔需要花60元；经过协商，每种钢笔单价下降1元，结果只花了48元，那么甲种钢笔可能购买（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）．</p><p>A．11支B．9支C．7支D．5支</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东济宁 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-16", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567478176118513664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567478176118513664", "title": "2025年山东省济宁市中考数学多校联考一模试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567477329812824064", "questionArticle": "<p>8．某校准备带领九年级同学参加物理和化学的实验考试，需要准备甲，乙两种手套，学校计划前往商场购买．通过调查，将获取的相关数据整理如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>购买数量（单位：副）</p></td><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>总费用</p><p>（单位：元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>甲种手套</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>乙种手套</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>35</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>130</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>29</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>178</p></td></tr></table><p>(1)甲种手套，乙种手套每副各多少元？</p><p>(2)该学校决定购买甲乙两种手套共1000副，且总费用不超过2350元，那么该中学最少可以购买甲种手套多少副？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁铁岭 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-04-16", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567477306513465344", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567477306513465344", "title": "2025年辽宁省铁岭市中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "566749447905910784", "questionArticle": "<p>9．在东莞市全力推进“百县千镇万村高质量发展工程”的背景下，荔枝产业蓬勃发展，鲜果畅销全国．某商贩看准商机，购进了一批桂味和糯米糍荔枝．已知购进桂味3千克、糯米糍1千克共需 $ 90 $ 元，购进桂味1千克、糯米糍2千克共需60元．</p><p>(1)每千克桂味和糯米糍的进价分别是多少元？</p><p>(2)该商贩决定购进桂味和糯米糍荔枝共100千克，投入资金不超过2040元，请问桂味最多可购进多少千克？将桂味的售价定为每千克40元，糯米糍的售价定为每千克30元，按照桂味的最大购进量，请算出该商贩把全部荔枝售出时获得的总利润．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东莞中 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-16", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "566749424241647616", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "566749424241647616", "title": "2025年广东省东莞市东莞中学九年级中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105195156480000", "questionArticle": "<p style=\"text-align: justify;\">10．阅读材料并回答下列问题：已知 $m$ ， $n$ 都是实数，且满足 $m-n=17$ ，就称点 $T\\left ( { m-1,2n+1 } \\right )$ 为“卓越点”．例如：点 $P\\left ( { 4,2 } \\right )$ ，令 $\\begin{cases} m-1=4 \\\\ 2n+1=2 \\end{cases}$ 得 $\\begin{cases} m=5 \\\\ n=\\dfrac { 1 } { 2 } \\end{cases}$ ， $m-n=\\dfrac { 9 } { 2 }\\ne 17$ ，所以 $P\\left ( { 4,2 } \\right )$ 不是“卓越点”，点 $Q\\left ( { 20,9 } \\right )$ ，令 $\\begin{cases} m-1=20 \\\\ 2n+1=9 \\end{cases}$ 得 $\\begin{cases} m=21 \\\\ n=4 \\end{cases}$ ， $m-n=21-4=17$ ，所以 $Q\\left ( { 20,9 } \\right )$ 是“卓越点”．</p><p>（1）请判断点 $A\\left ( { 22,13 } \\right )$ ， $B\\left ( { 27,10 } \\right )$ 是否为“卓越点”，并说明理由．</p><p>（2）以关于 $x$ ， $y$ 的方程组 $\\begin{cases} x-y=3 \\\\ 3x+y=s \\end{cases}$ 的解为坐标的点 $F\\left ( { x,y } \\right )$ 是“卓越点”，求 $s$ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "42", "showQuestionTypeName": "综合题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-16", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105165871849472", "questionFeatureName": "阅读材料题|新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567105165871849472", "title": "黑龙江省哈尔滨市第十七中学2024−2025学年七年级下学期3月考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 130, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 130, "timestamp": "2025-07-01T02:16:14.752Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}