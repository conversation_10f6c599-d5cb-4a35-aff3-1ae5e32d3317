{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 104, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "572596837762244608", "questionArticle": "<p>1．《孙子算经》是中国古代最重要的数学著作，约成书于四、五世纪．现在的传本共三卷，卷上叙述算筹记数的纵横相间制度和筹算乘除法；卷中举例说明筹算分数算法和筹算开平方法；卷下记录算题，不但提供了答案，而且还给出了解法．其中记载：“今有木，不知长短．引绳度之，余绳四尺五寸，屈绳量之，不足一尺．木长几何？”译文：“用一根绳子去量一根长木，绳子还剩余4.5尺，将绳子对折再量长木，长木还剩余1尺，问木长多少尺？”设绳子长<i>x</i>尺，木长<i>y</i>尺，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/01/2/1/0/0/0/573117204511629313/images/img_1.png\" style='vertical-align:middle;' alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} x-y=4.5 \\\\ \\dfrac { 1 } { 2 }x=y+1 \\end{cases}  $</p><p>B． $ \\begin{cases} x-y=4.5 \\\\ \\dfrac { 1 } { 2 }x=y-1 \\end{cases}  $</p><p>C． $ \\begin{cases} y-x=4.5 \\\\ \\dfrac { 1 } { 2 }y=x+1 \\end{cases}  $</p><p>D． $ \\begin{cases} x-y=4.5 \\\\ \\dfrac { 1 } { 2 }y=x-1 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西柳州 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-04-30", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572596820309745664", "questionFeatureName": "数学文化题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "572596820309745664", "title": "2025年广西壮族自治区柳州市中考二模数学试题", "paperCategory": 1}, {"id": "209994876129157120", "title": "河北省石家庄市行唐县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "572226512738361344", "questionArticle": "<p>2．解方程组 $ \\begin{cases} 2x+y=4 \\\\ x-y=2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东珠海 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226492811223040", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572226492811223040", "title": "广东省珠海部分学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572226507780694016", "questionArticle": "<p>3．二元一次方程 $ 2x+y=5 $ 中，当 $ x=2 $ 时， $ y= $  <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025广东珠海 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226492811223040", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572226492811223040", "title": "广东省珠海部分学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572226505209585664", "questionArticle": "<p>4．下列方程组中，是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-y=5 \\\\ y+z=3 \\end{cases}  $ B． $ \\begin{cases} x-3y=2 \\\\ \\dfrac { 1 } { y }+x=5 \\end{cases}  $ C． $ \\begin{cases} x-y=3 \\\\ 3x-y=1 \\end{cases}  $ D． $ \\begin{cases} x+y=7 \\\\ x{^{2}}-y{^{2}}=7 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025广东珠海 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226492811223040", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572226492811223040", "title": "广东省珠海部分学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "572226959939248128", "questionArticle": "<p>5．综合实践</p><table style=\"border: solid 1px;border-collapse: collapse; width:474.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p>背景</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 451.5pt;\"><p>亚运会期间，小明所在的班级开展知识竞赛，需要去商店购买<i>A</i>，<i>B</i>两种款式的亚运盲盒作为奖品．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p>素材1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 263.25pt;\"><p>某商店在无促销活动时，若买 $ 15 $ 个<i>A</i>款亚运盲盒， $ 10 $ 个<i>B</i>款亚运盲盒，共需 $ 230 $ 元；若买 $ 25 $ 个<i>A</i>款亚运盲盒， $ 25 $ 个<i>B</i>款亚运盲盒，共需 $ 450 $ 元．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/02/2/1/0/0/0/573493933340139520/images/img_1.png\" style='vertical-align:middle;' width=\"158\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p>素材2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 451.5pt;\"><p>该商店龙年迎新春促销活动：用 $ 35 $ 元购买会员卡成为会员后，凭会员卡购买商店内任何商品，一律按商品价格的8折出售（已知小明在此之前不是该商店的会员）；线上淘宝店促销活动：购买商店内任何商品，一律按商品价格的9折出售且包邮．</p></td></tr><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 474.75pt;\"><p>问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p>任务1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 451.5pt;\"><p>某商店在无促销活动时，求<i>A</i>款亚运盲盒和<i>B</i>款亚运盲盒的销售单价各是多少元？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p>任务2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 451.5pt;\"><p>小明计划在促销期间购买<i>A</i>，<i>B</i>两款盲盒共 $ 40 $ 个，其中<i>A</i>款盲盒<i>m</i>个（ $ 0  <  m  <  40 $ ），</p><p>若在线下商店购买，共需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元；</p><p>若在线上淘宝店购买，共需要<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．（均用含<i>m</i>的代数式表示）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 23.25pt;\"><p>任务3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 451.5pt;\"><p>请你帮小明算一算，在任务2的条件下，购买<i>A</i>款盲盒的数量在什么范围内时，线下购买方式更合算？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河北保定 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-04-29", "keyPointIds": "16304|16437|16490", "keyPointNames": "列代数式|销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575776451527483392", "questionFeatureName": "综合与实践题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "575776451527483392", "title": "河北省保定市第十七中学2024−2025学年八年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "572226932865015808", "title": "河南省实验中学2024−2025学年八年级下学期数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572226821074231296", "questionArticle": "<p>6．解方程组：</p><p>(1) $ \\begin{cases} 2x-y=-4 \\\\ 5x+4y=3 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} x+y=20 \\\\ 4x+2y=60 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南开封 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226800136265728", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572226800136265728", "title": "河南省开封市金明中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572226684289589248", "questionArticle": "<p>7．目前，近几年来，新能源汽车在中国已然成为汽车工业发展的主流趋势，某汽车制造厂开发了一款新式电动汽车，计划一年生产安装288辆．由于抽调不出足够的熟练工来完成新式电动汽车的安装，工厂决定招聘一些新工人．他们经过培训后上岗，也能独立进行电动汽车的安装．生产开始后，调研部门发现：2名熟练工和1名新工人每月可安装10辆电动汽车；3名熟练工和2名新工人每月可安装16辆电动汽车．</p><p>(1)每名熟练工和新工人每月分别可以安装多少辆电动汽车？</p><p>(2)如果工厂抽调<i>n</i>（0＜<i>n</i>＜5）名熟练工，使得招聘的新工人和抽调的熟练工刚好能完成一年的安装任务，那么工厂有哪几种新工人的招聘方案？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄市第四十中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16420|16441", "keyPointNames": "二元一次方程的解|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226659476086784", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572226659476086784", "title": "河北省 石家庄市第四十中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572226680921563136", "questionArticle": "<p>8．解二元一次方程组：</p><p>(1) $ \\begin{cases} y=2x-3 \\\\ 3x+2y=8 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} m-n=1 \\\\ 2m+3n=7 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄市第四十中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226659476086784", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572226659476086784", "title": "河北省 石家庄市第四十中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572226815978151936", "questionArticle": "<p>9．若关于 $ x,y $ 的方程 $ 2x{^{\\left  | { m } \\right  | }}+\\left ( { m-1 } \\right ) y=3 $ 是二元一次方程，则 $ m $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南开封 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226800136265728", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572226800136265728", "title": "河南省开封市金明中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572226674512666624", "questionArticle": "<p>10．学校计划采购一批白色和黄色乒乓球，若购买白色乒乓球3盒、黄色乒乓球2盒，共需34元；若购买白色乒乓球2盒、黄色乒乓球3盒，共需36元，通过设适当的未知量可列出方程组 $ \\begin{cases} 3x+2y=34① \\\\ 2x+3y=36② \\end{cases}  $ ，若用 $ { \\rm{ ① } }-{ \\rm{ ② } } $ 可得 $ x-y=-2 $ ，下列关于“ $ x-y=-2 $ ”的意义解释正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．每盒白色乒乓球比黄色乒乓球贵2元　　　　B．白色乒乓球比黄色乒乓球多买了2盒</p><p>C．每盒白色乒乓球比黄色乒乓球便宜2元　　　　D．白色乒乓球比黄色乒乓球少买了2盒</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄市第四十中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-29", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226659476086784", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572226659476086784", "title": "河北省 石家庄市第四十中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 105, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 105, "timestamp": "2025-07-01T02:13:14.965Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}