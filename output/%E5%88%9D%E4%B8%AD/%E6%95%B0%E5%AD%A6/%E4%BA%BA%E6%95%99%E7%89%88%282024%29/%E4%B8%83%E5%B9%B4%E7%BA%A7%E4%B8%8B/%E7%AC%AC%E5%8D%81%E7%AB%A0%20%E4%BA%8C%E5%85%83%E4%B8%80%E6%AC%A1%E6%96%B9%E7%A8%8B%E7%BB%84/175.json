{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 174, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "555512405494636544", "questionArticle": "<p>1．下列是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} \\dfrac { 1 } { x }+y=4 \\\\ x-y=1 \\end{cases}  $ B． $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ C． $ \\begin{cases} 2x-y=1 \\\\ 3y+z=2 \\end{cases}  $ D． $ \\begin{cases} 5x+y=2 \\\\ xy=1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-03-14", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}, {"id": "427737444303282176", "title": "浙江省杭州市西湖区弘益中学2023-2024学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555082957112778752", "questionArticle": "<p>2．中国清代算书《御制数理精蕴》中有这样一题:“马四匹、牛六头,共价四十八两(‘两’为我国古代货币单位);马二匹、牛五头,共价三十八两.问马、牛各价几何?”设马每匹<i>x</i>两,牛每头<i>y</i>两,根据题意可列方程组为&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}4x+6y=38,\\\\ 2x+5y=48\\end{cases} $ B． $ \\begin{cases}4x+6y=48,\\\\ 2x+5y=38\\end{cases} $ </p><p>C． $ \\begin{cases}4x+6y=48,\\\\ 5x+2y=38\\end{cases} $ D． $ \\begin{cases}4y+6x=48,\\\\ 2y+5x=38\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 3, "createTime": "2025-03-13", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555082952968806400", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555082952968806400", "title": "10.3实际问题与二元一次方程组（练习）", "paperCategory": 10}, {"id": "566992863394832384", "title": "2025年广东省中山市君里学校一模数学试卷", "paperCategory": 11}, {"id": "463457430803881984", "title": "湖北省荆门市2023-2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555086518404030464", "questionArticle": "<p>3．解三元一次方程组: $ \\begin{cases}x+y+z=4,\\\\ x-y+z=8,\\\\ 4x+2y+z=17.\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-03-13", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555086513714798592", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555086513714798592", "title": "10.4三元一次方程组的解法（练习）", "paperCategory": 10}, {"id": "170288833400971264", "title": "2022年八年级上册北师版数学第五章8三元一次方程组课时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555082966491242496", "questionArticle": "<p>4．某隧道长1 200 m,现有一列火车从隧道通过,测得该火车从开始进隧道到完全出隧道共用了70 s,整列火车完全在隧道里的时间是50 s,求火车的速度和长度.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 16, "referenceNum": 2, "createTime": "2025-03-13", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555082952968806400", "questionFeatureName": "生活背景问题", "questionMethodName": "函数与方程思想", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555082952968806400", "title": "10.3实际问题与二元一次方程组（练习）", "paperCategory": 10}, {"id": "202455279753011200", "title": "江苏省连云港市海州区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555082956286500864", "questionArticle": "<p>5．用如图(1)中的长方形和正方形纸板作侧面和底面,做成如图(2)的竖式和横式的两种无盖纸盒.现有<i>m</i>张正方形纸板和<i>n</i>张长方形纸板,如果做两种纸盒若干个(两种都有),恰好将纸板用完,则<i>m</i>+<i>n</i>的值可能是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p style=\"text-align:center;\"><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/12/2/1/0/0/0/555082776715763712/images/img_1.jpg\" style=\"vertical-align:middle;\" width=\"60\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/12/2/1/0/0/0/555082776715763713/images/img_2.jpg\" style=\"vertical-align:middle;\" width=\"106\" alt=\"试题资源网 https://stzy.com\"></p><p>A．2 022&nbsp;&nbsp;B．2 023</p><p>C．2 024&nbsp;&nbsp;D．2 025</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 10, "referenceNum": 2, "createTime": "2025-03-13", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555082952968806400", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555082952968806400", "title": "10.3实际问题与二元一次方程组（练习）", "paperCategory": 10}, {"id": "220677735223435264", "title": "浙江省杭州市杭州外国语学校2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555081603610877952", "questionArticle": "<p>6．根据经营情况,公司对某商品在甲、乙两地的销售单价进行了如下调整:甲地上涨10%,乙地降价5元.已知销售单价调整前甲地比乙地少10元,调整后甲地比乙地少1元.求调整前甲、乙两地该商品的销售单价.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|610000|340000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 3, "createTime": "2025-03-13", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555081592038793216", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555081592038793216", "title": "10.3实际问题与二元一次方程组（诊断）", "paperCategory": 10}, {"id": "326388220073123840", "title": "2023年安徽中考数学真题", "paperCategory": 1}, {"id": "438097984955392000", "title": "2024年陕西省西安市灞桥区铁一中滨河学校中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555081603019481088", "questionArticle": "<p>7．我国古代数学名著《九章算术》记载:“今有牛五、羊二,直金十九两;牛二、羊三,直金十二两.问牛、羊各直金几何?”题目大意是5头牛、2只羊共19两银子;2头牛、3只羊共12两银子,每头牛、每只羊各多少两银子?设1头牛<i>x</i>两银子,1只羊<i>y</i>两银子,则可列方程组为&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}5x+2y=19,\\\\ 2x+3y=12\\end{cases} $ &nbsp;&nbsp;B． $ \\begin{cases}5x+2y=12,\\\\ 2x+3y=19\\end{cases} $ </p><p>C． $ \\begin{cases}2x+5y=19,\\\\ 3x+2y=12\\end{cases} $ &nbsp;&nbsp;D． $ \\begin{cases}2x+5y=12,\\\\ 3x+2y=19\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|510000|410000|430000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 11, "referenceNum": 8, "createTime": "2025-03-13", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555081592038793216", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555081592038793216", "title": "10.3实际问题与二元一次方程组（诊断）", "paperCategory": 10}, {"id": "207238341837758464", "title": "四川省眉山市2022年中考数学真题", "paperCategory": 1}, {"id": "524198729462620160", "title": "湖南省长沙市开福区青竹湖湘一外国语学校2023−2024学年九年级上学期期末数学试题", "paperCategory": 1}, {"id": "467872801816354816", "title": "湖南省长沙市雅礼教育集团联考2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "463458546786541568", "title": "河南省南阳市2023-2024学年七年级下学期期末考试数学试题", "paperCategory": 1}, {"id": "344046808564277248", "title": "2023年湖南省长沙市岳麓区麓山国际实验学校中考模拟数学试题", "paperCategory": 1}, {"id": "460214197453889536", "title": "湖南省株洲市第二中学初中部2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "206707688847220736", "title": "2022年七年级上册沪科版数学第3章3.4二元一次方程组的应用课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555081602293866496", "questionArticle": "<p>8．已知:用2辆A型车和1辆B型车载满货物一次可运货10吨;用1辆A型车和2辆B型车载满货物一次可运货11吨,某物流公司现有26吨货物,计划租用A型车<i>a</i>辆,B型车<i>b</i>辆,一次运完,且恰好每辆车都载满货物(A型车、B型车均要有).根据以上信息,解答下列问题:</p><p>(1)1辆A型车和1辆B型车都载满货物一次可分别运货多少吨?</p><p>(2)请你帮该物流公司设计租车方案.</p><p>(3)若A型车每辆需租金为100元/次,B型车每辆需租金为120元/次.请选出最省钱的租车方案,并求出最少租车费．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|610000|110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 13, "referenceNum": 4, "createTime": "2025-03-13", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555081592038793216", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555081592038793216", "title": "10.3实际问题与二元一次方程组（诊断）", "paperCategory": 10}, {"id": "526524250452172800", "title": "陕西省西安市第三中学名校2023−2024学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "449183571674177536", "title": "北京市清华大学附属中学望京学校2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "401876030409973760", "title": "陕西西安市西咸新区秦汉中学2022-2023年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555081601698275328", "questionArticle": "<p>9．我国古代数学名著《九章算术》中记载:“粟米之法:粟率五十;粝米三十.今有米在十斗桶中,不知其数.满中添粟而舂之,得米七斗.问故米几何?”意思为50斗谷子能出30斗米,即出米率为 $ \\dfrac{3}{5} {\\rm \\mathit{.}} $ 今有米在容量为10斗的桶中,但不知道数量是多少.再向桶中加满谷子,再舂成米,共得米7斗.问原来有米多少斗?如果设原来有米<i>x</i>斗,向桶中加谷子<i>y</i>斗,那么可列方程组为&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}x+y=10,\\\\ x+\\dfrac{3}{5}y=7\\end{cases} $ &nbsp;&nbsp;B． $ \\begin{cases}x+y=10,\\\\ \\dfrac{3}{5}x+y=7\\end{cases} $ &nbsp;&nbsp;C． $ \\begin{cases}x+7=7,\\\\ x+\\dfrac{5}{3}y=10\\end{cases} $ &nbsp;&nbsp;D． $ \\begin{cases}x+y=7,\\\\ \\dfrac{5}{3}x+y=10\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|210000|-1|330000|420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 15, "referenceNum": 7, "createTime": "2025-03-13", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555081592038793216", "questionFeatureName": "数学文化题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555081592038793216", "title": "10.3实际问题与二元一次方程组（诊断）", "paperCategory": 10}, {"id": "207181517549772800", "title": "浙江省宁波市2022年中考数学真题", "paperCategory": 1}, {"id": "430664073409241088", "title": "2024年江苏省南通市部分学校初中一模数学试题", "paperCategory": 1}, {"id": "306920891442569216", "title": "湖北省武汉市一初慧泉中学2022-2023学年九年级下学期三月数学试题", "paperCategory": 1}, {"id": "430010138839261184", "title": "2023年江苏省苏州市常熟市等4地中考一模数学试题", "paperCategory": 1}, {"id": "336829881668378624", "title": "2023年江苏省苏州立达中学中考二模数学试题", "paperCategory": 1}, {"id": "274491037862633472", "title": "辽宁省沈阳市和平区第一三四中学2022-2023学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555078190781210624", "questionArticle": "<p>10．已知 $ \\begin{cases}x=-2,\\\\ y=-8\\end{cases} $ 和 $ \\begin{cases}x=3,\\\\ y=7\\end{cases} $ 是关于<i>x</i>,<i>y</i>的二元一次方程<i>y</i>=<i>kx</i>+<i>b</i>的解,求<i>k</i>,<i>b</i>的值.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-13", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555078178819055616", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555078178819055616", "title": "10.2消元-解二元一次方程组（练习）", "paperCategory": 10}, {"id": "196952766949924864", "title": "北京市第一六一中学2020-2021学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 175, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 175, "timestamp": "2025-07-01T02:21:34.375Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}