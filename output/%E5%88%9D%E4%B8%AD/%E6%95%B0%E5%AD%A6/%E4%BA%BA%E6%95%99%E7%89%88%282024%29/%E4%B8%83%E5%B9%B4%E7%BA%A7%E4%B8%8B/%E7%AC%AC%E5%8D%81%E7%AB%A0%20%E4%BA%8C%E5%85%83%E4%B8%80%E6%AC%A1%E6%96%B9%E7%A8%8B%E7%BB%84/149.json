{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 148, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "562994048937533440", "questionArticle": "<p>1．某商场用相同的价格分两次购进<i>A</i>型和<i>B</i>型两种型号的电脑，前两次购进情况如下表．</p><table style=\"border: solid 1px;border-collapse: collapse; width:348pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p><i>A</i>型（台）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p><i>B</i>型（台）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p>总进价（元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p>第一次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p>20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p>30</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p>210000</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p>第二次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p>10</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p>20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p>130000</p></td></tr></table><p>(1)求该商场购进<i>A</i>型和<i>B</i>型电脑的单价各为多少元？</p><p>(2)已知商场<i>A</i>型电脑的标价为每台4000元，<i>B</i>型电脑的标价为每台6000元，两种电脑销售一半后，为了促销，剩余的<i>A</i>型电脑打九折，<i>B</i>型电脑打八折全部销售完，问两种电脑商场获利多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-05", "keyPointIds": "16437|28548", "keyPointNames": "销售利润问题|有理数混合运算的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562994025243910144", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "562994025243910144", "title": "河北省石家庄市第九中学2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562994045766639616", "questionArticle": "<p>2．解下列方程组：</p><p>(1) $ \\begin{cases} 2x+y=10 \\\\ x=2y \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 2x+3y=12 \\\\ 2x-y=4 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-05", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562994025243910144", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "562994025243910144", "title": "河北省石家庄市第九中学2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562994039760396288", "questionArticle": "<p>3．若方程组 $ \\begin{cases} a{{}_{ 1 } }x+b{{}_{ 1 } }y=c{{}_{ 1 } } \\\\ a{{}_{ 2 } }x+b{{}_{ 2 } }y=c{{}_{ 2 } } \\end{cases}  $ 的解是 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ ，则方程组 $ \\begin{cases} 3a{{}_{ 1 } }x+2b{{}_{ 1 } }y=3c{{}_{ 1 } } \\\\ 3a{{}_{ 2 } }x+2b{{}_{ 2 } }y=3c{{}_{ 2 } } \\end{cases}  $ 的解是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=\\dfrac { 1 } { 2 } \\\\ y=\\dfrac { 2 } { 3 } \\end{cases}  $ B． $ \\begin{cases} x=\\dfrac { 2 } { 3 } \\\\ y=\\dfrac { 1 } { 2 } \\end{cases}  $ C． $ \\begin{cases} x=1 \\\\ y=3 \\end{cases}  $ D． $ \\begin{cases} x=3 \\\\ y=1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-05", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562994025243910144", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "562994025243910144", "title": "河北省石家庄市第九中学2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562994038967672832", "questionArticle": "<p>4．为打造沙滨公园风光带，准备修建一段长为140米的人行步道．该任务由<i>A</i>，<i>B</i>两个工程小组先后接力完成，<i>A</i>工程小组每天修建12米，<i>B</i>工程小组每天修建8米，共用时16天．设<i>A</i>工程小组共修建人行步道<i>x</i>米，<i>B</i>工程小组共修建人行步道<i>y</i>米，依题意，可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=140 \\\\ \\dfrac { x } { 8 }+\\dfrac { y } { 12 }=16 \\end{cases}  $ B． $ \\begin{cases} x+y=16 \\\\ 8x+12y=140 \\end{cases}  $ C． $ \\begin{cases} x+y=16 \\\\ 12x+8y=140 \\end{cases}  $ D． $ \\begin{cases} x+y=140 \\\\ \\dfrac { x } { 12 }+\\dfrac { y } { 8 }=16 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-05", "keyPointIds": "16431", "keyPointNames": "工程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562994025243910144", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "562994025243910144", "title": "河北省石家庄市第九中学2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562994037185093632", "questionArticle": "<p>5．由方程组 $ \\begin{cases} x+3m=-4 \\\\ y-3=m \\end{cases}  $ 可得出 $ x $ 与 $ y $ 之间的关系是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x+3y=-12 $ B． $ x+3y=5 $ C． $ x+3y=-5 $ D． $ x-3y=-5 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-05", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562994025243910144", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "562994025243910144", "title": "河北省石家庄市第九中学2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562994035339599872", "questionArticle": "<p>6．代数式 $ kx+b $ 中，当 $ x $ 取值分别为 $ -1 {\\rm ，0，1，2} $ 时，对应代数式的值如表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 39.25pt;\"><p style=\"text-align:center;\"> $ x $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p style=\"text-align:center;\">…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 25.15pt;\"><p style=\"text-align:center;\"> $ -1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p style=\"text-align:center;\">0</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p style=\"text-align:center;\">…</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 39.25pt;\"><p style=\"text-align:center;\"> $ kx+b $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p style=\"text-align:center;\">…</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 25.15pt;\"><p style=\"text-align:center;\"> $ -1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 17.25pt;\"><p style=\"text-align:center;\">5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 22.5pt;\"><p style=\"text-align:center;\">…</p></td></tr></table><p>则 $ 2k+b $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -1 $ B．1C．3D．5</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-05", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562994025243910144", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "562994025243910144", "title": "河北省石家庄市第九中学2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562994031774441472", "questionArticle": "<p>7．解方程组<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/03/2/1/0/0/0/562993999310528513/images/img_2.png\" style=\"vertical-align:middle;\" width=\"92\" alt=\"试题资源网 https://stzy.com\">，用加减法消去<i>y</i>，需要（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．①×2﹣②B．①×3﹣②×2C．①×2+②D．①×3+②×2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025河北石家庄 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-05", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562994025243910144", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "562994025243910144", "title": "河北省石家庄市第九中学2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562993715591028736", "questionArticle": "<p>8．我国古代《四元玉鉴》中记载“二果问价”问题，其内容大致如下：用九百九十九文钱，可买甜果苦果共一千个，若…，…，试问买甜苦果各几个？若设买甜果<i>x</i>个，买苦果<i>y</i>个，列出符合题意的二元一次方程组： $ \\begin{cases} x+y=1000 \\\\ \\dfrac { 11 } { 9 }x+\\dfrac { 4 } { 7 }y=999 \\end{cases}  $ ．根据已有信息，题中用“…，…”表示的缺失的条件应为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．甜果七个用四文钱，苦果九个用十一文钱</p><p>B．甜果十一个用九文钱，苦果四个用七文钱</p><p>C．甜果四个用七文钱，苦果十一个用九文钱</p><p>D．甜果九个用十一文钱，苦果七个用四文钱</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-05", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562993702471245824", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "562993702471245824", "title": "福建省厦门市松柏中学2024−2025学年九年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562993217089609728", "questionArticle": "<p>9．某小区计划购买 $ 10 $ 台健身器材供小区居民锻炼使用，了解到购买 $ 1 $ 台 $ B $ 型健身器材比购买 $ 1 $ 台 $ A $ 型健身器材贵 $ 200 $ 元，购买 $ 2 $ 台 $ A $ 型健身器材和 $ 5 $ 台 $ B $ 型健身器材共花 $ 8000 $ 元．</p><p>(1)  $ A $ 型健身器材和 $ B $ 型健身器材的单价各是多少元？</p><p>(2)该小区计划购买 $ B $ 型健身器材的数量不得超过 $ A $ 型健身器材，购买资金不低于 $ 10800 $ 元．请问共有几种购买方案？哪一种方案最省钱？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽蚌埠 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-05", "keyPointIds": "16438|16489", "keyPointNames": "和差倍分问题|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562993196898230272", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "562993196898230272", "title": "安徽省蚌埠市初中教联体2024−2025学年七年级下学期月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562773597472727040", "questionArticle": "<p>10．已知下列表格中的每组 $ x，y $ 的值分别是关于 $ x，y $ 的二元一次方程 $ ax+b=y $ 的解，则关于 $ x $ 的不等式 $ ax+b\\geqslant  0 $ 的解集为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><table style=\"border: solid 1px;border-collapse: collapse; width:208.5pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ x $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ \\cdots  $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ -3 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ -2 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ -1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 0 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ \\cdots  $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ y $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ \\cdots  $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ -1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 0 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 2 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 3 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ \\cdots  $ </p></td></tr></table><p>&nbsp;</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西太原师范学院附中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-05", "keyPointIds": "16420|16424|16485", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562773577402982400", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562773577402982400", "title": "山西省太原市师范学院附属中学2024−2025学年八年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 149, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 149, "timestamp": "2025-07-01T02:18:30.854Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}