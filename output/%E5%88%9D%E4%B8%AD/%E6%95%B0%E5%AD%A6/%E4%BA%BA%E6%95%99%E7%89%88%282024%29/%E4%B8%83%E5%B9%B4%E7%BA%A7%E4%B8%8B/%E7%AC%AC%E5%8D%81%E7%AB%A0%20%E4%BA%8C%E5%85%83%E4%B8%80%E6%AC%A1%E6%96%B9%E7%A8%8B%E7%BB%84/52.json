{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 51, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "583056871646343168", "questionArticle": "<p>1．下列各组是二元一次方程 $ x+3y=14 $ 的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=2 \\\\ y=4 \\end{cases}  $ B． $ \\begin{cases} x=3 \\\\ y=4 \\end{cases}  $ </p><p>C． $ \\begin{cases} x=5 \\\\ y=2 \\end{cases}  $ D． $ \\begin{cases} x=5 \\\\ y=4 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东执信中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-02", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583056859315089408", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583056859315089408", "title": "广东省广州市执信中学2024-2025学年七年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "583056323593416704", "questionArticle": "<p>2．我国古代数学著作《九章算术》中记载了这样一道题：今有牛五、羊二，直金十两；牛二、羊五，直金八两．问牛、羊各直金几何？意思是：假设5头牛、2只羊，共值金10两；2头牛、5只羊，共值金8两．那么每头牛、每只羊分别值金多少两？设每头牛和每只羊分别值金 $ x $ 两和 $ y $ 两，则可列方程为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+2y=8 \\\\ 2x+5y=10 \\end{cases}  $ B． $ \\begin{cases} 5x+5y=10 \\\\ 2x+2y=8 \\end{cases}  $ </p><p>C． $ \\begin{cases} 2x+2y=10 \\\\ 5x+5y=8 \\end{cases}  $ D． $ \\begin{cases} 5x+2y=10 \\\\ 2x+5y=8 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津西青 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-02", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583056307608924160", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "583056307608924160", "title": "2025年天津市西青区九年级二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "583056176511754240", "questionArticle": "<p>3．《算法统宗》是我国古代著名的数学典籍，其中有一道题：我问开店李三公，众客都来到店中，一房七客多七客，一房九客一房空，问房几间？客几何？意思是：李三公家开店，来了一批客人，一个房间住7位客人则多出7位客人，一个房间住9位客人则多出1个房间，问李三公家的店有多少个房间？来了多少位客人？设李三公家的店有 $ x $ 个房间，来了 $ y $ 位客人，则可以列出的方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 7x-7=y \\\\ 9\\left ( { x-1 } \\right ) =y \\end{cases}  $ B． $ \\begin{cases} 7x-7=y \\\\ 9\\left ( { x+1 } \\right ) =y \\end{cases}  $ C． $ \\begin{cases} 7x+7=y \\\\ 9\\left ( { x-1 } \\right ) =y \\end{cases}  $ D． $ \\begin{cases} 7x+7=y \\\\ 9\\left ( { x+1 } \\right ) =y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津滨海新 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-02", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "583056158664990720", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "583056158664990720", "title": "2025年天津市滨海新区中考二模考试数学试题（二）", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "581950874622144512", "questionArticle": "<p>4．已知 $ \\begin{cases} x+2y=4k \\\\ 2x+y=2k+1 \\end{cases}  $ 的解满足<i>y</i>﹣<i>x</i>＜1，则<i>k</i>的取值范围是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．<i>k</i>＞0B．<i>k</i>＜0C．<i>k</i>＜1D．<i>k</i>＜﹣ $ \\dfrac { 1 } { 2 } $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川内江 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950860290207744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950860290207744", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "581950892699594752", "questionArticle": "<p>5．某公司有甲、乙两种型号的客车共 $ 20 $ 辆，它们的载客量、每天的租金如表所示．已知在这 $ 20 $ 辆客车都坐满的情况下，共载客 $ 720 $ 人．</p><table style=\"border: solid 1px;border-collapse: collapse; width:186.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>甲型客车</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>乙型客车</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>载客量（人/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 30 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 45 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>日租金（元/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 450 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ 600 $ </p></td></tr></table><p>（1）求甲、乙两种型号的客车各有多少辆？</p><p>（2）某中学计划租用甲、乙两种型号的客车共 $ 10 $ 辆，接送七年级的师生到基地参加暑期社会实践活动，已知该中学租车的总费用不超过 $ 5600 $ 元．</p><p>①至少要租用多少辆甲型客车？</p><p>②若七年级的师生共有 $ 370 $ 人，请写出所有可能的租车方案，并确定最省钱的租车方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川内江 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16434|16438|16490", "keyPointNames": "方案问题|和差倍分问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950860290207744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950860290207744", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581950888874389504", "questionArticle": "<p>6．若 $ m $ 使得关于 $ x $ 的不等式 $ \\begin{cases} 6x-5\\geqslant  m \\\\ \\dfrac { x } { 4 }-\\dfrac { x-1 } { 6 }  &lt;  \\dfrac { 1 } { 2 } \\end{cases}  $ 至少2个整数解，且关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+y=4 \\\\ x+2y=-3m+2 \\end{cases}  $ 的解满足 $ x-y &gt; 10 $ ，则满足条件的整数 $ m $ 之和是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川内江 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16426|16489", "keyPointNames": "二元一次方程组的应用|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950860290207744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950860290207744", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581950886303281152", "questionArticle": "<p>7．某商场出售甲、乙、丙三种型号的商品，若购买甲2件，乙3件，丙1件，共需130元；购买甲3件，乙5件，丙1件，共需205元．若购买甲，乙，丙各1件，则需<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川内江 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950860290207744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950860290207744", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581950885577666560", "questionArticle": "<p>8．为落实“五育并举”，绿化美化环境，学校在劳动周组织学生到校园周边种植甲、乙两种树苗．已知购买甲种树苗3棵，乙种树苗2棵共需12元，；购买甲种树苗1棵，乙种树苗3棵共需11元．</p><p>（1）求每棵甲、乙树苗的价格．</p><p>（2）本次活动共种植了200棵甲、乙树苗，假设所种的树苗若干年后全部长成了参天大树，并且平均每棵树的价值（含生态价值，经济价值）均为原来树苗价的100倍，要想获得不低于5万元的价值，请问乙种树苗种植数量不得少于多少棵？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川内江 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950860290207744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950860290207744", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581950884852051968", "questionArticle": "<p>9．已知关于 $ x,y $ 的方程组 $ \\begin{cases} 3x+y=3a+9 \\\\ x-y=5a+7 \\end{cases}  $ 的解均为非负数，</p><p>（1）用 $ a $ 的代数式表示方程组的解；</p><p>（2）求 $ a $ 的取值范围；</p><p>（3）化简： $ \\left  | { 2a+4 } \\right  | -\\left  | { a-1 } \\right  |  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川内江 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16256|16424|16489", "keyPointNames": "化简绝对值|加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950860290207744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950860290207744", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "581950879441399808", "questionArticle": "<p>10．方程 $ \\left ( { m-2 } \\right ) x-y{^{\\left  | { m-3 } \\right  | }}=1 $ 是关于<i>x</i>，<i>y</i>的二元一次方程，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025四川内江 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-01", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581950860290207744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "581950860290207744", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 52, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 52, "timestamp": "2025-07-01T02:06:57.617Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}