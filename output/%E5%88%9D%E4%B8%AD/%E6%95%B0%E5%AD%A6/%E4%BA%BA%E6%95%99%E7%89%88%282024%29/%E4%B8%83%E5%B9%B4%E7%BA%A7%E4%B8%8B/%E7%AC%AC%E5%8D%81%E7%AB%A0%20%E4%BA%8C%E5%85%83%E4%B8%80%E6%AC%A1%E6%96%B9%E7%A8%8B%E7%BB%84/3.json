{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 2, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "594704211268378624", "questionArticle": "<p>1．下列方程组中，是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} xy=1 \\\\ x+2y=3 \\end{cases}  $ B． $ \\begin{cases} x=2 \\\\ 3y-x=1 \\end{cases}  $ </p><p>C． $ \\begin{cases} \\dfrac { 1 } { x }+\\dfrac { 1 } { y }=1 \\\\ x+y=1 \\end{cases}  $ D． $ \\begin{cases} x+z=2 \\\\ x+y=3 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|410000|440000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 7, "referenceNum": 4, "createTime": "2025-06-30", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "594704201038471168", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "594704201038471168", "title": "黑龙江省哈尔滨市第十七中学校2024−2025学年七年级下学期6月月考数学试卷", "paperCategory": 1}, {"id": "205448651270823936", "title": "广东省江门市第二中学2021-2022学年八年级上学期开学考试数学试题", "paperCategory": 1}, {"id": "178086138573266944", "title": "河南省南阳市第三中学2021-2022学年七年级下学期开学考试数学试题", "paperCategory": 1}, {"id": "160081234185461760", "title": "黑龙江省哈尔滨市哈尔滨德强学校2020-2021学年七年级下学期4月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "593322701697101824", "questionArticle": "<p>2．新华商场购进<i>A</i>品牌台灯400台，<i>B</i>品牌台灯若干台，其中<i>A</i>品牌台灯的进货单价比<i>B</i>品牌台灯的进货单价多3元/个．</p><p>（1）若进货款是16200元，且购进<i>B</i>品牌台灯600台，</p><p>①<i>A</i>、<i>B</i>两种品牌台灯的进货单价各是多少？</p><p>②已知<i>A</i>品牌台灯的售价为23元/个，若使这批台灯全部售完后利润不低于5000元，<i>B</i>品牌台灯的销售单价最少是多少元？</p><p>（2）若<i>B</i>品牌台灯进货价为20元/个，第1个月<i>B</i>品牌台灯以24元/个的价格售出 $ \\dfrac { 1 } { 3 } {\\rm ，\\mathit{A}} $ 品牌台灯以25元/个售出的数量是<i>B</i>品牌台灯售出数量的一半；第2个月以22元/个的价格将这批台灯全部售出，最后获利超过3700元，则<i>B</i>品牌台灯至少进多少个？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16437|16438|16486", "keyPointNames": "销售利润问题|和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593322677009428480", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "593322677009428480", "title": "江苏省南京师范大学附属中学树人学校2024—2025学年下学期6月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593322698261966848", "questionArticle": "<p>3．解方程组：</p><p>（1） $ \\begin{cases} 2x-y=-4 \\\\ 4x-5y=-23 \\end{cases}  $ </p><p>（2） $ \\begin{cases} x+2y+z=1 \\\\ x+y=-1 \\\\ 2y=4 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593322677009428480", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "593322677009428480", "title": "江苏省南京师范大学附属中学树人学校2024—2025学年下学期6月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593322691261673472", "questionArticle": "<p>4．已知 $ \\begin{cases} x=3 \\\\ y=-2 \\end{cases}  $ 是方程 $ x+my=6 $ 的一个解，则<i>m</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-30", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593322677009428480", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "593322677009428480", "title": "江苏省南京师范大学附属中学树人学校2024—2025学年下学期6月月考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592871341373562880", "questionArticle": "<p>5．已知 $ \\left ( { m-1 } \\right ) x+y{^{\\left  | { m } \\right  | }}=4 $ 是关于 $ x $ 、 $ y $ 二元一次方程，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川宜宾 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-06-30", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592871319680622592", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592871319680622592", "title": "四川省宜宾市第二中学校2024−2025学年七年级下学期期末模拟考试数学试题", "paperCategory": 1}, {"id": "579473351652978688", "title": "天津市第一中学2024—2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593322599830036480", "questionArticle": "<p>6．（1）解方程组 $ \\begin{cases} x+2y=4 \\\\ 2x+3y=1 \\end{cases}  $ </p><p>（2）解不等式组 $ \\begin{cases} 6x+15 &gt; 2\\left ( { 4x+3 } \\right ) ① \\\\ \\dfrac { 2x-1 } { 3 }\\geqslant  \\dfrac { 1 } { 2 }x-\\dfrac { 2 } { 3 }② \\end{cases}  $ ．把解集在数轴上表示出来，并找出最小整数解．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/26/2/1/0/0/0/593322506867482626/images/img_11.png\" style=\"vertical-align:middle;\" width=\"289\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏钟英中学 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-29", "keyPointIds": "16424|16489|28266", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组|在数轴上表示不等式的解集", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593322575096225792", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "593322575096225792", "title": "江苏省南京秦淮区钟英中学2024—2025学年下学期期末考试七年级数学模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593321874811035648", "questionArticle": "<p>7．某校积极践行阳光体育特色大课间活动，现需购买一批霸王鞭和小皮鼓．已知购进2套霸王鞭和1套小皮鼓共花费70元，购进3套霸王鞭和5套小皮鼓共花费245元．</p><p>（1）求购进的霸王鞭和小皮鼓的单价；</p><p>（2）现需购买这两类运动设备共120套，并且购买霸王鞭的数量要不超过小皮鼓数量的3倍，当购买这两类运动设备各多少套时学校花费最少？最少的费用是多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东华南师大附中 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-29", "keyPointIds": "16438|16486|16535", "keyPointNames": "和差倍分问题|一元一次不等式的应用|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593321847367704576", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "593321847367704576", "title": "广东省广州市华南师范大学附属中学2024−2025学年下学期八年级数学期末模拟卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593319543482658816", "questionArticle": "<p>8．学校组织学生参加户外拓展活动，需准备帐篷和睡袋．已知每顶大帐篷可住8名学生，每顶小帐篷可住5名学生．若租用大帐篷 $ x $ 顶，小帐篷 $ y $ 顶，刚好能住下150名学生，且大帐篷比小帐篷多5顶．则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 8x+5y=150 \\\\ x-y=5 \\end{cases}  $ B． $ \\begin{cases} 5x+8y=150 \\\\ x-y=5 \\end{cases}  $ </p><p>C． $ \\begin{cases} 8x+5y=150 \\\\ y-x=5 \\end{cases}  $ D． $ \\begin{cases} 5x+8y=150 \\\\ y-x=5 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-29", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593319523349999616", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "593319523349999616", "title": "广东省深圳市宝安区外国语学校2025年九年级数学三模试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "593322595543457792", "questionArticle": "<p>9．已知方程组 $ \\begin{cases} 2x+y=5 \\\\ x+2y=4 \\end{cases}  $ ，则 $ x{^{2}}-y{^{2}}= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏钟英中学 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-29", "keyPointIds": "16331|16424", "keyPointNames": "平方差公式|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593322575096225792", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "593322575096225792", "title": "江苏省南京秦淮区钟英中学2024—2025学年下学期期末考试七年级数学模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "593322588643827712", "questionArticle": "<p>10．已知方程组 $ \\begin{cases} ax+by=2 \\\\ cx+dy=3 \\end{cases}  $ 的解是 $ \\begin{cases} x=2 \\\\ y=3 \\end{cases}  $ ，则方程组 $ \\begin{cases} 2ax+b\\left ( { y-1 } \\right ) =2 \\\\ 2cx+d\\left ( { y-1 } \\right ) =3 \\end{cases}  $ 的解为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=1 \\\\ y=4 \\end{cases}  $ B． $ \\begin{cases} x=2 \\\\ y=3 \\end{cases}  $ C． $ \\begin{cases} x=2 \\\\ y=4 \\end{cases}  $ D． $ \\begin{cases} x=4 \\\\ y=2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏钟英中学 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-29", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593322575096225792", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "593322575096225792", "title": "江苏省南京秦淮区钟英中学2024—2025学年下学期期末考试七年级数学模拟试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 3, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 3, "timestamp": "2025-07-01T02:01:08.075Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}