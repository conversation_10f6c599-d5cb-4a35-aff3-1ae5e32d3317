{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 28, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "590300972137619456", "questionArticle": "<p>1．五月的昆明沉浸在一片紫色的花海中，众多游客乘坐“蓝花楹专列”欣赏美景，拍照打卡．某文创销售店看准商机，推出“蓝花楹棉布袋”和“蓝花楹笔记本”两种文创产品．已知销售店老板购进2个“蓝花楹棉布袋”和4个“蓝花楹笔记本”一共需要100元；购进3个“蓝花楹棉布袋”和2个“蓝花楹笔记本”一共需要90元．</p><p>（1）分别求每个“蓝花楹棉布袋”和“蓝花楹笔记本”的进货价格；</p><p>（2）该销售店计划购进两种文创产品共100个，且“蓝花楹棉布袋”的数量不超过“蓝花楹笔记本”数量的一半．若每个“蓝花楹棉布袋”的售价为40元，每个“蓝花楹笔记本”的售价为30元，则购进多少个“蓝花楹棉布袋”时，销售这批文创产品的利润最大？最大利润是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "530000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025云南云大附中 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-18", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590300935907221504", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590300935907221504", "title": "2025年云南大学附属中学初中学业水平考试第三次模拟考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590298672966967296", "questionArticle": "<p>2．商家推出两种纪念品．已知购买 $ 7 $ 个甲种纪念品和买 $ 10 $ 个乙种纪念品的费用相同；每个甲种纪念品的进价比每个乙种纪念品的进价多 $ 3 $ 元．求每个甲种纪念品和每个乙种纪念品的进价．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东中山纪中 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-18", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590298650602938368", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590298650602938368", "title": "2025年广东省中山市纪中教育集团九年级三模联考 数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590467727132762112", "questionArticle": "<p>3．《九章算术》中有一道题，原文是“今有共买物，人出八，盈三；人出七，不足四．问：人数、物价各几何？”译文是假设共同买东西，如果每人出8钱，盈余3钱；每人出7钱，不足4钱．问：人数、物价各多少？设人数为<i>x</i>，物价为<i>y</i>，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y=8x+3 \\\\ y=7x+4 \\end{cases}  $　　　　B． $ \\begin{cases} y=8x-3 \\\\ y=7x+4 \\end{cases}  $　　　　C． $ \\begin{cases} y=8x-3 \\\\ y=7x-4 \\end{cases}  $　　　　D． $ \\begin{cases} y=8x+3 \\\\ y=7x-4 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川广安 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-18", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590467712205234176", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590467712205234176", "title": "2025年四川省广安市中考数学真题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590299962933551104", "questionArticle": "<p>4．有一个三位数，现将它最左边的数字移至最右边所得到的数比原来的数小 $ 144 $ ；而由它的十位数字与个位数字所组成的两位数除以百位数字，商是 $ 7 $ ，余数是 $ 4 $ ．如果设这个三位数的百位为 $ x $ ，十位与个位数字组成的两位数为 $ y $ ，可得方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）个．</p><p>A． $ \\begin{cases} \\left ( { 100x+y } \\right ) -\\left ( { 100y+x } \\right ) =144 \\\\ y=7x+4 \\end{cases}  $ B． $ \\begin{cases} \\left ( { 100x+y } \\right ) -\\left ( { 10y+x } \\right ) =144 \\\\ y=7x+4 \\end{cases}  $ </p><p>C． $ \\begin{cases} \\left ( { 100x+y } \\right ) -\\left ( { 100x+y } \\right ) =144 \\\\ y=7x+4 \\end{cases}  $ D． $ \\begin{cases} \\left ( { 100x+y } \\right ) -\\left ( { 10x+y } \\right ) =144 \\\\ y=7x+4 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁沈阳 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-06-18", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590299945871122432", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590299945871122432", "title": "2025年辽宁省沈阳市第四十三中学中考三模数学试题", "paperCategory": 1}, {"id": "536333980448104448", "title": "山东省青岛大学附属中学2024−2025学年八年级上学期期末数学模拟检测题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590200655437934592", "questionArticle": "<p>5．《九章算术》中记载了这样一道题：牛5头和羊2只共值10金，牛2头和羊5只共值8金，问牛和羊各值多少金？设每头牛值<i>x</i>金，每只羊值<i>y</i>金，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+2y=10 \\\\ 2x+2y=8 \\end{cases}  $　　　　B． $ \\begin{cases} 2x+5y=10 \\\\ 5x+2y=6 \\end{cases}  $　　　　C． $ \\begin{cases} 5x+5y=10 \\\\ 2x+5y=8 \\end{cases}  $　　　　D． $ \\begin{cases} 5x+2y=10 \\\\ 2x+5y=8 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川达州 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590200641114386432", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590200641114386432", "title": "2025年四川省达州市中考数学真题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590201876978638848", "questionArticle": "<p>6．2025年6月5日是第54个“世界环境日”，为打造绿色低碳社区，某社区决定购买甲、乙两种太阳能路灯安装在社区公共区域，升级改造现有照明系统．已知购买1盏甲种路灯和2盏乙种路灯共需220元，购买3盏甲种路灯比4盏乙种路灯的费用少140元．</p><p>（1）求甲、乙两种路灯的单价；</p><p>（2）该社区计划购买甲、乙两种路灯共40盏，且甲种路灯的数量不超过乙种路灯数量的 $ \\dfrac { 1 } { 3 } $ ，请通过计算设计一种购买方案，使所需费用最少．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东烟台 · 中考真题", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590201850902650880", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "590201850902650880", "title": "2025年山东省烟台市中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590199249943437312", "questionArticle": "<p>7．为了建设美好家园，提高垃圾分类意识，某社区决定购买 $ A，B $ 两种型号的新型垃圾桶．现有如下材料：</p><p>材料一：已知购买 $ 3 $ 个 $ A $ 型号的新型垃圾桶和购买 $ 2 $ 个 $ B $ 型号的新型垃圾桶共 $ 380 $ 元；购买 $ 5 $ 个 $ A $ 型号的新型垃圾桶和购买 $ 4 $ 个 $ B $ 型号的新型垃圾桶共 $ 700 $ 元．</p><p>材料二：据统计该社区需购买 $ A，B $ 两种型号的新型垃圾桶共 $ 200 $ 个，但总费用不超过 $ 15300 $ 元，且 $ B $ 型号的新型垃圾桶数量不少于 $ A $ 型号的新型垃圾桶数量的 $ \\dfrac { 2 } { 3 } $ ．</p><p>请根据以上材料，完成下列任务：</p><p>任务一：求 $ A，B $ 两种型号的新型垃圾桶的单价？</p><p>任务二：有哪几种购买方案？</p><p>任务三：哪种方案更省钱，最低购买费用是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川遂宁 · 中考真题", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16434|16490", "keyPointNames": "方案问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590199215944409088", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590199215944409088", "title": "2025年四川省遂宁市中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590205032022192128", "questionArticle": "<p>8．如图，制作甲、乙两种无盖的长方体纸盒，需用正方形和长方形两种硬纸片，且长方形的宽与正方形的边长相等．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/22/2/1/0/0/0/591995415353929729/images/img_1.png\" style='vertical-align:middle;' width=\"426\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）现用200张正方形硬纸片和400张长方形硬纸片，恰好能制作甲、乙两种纸盒各多少个?</p><p>（2）如果需要制作100个长方体纸盒，要求乙种纸盒数量不低于甲种纸盒数量的一半，那么至少需要多少张正方形硬纸片?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏连云港 · 中考真题", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16432|16486|16547", "keyPointNames": "配套问题|一元一次不等式的应用|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590205000703324160", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590205000703324160", "title": "2025年江苏省连云港市中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590198903196131328", "questionArticle": "<p>9．2025年春节期间，我国国产动画电影《哪吒之魔童闹海》刷新了中国电影票房的新纪录，商家推出A，B两款“哪吒”文旅纪念品．已知购进A款200个，B款300个，需花费14000元；购进A款100个，B款200个，需花费8000元．</p><p>（1）求A，B两款“哪吒”纪念品每个进价分别为多少元？</p><p>（2）根据网上预约的情况，如果该商家计划用不超过12000元的资金购进A，B两款“哪吒”纪念品共400个，那么至少需要购进B款纪念品多少个？</p><p>（3）在销售中，该商家发现每个A款纪念品售价60元时，可售出200个，售价每增加1元，销售量将减少5个,设每个A款纪念品售价 $ a\\left ( { 60\\leqslant  a\\leqslant  100 } \\right )  $ 元，<i>W</i>表示该商家销售A款纪念品的利润（单位：元），求<i>W</i>关于<i>a</i>的函数表达式，并求出<i>W</i>的最大值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川内江 · 中考真题", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16437|16486|16565", "keyPointNames": "销售利润问题|一元一次不等式的应用|销售问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590198865048936448", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "590198865048936448", "title": "2025年四川省内江市中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590198622320373760", "questionArticle": "<p>10．我国宋代数学家秦九韶发明的“大衍求一术”阐述了多元方程的解法，大衍问题源于《孙子算经》中“物不知数”问题：“今有物，不知其数，三三数之剩二，五五数之剩三……，问物几何？”意思是：有一些物体不知个数，每3个一数，剩余2个；每5个一数，剩余3个……．问这些物体共有多少个？设3个一数共数了<i>x</i>次，5个一数共数了<i>y</i>次，其中<i>x</i>，<i>y</i>为正整数，依题意可列方程（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 3x+2=5y+3 $　　　　B． $ 5x+2=3y+3 $</p><p>C． $ 3x-2=5y-3 $　　　　D． $ 5x-2=3y-3 $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川南充 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-17", "keyPointIds": "16419|16441", "keyPointNames": "二元一次方程的定义|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590198607376068608", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590198607376068608", "title": "2025年四川省南充市中考数学真题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 29, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 29, "timestamp": "2025-07-01T02:04:12.995Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}