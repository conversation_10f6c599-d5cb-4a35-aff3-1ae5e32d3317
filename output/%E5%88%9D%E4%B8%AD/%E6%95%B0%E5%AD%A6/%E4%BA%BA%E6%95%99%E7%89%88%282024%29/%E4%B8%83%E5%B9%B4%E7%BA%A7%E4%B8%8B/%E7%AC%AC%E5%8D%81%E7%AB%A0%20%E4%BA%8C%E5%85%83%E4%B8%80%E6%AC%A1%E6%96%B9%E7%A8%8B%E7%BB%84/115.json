{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 114, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "569703523052462080", "questionArticle": "<p>1．已知 $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $ <sub> </sub>是方程 $ 2x-ay=3 $ 的一个解，那么 $ a $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 1 $ B． $ 3 $ C． $ -3 $ D． $ -1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|-1|430000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 5, "createTime": "2025-04-25", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703514873569280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "569703514873569280", "title": "河北省 石家庄市第六中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}, {"id": "491398862457839616", "title": "湖南省长沙市一中雨花新华都学校2024−2025学年八年级上学期开学考试数学试题", "paperCategory": 1}, {"id": "197042091737784320", "title": "河北省邢台市信都区第五中学2021-2022学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "220246740925980672", "title": "广东省肇庆市怀集县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "198107997083574272", "title": "2022年七年级下册冀教版数学第六章6.1二元一次方程组课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "569703109754134528", "questionArticle": "<p>2．“一盔一带”安全守护行动是公安部在全国开展的一项安全守护行动，也是营造文明城市，做文明市民的重要标准，“一盔”是指安全头盔，电动自行车驾驶人和乘坐人员应当戴安全头盔，某商场欲购进一批头盔，已知购进 $ 8 $ 个甲型头盔和 $ 6 $ 个乙型头盔需要 $ 630 $ 元，购进 $ 6 $ 个甲型头盔和 $ 8 $ 个乙型头盔需要 $ 700 $ 元．</p><p>(1)购进 $ 1 $ 个甲型头盔和 $ 1 $ 个乙型头盔分别需要多少元？</p><p>(2)若该商场准备购进 $ 200 $ 个这两种型号的头盔，总费用不超过 $ 10200 $ 元，则最多可购进乙型头盔多少个？</p><p>(3)在( $ 2 $ )的条件下，若该商场分别以 $ 58 $ 元 $ / $ 个、 $ 98 $ 元 $ / $ 个的价格销售完甲，乙两种型号的头盔 $ 200 $ 个，能否实现利润超过 $ 6190 $ 元的目标？若能，请给出相应的采购方案；若不能，请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000|450000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽池州 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 3, "createTime": "2025-04-25", "keyPointIds": "16438|16485|16486", "keyPointNames": "和差倍分问题|解一元一次不等式|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569703086958092288", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "569703086958092288", "title": "安徽省池州市2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 1}, {"id": "549096865448370176", "title": "湖南省长沙市北雅中学2024−2025学年九年级下学期入学考试数学试卷", "paperCategory": 1}, {"id": "506585756791513088", "title": "广西壮族自治区南宁市兴宁区第三中学2023−2024学年九年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569342450218606592", "questionArticle": "<p>3．已知 $ x{^{2}}-2x+1+\\left  | { x-y+3 } \\right  | =0 $ ，则 $ x= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川德阳 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-24", "keyPointIds": "16257|16332|16424", "keyPointNames": "绝对值非负性的应用|完全平方公式|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569342430559903744", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "569342430559903744", "title": "2025年四川省德阳市九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569696466576384000", "questionArticle": "<p>4．湖南茶陵是中华茶文化的发源地之一，茶陵县也是中国历史上唯一以茶命名的行政县，相传炎帝神农氏在这里发现了茶，并被称为“茶祖”，湖南不仅在茶文化上有重要地位，其茶叶品种也非常丰富，其所产的君山银针和古丈毛尖更是享誉全国，某茶庄主要经营的茶类有君山银针和古丈毛尖，其中君山银针卖得比较好的是<i>A</i>规格的，古丈毛尖卖得比较好的是<i>B</i>规格的，它们的进价和售价如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">种类</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.7pt;\"><p style=\"text-align:center;\">君山银针<i>A</i>规格</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.7pt;\"><p style=\"text-align:center;\">古丈毛尖<i>B</i>规格</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">进价（元/斤）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.7pt;\"><p style=\"text-align:center;\">160</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.7pt;\"><p style=\"text-align:center;\">500</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p style=\"text-align:center;\">售价（元/斤）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.7pt;\"><p style=\"text-align:center;\">200</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 86.7pt;\"><p style=\"text-align:center;\">600</p></td></tr></table><p>该茶庄计划购进这两种规格的茶共100斤．</p><p>(1)若该茶庄购进这两种规格的茶共花费29600元，求该茶庄购进<i>A</i>，<i>B</i>两种规格的茶各多少斤？</p><p>(2)根据市场销售分析，<i>A</i>规格茶的进货量不低于<i>B</i>规格茶进货量的3倍．问：该茶庄如何进货才能使本次购进的茶全部销售完获得的利润最大？最大利润是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖南 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-24", "keyPointIds": "16438|16535|16544", "keyPointNames": "和差倍分问题|一次函数的图象和性质|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569696440710111232", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "569696440710111232", "title": "2025年湖南省十三市州初中学业水平考试阶段性试题一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570267691568635904", "questionArticle": "<p>5．学校组织部分师生前往“冰雪大世界”开展社会实践活动．据了解，成人票每张240元，学生票按成人票五折优惠．他们一共23人，分别购票共需门票3120元．</p><p>(1)共去了几名老师，几名学生？</p><p>(2)若另有5名老师和24名学生，与这批师生一起去“冰雪大世界”开展社会实践活动，他们上网查到，如果按团体票（30人及以上）购票，每人按成人票六折优惠，请你帮他们算一算，怎样购票更省钱？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川泸州高中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-24", "keyPointIds": "16434|16438", "keyPointNames": "方案问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267665626865664", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267665626865664", "title": "四川省泸州高级中学校2024—2025学年下学期期中考试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570267684119552000", "questionArticle": "<p>6．已知关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} 3x-y=4m+1 \\\\ x+y=2m-5 \\end{cases}  $ 的解满足 $ x-y=4 $ ，则<i>m</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川泸州高中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-24", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267665626865664", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267665626865664", "title": "四川省泸州高级中学校2024—2025学年下学期期中考试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570267591006003200", "questionArticle": "<p>7．为了鼓励市民节约用水，某市居民生活用水按阶梯式水价计费．下表是该市“一户一表”生活用水阶梯式计费价格表的部分信息：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">自来水销售价格</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">污水处理价格</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">每户每月用水量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">单价：元/吨</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">单价：元/吨</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">17吨及以下</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ a $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 0.90 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">超过17吨但不超过30吨的部分</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ b $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 0.90 $ </p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">超过30吨的部分</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 6.00 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"> $ 0.90 $ </p></td></tr></table><p>（说明：①每户生产的污水量等于该户自来水用量；②水费 $ = $ 自来水费用 $ + $ 污水处理费）</p><p>已知小王家 $ 2024 $ 年 $ 7 $ 月用水 $ 15 $ 吨，交水费 $ 40.5 $ 元 $ {\\rm ．} 8 $ 月份用水 $ 26 $ 吨，交水费 $ 79.2 $ 元．</p><p>(1)求 $ a $ ， $ b $ 的值；</p><p>(2)如果小王家 $ 9 $ 月份交水费 $ 149.2 $ 元，则小王家这个月用水多少吨？</p><p>(3)小王家 $ 10 $ 月份忘记了去交水费，当他 $ 11 $ 月去交水费时发现两个月一共用水 $ 52 $ 吨，其中 $ 10 $ 月份用水超过 $ 30 $ 吨，一共交水费 $ 221.2 $ 元，其中包含 $ 30 $ 元滞纳金，求小王家 $ 11 $ 月份用水多少吨？（滞纳金：因未能按期缴纳水费，逾期要缴纳的“罚款金额”）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东淄博实验中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-24", "keyPointIds": "16413|16440", "keyPointNames": "电费、水费问题|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267563923382272", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267563923382272", "title": "山东省 淄博市张店区实验中学2024−2025学年七年级下学期期中考试数学试卷（五四制）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570267479575928832", "questionArticle": "<p>8．国漫之光《哪吒之魔童闹海》已连续创造多项纪录，成为全球动画电影票房榜首．某商家决定购进“哪吒”、“敖丙”两种纪念品进行销售，若购进“哪吒”纪念品1件和“敖丙”纪念品2件共需要70元；若购进“哪吒”纪念品3件和“敖丙”纪念品1件共需要110元．</p><p>(1)求购进“哪吒”、“敖丙”两种纪念品每件各需要多少元？</p><p>(2)该商场计划用不超过3100元的资金购进“哪吒”、“敖丙”两种纪念品共120件，求最多购进“哪吒”纪念品多少件？</p><p>(3)在(2)的条件下，若每件“哪吒”纪念品的售价为40元，每件“敖丙”纪念品的售价为25元，销售完这120件纪念品所获得的利润不低于940元，则该商场有哪些可行的进货方案？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北黄冈 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-04-24", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586882756472254464", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "586882756472254464", "title": "湖北省黄冈市部分学校2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 11}, {"id": "570267452203900928", "title": "山东省潍坊市2024−2025学年八年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "570267679505817600", "questionArticle": "<p>9．如图，用形状、大小完全相同的小长方形墙砖拼成一个大长方形，设每个小长方形墙砖长和宽分别为 $ x{ \\rm{ c } }{ \\rm{ m } } $ 和 $ y{ \\rm{ c } }{ \\rm{ m } } $ ，则依题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/04/2/1/0/0/0/574224267748876288/images/img_1.png\" style='vertical-align:middle;' width=\"165\" alt=\"试题资源网 https://stzy.com\">  </p><p>A． $ \\begin{cases} x+2y=22 \\\\ y=3x \\end{cases}  $　　　　B． $ \\begin{cases} x+2y=22 \\\\ x=3y \\end{cases}  $</p><p>C． $ \\begin{cases} 2x+y=22 \\\\ y=3x \\end{cases}  $　　　　D． $ \\begin{cases} 2x+y=22 \\\\ 5y=22 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川泸州高中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-24", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267665626865664", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267665626865664", "title": "四川省泸州高级中学校2024—2025学年下学期期中考试七年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570267584966205440", "questionArticle": "<p>10．对于有理数 $ x $ ， $ y $ ，定义新运算： $ x\\#y=ax+by $ ， $ x\\oplus y=ax-by $ ，其中 $ a $ ， $ b $ 是常数．已知 $ 1\\#1=1 $ ， $ 3\\oplus 2=8 $ ．</p><p>(1)求 $ a $ ， $ b $ 的值；</p><p>(2)若关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} x{ \\rm{ \\# } }y=4-m \\\\ x\\oplus y=5m \\end{cases}  $ 的解也满足方程 $ x+y=3 $ ，求 $ m $ 的值.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东淄博实验中学 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-24", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570267563923382272", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "570267563923382272", "title": "山东省 淄博市张店区实验中学2024−2025学年七年级下学期期中考试数学试卷（五四制）", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 115, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 115, "timestamp": "2025-07-01T02:14:22.966Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}