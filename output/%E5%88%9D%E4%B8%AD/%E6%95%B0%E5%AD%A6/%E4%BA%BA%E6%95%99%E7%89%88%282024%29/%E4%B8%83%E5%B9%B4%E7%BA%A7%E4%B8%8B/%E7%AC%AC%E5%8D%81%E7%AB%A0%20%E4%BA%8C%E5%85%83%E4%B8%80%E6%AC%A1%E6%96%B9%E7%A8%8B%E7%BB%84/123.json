{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 122, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "568254250154762240", "questionArticle": "<p>1．若方程组 $ \\begin{cases}x-(c+3)xy=3,\\\\ {x}^{a-2}-{y}^{b+3}=4\\end{cases} $ 是关于<i>x</i>,<i>y</i>的二元一次方程组,则代数式<i>a</i>+<i>b</i>+<i>c</i>的值是<u>　　　　</u><i>.&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时1 认识二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568255117884956672", "questionArticle": "<p>2．李明、王超两位同学同时解方程组 $ \\begin{cases}ax+by=2,\\\\ mx-7y=-9,\\end{cases} $ 李明解对了,得 $ \\begin{cases}x=2,\\\\ y=3,\\end{cases} $ 王超抄错了<i>m</i>,得 $ \\begin{cases}x=-2,\\\\ y=-2,\\end{cases} $ 则原方程组中<i>a</i>的值为<u>　　　　</u><i>.&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "期末综合测试《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568254248854528000", "questionArticle": "<p>3．方程 $ {\\rm (\\mathit{m}-1)\\mathit{x}-} {{{y}^{|}}^{m}}^{|} {\\rm =1} $ 是关于<i>x</i>,<i>y</i>的二元一次方程,则<i>m</i>=<u>　　　　</u><i>.&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 14, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时1 认识二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568254248237965312", "questionArticle": "<p>4．下列方程中,是二元一次方程的是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>　　　　　 　　　　　 　　　　　 </p><p>A．<i>y</i>=3<i>x</i>−1B．<i>xy</i>=1</p><p>C． $ {\\rm \\mathit{x}+} \\dfrac{1}{y} {\\rm =2} $ D．<i>x</i>+<i>y</i>+<i>z</i>=1</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.3 课时1 认识二元一次方程组《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568255120116326400", "questionArticle": "<p>5．(1)解方程: $ \\dfrac{1-2x}{7} {\\rm -1=} $  $ \\dfrac{x+3}{3} $ ;</p><p>(2)解方程组: $ \\begin{cases}5n+3m=8,\\\\ 2m-n=1.\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16402|16423", "keyPointNames": "解一元一次方程|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "期末综合测试《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568255205915009024", "questionArticle": "<p>6．已知二元一次方程<i>x</i>+3<i>y</i>=14,请写出该方程的一组整数解:<u>　　　　</u><i>.&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "七年级 · 专题模块", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 0, "questionFeatureName": "开放性试题", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "中考新考向备训《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568255205369749504", "questionArticle": "<p>7．“绿水青山就是金山银山”.科学研究表明:树叶在光合作用后产生的分泌物能够吸附空气中的悬浮颗粒物,具有滞尘净化空气的作用.已知一片银杏树叶一年的平均滞尘量比一片国槐树叶一年的平均滞尘量的2倍少4 mg,若一片国槐树叶与一片银杏树叶一年的平均滞尘总量为62 mg.</p><p>(1)请分别求出一片国槐树叶和一片银杏树叶一年的平均滞尘量;</p><p>(2)娄底市双峰县九峰山森林公园某处有始于唐代的三棵银杏树,据估计三棵银杏树共有约50 000片树叶.问这三棵银杏树一年的平均滞尘总量约多少千克?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 专题模块", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "中考新考向备训《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "567841294757699584", "questionArticle": "<p>8．方程组 $ \\begin{cases} x-y=2 \\\\ x+2y=4 \\end{cases}  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江温州 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567841279935029248", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567841279935029248", "title": "2025年浙江省温州市中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569705156872282112", "questionArticle": "<p>9．从1分、2分、5分3种硬币中取出100枚，总计3元，求其中2分硬币枚数的可能情况有多少种？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西高新一中 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16444|16486", "keyPointNames": "三元一次方程组的应用|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569705131249278976", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "569705131249278976", "title": "陕西省西安高新第一中学2024−2025学年九年级下学期四月份考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569705142922027008", "questionArticle": "<p>10．如图，将钢琴上的12个键依次记为 $ a{{}_{ 1 } } $ ， $ a{{}_{ 2 } } $ ，…， $ a{{}_{ 12 } } $ ．设 $ 1\\leqslant  i  &lt;  j  &lt;  k\\leqslant  12 $ ．若 $ k-j=3 $ 且 $ j-i=4 $ ，则称 $ a{{}_{ i } } $ ， $ a{{}_{ j } } $ ， $ a{{}_{ k } } $ 为原位大三和弦；若 $ k-j=4 $ 且 $ j-i=3 $ ，则称 $ a{{}_{ i } } $ ， $ a{{}_{ j } } $ ， $ a{{}_{ k } } $ 为原位小三和弦．用这12个键可以构成的原位大三和弦与原位小三和弦的个数之和为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/22/2/1/0/0/0/569705112332967936/images/img_9.png\" style=\"vertical-align:middle;\" width=\"266\" alt=\"试题资源网 https://stzy.com\"></p><p>A．5B．8C．10D．15</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西高新一中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569705131249278976", "questionFeatureName": "阅读材料题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "569705131249278976", "title": "陕西省西安高新第一中学2024−2025学年九年级下学期四月份考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 123, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 123, "timestamp": "2025-07-01T02:15:23.080Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}