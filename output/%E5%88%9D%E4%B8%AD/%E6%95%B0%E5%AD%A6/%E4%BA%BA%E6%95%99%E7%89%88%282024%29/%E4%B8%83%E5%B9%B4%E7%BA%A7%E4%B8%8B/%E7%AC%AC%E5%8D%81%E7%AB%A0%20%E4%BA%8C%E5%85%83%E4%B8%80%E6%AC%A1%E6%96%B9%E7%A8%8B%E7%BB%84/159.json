{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 158, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "559472987528798208", "questionArticle": "<p>1．下列方程是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x+1=0 $　　　　B． $ x+y=1 $　　　　C． $ x{^{2}}+x=0 $　　　　D． $ x{^{2}}+y{^{2}}=1 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆七中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-03-25", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559472981002461184", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "559472981002461184", "title": "重庆市第七中学校2024−2025学年七年级下学期第一次数学定时作业", "paperCategory": 1}, {"id": "557959326305918976", "title": "重庆市第七中学校2024−2025年度七年级下学期月考数学试题", "paperCategory": 11}, {"id": "335043677616644096", "title": "重庆市沙坪坝区第七中学校2022-2023学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557198633315115008", "questionArticle": "<p>2． 我国古代数学名著《孙子算经》中有一问题：“今五人共车，两车空；三人共车，八人步.问人与车各几何？”其大意为：现有若干人和车，若每辆车乘坐5人，则空余两辆车；若每辆车乘坐3人，则有8人步行.问人与车各多少？设有<i>x</i>人，<i>y</i>辆车，则所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．  $ \\begin{cases}\\dfrac{x}{5}=y-2\\\\ \\dfrac{x-8}{3}=y\\end{cases} $ B．  $ \\begin{cases}\\dfrac{x}{5}=y+2\\\\ \\dfrac{x+8}{3}=y\\end{cases} $ C．  $ \\begin{cases}\\dfrac{x}{5}=y+2\\\\ \\dfrac{x}{3}+8=y\\end{cases} $ D．  $ \\begin{cases}\\dfrac{x}{5}=y-2\\\\ \\dfrac{x}{3}-8=y\\end{cases} $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏苏州 · 模拟", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-25", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557198622711914496", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557198622711914496", "title": "2025年江苏省苏州市中考数学模拟试卷", "paperCategory": 11}], "questionTypeCode": "1"}, {"questionId": "559118624243359744", "questionArticle": "<p>3．某中学为落实教育部办公厅关于进一步加强中小学生体质管理的通知文件要求，决定增设篮球、足球两门选修课程，为此需要购进一批篮球和足球．已知购买2个篮球和3个足球需要510元,购买3个篮球和5个足球需要810元．根据以上信息解答,</p><p>(1)购买1个篮球和1个足球各需要多少钱？</p><p>(2)学校计划采购篮球，足球共50个，并要求篮球不少于30个，且总费用不超过5500元，则有几种购买方案？哪一种方案所需费用最少？最少费用是多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西安交通大学附属中学分校 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-24", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559118601560563712", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "559118601560563712", "title": "陕西省西安交通大学附属中学分校2024−2025学年下学期八年级3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559118615338852352", "questionArticle": "<p>4．若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+y=4 \\\\ x+2y=-3m+2 \\end{cases}  $ 的解满足 $ x-y > 5 $ ，则<i>m</i>的值可能是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -3 $　　　　B．0　　　　C．1　　　　D．2</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西安交通大学附属中学分校 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-24", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559118601560563712", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "559118601560563712", "title": "陕西省西安交通大学附属中学分校2024−2025学年下学期八年级3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "559118344713969664", "questionArticle": "<p>5．若关于<i>x</i>、<i>y</i>的二元一次方程组 $ \\begin{cases} x-y=2m+1 \\\\ x+3y=3 \\end{cases}  $ 的解满足<i>x</i>+<i>y</i>＞0，则<i>m</i>的取值范围是<u>&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东青岛 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-03-24", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559118328091942912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559118328091942912", "title": "山东省青岛市城阳第八中学2024−2025学年八年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "161816479498805248", "title": "湖北省随州市曾都区2020年九年级升学适应性考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559118232524726272", "questionArticle": "<p>6．解方程或方程组：</p><p>(1) $ x{^{{ { 2 } }}}-{ { 2 } }x-{ { 8 } }{ \\rm{ = } }{ { 0 } } $ ；</p><p>(2) $ \\begin{cases} x+2y=0 \\\\ 3x+4y=6 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏无锡市天一实验中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-24", "keyPointIds": "16424|16456", "keyPointNames": "加减消元法解二元一次方程组|因式分解法解一元二次方程", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559118207035940864", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "559118207035940864", "title": "江苏省无锡市天一实验学校2024−2025学年九年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559118349801660416", "questionArticle": "<p>7．某小区为了绿化环境，计划分两次购进<i>A</i>、<i>B</i>两种花草，第一次分别购进<i>A</i>、<i>B</i>两种花草30棵和15棵，共花费675元；第二次分别购进<i>A</i>、<i>B</i>两种花草12棵和5棵．两次共花费940元（两次购进的<i>A</i>、<i>B</i>两种花草价格均分别相同）．</p><p>（1）<i>A</i>、<i>B</i>两种花草每棵的价格分别是多少元？</p><p>（2）若购买<i>A</i>、<i>B</i>两种花草共31棵，且<i>B</i>种花草的数量少于<i>A</i>种花草的数量的2倍，请你给出一种费用最省的方案，并求出该方案所需费用．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|140000|350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东青岛 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 3, "createTime": "2025-03-24", "keyPointIds": "16434|16543", "keyPointNames": "方案问题|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559118328091942912", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559118328091942912", "title": "山东省青岛市城阳第八中学2024−2025学年八年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "333916693457051648", "title": "山西省大同市平城区2022-2023学年八年级下学期6月月考数学试题", "paperCategory": 1}, {"id": "221582749345292288", "title": "福建省福州市平潭县2021-2022学年八年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557315724273819648", "questionArticle": "<p>8．已知 $ \\begin{cases} x+2y=5 \\\\ 2x+y=4 \\end{cases}  $ ，是关于<i>x</i>，<i>y</i>的二元一次方程组，则 $ 5\\left ( { x+y } \\right ) = $ （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 15 $ B． $ 12 $ C． $ 9 $ D． $ 3 $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北沧州 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-03-23", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557315712068395008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "557315712068395008", "title": "2025年河北省沧州市部分学校九年级 中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557316060564725760", "questionArticle": "<p>9．商场两次购进 $ A、B $ 两款巧克力．第一次购进<i>A</i>款巧克力50件， $ B $ 款巧克力70件，共4300元，第二次购进<i>A</i>款巧克力120件， $ B $ 款巧克力90件，共7200元．</p><p>(1)求 $ A,B $ 两款巧克力的进价各是多少元？</p><p>(2)商场为了尽快将<i>A</i>款巧克力销售完，决定对<i>A</i>款巧克力进行降价销售，当销售单价为每个60元时，每周可以卖出50个，每降1元，每周就可以多卖10个，请问商场将每个<i>A</i>款巧克力降价多少元时，每周销售<i>A</i>款巧克力的利润为2640元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆八中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-23", "keyPointIds": "16438|16463", "keyPointNames": "和差倍分问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557316034161582080", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "557316034161582080", "title": "重庆市第八中学校2024−2025学年九年级下学期定时练习（三）数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557316166256992256", "questionArticle": "<p>10．“阅百十风华，致生涯广大”——我校将迎来办学 $ 110 $ 周年庆活动，文创产品深受校友们的喜爱．某工厂计划生产文创产品“烟雨伞” $ 10000 $ 把，安排甲、乙两车间完成任务，乙车间主产烟雨伞的数量比甲车间生产烟雨伞的数量的 $ 2 $ 倍少 $ 2000 $ 把．</p><p>(1)求甲、乙两车间各生产多少把烟雨伞？</p><p>(2)在生产过程中，乙车间每天生产烟雨伞的数量是甲车间每天生产烟雨伞数量的 $ 1.2 $ 倍，两个车间同时生产，结果甲车间比乙车间提前 $ 4 $ 天完成任务，求甲车间每天生产多少把烟雨伞？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市育才中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-03-23", "keyPointIds": "16438|16476", "keyPointNames": "和差倍分问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557316137857359872", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "557316137857359872", "title": "重庆育才中学教育集团2025年九年级第一次自主作业数学试题（模拟三）", "paperCategory": 1}, {"id": "562781848453029888", "title": "重庆市潼南区2024−2025学年九年级下学期第一次联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 159, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 159, "timestamp": "2025-07-01T02:19:40.392Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}