{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 128, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "568163784591712256", "questionArticle": "<p>1．中国古代的数学著作《孙子算经》中，有这样一道题：“今有三人共车，二车空；二人共车，九人步．问：人与车各几何？”其大意如下：有若干人要坐车，如果每辆车坐3个人，那么就有2辆车空出来；如果每辆车坐2个人，那么就有9个人没车可坐，需步行．假设有<i>x</i>个人，有<i>y</i>辆车，可以获得的方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} \\dfrac { x } { 3 }=y-2 \\\\ \\dfrac { x } { 2 }=y+9 \\end{cases}  $</p><p>B． $ \\begin{cases} \\dfrac { x } { 3 }=y-2 \\\\ \\dfrac { x } { 2 }=y-9 \\end{cases}  $</p><p>C． $ \\begin{cases} x=3\\left ( { y-2 } \\right )  \\\\ x+9=2y \\end{cases}  $</p><p>D． $ \\begin{cases} x=3\\left ( { y-2 } \\right )  \\\\ x-9=2y \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-18", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568163772403064832", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "568163772403064832", "title": "浙江省J12共同体联盟学校2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "568163782314205184", "questionArticle": "<p>2．对于二元一次方程组 $ \\begin{cases} y=x-1① \\\\ x-2y=7② \\end{cases}  $ ，将①式代入②式，消去 $ y $ 可以得到（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x-2x-1=7 $</p><p>B． $ x-2x-2=7 $</p><p>C． $ x-2x+2=7 $</p><p>D． $ x+2x+2=7 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门一中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-04-18", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579859714340593664", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "579859714340593664", "title": "福建省厦门第一中学2024—2025学年七年级下学期数学期中试题", "paperCategory": 1}, {"id": "568163772403064832", "title": "浙江省J12共同体联盟学校2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "568163779952812032", "questionArticle": "<p>3．下列是二元一次方程 $ 3x-2y=-2 $ 的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=1 \\\\ y=1 \\end{cases}  $</p><p>B． $ \\begin{cases} \\,\\,x=-2 \\\\ y=2 \\end{cases}  $</p><p>C． $ \\begin{cases} x=0 \\\\ y=0 \\end{cases}  $</p><p>D． $ \\begin{cases} x=0 \\\\ y=1 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市长寿中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-04-18", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579474623105904640", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "579474623105904640", "title": "重庆市长寿区重庆市长寿中学校2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}, {"id": "568163772403064832", "title": "浙江省J12共同体联盟学校2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "568163490466144256", "questionArticle": "<p>4．秦汉学校组织全体师生参加夏令营活动，现准备租用 $ \\mathrm{ A } $ 、 $ B $ 两种型号的客车（每种型号的客车至少租用一辆），其中 $ \\mathrm{ A } $ 型客车每辆租金500元， $ B $ 型客车每辆租金600元．已知5辆 $ \\mathrm{ A } $ 型客车和2辆 $ B $ 型客车坐满后共载客310人；3辆 $ \\mathrm{ A } $ 型客车和4辆 $ B $ 型客车坐满后共载客340人．</p><p>(1)求每辆 $ \\mathrm{ A } $ 型客车，每辆 $ B $ 型客车坐满后各载客多少人；</p><p>(2)若该校计划租用 $ \\mathrm{ A } $ 型和 $ B $ 型两种客车共10辆，总租金不高于5500元，并能将全校420名师生全部载至目的地，请列举出该校所有的租车方案；并比较哪种租车方案最省钱．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西安 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-18", "keyPointIds": "16438|16489|16490", "keyPointNames": "和差倍分问题|解一元一次不等式组|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568163463530323968", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "568163463530323968", "title": "陕西省西安市西咸新区秦汉中学2024−2025学年八年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567100441357492224", "questionArticle": "<p>5．已知方程组 $ \\begin{cases} 2x-y=1+2a \\\\ x+4y=2+a \\end{cases}  $ 的解满足 $ -1  <  x+y\\leqslant  3 $ ，求<i>a</i>的取值范围<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川第四十九中学校 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-18", "keyPointIds": "16426|16490", "keyPointNames": "二元一次方程组的应用|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567100417777115136", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567100417777115136", "title": "四川省成都市第四十九中学校2024−2025学年下学期八年级3月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568163365136146432", "questionArticle": "<p>6．随着新能源汽车使用的日益普及，各个小区都纷纷完善新能源汽车的配套设施．某小区计划购置如图所示的单枪、双枪两款新能源充电桩，购置充电桩的相关信息如表：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/05/2/1/0/0/0/574614190230380545/images/img_1.png\" style='vertical-align:middle;' width=\"224\" alt=\"试题资源网 https://stzy.com\"></p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 148.5pt;\"><p style=\"text-align:center;\">单枪充电桩数量（单位：个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 148.5pt;\"><p style=\"text-align:center;\">双枪充电桩数量（单位：个）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p style=\"text-align:center;\">总价（单位：元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 148.5pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 148.5pt;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p style=\"text-align:center;\">4400</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 148.5pt;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 148.5pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 96pt;\"><p style=\"text-align:center;\">4600</p></td></tr></table><p>(1)求单枪、双枪两款新能源充电桩的单价；</p><p>(2)如果生产每个单枪充电桩和每个双枪充电桩的时间一样，新能源厂计划制作300个充电桩进行网上销售，为了尽快完成任务，实际平均每天完成的数量是原计划的1.5倍，结果提前5天完成任务，问原计划平均每天制作多少个充电桩?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东济宁 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-18", "keyPointIds": "16437|16476", "keyPointNames": "销售利润问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568163335067181056", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "568163335067181056", "title": "山东省济宁市兖州区多校2024-−2025学年九年级期中联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567102129791344640", "questionArticle": "<p>7．《九章算术》中，一次方程组是由算筹布置而成的．如图1所示的算筹图，表示的方程组就是 $ \\begin{cases} 2x+y=11 \\\\ 4x+3y=27 \\end{cases}  $ ，类似地，图2所示的算筹图表示的方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/14/2/1/0/0/0/567102091719647233/images/img_11.png\" style=\"vertical-align:middle;\" width=\"382\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} 3x+2y=-14 \\\\ x+4y=23 \\end{cases}  $ B． $ \\begin{cases} 3x+2y=-9 \\\\ x+4y=23 \\end{cases}  $ C． $ \\begin{cases} 3x+2y=19 \\\\ x+4y=23 \\end{cases}  $ D． $ \\begin{cases} 3x+2y=19 \\\\ x+4y=3 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000|520000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江金华 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 3, "createTime": "2025-04-18", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567102116717699072", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567102116717699072", "title": "浙江省金华市义乌市七校联考2024−2025学年九年级下学期第一次作业检测数学试题", "paperCategory": 1}, {"id": "470587723989950464", "title": "贵州省安顺市2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "220647145694601216", "title": "陕西省渭南市合阳县2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "568163035728093184", "questionArticle": "<p>8．某商场计划购进甲、乙两种商品，已知购进甲商品 $ 2 $ 件和乙商品 $ 1 $ 件共需 $ 50 $ 元，购进甲商品 $ 1 $ 件和乙商品 $ 2 $ 件共需 $ 70 $ 元．</p><p>(1)求甲、乙两种商品每件的进价分别是多少元？</p><p>(2)商场决定甲商品每件 $ 20 $ 元出售，乙商品每件 $ 50 $ 元出售，为了满足市场需求，需购进甲、乙两种商品共 $ 60 $ 件，且甲商品的数量不少于乙商品数量的 $ 4 $ 倍，请求出获得利润最大的进货方案．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏南外（NFLS） · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-04-18", "keyPointIds": "16424|16438|16535|16544", "keyPointNames": "加减消元法解二元一次方程组|和差倍分问题|一次函数的图象和性质|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "568163010247696384", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "568163010247696384", "title": "江苏省南京市外国语学校2024−2025学年下学期九年级阶段练习数学试题", "paperCategory": 1}, {"id": "431885747316105216", "title": "2023年山东省菏泽市曹县中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "568163475941269504", "questionArticle": "<p>9．关于 $ x,y $ 二元一次方程组 $ \\begin{cases} 3x+y=1+m \\\\ x+y=3 \\end{cases}  $ 的解满足 $ 2x+y  &lt;  1 $ ，则 $ m $ 的取值范围是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ m  &lt;  -2 $ B． $ m &gt; -2 $ C． $ m  &lt;  2 $ D． $ m &gt; 2 $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南河南实验中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-04-18", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "558040427993014272", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "558040427993014272", "title": "河南省实验中学2024−2025学年八年级下学期学业诊断数学试卷", "paperCategory": 1}, {"id": "557200176558940160", "title": "河南省实验中学2024−2025学年八年级下学期学业诊断数学试卷", "paperCategory": 11}, {"id": "568163463530323968", "title": "陕西省西安市西咸新区秦汉中学2024−2025学年八年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "566750812162007040", "questionArticle": "<p>10．某印刷厂每月生产甲、乙两种练习本共40万本且所有练习本当月全部卖出，其中成本、售价如表所示．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>品种</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 51.7pt;\"><p>甲</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 51.7pt;\"><p>乙</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>成本</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 51.7pt;\"><p>1． 2元/本</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 51.7pt;\"><p>0． 4元/本</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p>售价</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 51.7pt;\"><p>1． 6元/本</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 51.7pt;\"><p>0． 6元/本</p></td></tr></table><p>(1)若该印刷厂五月份的利润为11万元，求生产甲、乙两种练习本分别是多少万本；</p><p>(2)某学校计划用7680元的经费到该印刷厂采购练习本，经商讨，该公司同意甲种练习本售价打九折，乙种练习本不能让利：若学校能采购到1万本，且不超支，问最多能购买甲种练习本多少本？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东中山 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 3, "createTime": "2025-04-18", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "566750788451606528", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "566750788451606528", "title": "广东省中山市部分学校2024−2025学年九年级下学期数学3月月考试题", "paperCategory": 1}, {"id": "466842003663265792", "title": "河北省沧州市2023−2024学年七年级下学期期末数学试题（冀教版）", "paperCategory": 1}, {"id": "452592431965446144", "title": "2024年广东省中山市纪中、纪雅、三鑫九年级中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 129, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 129, "timestamp": "2025-07-01T02:16:07.651Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}