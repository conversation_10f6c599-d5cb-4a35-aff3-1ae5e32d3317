{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 191, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "545400809250398208", "questionArticle": "<p>1．对于方程组 $ \\begin{cases} 3x+4y=2① \\\\ 2x-y=5② \\end{cases}  $ 下列变形中错误的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．由①，得 $ x=\\dfrac { 2-4y } { 3 } $ B．由①，得 $ y=\\dfrac { 2-3x } { 4 } $ </p><p>C．由②，得 $ x=\\dfrac { y+5 } { 2 } $ D．由②，得 $ y=2x+5 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|450000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025广西北海 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 11, "referenceNum": 3, "createTime": "2025-02-18", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545400803885883392", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "545400803885883392", "title": "广西壮族自治区北海市2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}, {"id": "425593035457601536", "title": "2023-2024学年七年级下册人教版数学第八章 二元一次方程组单元测试", "paperCategory": 1}, {"id": "158969841818312704", "title": "人教版七年级下册第八章二元一次方程组单元测试", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "546878924241805312", "questionArticle": "<p>2．《九章算术》卷八方程第十题原文为“今有甲、乙二人持钱不知其数，甲得乙半而钱五十，乙得甲太半而亦钱五十．问：甲、乙持钱各几何？”题目大意是，甲、乙两人各带了若干钱．如果甲得到乙所有钱的一半，那么甲共有钱50；如果乙得到甲所有钱的 $ \\dfrac { 2 } { 3 } $ ，那么乙也共有钱50，问：甲、乙两人各带了多少钱？设甲、乙两人持钱的数量分别为<i>x</i>，<i>y</i>，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=50 \\\\ y+\\dfrac { 2 } { 3 }x=50 \\end{cases}  $</p><p>B． $ \\begin{cases} x-\\dfrac { 1 } { 2 }y=50 \\\\ y+\\dfrac { 2 } { 3 }x=50 \\end{cases}  $</p><p>C． $ \\begin{cases} 2x-y=50 \\\\ x-\\dfrac { 2 } { 3 }x=50 \\end{cases}  $</p><p>D． $ \\begin{cases} 2x-y=50 \\\\ x-\\dfrac { 2 } { 3 }y=50 \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|510000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2021四川成都 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 9, "referenceNum": 3, "createTime": "2025-02-18", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "129301279344467968", "questionFeatureName": "数学文化题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "129301279344467968", "title": "四川省成都市2021年中考真题数学试卷", "paperCategory": 1}, {"id": "546878917648359424", "title": "广东省深圳市深圳大学附属中学2024—2025学年八年级上学期期末考试数学试卷", "paperCategory": 1}, {"id": "170286880885350400", "title": "2022年八年级上册北师版数学第五章3应用二元一次方程组——鸡兔同笼课时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "546487872578166784", "questionArticle": "<p>3．计算</p><p>(1) $ \\sqrt { 8 }+2\\sqrt { 18 }-\\sqrt { 32 } $ </p><p>(2) $ \\begin{cases} 2x-3y=-3 \\\\ 2x+3y=23 \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西景德镇 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-17", "keyPointIds": "16299|16423", "keyPointNames": "实数的运算|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "546487866773250048", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "546487866773250048", "title": "江西省景德镇市2024−2025学年八年级上学期1月期末考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "545399729993064448", "questionArticle": "<p>4．解方程（组）：</p><p>(1) $ \\dfrac { 2x+1 } { 3 }-1=\\dfrac { 2-x } { 2 } $ </p><p>(2) $ \\begin{cases} x+2y=6 \\\\ 5x-4y=2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽宣城 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-17", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545399717988966400", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "545399717988966400", "title": "安徽省宣城市2024−2025学年七年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "545403741509296128", "questionArticle": "<p>5．幻方起源于中国，它是一个由数字组成的方阵，其中每个数字只出现一次，且每行、每列和对角线上的数字之和都相等．如图1所示的幻方中， $ x= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；如图2所示的幻方中， $ y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/14/2/1/0/0/0/545403704356151312/images/img_17.jpg\" style=\"vertical-align:middle;\" width=\"182\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆渝中 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-16", "keyPointIds": "16410|16433", "keyPointNames": "数字问题|数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545403734844547072", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "545403734844547072", "title": "重庆市渝中区2024−2025学年七年级上学期1月期末考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "545403547761811456", "questionArticle": "<p>6．超级计算机，简称“超算”，随着超算的发展、算力的提升，使人工智能的发展也达到了新的高度，我国先后研发出了“天河一号”和“天河二号”超级计算机．</p><p>(1)已知一台“天河一号”2秒计算的次数与一台“天河二号”1秒计算的次数之和为6.5亿亿次，一台“天河一号”3秒计算的次数与一台“天河二号”5秒计算的次数之和为29亿亿次．求一台“天河一号”与一台“天河二号”每秒平均计算次数各为多少亿亿次？</p><p>(2)我国在2023年正式发布了新一代超级计算机“天河星逸”，其每秒计算次数为“天河二号”的11倍．计算605亿亿次，一台“天河星逸”所用的时间比一台“天河二号”少100秒，求一台“天河二号”与一台“天河星逸”每秒平均计算次数各为多少亿亿次？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆合川 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-16", "keyPointIds": "16441|16476", "keyPointNames": "其他问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545403538920218624", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "545403538920218624", "title": "重庆市合川区2024−2025学年上学期八年级数学期末测试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "545402043139137536", "questionArticle": "<p>7．七年级学生在数学实践课上进行了项目化学习研究，已知某项目化小组的研究如下：</p><p>【提出研究问题】销售问题</p><p>【设计实践任务】选择“素材1”、“素材2”，设计出了相关问题“任务1”、“任务2”，请尝试解决问题．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>学校开展“师生齐健身”活动，七年级（1）班需要购买篮球、足球若干个．</p><p>班长小明了解到本市有一体育用品商店对篮球和足球统一进行打折出售（折扣数相同）．打折前买3个篮球和2个足球需480元，买2个篮球和3个足球需470元．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>班长小明买了5个篮球和4个足球，一共花费了688元．</p></td></tr><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">[相关问题]</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>打折前，篮球和足球的单价各为多少元？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>篮球和足球打几折出售？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南娄底 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-16", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545402035413229568", "questionFeatureName": "项目化学习题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "545402035413229568", "title": "湖南省娄底市2024−2025学年七年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "545402042283499520", "questionArticle": "<p>8．解下列方程或方程组</p><p>(1) $ \\dfrac { 3-x } { 2 }=\\dfrac { x+2 } { 3 } $ ．</p><p>(2) $ \\begin{cases} x-y=3 \\\\ 2x+3y=11 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南娄底 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-16", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545402035413229568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "545402035413229568", "title": "湖南省娄底市2024−2025学年七年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "545402041494970368", "questionArticle": "<p>9．已知a、b满足方程组 $ \\begin{cases} 2a-b=2 \\\\ a+2b=6 \\end{cases}  $ ，则3<i>a</i>+<i>b</i>的值为<u><i>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</i></u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南娄底 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-16", "keyPointIds": "16305|16424", "keyPointNames": "代数式求值|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545402035413229568", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "545402035413229568", "title": "湖南省娄底市2024−2025学年七年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "545402039901134848", "questionArticle": "<p>10．长江江豚因其珍贵稀有，被誉为“水中大熊猫”，对维护长江生物多样性和生态安全意义重大．长江某文创店出售不同规格的江豚玩具，已知3个大号玩具和1个小号玩具共需110元：1个大号玩具和2个小号玩具共需70元，求大号玩具、小号玩具各需多少钱？设1个大号玩具<i>x</i>元，1个小号玩具<i>y</i>元．则可列出方程组为</p><p>A． $ \\begin{cases} 3x+y=110 \\\\ x+2y=70 \\end{cases}  $ B． $ \\begin{cases} 3x+y=110 \\\\ 2x+y=70 \\end{cases}  $ C． $ \\begin{cases} x+3y=110 \\\\ x+2y=70 \\end{cases}  $ D． $ \\begin{cases} x+3y=110 \\\\ 2x+y=70 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南娄底 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-16", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545402035413229568", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "545402035413229568", "title": "湖南省娄底市2024−2025学年七年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 192, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 192, "timestamp": "2025-07-01T02:23:32.550Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}