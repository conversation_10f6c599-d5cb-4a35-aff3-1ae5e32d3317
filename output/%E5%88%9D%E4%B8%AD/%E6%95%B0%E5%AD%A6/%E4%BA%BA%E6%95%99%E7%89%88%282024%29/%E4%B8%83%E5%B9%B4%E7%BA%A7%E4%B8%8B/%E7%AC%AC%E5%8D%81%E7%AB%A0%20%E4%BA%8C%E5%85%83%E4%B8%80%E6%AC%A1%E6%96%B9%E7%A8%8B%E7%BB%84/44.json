{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 43, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "584852655979147264", "questionArticle": "<p>1．某班级准备到文化用品商店购买今年畅销的两种毕业纪念册 $ A、B $ ，若购买 $ 1 $ 本毕业纪念册 $ \\mathrm{ A } $ 和 $ 2 $ 本毕业纪念册 $ B $ 共需要 $ 26 $ 元，购买 $ 2 $ 本毕业纪念册 $ \\mathrm{ A } $ 和 $ 1 $ 本毕业纪念册 $ B $ 共需要 $ 28 $ 元．</p><p>（1）求每本毕业纪念册 $ A、B $ 的销售价格；</p><p>（2）该班准备用不多于 $ 580 $ 元的金额购买这两种毕业纪念册 $ 65 $ 本，问最多能买多少本毕业纪念册 $ \\mathrm{ A } $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "520000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025贵州毕节 · 二模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-07", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584852622353412096", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "584852622353412096", "title": "2025年贵州省毕节市中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584852386725797888", "questionArticle": "<p>2．某铁件加工厂用图1的长方形和正方形铁片（长方形的宽与正方形的边长相等）可以加工成图2的竖式与横式两种无盖的长方体容器（加工时接缝材料不计）．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/02/2/1/0/0/0/584852335597236232/images/img_9.png\" style=\"vertical-align:middle;\" width=\"427\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）根据题意可列出以下表格：</p><table style=\"border: solid 1px;border-collapse: collapse; width:269.25pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1个竖式无盖容器</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>1个横式无盖容器</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>长方形铁片的数量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>4张</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>a</i>张</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>正方形铁片的数量</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>b</i>张</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>2张</p></td></tr></table><p>则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p><p>（2）若现有170张长方形铁片和80张正方形铁片，用于加工图2的竖式容器和横式容器时，两种铁片刚好全部用完，则可以加工出无盖竖式容器和无盖横式容器各多少个？</p><p>（3）已知该铁件加工厂加工出的此竖式容器费用为50元/个，此横式容器的费用为60元/个．若五金店老板计划支付800元用于采购一批竖式容器和横式容器（两种容器都要有），则有哪几种方案可供选择？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-07", "keyPointIds": "16432|16434", "keyPointNames": "配套问题|方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584852359609622528", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "584852359609622528", "title": "浙江省杭州市十三中教育集团2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584852381822656512", "questionArticle": "<p>3．解下列方程组：</p><p>（1） $ \\begin{cases} x+y=200 \\\\ y=x+10 \\end{cases}  $ </p><p>（2） $ \\begin{cases} 2x-3y=1 \\\\ 3x-y=2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-07", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584852359609622528", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "584852359609622528", "title": "浙江省杭州市十三中教育集团2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584852380384010240", "questionArticle": "<p>4．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x+2y=1-a \\\\ x-y=2a \\end{cases}  $ ，现给出以下结论：① $ \\begin{cases} x=\\dfrac { 2 } { 3 } \\\\ y=0 \\end{cases}  $ 是该方程组的一个解；</p><p>②无论<i>a</i>取何值， $ x+y $ 的值始终是一个定值；</p><p>③当 $ a=1 $ 时，该方程组的解也是方程 $ x+y=a-\\dfrac { 1 } { 3 } $ 的解；</p><p>④若 $ x{^{2}}-y{^{2}}=4 $ ，则 $ a=-3 $ ．其中正确的是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>（填序号）</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-07", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584852359609622528", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "584852359609622528", "title": "浙江省杭州市十三中教育集团2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584852378655956992", "questionArticle": "<p>5．在解关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} ax+by=5 \\\\ x+cy=-5 \\end{cases}  $ 时，甲同学因看错了<i>c</i>，得到的解为 $ \\begin{cases} x=5 \\\\ y=-5 \\end{cases}  $ ，而正确的解为 $ \\begin{cases} x=1 \\\\ y=3 \\end{cases}  $ ，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ c= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-07", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584852359609622528", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "584852359609622528", "title": "浙江省杭州市十三中教育集团2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584852376349089792", "questionArticle": "<p>6．若 $ \\begin{cases} x=3 \\\\ y=1 \\end{cases}  $ 是方程 $ x+y=a-2 $ 的一个解，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-07", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584852359609622528", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "584852359609622528", "title": "浙江省杭州市十三中教育集团2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584852372498718720", "questionArticle": "<p>7．已知实数<i>x</i>、<i>y</i>、<i>k</i>满足 $ \\begin{cases} 2x+y+k=3 \\\\ x-2y-k=2 \\end{cases}  $ ，则代数式 $ 3x-y $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．4B．6C．5D．7</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-07", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584852359609622528", "proofreadStatus": 4, "downloadCount": 0, "questionSourceList": [{"id": "584852359609622528", "title": "浙江省杭州市十三中教育集团2024−2025学年下学期七年级数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "584851933740965888", "questionArticle": "<p>8．某家电专卖店销售<i>A</i>，<i>B</i>两种型号的环保空调，已知其中两单的销售情况如表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p style=\"text-align:center;\"><i>A</i>型空调数量/台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p style=\"text-align:center;\"><i>B</i>型空调数量/台</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 67.45pt;\"><p style=\"text-align:center;\">总销售额/元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 67.45pt;\"><p style=\"text-align:center;\">14000</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 87pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 67.45pt;\"><p style=\"text-align:center;\">24000</p></td></tr></table><p>（1）求两种型号的空调的销售单价各是多少．</p><p>（2）为了响应国家家电以旧换新政策，更多让利于老百姓，专卖店决定推出“以旧换新”和打折促销两种优惠政策．小李计划购买<i>A</i>，<i>B</i>型空调各一台，其中一台用家中旧空调以旧换新购买．可采取如下两种方案．</p><p>方案一：旧空调可以抵消<i>A</i>型空调的售价的1000元，<i>B</i>型空调优惠<i>a</i>%；</p><p>方案二：旧空调可以抵消<i>B</i>型空调的售价的800元，<i>A</i>型空调优惠10%．</p><p>若方案一优惠额不小方案二，求<i>a</i>的最小值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南洛阳 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-06-07", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584851633470742528", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "584851633470742528", "title": "河南省洛阳市2024−2025学年九年级下学期第三次联考数学试题试卷", "paperCategory": 1}, {"id": "584851899054071808", "title": "河南省三门峡市2024−2025学年九年级下学期第三次联考数学试题试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586254075651469312", "questionArticle": "<p>9． 某厂生产三种不同型号的电脑，出厂价分别为甲种2000元，乙种2500元，丙种3000元.</p><p> $ (1) $ 某商场同时购进该厂两种不同型号的电脑共50台，用去 $ 11.5 $ 万元，请你分析下该商场的购买方案；</p><p> $ (2) $ 若商场销售一台甲种电脑盈利120元，销售一台乙种电脑盈利200元，销售一台丙种电脑盈利300元.在 $ (1) $ 中的购买方案中，哪种方案盈利最多？</p><p> $ (3) $ 在 $ (2) $ 的条件下，若商场准备用12万元 $ ( $ 用完 $ ) $ 同时购进三种不同型号的电脑共50台，共有多少种购买方案？盈利最多的方案是哪个？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江西南昌 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16437|16547", "keyPointNames": "销售利润问题|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586254048610791424", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586254048610791424", "title": "江西省南昌二十八中、高新实验学校2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "586254072212140032", "questionArticle": "<p>10． 已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases}x+2y=5\\\\ x-2y+mx+9=0\\end{cases}. $ </p><p> $ (1) $ 请直接写出方程 $ x+2y=5 $ 的所有正整数解；</p><p> $ (2) $ 若方程组的解满足 $ x+y=0 $ ，求<i>m</i>的值；</p><p> $ (3)m\\ne -3 $ 时，方程 $ x-2y+mx+9=0 $ 总有一个公共解，请求出这个方程的公共解吗？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江西南昌 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16420|16424|16425", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组|二元一次方程组的特殊解法", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586254048610791424", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586254048610791424", "title": "江西省南昌二十八中、高新实验学校2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 44, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 44, "timestamp": "2025-07-01T02:06:01.034Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}