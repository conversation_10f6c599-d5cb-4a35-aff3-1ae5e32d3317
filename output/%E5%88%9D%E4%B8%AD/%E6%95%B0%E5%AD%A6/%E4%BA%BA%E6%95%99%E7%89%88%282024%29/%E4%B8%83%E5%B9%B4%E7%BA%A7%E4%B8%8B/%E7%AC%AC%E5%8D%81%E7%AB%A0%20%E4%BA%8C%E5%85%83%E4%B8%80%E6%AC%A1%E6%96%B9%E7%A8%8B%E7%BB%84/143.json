{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 142, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "564226152904040448", "questionArticle": "<p>1．（1）计算： $ \\left  | { -3 } \\right  | -{ \\rm{ π } }{^{0}} $ ；</p><p>（2）解方程组： $ \\begin{cases} 2x+y=5① \\\\ x-y=1② \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西新余四中 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-10", "keyPointIds": "16323|16424", "keyPointNames": "零指数幂|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564226138144284672", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "564226138144284672", "title": "2025年江西省新余市第四中学中考一模考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564941636699987968", "questionArticle": "<p>2．设计动态验证码</p><p>日常生活中，我们会运用验证码技术来协助平台账号的登录，其原理是：用户每次向网页提交信息时，系统会根据算法随机生成一串数字（即验证码），只有正确输入验证码才能成功提交信息．学生小实自行设计验证码生成器，其原理是通过二元一次方程设置算法，随机生成动态验证码．</p><p>【步骤一：根据方程生成非负整数解】以二元一次方程 $ 5x+3y=150 $ 为例，利用该方程的非负整数解生成验证码．通过计算，以 $ x $ 从小到大为序对非负整数解进行编码，请观察并填写下列表格：</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/11/2/1/0/0/0/565896398337515521/images/img_1.png\" style='vertical-align:middle;' width=\"470\" alt=\"试题资源网 https://stzy.com\"></p><p>【步骤二：依据编码随机生成验证码】随机抽取 $ 5x+3y=150 $ 的两组非负整数解生成验证码，如抽取序号 $ n=1 $ 和 $ n\\mathrm{ = }2 $ 两组解∶ $ \\begin{cases} x=0 \\\\ y=50 \\end{cases}  $ 和 $ \\begin{cases} x=3 \\\\ y=45 \\end{cases}  $ 规定将两组整数解按照 $ x $ 在前 $ y $ 在后的顺序填入指定区域内∶<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/11/2/1/0/0/0/565896398337515522/images/img_2.png\" style='vertical-align:middle;' width=\"171\" alt=\"试题资源网 https://stzy.com\">，可生成如下2个验证码∶</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/11/2/1/0/0/0/565896398337515523/images/img_3.png\" style='vertical-align:middle;' width=\"360\" alt=\"试题资源网 https://stzy.com\"></p><p>【任务一：理解算法】</p><p>（1）请补全表2．</p><p>（2）结合表2，求出二元一次方程 $ 5x+3y=150 $ 的第 $ n $ 组非负整数解．</p><p>（3）当表2中 $ n $ 取最大值时，求出对应的 $ x $ 和 $ y $ 的值．</p><p>【任务二：应用算法】</p><p>学生小实利用 $ ax+by=35 $ (<i>a</i>，<i>b</i>为正整数)生成验证码∶</p><table style=\"border: solid 1px;border-collapse: collapse; width:414.75pt;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>规则</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>①取一组<i>a</i><i>，</i><i>b</i>的值，确定方程</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>②在该方程的非负整数解中，抽取序号 $ n=5 $ 和 $ n=11 $ 两组非负整数解作为验证码</p></td></tr></table><p>请在满足规则的情况下，选出非负整数解数量最少的方程，依据抽取序号写出一组验证码∶</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/11/2/1/0/0/0/565896398337515524/images/img_4.png\" style='vertical-align:middle;' width=\"185\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江温州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-10", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564941612041674752", "questionFeatureName": "阅读材料题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564941612041674752", "title": "浙江省温州市实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564220482490245120", "questionArticle": "<p>3．根据以下素材，完成任务．</p><table style=\"border: solid 1px;border-collapse: collapse; width:416.25pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 416.25pt;\"><p>某商店在无促销活动时，若买1件<i>A</i>商品，2件<i>B</i>商品，共需56元；若买2件<i>A</i>商品，1件<i>B</i>商品，共需52元．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>素材2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 416.25pt;\"><p>该商店为了鼓励消费者使用外卖配送服务，开展促销活动：</p><p>①若消费者使用外卖配送服务，须用25元购买“神券”，则本店内所有商品一律按标价的七五折出售；</p><p>②若消费者不使用外卖配送服务，本店内所有商品一律按标价的八折出售．</p></td></tr><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 416.25pt;\"><p>问题解决</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 416.25pt;\"><p>（1）该商店无促销活动时，求 $ \\mathrm{ A } $ ， $ B $ 商品的销售单价分别是多少？</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 416.25pt;\"><p>（2）小明在促销期间购买 $ \\mathrm{ A } $ ， $ B $ 两款商品共30件，其中 $ \\mathrm{ A } $ 商品购买 $ a $ 件 $ \\left ( { 0  &lt;  a  &lt;  30 } \\right )  $ ．</p><p>①若使用外卖配送商品，共需要_元；</p><p>②若不使用外卖配送商品，共需要_元（结果均用含 $ a $ 的代数式表示）．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>任务3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 416.25pt;\"><p>（3）在（2）的条件下，什么情况下使用外卖配送服务更合算？</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025广东佛山 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 1, "createTime": "2025-04-10", "keyPointIds": "16440|16486", "keyPointNames": "表格或图示问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564220456204541952", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564220456204541952", "title": "2025年广东省佛山市中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "565309608186650624", "questionArticle": "<p>4．学校计划用200元钱购买 $ A $ ， $ B $ 两种奖品， $ A $ 种每个15元，<i>B</i>种每个25元，在钱全部用完的情况下，有多少种购买方案（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．2种</p><p>B．3种</p><p>C．4种</p><p>D．5种</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2020黑龙江鹤岗 · 中考真题", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-04-10", "keyPointIds": "16434", "keyPointNames": "方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "204610152133074944", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "204610152133074944", "title": "黑龙江省鹤岗市2020年中考数学试题", "paperCategory": 1}, {"id": "565309594727129088", "title": "2025年四川省宜宾市第二中学校九年级一诊考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564941630014267392", "questionArticle": "<p>5．关于 $ x $ 的整式 $ A=2x+1 $ ，它的各项系数之和为 $ 2+1=3 $ (常数项系数为常数项本身)．已知 $ B $ 是关于 $ x $ 的整式，最高次项次数为2，系数为1．若 $ B\\cdot (x+3)=C，C $ 是一个只含两项的多项式，则 $ B $ 各项系数之和的最大值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江温州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-10", "keyPointIds": "16327|16424", "keyPointNames": "多项式乘多项式|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564941612041674752", "questionMethodName": "分类讨论思想", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564941612041674752", "title": "浙江省温州市实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564941631687794688", "questionArticle": "<p>6．计算：</p><p>(1) $ {\\left( { \\sqrt { 3 }-1 } \\right) ^ {0}}+{\\left( { \\dfrac { 1 } { 2 } } \\right) ^ {-3}}-8{^{6}}\\times {\\left( { \\dfrac { 1 } { 8 } } \\right) ^ {5}} $ ．</p><p>(2) $ \\begin{cases} 3x-2y=10 \\\\ x=y+2 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江温州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-10", "keyPointIds": "16321|16323|16372|16424", "keyPointNames": "积的乘方|零指数幂|负整数指数幂|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564941612041674752", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564941612041674752", "title": "浙江省温州市实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "565310391741358080", "questionArticle": "<p>7．解方程组 $ \\begin{cases} 2x+4y=5 \\\\ x=1-y \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|350000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2020江苏连云港 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 3, "createTime": "2025-04-10", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "195537611162689536", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "195537611162689536", "title": "江苏省连云港市2020年中考数学试题", "paperCategory": 1}, {"id": "565310370648203264", "title": "2025年浙江温州鹿城区温州外国语学校九年级中考一模数学试卷", "paperCategory": 1}, {"id": "559115954417868800", "title": "福建省永春第二中学2024−2025学年下学期七年级3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564941633059332096", "questionArticle": "<p>8．甲、乙两人准备自行车骑行比赛，相约一同训练．两人从相距80千米的两地同时出发，相向而行，经过2个小时相遇；若甲比乙提前1小时出发，那么乙出发 $ 1.6 $ 小时后两者相遇．求甲、乙两人的速度．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江温州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-10", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564941612041674752", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564941612041674752", "title": "浙江省温州市实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564941620937793536", "questionArticle": "<p>9．解方程组 $ \\begin{cases} 3x+2y=9① \\\\ x-y=3② \\end{cases}  $ 时，将①+②×2消去<i>y</i>，得到的方程正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 2x=6 $　　　　B． $ 2x=15 $　　　　C． $ 5x=6 $　　　　D． $ 5x=15 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江温州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-10", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564941612041674752", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564941612041674752", "title": "浙江省温州市实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564941624121270272", "questionArticle": "<p>10．明代数学家程大位所著的《算法统宗》里有这样一个问题：隔壁听得客分银，不知人数不知银，七两分之多四两，九两分之少半斤．其大意为：有一群人分银子，如果每人分七两，则剩余四两；如果每人分九两，则还差八两．设有 $ x $ 人，分 $ y $ 两银，根据题意列二元一次方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 7x+4=y \\\\ 9x-8=y \\end{cases}  $ B． $ \\begin{cases} 7y+4=x \\\\ 9y-8=x \\end{cases}  $ </p><p>C． $ \\begin{cases} 7x-4=y \\\\ 9x+8=y \\end{cases}  $ D． $ \\begin{cases} 7y-4=x \\\\ 9y+8=x \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江温州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-10", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564941612041674752", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564941612041674752", "title": "浙江省温州市实验中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 143, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 143, "timestamp": "2025-07-01T02:17:47.702Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}