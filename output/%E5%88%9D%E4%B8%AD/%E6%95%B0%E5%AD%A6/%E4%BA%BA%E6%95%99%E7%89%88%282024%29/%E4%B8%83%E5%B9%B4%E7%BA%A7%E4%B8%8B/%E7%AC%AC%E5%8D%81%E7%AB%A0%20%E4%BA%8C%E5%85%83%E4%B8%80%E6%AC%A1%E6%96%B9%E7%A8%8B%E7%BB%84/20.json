{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 19, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "591002474300874752", "questionArticle": "<p>1．若关于 $ x,y $ 的二元一次方程组 $ \\begin{cases} 3x+y=1+a, \\\\ x+3y=3 \\end{cases}  $ 的解满足 $ 9x+9y  &lt;  -2y-7 $ ，则 $ a $ 的取值范围是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ a  &lt;  -9 $ B． $ a  &lt;  9 $ C． $ a &gt; -9 $ D． $ a &gt; 9 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏南京 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-06-23", "keyPointIds": "16426|16485", "keyPointNames": "二元一次方程组的应用|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570807201557684224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "570807201557684224", "title": "江苏省南京市鼓楼区2024−2025学年七年级下学期期中考试数学卷", "paperCategory": 1}, {"id": "591002458463182848", "title": "安徽省阜阳市第十八中学2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "591003642108354560", "questionArticle": "<p>2．已知 $ \\begin{cases} x=3 \\\\ y=-1 \\end{cases}  $ 是方程 $ 2x-5y=m $ 的解，则 $ m $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 11 $</p><p>B． $ -11 $</p><p>C． $ 2 $</p><p>D． $ -2 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川射洪中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-06-23", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591003630657904640", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "591003630657904640", "title": "四川省遂宁市射洪市射洪中学校2024−2025学年七年级下学期6月月考数学试题", "paperCategory": 1}, {"id": "423866284167700480", "title": "浙江省义乌市绣湖学校2022-2023学年七年级下学期数学学情调研3月", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589575599716347904", "questionArticle": "<p>3．在篮球联赛中，每场比赛都要分出胜负．每队胜一场得 $ 2 $ 分，负一场得 $ 1 $ 分．某队在 $ 10 $ 场比赛中得到了 $ 16 $ 分．那这个队的胜负场数分别是多少呢？设这个队胜的场数是 $ x $ ，负的场数是 $ y $ ，则可以列出的方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=10 \\\\ 2x+y=16 \\end{cases}  $ B． $ \\begin{cases} x+y=10 \\\\ 2x-y=16 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+2y=10 \\\\ 2x+y=16 \\end{cases}  $ D． $ \\begin{cases} x+y=10 \\\\ 2y-x=16 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津河西 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575582393872384", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575582393872384", "title": "2025年天津市河西区中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590678737898745856", "questionArticle": "<p>4．对实数 $ x、y $ 定义一种新运算 $ \\vartriangle  $ ，规定： $ \\vartriangle \\left ( { x,y } \\right ) =ax+bxy-2 $ （其中 $ a、b $ 均为非零常数），这里等式右边是通常的四则运算，例如： $ \\vartriangle \\left ( { 5，7 } \\right ) =a\\times 5+b\\times 5\\times 7-2 $ ．若 $ \\vartriangle \\left ( { 2,3 } \\right ) =-5 $ ， $ \\vartriangle \\left ( { -4,4 } \\right ) =2 $ ，则下列结论正确的个数为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>（ $ 1 $ ） $ a=-3，b=\\dfrac { 1 } { 2 } $ ；</p><p>（ $ 2 $ ）若 $ \\vartriangle \\left ( { d,d } \\right ) =-3d $ ，则 $ \\vartriangle \\left ( { d,d } \\right ) =\\pm 6 $ ；</p><p>（ $ 3 $ ）若 $ \\vartriangle \\left ( { p,q } \\right ) =-6 $ ，则 $ p、q $ 有且仅有 $ 3 $ 组正整数解；</p><p>（ $ 4 $ ）如果 $ \\vartriangle \\left ( { nx,y } \\right ) =\\vartriangle \\left ( { ny,x } \\right )  $ ，那么 $ n=0 $ 或 $ x=y $ ；</p><p>A． $ 4 $ 个B． $ 3 $ 个C． $ 2 $ 个D． $ 1 $ 个</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市松树桥中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590678720622407680", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590678720622407680", "title": "重庆市渝北区松树桥中学2024-−2025学年七年级下学期数学第三次定时练习", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590678747856023552", "questionArticle": "<p>5．暑期临近，朝天门一服装店老板计划购进甲、乙两种童装<i>T</i>恤．已知购进甲种<i>T</i>恤2件和乙种<i>T</i>恤3件共需310元；购进甲种<i>T</i>恤1件和乙种<i>T</i>恤2件共需190元．</p><p>（1）求甲、乙两种<i>T</i>恤每件的进价分别是多少元？</p><p>（2）为满足市场需求，服装店需购进甲、乙两种<i>T</i>恤共100件，要求购买两种<i>T</i>恤的总费用不超过6540元，并且购买的甲种<i>T</i>恤的数量的三倍不超过乙种<i>T</i>恤的数量，请你通过计算，确定服装店购买甲、乙两种<i>T</i>恤的购买方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市松树桥中学 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16438|16490", "keyPointNames": "和差倍分问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590678720622407680", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590678720622407680", "title": "重庆市渝北区松树桥中学2024-−2025学年七年级下学期数学第三次定时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590678741396795392", "questionArticle": "<p>6．已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} x+2y=8 \\\\ ax+by=2 \\end{cases}  $ 与 $ \\begin{cases} 2x-y=1 \\\\ bx+ay=-4 \\end{cases}  $ 有相同的解，则 $ a+b $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市松树桥中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590678720622407680", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590678720622407680", "title": "重庆市渝北区松树桥中学2024-−2025学年七年级下学期数学第三次定时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590678744169230336", "questionArticle": "<p>7．解方程组：</p><p>（1） $ \\begin{cases} x-2y=-3 \\\\ 2x+y=4 \\end{cases}  $ ；</p><p>（2）解不等式组： $ \\begin{cases} 3\\left ( { x+2 } \\right ) \\leqslant  5x+8 \\\\ x-4  &lt;  \\dfrac { x-6 } { 3 } \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市松树桥中学 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590678720622407680", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590678720622407680", "title": "重庆市渝北区松树桥中学2024-−2025学年七年级下学期数学第三次定时练习", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590678601885851648", "questionArticle": "<p>8．列方程（组）或不等式（组）解应用题．</p><p>随着夏日的到来，“茶千道”奶茶店推出了“醒橄榄”、“香芒果”两款新饮品．已知购买3杯“醒橄榄”和5杯“香芒果”需花费130元，购买5杯“醒橄榄”和7杯“香芒果”需花费194元．</p><p>（1）求“醒橄榄”和“香芒果”的单价各是多少元．</p><p>（2）“六一”儿童节，李老师打算购买两种奶茶共60杯来奖励表现优秀的学生．通过外卖平台对比，发现该店在美团和京东上分别对两款产品在进行促销活动．美团上购买“醒橄榄”买两杯第二杯半价（不单杯销售），同时每杯需支付1元打包费；京东外卖购买“香芒果”每杯立减4元，没有打包费．为了享受最大的优惠，李老师打算只在美团上购买“醒橄榄”，只在京东上购买“香芒果”．在实际购买时，为了扩大奖励范围，李老师在原来基础上，又新增加购买了一些“醒橄榄”和“香芒果”，新增加购买“香芒果”数量是新增加购买“醒橄榄”的3倍．在最后结算时，李老师发现购买“醒橄榄”的总数是“香芒果”的总数的2倍，两个外卖平台总计支付不超过1180元，请问李老师在京东外卖新增购买“香芒果”最多能订多少杯？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆川外附中 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590678568251727872", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "590678568251727872", "title": "重庆市外国语学校2024−2025学年七年级下学期第三次定时作业数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590678592704520192", "questionArticle": "<p>9．若关于<i>x</i>的不等式组 $ \\begin{cases} 2(x+1)-\\dfrac { 3x-1 } { 2 } > 2 \\\\ \\dfrac { 3 } { 2 }a-6x\\geqslant  3 \\end{cases}  $ 有整数解且最多有3个整数解，且关于<i>m</i>，<i>n</i>的方程 $ \\begin{cases} m-2n=a \\\\ m+2n=4 \\end{cases}  $ 的解均为整数，则所有满足条件的整数<i>a</i>的和为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆川外附中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590678568251727872", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "590678568251727872", "title": "重庆市外国语学校2024−2025学年七年级下学期第三次定时作业数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590678591056158720", "questionArticle": "<p>10．一个等腰三角形一条腰上的中线把这个三角形的周长分成了6和12两部分，则这个等腰三角形的底边长为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆川外附中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16439|16640|16661", "keyPointNames": "几何问题|三角形三边关系|等腰三角形的性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590678568251727872", "questionMethodName": "分类讨论思想", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "590678568251727872", "title": "重庆市外国语学校2024−2025学年七年级下学期第三次定时作业数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 20, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 20, "timestamp": "2025-07-01T02:03:10.834Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}