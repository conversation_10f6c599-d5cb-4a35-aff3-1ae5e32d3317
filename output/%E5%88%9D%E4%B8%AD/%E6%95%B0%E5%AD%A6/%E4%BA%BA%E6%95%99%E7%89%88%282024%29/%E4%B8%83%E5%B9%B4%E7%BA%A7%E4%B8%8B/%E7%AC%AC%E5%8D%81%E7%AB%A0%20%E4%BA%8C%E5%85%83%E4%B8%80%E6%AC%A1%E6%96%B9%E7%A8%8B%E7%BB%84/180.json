{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 179, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "554324340877074432", "questionArticle": "<p>1． 为了进一步加强学生的校园安全意识，某班开展校园安全知识竞赛活动，去奶茶店购买<i>A</i>，<i>B</i>两种款式的奶茶作为奖品.若买10杯<i>A</i>款奶茶，5杯<i>B</i>款奶茶，共需160元；若买15杯<i>A</i>款奶茶，10杯<i>B</i>款奶茶，共需270元.奶茶店为了满足市场的需求，推出每杯2元的加料服务，顾客在选完款式后可以自主选择加料一份或者不加料.</p><p> $ (1) $ 求<i>A</i>款奶茶和<i>B</i>款奶茶的销售单价各是多少元；</p><p> $ (2) $ 在不加料的情况下，购买<i>A</i>，<i>B</i>两种款式的奶茶 $ ( $ 两种都买 $ ) $ ，刚好用了220元，请问有几种购买方案？</p><p> $ (3) $ 若小华恰好用了380元购买<i>A</i>，<i>B</i>两款奶茶，其中<i>A</i>款不加料的数量是总数量的 $ \\dfrac{1}{3} $ ，则<i>B</i>款加料的奶茶买了多少杯？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽池州 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-10", "keyPointIds": "16420|16437", "keyPointNames": "二元一次方程的解|销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554324317854539776", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554324317854539776", "title": "安徽省池州市2024−2025学年七年级下学期开学数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "554324338108833792", "questionArticle": "<p>2． 已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases}2x-y=7\\\\ 2ax-by=4\\end{cases} $ 和 $ \\begin{cases}x+2y=1\\\\ ax+2by=7\\end{cases} $ 有相同的解.</p><p> $ (1) $ 求出它们的相同解；</p><p> $ (2) $ 求 $ (a+b)^{2024} $ 的值.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽池州 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-10", "keyPointIds": "16424|30400", "keyPointNames": "加减消元法解二元一次方程组|有理数的乘方", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554324317854539776", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554324317854539776", "title": "安徽省池州市2024−2025学年七年级下学期开学数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "554324327610490880", "questionArticle": "<p>3． 若关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases}4x+2y=5k-4\\\\ 2x+4y=5\\end{cases} $ 的解满足 $ x+y=1 $ ，则<i>k</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0B．1C．2D． $ -1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽池州 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-10", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554324317854539776", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "554324317854539776", "title": "安徽省池州市2024−2025学年七年级下学期开学数学试卷", "paperCategory": 11}], "questionTypeCode": "1"}, {"questionId": "552324731392794624", "questionArticle": "<p>4．某市为进一步加快文明城市的建设，园林局尝试种植<i>A</i>、<i>B</i>两种树种．经过试种后发现，种植<i>A</i>种树苗<i>a</i>棵，种下后成活了 $ \\left ( { \\dfrac { 1 } { 2 }a+5 } \\right )  $ 棵，种植<i>B</i>种树苗<i>b</i>棵，种下后成活了 $ {\\rm （\\mathit{b}-2）} $ 棵．第一阶段两种树苗共种植40棵，且两种树苗的成活棵树相同，则种植<i>A</i>种树苗<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>棵．第二阶段，该园林局又种植<i>A</i>种树苗<i>m</i>棵，<i>B</i>种树苗<i>n</i>棵，若<i>m</i>=2<i>n</i>，在第一阶段的基础上进行统计，则这两个阶段种植<i>A</i>种树苗成活棵数<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>种植<i>B</i>种树苗成活棵数（填“＞”“＜”或“＝”）．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京171中学 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-10", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552324712115773440", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "552324712115773440", "title": "北京市第一七一中学2024−2025学年九年级下学期开学数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553368270331486208", "questionArticle": "<p>5．出口贸易是我国经济发展的重要因素，由于出口贸易持续增长，一企业生产某种商品的数量增加明显.已知今年生产该商品的数量比今年和去年生产的数量总和的一半多11万件，去年的数量比今年和去年生产数量总和的三分之一少2万件.设今年生产该商品的数量为<i>x</i>万件，去年生产该商品的数量为<i>y</i>万件，根据题意可列出的方程组是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=\\dfrac { 1 } { 3 }\\left ( { x+y } \\right ) -11 \\\\ y=\\dfrac { 1 } { 2 }\\left ( { x+y } \\right ) +2 \\end{cases}  $ B． $ \\begin{cases} x=\\dfrac { 1 } { 3 }\\left ( { x+y } \\right ) +11 \\\\ y=\\dfrac { 1 } { 2 }\\left ( { x+y } \\right ) -2 \\end{cases}  $ </p><p>C． $ \\begin{cases} x=\\dfrac { 1 } { 2 }\\left ( { x+y } \\right ) -11 \\\\ y=\\dfrac { 1 } { 3 }\\left ( { x+y } \\right ) +2 \\end{cases}  $ D． $ \\begin{cases} x=\\dfrac { 1 } { 2 }\\left ( { x+y } \\right ) +11 \\\\ y=\\dfrac { 1 } { 3 }\\left ( { x+y } \\right ) -2 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江宁波 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-10", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553368254825144320", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "553368254825144320", "title": "2025年浙江省宁波市江北区中考一模数学模拟试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "553373874735325184", "questionArticle": "<p>6．“阅读与人文滋养内心”，某校开展阅读经典活动，小明 $ 3 $ 天里阅读的总页数比小颖 $ 5 $ 天里阅读的总页数少 $ 6 $ 页，小颖平均每天阅读的页数比小明平均每天阅读的页数的 $ 2 $ 倍少 $ 10 $ 页，若小明、小颖平均每天分别阅读 $ x $ 页， $ y $ 页，则下列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3x=5y-6 \\\\ y=2x-10 \\end{cases}  $　　　　B． $ \\begin{cases} 3x+6=5y \\\\ y=2x+10 \\end{cases}  $　　　　C． $ \\begin{cases} 3x-6=5y \\\\ y=2x-10 \\end{cases}  $　　　　D． $ \\begin{cases} 3x=5y+6 \\\\ y=2x+10 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津南大附中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-03-10", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553373861934309376", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "553373861934309376", "title": "天津市南开区南开大学附属中学2024−2025学年九年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "480840365550903296", "title": "福建省福州市仓山区福建师范大学附属中学2023−2024学年七年级上学期月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "553050309280440320", "questionArticle": "<p>7．列方程（组）解应用题：</p><p>为支持农业现代化建设，甲、乙两机械生产公司接受3600台微耕机的生产任务．已知甲公司每天生产微耕机的台数是乙公司每天生产微耕机台数的 $ \\dfrac { 3 } { 2 } $ ．</p><p>(1)若甲公司生产40天，乙公司生产30天，则恰好完成生产任务．问乙公司每天生产多少台微耕机？</p><p>(2)由于时间紧任务重，甲、乙两公司每天生产微耕机的台数均在原来的基础上提高了 $ 50\\% $ ，甲、乙两公司各完成总生产任务的一半，甲公司完成任务所需要的时间比乙公司完成任务的时间少5天．问乙公司现在每天生产多少台微耕机？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024重庆重庆市渝高中学校 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-09", "keyPointIds": "16438|16476", "keyPointNames": "和差倍分问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553050282269122560", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "553050282269122560", "title": "2024年重庆市渝高中学教育集团九年级下学期二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551914674272378880", "questionArticle": "<p>8．解方程组： $ \\begin{cases} 3x+y=10 \\\\ 2x-y=5 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江温州 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-09", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551914653200195584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "551914653200195584", "title": "浙江省温州市第十二中学2024-−2025学年九年级下学期开学考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552321368286601216", "questionArticle": "<p>9．解方程组： $ \\begin{cases} x+3y=1 \\\\ 2x-y=-5 \\end{cases}  $  </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024广东广州 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-08", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552321349286404096", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552321349286404096", "title": "2024年广东省广州市越秀区华侨中学中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552321663926312960", "questionArticle": "<p>10．李老师逛超市时看中一套碗，她将碗叠成一列（如图），测量后发现：用2只碗叠放时总高度为7.5cm，用4只碗叠放时总高度为11.5cm．若将8个碗叠成一列正好能放入消毒柜，则这个消毒柜的高度至少有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/05/2/1/0/0/0/552321602102272014/images/img_15.png\" style=\"vertical-align:middle;\" width=\"118\" alt=\"试题资源网 https://stzy.com\">&nbsp;&nbsp;</p><p>A．15.5cmB．19.5cmC．23cmD．30cm</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024湖北武汉 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-03-08", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552321649879588864", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552321649879588864", "title": "2024年湖北省武汉市江汉区中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 180, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 180, "timestamp": "2025-07-01T02:22:09.151Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}