{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 20, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "590678246502473728", "questionArticle": "<p>1．某中学积极开展“阳光体育”运动，开设“足球课间活动”．购买了甲种品牌的足球 $ 50 $ 个，乙种品牌的足球 $ 25 $ 个，共花费 $ 4500 $ 元，已知乙种品牌足球的单价比甲种品牌足球的单价高30元．</p><p>（1）求甲、乙两种品牌足球的单价各多少元？</p><p>（2）为参加“足球联谊赛”活动，根据需要，学校决定再次购进甲、乙两种品牌的足球50个．正逢体育用品商店“优惠促销”活动，甲种品牌的足球单价优惠4元，乙种品牌的足球单价打8折．如果此次学校购买甲、乙两种品牌足球的总费用不超过 $ 2750 $ 元，且购买乙种品牌的足球不少于 $ 23 $ 个，那么有几种购买方案？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川荣中 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16438|16490", "keyPointNames": "和差倍分问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590678219646345216", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590678219646345216", "title": "四川省自贡市荣县中学校2024−2025学年七年级下学期创新班6月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590678241473503232", "questionArticle": "<p>2．关于<i>x</i><i>、</i><i>y</i>的二元一次方程组 $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} 2x-ay=-1 \\\\ bx+3y=8 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ 的解为 $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} x=1 \\\\ y=-5 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ ，则关于<i>m</i>，<i>n</i>的二元一次方程组 $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} 2\\left ( { m+n } \\right ) -a\\left ( { m-n } \\right ) =-1 \\\\ b\\left ( { m+n } \\right ) +3\\left ( { m-n } \\right ) =8 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川荣中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590678219646345216", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590678219646345216", "title": "四川省自贡市荣县中学校2024−2025学年七年级下学期创新班6月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590678242790514688", "questionArticle": "<p>3．解方程组 $ \\begin{cases} 6\\left ( { x+y } \\right ) -5\\left ( { 2x+y } \\right ) =-10① \\\\ \\dfrac { x+y } { 6 }-\\dfrac { x-y } { 4 }=-1② \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川荣中 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590678219646345216", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590678219646345216", "title": "四川省自贡市荣县中学校2024−2025学年七年级下学期创新班6月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590678419383300096", "questionArticle": "<p>4．国家一直倡导节能减排，改善环境，大力扶持新能源汽车的销售，某汽车专实店销售<i>A</i>，<i>B</i>两种型号的新能源汽车．上周售出1辆<i>A</i>型车和3辆<i>B</i>型车，销售额为96万元；本周已售出2辆<i>A</i>型车和1辆<i>B</i>型车，销售额为62万元．</p><p>（1）求每辆<i>A</i>型车和<i>B</i>型车的售价各为多少万元？</p><p>（2）甲公司拟向该店购买<i>A</i>，<i>B</i>两种型号的新能源汽车共6辆，且<i>A</i>型号车不少于2辆，购车费不少于130万元，则有哪几种购车方案？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津南开中学 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590678392573308928", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590678392573308928", "title": "天津市南开中学2024−2025学年七年级下学期第二次阶段练习数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590678416023662592", "questionArticle": "<p>5．解方程组</p><p>（1） $ \\begin{cases} y=1+x \\\\ 2x-3y=-4 \\end{cases}  $ </p><p>（2） $ \\begin{cases} 3a+2b=5 \\\\ 2a-b=1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津南开中学 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590678392573308928", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590678392573308928", "title": "天津市南开中学2024−2025学年七年级下学期第二次阶段练习数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590678403679825920", "questionArticle": "<p>6．在下列方程组中，属于二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} 2x-y=1 \\\\ x+z=3 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ B． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} 3x+2y=2 \\\\ 2x-3y=-1 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ </p><p>C． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} x-y=1 \\\\ xy=0 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ D． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} \\dfrac { 1 } { x }+y=2 \\\\ 3x-y=0 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025天津南开中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590678392573308928", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590678392573308928", "title": "天津市南开中学2024−2025学年七年级下学期第二次阶段练习数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590295140972933120", "questionArticle": "<p>7．绿动未来—追踪碳排放</p><p>【素材呈现】</p><p>素材一：在对<i>A</i>城市交通工具的二氧化碳排放量所进行的一项调研中，我们发现：10辆燃油车与10辆电动汽车每公里共同排放的二氧化碳总量约为2600克，而5辆燃油车与6辆电动汽车每公里的总排放量则为1374克．</p><p>素材二：为了中和二氧化碳排放量，我们可以采取植树造林等绿化措施．根据相关换算标准，每棵成年的阔叶树种（例如杨树）每年大约吸收172千克二氧化碳，而每棵成年的针叶树种（例如冷杉）每年大约吸收111千克的二氧化碳．</p><p>【问题解决】</p><p>问题一：一辆燃油车和一辆电动汽车每公里分别产生的二氧化碳排放量是多少克？</p><p>问题二：某环保企业计划购买成年杨树和冷杉共100棵，设购买杨树<i>a</i>棵，这100棵树木一年内吸收的二氧化碳总量为<i>w</i>千克．</p><p>（1）求<i>w</i>与<i>a</i>的函数关系式；</p><p>（2）杨树会产生较多的飘絮物，因此规定采购杨树不超过30棵，请设计一个最优的采购方案，使得这100棵树木在一年内吸收的二氧化碳总量最大．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025山东济南 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16438|16535", "keyPointNames": "和差倍分问题|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590295110912356352", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590295110912356352", "title": "2025年山东省济南市历下区四校联考初中学业水平数学模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590295015491936256", "questionArticle": "<p>8．我国古代数学专著《九章算术》中有一道关于“分钱”的问题：甲、乙二人有钱若干，若甲给乙10钱，则甲的钱是乙的2倍；若乙给甲5钱，则乙的钱是甲的 $ \\dfrac { 1 } { 3 } $ ．若设甲原有 $ x $ 钱，乙原有 $ y $ 钱，则可列方程（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-10=2\\left ( { y+10 } \\right )  \\\\ \\dfrac { 1 } { 3 }\\left ( { x+5 } \\right ) =y-5 \\end{cases}  $ B． $ \\begin{cases} 2\\left ( { x-10 } \\right ) =y+10 \\\\ \\dfrac { 1 } { 3 }\\left ( { x+5 } \\right ) =y-5 \\end{cases}  $ </p><p>C． $ \\begin{cases} x-10=2\\left ( { y+10 } \\right )  \\\\ x+5=\\dfrac { 1 } { 3 }\\left ( { y-5 } \\right )  \\end{cases}  $ D． $ \\begin{cases} 2\\left ( { x-10 } \\right ) =y+10 \\\\ x+5=\\dfrac { 1 } { 3 }\\left ( { y-5 } \\right )  \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁鞍山 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590295000371470336", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590295000371470336", "title": "2025年辽宁省鞍山市海城市协作体三模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "590294661031309312", "questionArticle": "<p>9．深圳市罗湖区作为深圳最早发展的城区之一，融合了自然景观、历史文化和现代都市风貌，有很多知名景区，比如“仙湖植物园”、“梧桐山”、“洪湖公园”、“东门老街”等．请同学们认真阅读以下材料，并完成相关的学习任务：</p><table style=\"border: solid 1px;border-collapse: collapse;\">\r\n  <tbody>\r\n    <tr>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 427.35pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">材料一：2025年“五一”劳动节假期，大批深圳市民进入“仙湖植物园”观光游玩，据统计，5月4日上午8：00−10：00有接近4200人乘坐私家车和客车两种交通工具进入仙湖植物园停车场，根据停车场监控统计，在此段时间内私家车和客车共320辆进入，假如每辆私家车平均乘坐3人，客车平均每辆乘坐30人．</p>\r\n      </td>\r\n    </tr>\r\n    <tr>\r\n      <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 427.35pt;\">\r\n      <p style=\" font-family:'Times New Roman',' ';\">材料二：某学校计划五一过后，组织学校720名师生到“仙湖植物园”研学，一共租甲、乙两种型号的客车20辆，根据下表提供的信息要求在保证将全部师生送达目的地的前提下租车费用不超过7200元．</p>\r\n      <table style=\"border: solid 1px;border-collapse: collapse; width:416.25pt;\">\r\n        <tbody>\r\n          <tr>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138.75pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">型号</p>\r\n            </td>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138.75pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">每辆载客量</p>\r\n            </td>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138.75pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">每辆租金</p>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138.75pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">甲型号</p>\r\n            </td>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138.75pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">30</p>\r\n            </td>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138.75pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">320</p>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138.75pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">乙型号</p>\r\n            </td>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138.75pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">45</p>\r\n            </td>\r\n            <td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 138.75pt;\">\r\n            <p style=\" font-family:'Times New Roman',' ';\">400</p>\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n      <p>&nbsp;</p>\r\n      </td>\r\n    </tr>\r\n  </tbody>\r\n</table><p>请同学们根据材料一、材料二提供的信息完成（1），（2）任务．</p><p>（1）请同学们估算材料一中提供的时间段内分别有多少辆私家车和客车进入停车场．</p><p>（2）有几种租车方案供学校选择？最少租车费用是多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东深高 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16441|16490", "keyPointNames": "其他问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590294637002141696", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "590294637002141696", "title": "2025年广东省深圳高级中学九年级6月质量检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589575495995400192", "questionArticle": "<p>10．清代康熙年间编辑的算书《御制数理精蕴》（卷九）中记载一题：“设如有甲乙二人入山采果共得三百枚，但云甲数加六百枚乙数加二百枚，则甲数比乙数多二倍，问甲乙各得几何？”其大意是：甲、乙二人入山采果共得三百枚，若甲的采果数加六百枚，乙的采果数加二百枚．则新得到的甲的采果数比乙的采果数多二倍，问甲、乙原来各采果多少枚？如果设甲原来采果数是 $ x $ 枚．乙原来采果数是 $ y $ 枚，则根据题意可列方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西咸阳 · 临考冲刺", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-22", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589575477607571456", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589575477607571456", "title": "2025年陕西省咸阳市秦都区多校模考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 21, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 21, "timestamp": "2025-07-01T02:03:18.361Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}