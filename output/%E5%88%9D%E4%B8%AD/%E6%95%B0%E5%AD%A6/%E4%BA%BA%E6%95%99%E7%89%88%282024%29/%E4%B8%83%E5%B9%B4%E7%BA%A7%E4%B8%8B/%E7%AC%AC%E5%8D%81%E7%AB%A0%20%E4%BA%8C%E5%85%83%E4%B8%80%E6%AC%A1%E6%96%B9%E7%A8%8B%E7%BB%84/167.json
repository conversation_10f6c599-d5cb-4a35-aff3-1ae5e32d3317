{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 166, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "557232729881878528", "questionArticle": "<p>1．用加减法解方程组 $ \\begin{cases} 2x-3y=5① \\\\ 2x-8y=3② \\end{cases}  $ 时， ①－②得（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．5<i>y</i>＝2B．−11<i>y</i>＝8C．−11<i>y</i>＝2D．5<i>y</i>＝8</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024福建泉州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-03-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557232720440500224", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557232720440500224", "title": "福建省泉州科技中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}, {"id": "208622151003316224", "title": "四川省内江市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555175260200411136", "questionArticle": "<p>2．一套衣服的上衣和裤子共100元．因市场需求变化，商家决定分开销售．裤子降价 $ 10\\% $ ，上衣提价 $ 20\\% $ ，调价后，这套衣服的售价比原来提高了8元．问调价后上衣和裤子的售价各是多少元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽芜湖 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-18", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555175238889152512", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "555175238889152512", "title": "安徽省芜湖市2024−2025学年九年级下学期开学考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555174189197467648", "questionArticle": "<p>3．如果 $ \\left  | { x-2y+1 } \\right  | { { + } }{\\left( { 2x-y-5 } \\right) ^ {2}}=0 $ ，则 $ x+y $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024陕西西安 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-17", "keyPointIds": "16257|16424", "keyPointNames": "绝对值非负性的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555174171577196544", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555174171577196544", "title": "陕西省西安市未央区部分学校2023−2024学年七年级下学期第一次月考数学模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555511553962844160", "questionArticle": "<p>4．用五个大小完全相同的长方形在平面直角坐标系中摆成如图所示的图案，若点 $ A $ 的坐标为 $ \\left ( { -4,13 } \\right )  $ ，则点 $ B $ 的坐标为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/19/2/1/0/0/0/557567067597086720/images/img_1.png\" style='vertical-align:middle;' width=\"131\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西临汾 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-03-17", "keyPointIds": "16439|16497", "keyPointNames": "几何问题|点的坐标", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "593322809478127616", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "593322809478127616", "title": "山西省临汾市部分学校2023−2024学年八年级下学期期末数学试题", "paperCategory": 1}, {"id": "555511536774586368", "title": "山西省部分学校2023−2024学年七年级下学期联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555511557720940544", "questionArticle": "<p>5．2024年五一假期期间，太原市某中学开展以“红色经典”为主题的研学活动，组织七年级师生参观红色文化传承实践教育基地．原计划租用45座甲型客车若干辆，但有15人没有座位；若租用同样数量的60座乙型客车，则多出三辆车，且其余客车恰好坐满．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/23/2/1/0/0/0/559050196493049856/images/img_1.png\" style='vertical-align:middle;' width=\"103\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)参加此次研学活动的师生人数是多少？原计划租用多少辆甲型客车？</p><p>(2)若同时租用甲、乙两种型号的客车，要使每位师生都有座位且无空位，有哪几种租车方案？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024山西 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-17", "keyPointIds": "16434|16438", "keyPointNames": "方案问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555511536774586368", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555511536774586368", "title": "山西省部分学校2023−2024学年七年级下学期联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555511552381591552", "questionArticle": "<p>6．若 $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ 是二元一次方程 $ ax+by=-3 $ 的一组解，则 $ 2a-b+2024 $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-17", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555511536774586368", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555511536774586368", "title": "山西省部分学校2023−2024学年七年级下学期联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555174630446637056", "questionArticle": "<p>7．解方程组 $ \\begin{cases} x-5y=0 \\\\ 3x+7y=44 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025天津南大附中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 9, "referenceNum": 2, "createTime": "2025-03-17", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555174608292323328", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555174608292323328", "title": "天津市南开区南开大学附属中学2024−2025学年七年级下学期数学第一次月考试卷", "paperCategory": 1}, {"id": "446418758711681024", "title": "天津市河西区2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555174618941661184", "questionArticle": "<p>8．若方程组 $ \\begin{cases} ax-by=8 \\\\ ax+by=4 \\end{cases}  $ 的解为 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ ，则 $ a+b $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．−1C．3D．−3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津南大附中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-03-17", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555174608292323328", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555174608292323328", "title": "天津市南开区南开大学附属中学2024−2025学年七年级下学期数学第一次月考试卷", "paperCategory": 1}, {"id": "452595070614675456", "title": "天津市和平区耀华中学2023-2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "554779207441620992", "questionArticle": "<p>9．阅读理解题</p><p>定义：如果一个数的平方等于 $ -1 $ ,记为 $ i{^{2}}=-1 $ ，这个数 $ i $ 叫做虚数单位,那么和我们所学的实数对应起来就叫做复数，表示为 $ a+b{\\rm i} $ （<i>a</i>，<i>b</i>为实数），<i>a</i>叫这个复数的实部，<i>b</i>叫做这个复数的虚部，它的加、减、乘法运算与整式的加、减、乘法运算类似．</p><p>例如计算： $ \\left ( { 2+{\\rm i} } \\right ) +\\left ( { 3-4{\\rm i} } \\right ) =5-3{\\rm i} $ ．</p><p>(1)填空 $ i{^{3}}= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ i{^{4}}= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>;</p><p>(2)若两个复数相等，则它们的实部和虚部必须分别相等，完成下面问题,</p><p>已知： $ \\left ( { x+y } \\right ) +3i=\\left ( { 1-x } \\right ) -yi $ ，（<i>x</i>，<i>y</i>为实数），求<i>x</i>，<i>y</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024北京北工大附中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-16", "keyPointIds": "16319|16320|16426", "keyPointNames": "同底数幂的乘法|幂的乘方|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "554779180023455744", "questionFeatureName": "阅读材料题", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "554779180023455744", "title": "北京市朝阳区北京工业大学附属中学2023~2024学年七年级下学期4月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "554779479580647424", "questionArticle": "<p>10．已知 $ \\begin{cases} x=2 \\\\ y=-2 \\end{cases}  $ 是关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} ax+3y=-4 \\\\ bx+y=-8 \\end{cases}  $ 的解．</p><p>(1)求<i>a</i>，<i>b</i>的值；</p><p>(2)求 $ 2024a-b $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西临汾 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-16", "keyPointIds": "16305|16420|16424", "keyPointNames": "代数式求值|二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562034021342945280", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "562034021342945280", "title": "山西省临汾市霍州市多校联考2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}, {"id": "554779459481542656", "title": "河南省南阳市2024—2025学年下学期多校联考七年级数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 167, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 167, "timestamp": "2025-07-01T02:20:37.316Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}