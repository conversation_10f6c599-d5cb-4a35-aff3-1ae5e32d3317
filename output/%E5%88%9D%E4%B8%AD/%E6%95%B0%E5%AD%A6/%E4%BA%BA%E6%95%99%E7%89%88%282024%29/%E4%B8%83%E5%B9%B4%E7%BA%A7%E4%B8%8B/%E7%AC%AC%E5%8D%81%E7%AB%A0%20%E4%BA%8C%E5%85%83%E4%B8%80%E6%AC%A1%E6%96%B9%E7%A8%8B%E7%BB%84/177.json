{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 176, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "553364375811170304", "questionArticle": "<p>1．（1）计算： $ -2{^{2}}+\\sqrt[3] { -8 }+\\dfrac { 1 } { 4 }\\div \\left ( { -\\dfrac { 3 } { 4 } } \\right )  $ ；</p><p>（2）解二元一次方程组： $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {} 2x+y=5 \\\\ x+4y=-1 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ ；</p><p>（3）化简： $ {\\left( { x-2y } \\right) ^ {2}}+\\left ( { x+y } \\right ) \\left ( { 5y-x } \\right )  $ ；</p><p>（4）化简： $ \\left ( { a-2-\\dfrac { 5 } { a+2 } } \\right ) \\div \\dfrac { a{^{2}}-6a+9 } { 3-a } $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市育才中学校 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-13", "keyPointIds": "16333|16370|16424", "keyPointNames": "整式的混合运算|分式的混合运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553364348237815808", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "553364348237815808", "title": "重庆市育才中学校2024−2025学年九年级下学期第一次定时作业数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553365241041559552", "questionArticle": "<p>2．我国古代数学名著《孙子算经》中记载了一个这样的问题：用一根绳子去量一根木条，绳子还余 $ 4.5 $ 尺，将绳子对折后量木条，木条多一尺，设绳子的长度为<i>x</i>尺，木条的长度为<i>y</i>尺，则可列方程为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "640000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024宁夏银川 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-13", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553365222343352320", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "553365222343352320", "title": "2024年宁夏回族自治区 银川市 兴庆区银川市第十七中学二模检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553365940588552192", "questionArticle": "<p>3．某中学要为体育社团购买一些篮球和排球，若购买3个篮球和2个排球，共需560元；若购买2个篮球和4个排球，共需640元．</p><p>(1)求每个篮球和每个排球的价格分别是多少元；</p><p>(2)该中学决定购买篮球和排球共10个，总费用不超过1100元，那么最多可以购买多少个篮球？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "640000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024宁夏银川 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-03-13", "keyPointIds": "16424|16434|16486", "keyPointNames": "加减消元法解二元一次方程组|方案问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553365915435311104", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "553365915435311104", "title": "2024年宁夏银川外国语实验学校九年级中考二模数学试题", "paperCategory": 1}, {"id": "261861352901222400", "title": "湖南省长沙麓山外国语实验中学2022-2023学年九年级上学期第三次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553365598912159744", "questionArticle": "<p>4．某玩具厂共有300名生产工人，每个工人每天可生产玩具车架20个或车轮40个，且1个车架与4个车轮可配成一套，设有<i>x</i>个工人生产车架，<i>y</i>个工人生产车轮，下列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=300 \\\\ 40x=20y \\end{cases}  $ B． $ \\begin{cases} x+y=300 \\\\ 20x=40y \\end{cases}  $ C． $ \\begin{cases} x+y=300 \\\\ 4\\times 20x=40y \\end{cases}  $ D． $ \\begin{cases} x+y=300 \\\\ 20x=4\\times 40y \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000|610000|640000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024宁夏银川市唐徕中学 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-03-13", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553365585402306560", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "553365585402306560", "title": "2024年宁夏银川市唐徕中学 九年级下学期第二次模拟考试数学试题", "paperCategory": 1}, {"id": "493853839642107904", "title": "陕西省榆林市多校联考2024−2025学年八年级上学期开学数学试题", "paperCategory": 1}, {"id": "213714338271502336", "title": "辽宁省大连市高新园区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555334919896997888", "questionArticle": "<p>5．妇女节即将到来,小红打算买一束康乃馨和百合的组合鲜花送给妈妈,已知买2支康乃馨和3支百合需21元,3支康乃馨和2支百合需19元．</p><p>(1)买1支康乃馨和1支百合各需多少元?</p><p>(2)小红准备买康乃馨和百合共12支,且百合的支数不少于康乃馨的 $ \\dfrac{1}{2} {\\rm \\mathit{．}} $ 设买这束鲜花所需费用为<i>w</i>元,其中康乃馨有<i>x</i>支,求<i>w</i>与<i>x</i>之间的函数关系式,并直接写出满足上述条件且费用最少的买花方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 7, "createTime": "2025-03-13", "keyPointIds": "16434|16543", "keyPointNames": "方案问题|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555334911785213952", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555334911785213952", "title": "第21讲 一次函数的应用（练习）LJ七上", "paperCategory": 10}, {"id": "537329881111109632", "title": "第21讲 一次函数的应用（练习）", "paperCategory": 10}, {"id": "535479830952845312", "title": "第18讲　一次函数与方程、不等式+课题学习　选择方案（练习）", "paperCategory": 10}, {"id": "537320979829137408", "title": "第4讲　二元一次方程与一次函数（练习）LJ七下", "paperCategory": 10}, {"id": "522810363038965760", "title": "第10讲　一次函数与方程、不等式+课题学习　选择方案（练习）JJ", "paperCategory": 10}, {"id": "520595638171508736", "title": "第15讲　一次函数与方程、不等式+课题学习　选择方案（练习）", "paperCategory": 10}, {"id": "291870312194416640", "title": "浙江省衢州市实验学校教育集团2022-2023学年九年级下学期数学综合试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555175887110447104", "questionArticle": "<p>6．已知关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} x-y=3a \\\\ x+3y=2-a \\end{cases}  $ ，下列结论中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>①当这个方程组的解<i>x</i>，<i>y</i>的值互为相反数时，<i>a</i>＝﹣1；</p><p>②当<i>x</i>为正数，<i>y</i>为非负数时，﹣ $ \\dfrac { 1 } { 4 } {\\rm ＜\\mathit{a}\\leqslant } $  $ \\dfrac { 1 } { 2 } $ ；</p><p>③无论<i>a</i>取何值，<i>x</i>+2<i>y</i>的值始终不变．</p><p>A．①②B．②③C．①③D．①②③</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|440000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025黑龙江哈尔滨 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 45, "referenceNum": 2, "createTime": "2025-03-13", "keyPointIds": "16305|16420|16424|16489", "keyPointNames": "代数式求值|二元一次方程的解|加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555175874812747776", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555175874812747776", "title": "黑龙江省哈尔滨市松雷中学2024—2025学年七年级下学期数学开学考试试卷", "paperCategory": 1}, {"id": "209605252298874880", "title": "广东省潮州市湘桥区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "553364880276889600", "questionArticle": "<p>7．2024年4月18日上午10时08分，华为 $ Pura70 $ 系列正式开售，华为 $ Pura70Ultra $ 和 $ Pura70Pro $ 已在华为商城销售，约一分钟即告售罄．“ $ 4G $ 改变生活， $ 5G $ 改变社会”，不一样的 $ 5G $ 手机给人们带来了全新的体验，某营业厅现有<i>A</i>、<i>B</i>两种型号的 $ 5G $ 手机出售，售出1部<i>A</i>型、1部<i>B</i>型手机共获利600元，售出3部<i>A</i>型、2部<i>B</i>型手机共获利1400元．</p><p>(1)求<i>A</i>、<i>B</i>两种型号的手机每部利润各是多少元；</p><p>(2)某营业厅再次购进<i>A</i>、<i>B</i>两种型号手机共20部，其中<i>B</i>型手机的数量不超过<i>A</i>型手机数量的 $ \\dfrac { 2 } { 3 } $ ，请设计一个购买方案，使营业厅销售完这20部手机能获得最大利润，并求出最大利润．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|640000|430000|440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024湖南长沙 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 5, "createTime": "2025-03-13", "keyPointIds": "16435|16544", "keyPointNames": "分配问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "449188568906375168", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "449188568906375168", "title": "2024年湖南省长沙市青竹湖湘一外国语学校中考二模数学试题", "paperCategory": 1}, {"id": "452959932121391104", "title": "2024年山东省青岛市多校联考中考数学一模试题", "paperCategory": 1}, {"id": "453148278114263040", "title": "2024年湖南省长沙市开福区青竹湖湘一外国语学校中考数学二模试卷", "paperCategory": 1}, {"id": "553364857233383424", "title": "2024年宁夏回族自治区银川北塔中学九年级第二次中考模拟测试数学试题", "paperCategory": 1}, {"id": "439934856656625664", "title": "2024年广东省深圳市宝安区中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "553364654879186944", "questionArticle": "<p>8．有一个两位数和一个一位数，它们的和为39，若将两位数放在一位数的前面，得到的三位数比将一位数放在两位数的前面得到的三位数大27，求这两个数．若设两位数是<i>x</i>，一位数是<i>y</i>，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=39 \\\\ xy-yx=27 \\end{cases}  $ B． $ \\begin{cases} x+y=39 \\\\ 10x+y+27=100y+x \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=39 \\\\ 10x+y-27=10y+x \\end{cases}  $ D． $ \\begin{cases} x+y=39 \\\\ 10x+y-(100y+x)=27 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|130000|-1|610000|650000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024新疆乌鲁木齐 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 5, "createTime": "2025-03-13", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553364645366505472", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "553364645366505472", "title": "2024学年新疆乌鲁木齐第132中学中考数学二模试题", "paperCategory": 1}, {"id": "238986616865333248", "title": "陕西省西北工业大学附属中学2021-2022学年八年级上学期期中数学试题", "paperCategory": 1}, {"id": "147792461103931392", "title": "山东省枣庄市第十五中学2021-2022学年八年级上学期12月月考数学试题", "paperCategory": 1}, {"id": "152068957402341376", "title": "河北省邯郸市临漳县2021-2022学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": null, "title": "第五章  二元一次方程组  5  应用二元一次方程组——里程碑上的数", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "555177132315418624", "questionArticle": "<p>9．某班共有学生49人，一天，该班某男生因事请假，当天的男生人数恰为女生人数的一半，若该班男生人数为<i>x</i>，女生人数为<i>y</i>，则所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-y=49 \\\\ y=2\\left ( { x+1 } \\right )  \\end{cases}  $</p><p>B． $ \\begin{cases} x+y=49 \\\\ y=2\\left ( { x+1 } \\right )  \\end{cases}  $</p><p>C． $ \\begin{cases} x-y=49 \\\\ y=2\\left ( { x-1 } \\right )  \\end{cases}  $</p><p>D． $ \\begin{cases} x+y=49 \\\\ y=2\\left ( { x-1 } \\right )  \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏宿迁 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-03-13", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555177121989042176", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "555177121989042176", "title": "江苏省宿迁市2023−2024学年七年级下学期5月月考数学试题", "paperCategory": 1}, {"id": "129910303542779904", "title": "山东省潍坊市诸城市辛兴初中2019年中考模拟数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "553364379569266688", "questionArticle": "<p>10．某城市自行车赛线路为从起点出发，先骑行一段缓下坡路，再骑行一段平路到达折返点，然后从折返点沿原路线返回起点（起点即终点）．假定某运动员<i>A</i>在平路上骑行的速度始终是25千米/小时，下坡的骑行速度始终是30千米/小时，上坡的骑行速度始终是20千米/小时，已知该运动员从起点到折返点用时46分钟，从折返点回到起点用时51分钟．</p><p>(1)求比赛的下坡路程、下坡结束到折返点的平路路程分别是多少千米？</p><p>(2)某参赛运动员<i>B</i>骑行时，下坡的速度是上坡速度的2倍，且从起点到折返点的用时比从折返点到终点少用10分钟，求该运动员<i>B</i>骑行时的上坡速度是多少千米/小时？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市育才中学校 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-13", "keyPointIds": "16424|16430|16471|16476", "keyPointNames": "加减消元法解二元一次方程组|行程问题|解分式方程|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "553364348237815808", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "553364348237815808", "title": "重庆市育才中学校2024−2025学年九年级下学期第一次定时作业数学试题", "paperCategory": 1}, {"id": "371056121476325376", "title": "重庆市第八中学2023-2024学年九年级上学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 177, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 177, "timestamp": "2025-07-01T02:21:48.662Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}