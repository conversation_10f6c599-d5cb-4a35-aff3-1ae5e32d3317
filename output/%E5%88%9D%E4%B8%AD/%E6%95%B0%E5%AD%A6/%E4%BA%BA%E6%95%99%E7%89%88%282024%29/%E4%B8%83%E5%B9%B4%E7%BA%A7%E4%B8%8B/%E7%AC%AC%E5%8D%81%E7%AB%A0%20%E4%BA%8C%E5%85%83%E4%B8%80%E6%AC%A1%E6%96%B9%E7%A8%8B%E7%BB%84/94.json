{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 93, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "574353115379441664", "questionArticle": "<p>1．文化情境·数学文化《九章算术》是中国古代的一本重要数学著作，其中有一道方程的应用题：“五只雀、六只燕，共重16两，雀重燕轻．互换其中一只，恰好一样重．问每只雀、燕的重量各为多少？”解：设雀每只<i>x</i>两，燕每只<i>y</i>两，则可列出方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x+6y=16 \\\\ 5x+y=6y+x \\end{cases}  $ B． $ \\begin{cases} 5x+6y=16 \\\\ 4x+y=5y+x \\end{cases}  $ </p><p>C． $ \\begin{cases} 6x+5y=16 \\\\ 6x+y=5y+x \\end{cases}  $ D． $ \\begin{cases} 6x+5y=16 \\\\ 5x+y=4y+x \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000|420000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 3, "createTime": "2025-05-08", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574353096396021760", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574353096396021760", "title": "湖北初中名校联盟2025年4月中考模拟考试数学试卷", "paperCategory": 1}, {"id": "559468709024145408", "title": "浙江省杭州市杭州中学2024—2025学年下学期3月月考九年级数学试卷", "paperCategory": 1}, {"id": "564225208569077760", "title": "2025年广东省深圳市南山第二外国语（集团）中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "575327845720825856", "questionArticle": "<p>2．某商场出售甲、乙、丙三种型号的商品，若购买甲2件，乙3件，丙1件，共需130元；购买甲3件，乙5件，丙1件，共需205元．若购买甲，乙，丙各1件，则需<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川内江市第一中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575327820416589824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575327820416589824", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "575327838716338176", "questionArticle": "<p>3．方程 $ \\left ( { m-2 } \\right ) x-y{^{\\left  | { m-3 } \\right  | }}=1 $ 是关于<i>x</i>，<i>y</i>的二元一次方程，则 $ m= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025四川内江市第一中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575327820416589824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575327820416589824", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "575327836677906432", "questionArticle": "<p>4．国家“双减”政策实施后，某班开展了主题为“书香满校园”的读书活动．班级决定为在活动中表现突出的同学购买笔记本和碳素笔进行奖励（两种奖品都买）．其中笔记本每本3元，碳素笔每支2元，共花费28元，则共有几种购买方案（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．5B．4C．3D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川内江市第一中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575327820416589824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575327820416589824", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 11}], "questionTypeCode": "1"}, {"questionId": "575327833637036032", "questionArticle": "<p>5．若 $ {\\left( { a+b+5 } \\right) ^ {2}}+\\left  | { 2a-b+1 } \\right  | =0， $ 则 $ {\\left( { b-a } \\right) ^ {2017}}= $ （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1B．−1C． $ 5{^{2017}} $ D． $ -5{^{2017}} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川内江市第一中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16257|16424", "keyPointNames": "绝对值非负性的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575327820416589824", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575327820416589824", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 11}], "questionTypeCode": "1"}, {"questionId": "575327835109236736", "questionArticle": "<p>6．在长方形 $ ABCD $ 中，放入5个形状大小相同的小长方形（空白部分），其中 $ AB=8{ \\rm{ c } }{ \\rm{ m } } $ ， $ BC=12{ \\rm{ c } }{ \\rm{ m } } $ ，则阴影部分图形的总面积为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;） $ { \\rm{ c } }{ \\rm{ m } }{^{2}} $ </p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/07/2/1/0/0/0/575327475338616836/images/img_6.png\" style=\"vertical-align:middle;\" width=\"132\" alt=\"试题资源网 https://stzy.com\"></p><p>A．27B．29C．34D．36</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|370000|-1|610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川内江市第一中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 10, "referenceNum": 4, "createTime": "2025-05-07", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575327820416589824", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "575327820416589824", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 11}, {"id": "524199475230842880", "title": "山东省枣庄市市中区第十五中学2023−2024学年七年级上学期期末数学试题", "paperCategory": 1}, {"id": "267627897904472064", "title": "河北省保定市第十七中学2022-2023学年八年级上学期期末学业质量检测数学试卷", "paperCategory": 1}, {"id": "399277400641544192", "title": "陕西省西安交通大学附属中学2022-2023学年八年级上学期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574755393181622272", "questionArticle": "<p>7．解方程组 $ \\begin{cases} x+2y=5① \\\\ 3x-y=8② \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东河源 · 一模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574755372017164288", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "574755372017164288", "title": "2025年广东省河源市中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574755180199059456", "questionArticle": "<p>8．为设计一类推理型模型，某公司计划投入2200万元购进<i>A</i>，<i>B</i>两种型号的芯片共1000片，其中<i>A</i>型芯片至少800片．已知购进2片<i>A</i>型芯片和1片<i>B</i>型芯片共需6万元，购进1片<i>A</i>型芯片和3片<i>B</i>型芯片共需6.5万元．为了满足基本需求，请判断该公司计划投入的资金是否够用，并说明理由．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京西城 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16441|16486", "keyPointNames": "其他问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574755142626484224", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "574755142626484224", "title": "2025年北京市西城区九年级数学中考一模试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574755049697484800", "questionArticle": "<p>9．某水稻实验基地防治病害虫有无人机喷洒和人工打药两种方式．在一次作业中，一架无人机工作2小时和一名工人工作8小时，共完成了340亩的打药任务（不重复作业），通过测量对比发现无人机每小时作业的面积恰好是人工的6倍．请问一架无人机和一名工人共同作业8小时能否完成960亩的打药任务，并说明理由．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京通州 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574755008014491648", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574755008014491648", "title": "2025年北京市通州区中考数学一模试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574754738446573568", "questionArticle": "<p>10．如图，某校的饮水机有温水、开水两个按钮．利用图中信息解决下列问题：</p><table style=\"border: solid 1px;border-collapse: collapse; width:570pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 357pt;\"><p>物理常识</p><p>开水和温水混合时会发生热传递，开水放出的热量等于温水吸收的热量，可以转化为“开水的体积 $ \\times  $ 开水降低的温度 $ = $ 温水的体积 $ \\times  $ 温水升高的温度．”</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 213pt;\"><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/06/2/1/0/0/0/574754674563129346/images/img_17.png\" style=\"vertical-align:middle;\" width=\"272\" alt=\"试题资源网 https://stzy.com\"></p></td></tr></table><p>(1)王老师拿空水杯先接了 $ 14{ \\rm{ s } } $ 的温水，又接了 $ 8{ \\rm{ s } } $ 的开水，刚好接满，则王老师的水杯容量为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ m } }{ \\rm{ l } } $ ；</p><p>(2)嘉琪同学拿空水杯先接了一会儿温水，又接了一会儿开水，得到一杯 $ 210{ \\rm{ m } }{ \\rm{ l } } $ ，温度为 $ 40℃ $ 的水（不计热损失），求嘉琪同学的接水时间．（列二元一次方程组解决问题）</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025北京北京二中 · 模拟", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-07", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574754699510849536", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574754699510849536", "title": "2025年北京市第二中学九年级数学中考零模试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 94, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 94, "timestamp": "2025-07-01T02:11:56.210Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}