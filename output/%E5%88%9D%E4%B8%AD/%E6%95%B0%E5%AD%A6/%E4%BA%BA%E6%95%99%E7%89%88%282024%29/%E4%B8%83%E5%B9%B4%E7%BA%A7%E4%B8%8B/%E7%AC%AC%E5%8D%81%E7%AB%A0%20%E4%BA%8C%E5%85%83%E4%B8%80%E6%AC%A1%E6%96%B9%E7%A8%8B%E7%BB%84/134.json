{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 133, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "567105521242644480", "questionArticle": "<p>1．在 $ y=\\dfrac { 2 } { 3 }x-4 $ 中，如果<i>y</i>＝0，那么<i>x </i>=<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105508978499584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567105508978499584", "title": "吉林省长春市东北师范大学附属实验学校2024−2025学年七年级下学期3月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105516830236672", "questionArticle": "<p>2．下列方程组中，是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=2 \\\\ x+z=3 \\end{cases}  $ B． $ \\begin{cases} x+y=3 \\\\ y=1 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=3 \\\\ xy=2 \\end{cases}  $ D． $ \\begin{cases} x-\\dfrac { 1 } { y }=2 \\\\ 2x+y=1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025吉林长春 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105508978499584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567105508978499584", "title": "吉林省长春市东北师范大学附属实验学校2024−2025学年七年级下学期3月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567105064428412928", "questionArticle": "<p>3．宣城市郎溪县是我国绿茶之乡，县内有八万亩茶园．为拓宽销售渠道，进一步向外扩大郎溪县茶叶市场，某乡镇帮助农户将<i>A</i>，<i>B</i>两个品种的茶叶包装成茶叶礼盒后再出售．已知每件<i>A</i>品种茶叶礼盒比<i>B</i>品种茶叶礼盒的售价少20元，且出售2件<i>A</i>品种茶叶礼盒和1件<i>B</i>品种茶叶礼盒的总价共500元．</p><p>(1)求<i>A</i>，<i>B</i>两种茶叶礼盒每件的售价分别为多少元？</p><p>(2)已知<i>A</i>，<i>B</i>两种茶叶礼盒每件的成本分别为100元、110元，该乡镇计划在某农产品展销活动中售出<i>A</i>，<i>B</i>两种茶叶礼盒共100盒，且<i>A</i>品种茶叶礼盒售出的数量不超过<i>B</i>品种茶叶礼盒数量的1.5倍，总成本不超过10500元，一共有多少种满足条件的方案？</p><p>(3)在(2)的条件下，要使农户收益最大，该乡镇应怎样安排<i>A</i>，<i>B</i>两种茶叶礼盒的销售方案，并求出农户在这次农产品展销活动中的最大收益为多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南郑州 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16437|16490|16544", "keyPointNames": "销售利润问题|一元一次不等式组的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105040587988992", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567105040587988992", "title": "河南省郑州市枫杨外国语中学、朗悦慧外国语、行知中学等联考2024−2025学年下学期八年级第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "565307892770840576", "questionArticle": "<p>4．《九章算术》中记载：“今有甲乙二人持钱不知其数，甲得乙半而钱五十，乙得甲太半而亦钱五十，问：甲、乙持钱各几何？”大意是：甲、乙二人带着钱，不知是多少，若甲得到乙的钱数的 $ \\dfrac { 1 } { 2 } $ ，则甲的钱数为50，若乙得到甲的钱数的 $ \\dfrac { 2 } { 3 } $ ，则乙的钱数也能为50．问甲、乙各有多少钱？设甲有钱为<i>x</i>，乙有钱为<i>y</i>，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+\\dfrac { 2 } { 3 }y=50 \\\\ y+\\dfrac { 1 } { 2 }x=50 \\end{cases}  $ B． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=50 \\\\ y+\\dfrac { 2 } { 3 }x=50 \\end{cases}  $ </p><p>C． $ \\begin{cases} x-\\dfrac { 1 } { 2 }y=50 \\\\ y-\\dfrac { 2 } { 3 }x=50 \\end{cases}  $ D． $ \\begin{cases} x-\\dfrac { 2 } { 3 }y=50 \\\\ y-\\dfrac { 1 } { 2 }x=50 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|370000|620000|350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025甘肃定西 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 4, "createTime": "2025-04-15", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565307879491674112", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "565307879491674112", "title": "2025年甘肃省定西市安定区城区三校联考二模数学试题", "paperCategory": 1}, {"id": "194096687777882112", "title": "重庆市秀山区2022年九年级模拟考试数学试题", "paperCategory": 1}, {"id": "210009743749849088", "title": "山东省泰安市高新区2021-2022学年八年级下学期期末数学试题", "paperCategory": 1}, {"id": "202442183353344000", "title": "福建省厦门市思明区厦门第六中学2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567105520584138752", "questionArticle": "<p>5．如图是由7个形状、大小都相同的小长方形和一块正方形无缝隙拼合而成，则图中阴影部分的面积为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/14/2/1/0/0/0/567105477835792387/images/img_3.png\" style=\"vertical-align:middle;\" width=\"160\" alt=\"试题资源网 https://stzy.com\"></p><p>A．15B．30C．36D．40</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000|340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 7, "referenceNum": 2, "createTime": "2025-04-15", "keyPointIds": "16423|16439", "keyPointNames": "代入消元法解二元一次方程组|几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105508978499584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567105508978499584", "title": "吉林省长春市东北师范大学附属实验学校2024−2025学年七年级下学期3月考数学试题", "paperCategory": 1}, {"id": "220141375668396032", "title": "安徽省芜湖市无为市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567105519900467200", "questionArticle": "<p>6．方程 $ 2x+3y=17 $ 的正整数解的对数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．1对B．2对C．3对D．4对</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000|220000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 3, "createTime": "2025-04-15", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105508978499584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567105508978499584", "title": "吉林省长春市东北师范大学附属实验学校2024−2025学年七年级下学期3月考数学试题", "paperCategory": 1}, {"id": "175208969211781120", "title": "北京市海淀区第一零一中学2021-2022学年七年级下学期开学考试数学试题", "paperCategory": 1}, {"id": "158649172891049984", "title": "广东省茂名市电白区2021-2022学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567106628941553664", "questionArticle": "<p>7．七件甲商品和八件乙商品共重48千克，甲商品比乙商品重，互换其中一件，恰好一样重，设每件甲商品重 $ x $ 千克，每件乙商品重 $ y $ 千克，根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 7x+8y=48 \\\\ 6x+y=7y+x \\end{cases}  $　　　　B． $ \\begin{cases} 8x+7y=48 \\\\ 6x-y=7y-x \\end{cases}  $</p><p>C． $ \\begin{cases} 7x+y=48 \\\\ 7x-y=8y-x \\end{cases}  $　　　　D． $ \\begin{cases} 7x+8y=48 \\\\ 7x+y=8y+x \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市育才中学校 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-04-15", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567106615091961856", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567106615091961856", "title": "重庆市育才中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "545280610144657408", "title": "重庆市巴蜀中学2024−2025学年八年级上学期期末数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564940152050589696", "questionArticle": "<p>8．已知关于 $ x $ ， $ y $ 的二元一次方程 $ \\left ( { m+1 } \\right ) x+\\left ( { 2m﹣1 } \\right ) y+2﹣m=0 $ ，无论实数 $ m $ 取何值，此二元一次方程都有一个相同的解，则这个相同的解是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江杭州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564940131838238720", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564940131838238720", "title": "浙江省杭州市拱墅区文澜中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564940146480553984", "questionArticle": "<p>9．已知二元一次方程 $ 2x+3y=2 $ ，用含 $ x $ 的代数式表示 $ y $ ，则 $ y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江杭州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564940131838238720", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564940131838238720", "title": "浙江省杭州市拱墅区文澜中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564940145025130496", "questionArticle": "<p>10．某校组织一批学生去研学，若单独租用45座新能源客车若干辆，则有15人没有座位；若单独租用35座新能源客车，则用车数量将增加2辆，并空出15个座位．现在要求同时租用 45座和35座两种车型的新能源客车，既保证每人有座位，又保证每辆车不空座位，则需45座和35座两种车型的数量分别为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3辆、2辆B．2辆、3辆C．1辆、4辆D．4辆、1辆</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江杭州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-14", "keyPointIds": "16416|16434", "keyPointNames": "其他问题|方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564940131838238720", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564940131838238720", "title": "浙江省杭州市拱墅区文澜中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 134, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 134, "timestamp": "2025-07-01T02:16:43.178Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}