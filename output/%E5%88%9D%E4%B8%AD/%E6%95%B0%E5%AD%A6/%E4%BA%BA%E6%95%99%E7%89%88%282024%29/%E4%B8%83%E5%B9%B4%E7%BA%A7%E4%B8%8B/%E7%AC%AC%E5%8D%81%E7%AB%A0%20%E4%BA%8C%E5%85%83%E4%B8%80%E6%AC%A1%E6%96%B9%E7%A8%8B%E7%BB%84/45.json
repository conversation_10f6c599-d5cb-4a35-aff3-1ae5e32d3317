{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 44, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "586254070387617792", "questionArticle": "<p>1． 把一堆书分给几名学生，如果每人分到4本，那么多4本；如果每人分到5本，那么最后1名学生只分到3本．</p><p>问：一共有多少名学生？多少本书？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西南昌 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586254048610791424", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586254048610791424", "title": "江西省南昌二十八中、高新实验学校2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "586254068080750592", "questionArticle": "<p>2． $ (1) $ 计算： $ \\sqrt{\\dfrac{1}{81}}+\\sqrt[3]{-27}+\\sqrt{(-2)^{2}}+(-1)^{2020} $ ；</p><p> $ (2)\\begin{cases}3x+2y=20\\\\ 4x-5y=19\\end{cases}. $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西南昌 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16299|16424", "keyPointNames": "实数的运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586254048610791424", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586254048610791424", "title": "江西省南昌二十八中、高新实验学校2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "586254066105233408", "questionArticle": "<p>3． 如果无理数<i>m</i>值介于两个连续正整数之间，即满足 $ a &lt; m &lt; b( $ 其中<i>a</i>，<i>b</i>是连续正整数 $ ) $ ，我们则称无理数<i>m</i>的“博雅区间”为 $ (a,b). $ 例： $ 2 &lt; \\sqrt{5} &lt; 3 $ ，所以 $ \\sqrt{5} $ 的“博雅区间”为 $ (2,3). $ 若某一无理数的“博雅区间”为 $ (a,b) $ ，且满足 $ 3\\leqslant  \\sqrt{a}+b &lt; 21 $ ，其中 $ \\begin{cases}x=b\\\\ y=\\sqrt{a}\\end{cases} $ 是关于<i>x</i>、<i>y</i>的二元一次方程 $ bx+ay=p $ 的一组正整数解，则 $ p= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江西南昌 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16298|16420", "keyPointNames": "估算无理数的大小|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586254048610791424", "questionFeatureName": "阅读材料题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586254048610791424", "title": "江西省南昌二十八中、高新实验学校2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "586254063332798464", "questionArticle": "<p>4． 若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases}4(x+1)+3a(x-2y)=16\\\\ -b(x+1)+2(x-2y)=15\\end{cases} $ 的解为 $ \\begin{cases}x=3\\\\ y=5\\end{cases} $ ，则方程组 $ \\begin{cases}4x+3ay=16\\\\ -bx+2y=15\\end{cases} $ 的解为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江西南昌 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586254048610791424", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586254048610791424", "title": "江西省南昌二十八中、高新实验学校2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "586254060098990080", "questionArticle": "<p>5． 现有如图 $ (1) $ 的小长方形纸片若干块，已知小长方形的长为<i>a</i>，宽为 $ b. $ 用3个如图 $ (2) $ 的全等图形和8个如图 $ (1) $ 的小长方形，拼成如图 $ (3) $ 的大长方形，若大长方形的宽为30<i>cm</i>，则图 $ (3) $ 中阴影部分面积与整个图形的面积之比为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/06/2/1/0/0/0/586254031695159297/images/img_7.png\" style=\"vertical-align:middle;\" alt=\"试题资源网 https://stzy.com\"></p><p>A．  $ \\dfrac{1}{5} $ B．  $ \\dfrac{1}{6} $ C．  $ \\dfrac{1}{7} $ D．  $ \\dfrac{1}{8} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江西南昌 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586254048610791424", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586254048610791424", "title": "江西省南昌二十八中、高新实验学校2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 11}], "questionTypeCode": "1"}, {"questionId": "586254058312216576", "questionArticle": "<p>6． 《孙子算经》有一道题．大概意思是：用一根绳子去量一根木头的长，绳子还余 $ 4.5 $ 尺，将绳子对折再量木头，则木头还剩余1尺，问木头长多少尺？可设木头为<i>x</i>尺，绳长为<i>y</i>尺，则所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．  $ \\begin{cases}y=x-4.5\\\\ y=2x-1\\end{cases} $ B．  $ \\begin{cases}y=x+4.5\\\\ y=2x-1\\end{cases} $ C．  $ \\begin{cases}y=x-4.5\\\\ 0.5y=x+1\\end{cases} $ D．  $ \\begin{cases}y=x+4.5\\\\ 0.5y=x-1\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江西南昌 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586254048610791424", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "586254048610791424", "title": "江西省南昌二十八中、高新实验学校2024−2025学年七年级下学期第三次月考数学试卷", "paperCategory": 11}], "questionTypeCode": "1"}, {"questionId": "339439584906878977", "questionArticle": "<p>7．为进行某项数学综合与实践活动，小明到一个批发兼零售的商店购买所需工具．该商店规定一次性购买该工具达到一定数量后可以按批发价付款，否则按零售价付款．小明如果给学校九年级学生每人购买一个，只能按零售价付款，需用3600元；如果多购买60个，则可以按批发价付款，同样需用3600元，若按批发价购买60个与按零售价购买50个所付款相同，求这个学校九年级学生有多少人？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2023山东泰安 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 12, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16437|16476", "keyPointNames": "销售利润问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "339439572441407488", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "339439572441407488", "title": "2023年山东省泰安市中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584432447850328064", "questionArticle": "<p>8．独具徽味特色的合肥卤菜深受全国吃货们的喜爱．“徽徽卤味食品”的老板将本店的卤肉技术处理后销往外地，外地的食客需付费（包含卤肉费和快递费），其中卤肉每千克 $ a $ 元．若购买卤肉数量在 $ 2{ \\rm{ k } }{ \\rm{ g } } $ 及以内（包含 $ 2{ \\rm{ k } }{ \\rm{ g } } $ ）一次性支付快递费30元；若超出 $ 2{ \\rm{ k } }{ \\rm{ g } } $ ，超出的部分每千克支付 $ b $ 元．外地某食客两次购买卤肉 $ 3{ \\rm{ k } }{ \\rm{ g } } $ 、 $ 5{ \\rm{ k } }{ \\rm{ g } } $ ，分别支付各种费用265元和435元．根据以上条件求 $ a $ ， $ b $ 的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽铜陵 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584432417366126592", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "584432417366126592", "title": "2025年5月安徽省铜陵市中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584431168147206144", "questionArticle": "<p>9．2024年12月4日，“春节”列入联合国教科文组织人类非物质文化遗产代表作名录，中国的春节文化将更好地走向世界．2025年春节临近，某商家购进了一批春联和灯笼进行销售，已知2副春联和1个灯笼的总售价为24元；1副春联和3个灯笼的总售价为42元．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/08/2/1/0/0/0/586892765872566273/images/img_1.png\" style='vertical-align:middle;' width=\"236\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）请你分别求出1副春联的售价和1个灯笼的售价；</p><p>（2）已知商家实际销售期间每副春联盈利3元，每个灯笼盈利5元，某个时段内该商家通过销售这批春联和灯笼共盈利40元，且春联和灯笼都有销售，请你求出该商家在这个时段内所有可能的销售方案（即销售了多少副春联和多少个灯笼）．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏无锡市天一实验中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584431136908029952", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584431136908029952", "title": "江苏省无锡市天一实验学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "584431165710315520", "questionArticle": "<p>10．解方程（组）：</p><p>（1） $ \\begin{cases} y=2x-4 \\\\ x+y=-1 \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} 3a+5b-8=0 \\\\ 2a-3b=-1 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏无锡市天一实验中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-06", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "584431136908029952", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "584431136908029952", "title": "江苏省无锡市天一实验学校2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 45, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 45, "timestamp": "2025-07-01T02:06:08.609Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}