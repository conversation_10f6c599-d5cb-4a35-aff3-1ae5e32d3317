{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 89, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "574751508119461888", "questionArticle": "<p>1．若 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是关于 $ x，y $ 的二元一次方程 $ x-2y=a $ 的解，则 $ a $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -5 $ B． $ -3 $ C．5D．3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏连云港市新海初级中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-10", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574751497012944896", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574751497012944896", "title": "江苏省连云港市新海初级中学2024−2025学年七年级下学期数学期中试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574751322366320640", "questionArticle": "<p>2．关于<i>x</i>，<i>y</i>的二元一次方程均可以变形为 $ ax+by=c $ 的形式，其中<i>a</i>，<i>b</i>，<i>c</i>，均为常数且 $ a\\ne 0 $ ， $ b\\ne 0 $ ，规定：方程 $ ax+by=c $ 的“关联系数”记为 $ \\left ( { a,b,c } \\right )  $ ．</p><p>(1)二元一次方程 $ 4x-3y=5 $ 的“关联系数”为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p>(2)已知关于<i>x</i>，<i>y</i>的二元一次方程的“关联系数”为 $ \\left ( { 2,-1,1 } \\right )  $ ，若 $ \\begin{cases} x=m+n \\\\ y=m+5 \\end{cases}  $ ，为该方程的一组解，且 $ m,n $ 均为正整数，求<i>m</i>，<i>n</i>的值．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025湖北黄冈 · 期中", "showQuestionTypeCode": "42", "showQuestionTypeName": "综合题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-10", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574751294113488896", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574751294113488896", "title": "湖北省黄冈市2024−2025学年八年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574751723543109632", "questionArticle": "<p>3．（1）解方程组 $ \\begin{cases} x-2y=2 \\\\ \\dfrac { x } { 2 }+\\dfrac { y } { 3 }=1 \\end{cases}  $ ；</p><p>（2）解不等式组： $ \\begin{cases} 5-x\\geqslant  x-1 \\\\ \\dfrac { 2x-1 } { 3 }-\\dfrac { 5x+1 } { 2 }  &lt;  1 \\end{cases}  $ ；</p><p>（3）解一元二次方程 $ x{^{2}}-6x-3=0 $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏苏州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-10", "keyPointIds": "16424|16453|16489", "keyPointNames": "加减消元法解二元一次方程组|配方法解一元二次方程|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574751694002626560", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574751694002626560", "title": "江苏省苏州市振华中学2024−2025学年八年级下学期期中数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574750054612770816", "questionArticle": "<p>4．某市为提高政务服务中心窗口服务质量，经过一段时间的观察，早上 $ 9:00 $ 开始平均每天有 $ n $ 个人在窗口等候，设购票人数按固定的速度增加，且每个窗口每分钟减少的排队人数也是固定的．若同时开放3个售票窗口，需要30分钟恰好不出现排队现象（即排队的人全部刚好购完票）；若同时开放5个售票窗口，需要15分钟恰好不出现排队现象．为减少旅客排队购票时间，车站承诺8分钟内不出现排队现象，则至少需要同时开放<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>个售票窗口．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽合肥 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-10", "keyPointIds": "16426|16486", "keyPointNames": "二元一次方程组的应用|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574750035021176832", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574750035021176832", "title": "安徽省合肥市第五十中学2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574750053757132800", "questionArticle": "<p>5．方程组 $ \\begin{cases} 3x+7y=k \\\\ 2x+5y=20 \\end{cases}  $ 的解 $ x $ ， $ y $ 都是正数，则整数 $ k= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽合肥 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 12, "referenceNum": 1, "createTime": "2025-05-10", "keyPointIds": "16424|16490", "keyPointNames": "加减消元法解二元一次方程组|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574750035021176832", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574750035021176832", "title": "安徽省合肥市第五十中学2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575779742902689792", "questionArticle": "<p>6．幻方的历史很悠久，传说最早出现在夏禹时代的“洛书”．三阶幻方的填写规则是将9个不同的整数填入方格中，使得每行、每列、每条对角线上的三个数之和都相等．</p><p>（1）如图1所示幻方，则 $ x= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>；（2）如图2所示幻方，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ b= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>.</p><table style=\"border: solid 1px;border-collapse: collapse; width:132.75pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 41.25pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 42.75pt;\"><p>9</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 46.5pt;\"><p>&nbsp;</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 41.25pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 42.75pt;\"><p><i>x</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 46.5pt;\"><p>&nbsp;</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 41.25pt;\"><p> $ x+3 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 42.75pt;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 46.5pt;\"><p> $ 2x-4 $ </p></td></tr></table><p style=\"text-indent:14pt;\">图1</p><table style=\"border: solid 1px;border-collapse: collapse; width:134.25pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 47.25pt;\"><p> $ 4b-2 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 47.25pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 38.25pt;\"><p>12</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 47.25pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 47.25pt;\"><p> $ 2a+1 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 38.25pt;\"><p>7</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 47.25pt;\"><p> $ 3b-3 $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 47.25pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 38.25pt;\"><p> $ 2a $ </p></td></tr></table><p style=\"text-indent:14pt;\">图2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆复旦中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575779715107037184", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "575779715107037184", "title": "重庆市复旦中学教育集团2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575779747000524800", "questionArticle": "<p>7．列二元一次方程组解应用题：</p><p>某工厂生产两种型号的智能手表：型号<i>A</i>和型号<i>B</i>．生产每块型号<i>A</i>需要消耗120克稀有金属和180毫升电子溶液，生产每块型号<i>B</i>需要消耗150克稀有金属和90毫升电子溶液．某日工厂收到一份紧急订单，生产完成后统计发现，当日共消耗 $ 19.5 $ 千克稀有金属和 $ 22.5 $ 升电子溶液．请问当天工厂分别生产了多少块型号<i>A</i>和型号<i>B</i>的手表？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆复旦中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575779715107037184", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "575779715107037184", "title": "重庆市复旦中学教育集团2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575779731351576576", "questionArticle": "<p>8．某工艺品店推出每件价格分别为 $ 100 $ 元、 $ 150 $ 元、 $ 200 $ 元三种工艺品，小安用 $ 5000 $ 元买了这三种工艺品共 $ 30 $ 件，则单价为 $ 200 $ 元的数量比单价为 $ 100 $ 元的数量多（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 5 $ 件</p><p>B． $ 10 $ 件</p><p>C． $ 15 $ 件</p><p>D． $ 20 $ 件</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆复旦中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16420|16438", "keyPointNames": "二元一次方程的解|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575779715107037184", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "575779715107037184", "title": "重庆市复旦中学教育集团2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "575779734878986240", "questionArticle": "<p>9．若 $ \\begin{cases} x=3 \\\\ y=-2 \\end{cases}  $ 是二元一次方程 $ ax+by=-\\dfrac { 1 } { 2 } $ 的一个解，则 $ 6a-4b+2026 $ 的值为 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆复旦中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575779715107037184", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "575779715107037184", "title": "重庆市复旦中学教育集团2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575779744353918976", "questionArticle": "<p>10．解方程组：</p><p>（1） $ \\begin{cases} y=2x-1① \\\\ x-2y=1② \\end{cases}  $ ；</p><p>（2） $ \\begin{cases} 2x-3y=4① \\\\ 4x+y=1② \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆复旦中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575779715107037184", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "575779715107037184", "title": "重庆市复旦中学教育集团2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 90, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 90, "timestamp": "2025-07-01T02:11:28.626Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}