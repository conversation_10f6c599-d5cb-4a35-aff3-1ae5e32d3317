{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 14, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "590999318166806528", "questionArticle": "<p>1．已知关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} 3x+2y=m+1 \\\\ 2x+y=m-1 \\end{cases}  $ ．</p><p>（1）若 $ 5x+3y=4 $ ，求 $ m $ 的值；</p><p>（2）若<i>x</i>，<i>y</i>均为非负数，求 $ m $ 的取值范围；</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏扬州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590999288739569664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590999288739569664", "title": "江苏高邮市南海中学2024−2025学年七年级下学期数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "590999312043122688", "questionArticle": "<p>2．若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x+5y=6m-3 \\\\ 5x+y=-3 \\end{cases}  $ 的解满足 $ x+y $  $   &lt;  3 $ ，则<i>m</i>的所有非负整数之和为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025江苏扬州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-25", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "590999288739569664", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "590999288739569664", "title": "江苏高邮市南海中学2024−2025学年七年级下学期数学第二次月考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592680936019369984", "questionArticle": "<p>3．（1）解方程组： $ \\begin{cases} 3x-y=5①， \\\\ x+y=3②. \\end{cases}  $ </p><p>（2）如图， $ AD=BC,\\angle DAB=\\angle CBA $ ，求证： $ AC=BD $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/25/2/1/0/0/0/593134992957419521/images/img_1.png\" style='vertical-align:middle;' width=\"144\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "650000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025新疆 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16424|26491", "keyPointNames": "加减消元法解二元一次方程组|全等三角形的判定与性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592680914380955648", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "592680914380955648", "title": "2025年新疆维吾尔族自治区中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592678381591764992", "questionArticle": "<p>4．同学们准备在劳动课上制作艾草香包，需购买 $ A $ ， $ B $ 两种香料．已知 $ A $ 种材料的单价比 $ B $ 种材料的单价多3元，且购买4件 $ A $ 种材料与购买6件 $ B $ 种材料的费用相等．</p><p>（1）求 $ A $ 种材料和 $ B $ 种材料的单价；</p><p>（2）若需购买 $ A $ 种材料和 $ B $ 种材料共50件，且总费用不超过360元，则最多能购买 $ A $ 种材料多少件？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南 · 中考真题", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16441|16486", "keyPointNames": "其他问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592678353150189568", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "592678353150189568", "title": "2025年湖南省中考数学真题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588454323824140288", "questionArticle": "<p>5．为拓展学生视野，促进书本知识与生活实践的深度融合，荆州市某中学组织八年级全体学生前往松滋洈水研学基地开展研学活动．在此次活动中，若每位老师带队14名学生，则还剩10名学生没老师带；若每位老师带队15名学生，就有一位老师少带6名学生，现有甲、乙两种大型客车，它们的载客量和租金如表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p style=\"text-align:center;\">&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">甲型客车</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">乙型客车</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p style=\"text-align:center;\">载客量（人/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">35</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">30</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p style=\"text-align:center;\">租金（元/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">400</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p style=\"text-align:center;\">320</p></td></tr></table><p>学校计划此次研学活动的租金总费用不超过3000元，为安全起见，每辆客车上至少要有2名老师．</p><p>（1）参加此次研学活动的老师和学生各有多少人？</p><p>（2）既要保证所有师生都有车坐，又要保证每辆车上至少要有2名老师，可知租车总辆数为<u>　 　</u>辆；</p><p>（3）学校共有几种租车方案？最少租车费用是多少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河北廊坊 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16434|16490|16543", "keyPointNames": "方案问题|一元一次不等式组的应用|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588454287186894848", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588454287186894848", "title": "河北省廊坊市第四中学2024−2025学年八年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588454429990363136", "questionArticle": "<p>6．【问题情境】某中学计划组织七年级师生进行春季研学活动，活动负责人李老师了解到，某租车公司有<i>A</i>、<i>B</i>两种型号的客车共15辆，它们的载客量、每天的租金和车辆数如下表所示，已知在15辆客车都坐满的情况下，共载客570人．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 91.35pt;\"><p>车型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p><i>A</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p><i>B</i></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 91.35pt;\"><p>载客量/（人/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>45</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>30</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 91.35pt;\"><p>租金/（元/辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>400</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p>280</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 91.35pt;\"><p>车辆数（辆）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p><i>a</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 27.75pt;\"><p><i>b</i></p></td></tr></table><p>（1）求表中<i>a</i>和<i>b</i>的值；</p><p>（2）李老师结合学校的实际情况，计划租用<i>A</i>型、<i>B</i>型客车共12辆，同时送七年级师生到基地参加研学活动，且租车总费用不超过4300元．求最多能租用多少辆<i>A</i>型客车？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北廊坊 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16440|16486", "keyPointNames": "表格或图示问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588454404010844160", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588454404010844160", "title": "河北省廊坊市第四中学2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588454425426960384", "questionArticle": "<p>7．计算：</p><p>（1） $ \\left  | { \\sqrt { 2 }-2 } \\right  | +\\sqrt { \\dfrac { 4 } { 9 } }-\\sqrt[3] { 8 } $ ．</p><p>（2）解方程组： $ \\begin{cases} x+4y=1, \\\\ 3x-2y=-4. \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北廊坊 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16288|16290|16424", "keyPointNames": "算术平方根|立方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588454404010844160", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588454404010844160", "title": "河北省廊坊市第四中学2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "592539285141626880", "questionArticle": "<p>8．某商场计划新建地上和地下两类充电桩以缓解电动汽车充电难的问题．已知新建1个地上充电桩和2个地下充电桩需要0.8万元；新建2个地上充电桩和1个地下充电桩需要0.7万元．</p><p>（1）求新建一个地上充电桩和一个地下充电桩各需多少万元？</p><p>（2）若该商场计划用不超过16.3万元的资金新建60个充电桩，且地上充电桩的数量不超过20个，求共有几种建造方案？并列出所有方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024河北邯郸 · 期末", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16434|16486", "keyPointNames": "方案问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592539258491019264", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592539258491019264", "title": "河北省邯郸市2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "588454417986265088", "questionArticle": "<p>9．第23届盱眙龙虾节举办之际，一知名大型企业若干人来盱考察，若每3人坐一辆车，则有2辆空车；若每2人坐一辆车，则有9人没有车坐，问人与车各有多少？设有<i>x</i>人，<i>y</i>辆车，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3\\left ( { y-2 } \\right ) =x \\\\ 2y-9=x \\end{cases}  $ B． $ \\begin{cases} 3y-2=x \\\\ 2y-9=x \\end{cases}  $ C． $ \\begin{cases} 3\\left ( { y-2 } \\right ) =x \\\\ 2y+9=x \\end{cases}  $ D． $ \\begin{cases} 3y-2=x \\\\ 2y+9=x \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北廊坊 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588454404010844160", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "588454404010844160", "title": "河北省廊坊市第四中学2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "592539280305594368", "questionArticle": "<p>10．按要求完成下列各小题</p><p>（1）解方程组： $ \\begin{cases} 4x-3y-10=0 \\\\ 3x-y=0 \\end{cases}  $ </p><p>（2）解不等式组 $ \\begin{cases} 3x-1  &lt;  x+1 \\\\ 2\\left ( { 2x-1 } \\right ) \\leqslant  5x+1 \\end{cases}  $ ，并求其整数解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024河北邯郸 · 期末", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-24", "keyPointIds": "16423|16424|16489", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "592539258491019264", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "592539258491019264", "title": "河北省邯郸市2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 15, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 15, "timestamp": "2025-07-01T02:02:34.204Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}