{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 157, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "559471867620270080", "questionArticle": "<p>1．第12届世界运动会将于2025年8月在成都举行，为迎接此次盛会，某社区举办了趣味运动比赛，并购买了<i>A</i>，<i>B</i>两种奖品．已知购买3份<i>A</i>种奖品和2份<i>B</i>种奖品需164元，购买5份<i>A</i>种奖品和4份<i>B</i>种奖品需292元．</p><p>(1)每份<i>A</i>种奖品与每份<i>B</i>种奖品的价格分别为多少元？</p><p>(2)该社区计划购进<i>A</i>，<i>B</i>两种奖品共100份，且总费用不超过3120元，那么最多能购进<i>A</i>种奖品多少份？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000|430000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025辽宁沈阳 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-25", "keyPointIds": "16441|16486", "keyPointNames": "其他问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577295741913178112", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "577295741913178112", "title": "辽宁省沈阳市第四十三中学2024−2025学年 八年级下学期4月数学期中试卷", "paperCategory": 1}, {"id": "559471838495023104", "title": "湖南省长沙市部分学校联考2024−2025学年九年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "558038591714140160", "questionArticle": "<p>2．2022年北京冬（残）奥运会吉祥物“冰墩墩”和“雪容融”深受广大观众朋友的喜爱，欣欣购买了2件“冰墩墩”和5件“雪容融”共花了310元，而小华购买了3件“冰墩 墩”和2件“雪容融”共245元．</p><p>(1)求两种纪念品的单价．</p><p>(2)某旅行团准备花12000元购买 “冰墩墩”和“雪容融”共250件，最多可以购买多少件“冰墩墩”．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东清远 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-25", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "558038564115619840", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "558038564115619840", "title": "2025年广东省清远市连州市北山中学九年级数学中考一模试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557696410654646272", "questionArticle": "<p>3．（1）计算： $ \\left  | { -4 } \\right  | +{\\left( { { \\rm{ π } }-\\sqrt { 2 } } \\right) ^ {0}}-{\\left( { \\dfrac { 1 } { 2 } } \\right) ^ {-1}} $ </p><p>（2）解方程组： $ \\begin{cases} 3x+y=8 \\\\ 2x-y=7 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南濮阳 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-25", "keyPointIds": "16299|16323|16372|16424", "keyPointNames": "实数的运算|零指数幂|负整数指数幂|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557696391859970048", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "557696391859970048", "title": "河南省濮阳市2024−2025学年九年级下学期第一次联考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559114308476510208", "questionArticle": "<p>4．我国古代数学名著《九章算术》中有这样一道题：今有善田一亩，价三百；恶田七亩，价五百．今并买一顷，价钱一万．问善、恶各几何？意思是：今有好田 $ 1 $ 亩价值 $ 300 $ 钱，坏田 $ 7 $ 亩价值 $ 500 $ 钱．今用 $ 10000 $ 钱购入好、坏田共 $ 1 $ 顷（ $ 1 $ 顷 $ =100 $ 亩）．问好田、坏田各有多少亩？如果设好田为 $ x $ 亩，坏田为 $ y $ 亩，那么可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=1 \\\\ 300x+500y=10000 \\end{cases}  $ B． $ \\begin{cases} x+y=100 \\\\ 300x+500y=10000 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=1 \\\\ 300x+\\dfrac { 7 } { 500 }y=10000 \\end{cases}  $ D． $ \\begin{cases} x+y=100 \\\\ 300x+\\dfrac { 500 } { 7 }y=10000 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东日照 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-25", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559114296166227968", "questionFeatureName": "数学文化题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "559114296166227968", "title": "山东省日照市田家炳实验中学2024−2025学年九年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "558038490434281472", "questionArticle": "<p>5．小华从家里到学校的路是一段平路和一段下坡路，假设他始终保持平路每分钟走60m，下坡路每分钟走80m，上坡路每分钟走40m，则他从家里到学校需10min，从学校到家里需15min．问：从小华家到学校的平路和下坡路各有多远？</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/20/2/1/0/0/0/558038443143503887/images/img_15.png\" style=\"vertical-align:middle;\" width=\"332\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽六安 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-25", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "558038464907747328", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "558038464907747328", "title": "2025年安徽省六安市清水河学校九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559473005476225024", "questionArticle": "<p>6．已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} 2x-y=5 \\\\ 2ax+by=8 \\end{cases}  $ 和 $ \\begin{cases} x+y=4 \\\\ ax+3by=9 \\end{cases}  $ 有相同的解．</p><p>(1)求这两个方程组的相同解．</p><p>(2)求 $ a $ ， $ b $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆七中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-25", "keyPointIds": "16424|16427", "keyPointNames": "加减消元法解二元一次方程组|同解方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559472981002461184", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "559472981002461184", "title": "重庆市第七中学校2024−2025学年七年级下学期第一次数学定时作业", "paperCategory": 1}, {"id": "557959326305918976", "title": "重庆市第七中学校2024−2025年度七年级下学期月考数学试题", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "559473003827863552", "questionArticle": "<p>7．解方程组：</p><p>(1) $ \\begin{cases} x=2y \\\\ 3x-5y=8 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 3x-2y=6 \\\\ 2x+3y=17 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆七中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 4, "createTime": "2025-03-25", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559472981002461184", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "559472981002461184", "title": "重庆市第七中学校2024−2025学年七年级下学期第一次数学定时作业", "paperCategory": 1}, {"id": "557959326305918976", "title": "重庆市第七中学校2024−2025年度七年级下学期月考数学试题", "paperCategory": 11}, {"id": "257546523881283584", "title": "重庆市沙坪坝区第八中学校2022-2023学年八年级上学期期中数学试题", "paperCategory": 1}, {"id": "257535074182144000", "title": "重庆市第八中学校2022-2023学年八年级上学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559473001193840640", "questionArticle": "<p>8．在长方形 $ ABCD $ 中，放入六个形状、大小完全相同的小长方形，所标尺寸如图所示，则图中阴影部分的面积是 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> $ { \\rm{ c } }{ \\rm{ m } }{^{2}} $ ．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/27/2/1/0/0/0/560469602540494849/images/img_1.png\" style='vertical-align:middle;' width=\"150\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆七中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-25", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559472981002461184", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "559472981002461184", "title": "重庆市第七中学校2024−2025学年七年级下学期第一次数学定时作业", "paperCategory": 1}, {"id": "557959326305918976", "title": "重庆市第七中学校2024−2025年度七年级下学期月考数学试题", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "559473000338202624", "questionArticle": "<p>9．已知关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} 2x-y=3a \\\\ x-2y=5-a \\end{cases}  $ 的解满足 $ x-y=5 $ ，则常数 $ a $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆七中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-03-25", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559472981002461184", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "559472981002461184", "title": "重庆市第七中学校2024−2025学年七年级下学期第一次数学定时作业", "paperCategory": 1}, {"id": "557959326305918976", "title": "重庆市第七中学校2024−2025年度七年级下学期月考数学试题", "paperCategory": 11}], "questionTypeCode": "6"}, {"questionId": "559472993472126976", "questionArticle": "<p>10．解方程组 $ \\begin{cases} ax+by=2 \\\\ cx-7y=8 \\end{cases}  $ 时，一学生因把 $ c $ 看错得到方程组的解是 $ \\begin{cases} x=-2 \\\\ y=2 \\end{cases}  $ ，而正确的解是 $ \\begin{cases} x=3 \\\\ y=-2 \\end{cases}  $ ，则 $ a+b+c $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．5　　　　B．6　　　　C．7　　　　D．8</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆七中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-25", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559472981002461184", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "559472981002461184", "title": "重庆市第七中学校2024−2025学年七年级下学期第一次数学定时作业", "paperCategory": 1}, {"id": "557959326305918976", "title": "重庆市第七中学校2024−2025年度七年级下学期月考数学试题", "paperCategory": 11}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 158, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 158, "timestamp": "2025-07-01T02:19:32.448Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}