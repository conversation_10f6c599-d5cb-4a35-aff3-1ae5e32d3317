{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 101, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "572597237659770880", "questionArticle": "<p>1．在农作物不同的生长阶段运用科技手段实现精准施肥，可以提高产量和质量．某农场为种植小麦需要配制复合肥料．小麦在生长过程中需要大量的氮 $ {\\rm （N）} $ 促进叶片生长，适量的磷 $ {\\rm （P）} $ 促进根系发育，以及足够的钾 $ {\\rm （K）} $ 提高果实品质．农场有两种原料可供使用，其氮、磷、钾含量及成本如下表：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 42.05pt;\"><p style=\"text-align:center;\">原料</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72.1pt;\"><p style=\"text-align:center;\">氮 $ {\\rm （N）} $ 含量</p><p style=\"text-align:center;\">（千克/吨）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 70.35pt;\"><p style=\"text-align:center;\">磷 $ {\\rm （P）} $ 含量</p><p style=\"text-align:center;\">（千克/吨）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72.1pt;\"><p style=\"text-align:center;\">钾 $ {\\rm （K）} $ 含量</p><p style=\"text-align:center;\">（千克/吨）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 56.95pt;\"><p style=\"text-align:center;\">成本</p><p style=\"text-align:center;\">（元/吨）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 42.05pt;\"><p style=\"text-align:center;\">原料<i>A</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72.1pt;\"><p style=\"text-align:center;\">20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 70.35pt;\"><p style=\"text-align:center;\">40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72.1pt;\"><p style=\"text-align:center;\">30</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 56.95pt;\"><p style=\"text-align:center;\">600</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 42.05pt;\"><p style=\"text-align:center;\">原料<i>B</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72.1pt;\"><p style=\"text-align:center;\">50</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 70.35pt;\"><p style=\"text-align:center;\">10</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 72.1pt;\"><p style=\"text-align:center;\">40</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 56.95pt;\"><p style=\"text-align:center;\">800</p></td></tr></table><p>(1)在小麦播种前农场根据土壤检测结果配制底肥，要求肥料中含有240千克氮、120千克磷，求使用<i>A</i>，<i>B</i>两种原料各多少吨？</p><p>(2)4月份，小麦进入拔节期，农场根据小麦长势和底肥用量计划配制追肥，要求追肥用量是底肥用量的 $ \\dfrac { 1 } { 2 } $ ，且含有不少于100千克钾，请设计出成本最低的配制方案．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南许昌 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16438|16486|16543", "keyPointNames": "和差倍分问题|一元一次不等式的应用|分配方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572597198958927872", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572597198958927872", "title": "2025年河南省许昌市九年级数学第一次中招模拟考试试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572597730326913024", "questionArticle": "<p>2．解方程组： $ \\begin{cases} x+2y=7 \\\\ 3x-2y=5 \\end{cases}  $ .</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025江苏苏州 · 一模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572597705270140928", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "572597705270140928", "title": "2025年江苏省苏州市中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572597351363158016", "questionArticle": "<p>3．“扎龙湿地芦苇米”富含硒元素，是齐齐哈尔市特色物产．现将160千克芦苇米全部分装为大箱和小箱销售，其中每个大箱可装20千克，每个小箱可装15千克，大、小箱都要装满，则所装的箱数最多为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．8箱B．9箱C．10箱D．11箱</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江齐齐哈尔 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16420|16438", "keyPointNames": "二元一次方程的解|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572597333931630592", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572597333931630592", "title": "2025年黑龙江省齐齐哈尔市中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "572596997565227008", "questionArticle": "<p>4．五指山革命根据地纪念园，是全国三十条红色旅游经典线路之一．为了带领学生感受历史的厚重与革命的激情，某校准备组织七年级学生去五指山革命根据地纪念园进行研学活动．请依据以下对话，求每辆大客车和每辆小客车可分别载学生多少人．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/30/2/1/0/0/0/572596952644231190/images/img_22.png\" style=\"vertical-align:middle;\" width=\"554\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "460000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025海南 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572596972542009344", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572596972542009344", "title": "2025年海南省部分学校中考第一次模拟数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "572596582559817728", "questionArticle": "<p>5．地理老师介绍到：长江比黄河长836千米，黄河长度的6倍比长江长度的5倍多1284千米．小东根据地理老师的介绍，设长江长为<i>x</i>千米，黄河长为<i>y</i>千米，然后通过列、解二元一次方程组，正确地求出了长江和黄河的长度，那么小东列的方程组可能是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-y=836 \\\\ 6y-5x=1284 \\end{cases}  $ B． $ \\begin{cases} x-y=836 \\\\ 6x-5y=1284 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=836 \\\\ 6y-5x=1284 \\end{cases}  $ D． $ \\begin{cases} x+y=836 \\\\ 5x-6y=1284 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳 · 模拟", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572596569834299392", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "572596569834299392", "title": "2025年广东省深圳市罗湖区部分学校九年级下学期4月中考适应性考试", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "570266339970949120", "questionArticle": "<p>6．九年级举行读书活动．学校要求各班班长根据学生阅读需求，统计需采购的书籍类型和数量，如下表所示．</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>文学类（本/人）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>科普类（本/人）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>九（1）班</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>2</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>九（2）班</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>5</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 64.5pt;\"><p>共计（本）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>246</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 88.45pt;\"><p>340</p></td></tr></table><p>请你根据以上信息，求九（1）班和九（2）班各有多少人．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽合肥四十五中 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "570266316398960640", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "570266316398960640", "title": "安徽省合肥市第四十五中学2024−2025学年九年级下学期中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884296509628416", "questionArticle": "<p>7．在《哪吒2》的剧情中，哪吒和敖丙一起炼制 $ A，B $ 两种丹药．已知炼制一颗 $ \\mathrm{ A } $ 丹药需要3份火莲精华和2份龙鳞粉末，炼制一颗<i>B</i>丹药需要5份火莲精华和4份龙鳞粉末．经过合作，哪吒和敖丙一共收集了45份火莲精华与34份龙鳞粉末，且炼制完丹药时这些材料刚好用完．则炼制 $ \\mathrm{ A } $ 丹药与 $ B $ 丹药各多少颗？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东潍坊 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-04-30", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884270358142976", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884270358142976", "title": "山东省潍坊市2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "571884380517343232", "title": "山东省潍坊市诸城市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884288913743872", "questionArticle": "<p>8．某市举行中学生足球联赛，比赛的计分规则为：胜1场得3分，平1场得1分，负1场得0分．某中学足球队在12场比赛中，平和负的场数之和等于胜的场数，共得20分．设该队在联赛中胜 $ x $ 场，平 $ y $ 场、负 $ z $ 场，则列三元一次方程组为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东潍坊 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-04-30", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884270358142976", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884270358142976", "title": "山东省潍坊市2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "571884380517343232", "title": "山东省潍坊市诸城市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884289609998336", "questionArticle": "<p>9．解下列方程组：</p><p>(1) $ \\begin{cases} x=y+2 \\\\ 3x+2y=16 \\end{cases}  $ </p><p>(2) $ \\begin{cases} 3a+2b=8 \\\\ 2a-3b=1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东潍坊 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-04-30", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884270358142976", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571884270358142976", "title": "山东省潍坊市2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "571884380517343232", "title": "山东省潍坊市诸城市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884287475097600", "questionArticle": "<p>10．“方程”二字最早见于我国（九章算术）这部经典著作中，该书的第八章名为“方程”，如：从左到右列出的算筹数分别表示方程中未知数 $ x，y $ 的系数与相应的常数项，即<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/28/2/1/0/0/0/571884185838723083/images/img_11.png\" style=\"vertical-align:middle;\" width=\"118\" alt=\"试题资源网 https://stzy.com\">可表示方程 $ 3x+y=41 $ ．按照上述规则，则<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/28/2/1/0/0/0/571884185838723084/images/img_12.png\" style=\"vertical-align:middle;\" width=\"123\" alt=\"试题资源网 https://stzy.com\">表示的方程是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东潍坊 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-04-30", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884270358142976", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884270358142976", "title": "山东省潍坊市2024−2025学年七年级下学期期中考试数学试卷", "paperCategory": 1}, {"id": "571884380517343232", "title": "山东省潍坊市诸城市2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 102, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 102, "timestamp": "2025-07-01T02:12:52.152Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}