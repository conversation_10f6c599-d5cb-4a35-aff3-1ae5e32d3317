{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 103, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "571884774274408448", "questionArticle": "<p>1．解方程组：</p><p>(1) $ \\begin{cases} 3x-y=5 \\\\ 5x+2y=23 \\end{cases}  $ </p><p>(2) $ \\begin{cases} \\dfrac { 2x } { 3 }+\\dfrac { y } { 4 }=1 \\\\ 2\\left ( { x-1 } \\right ) -3y=6 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884753671987200", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571884753671987200", "title": "省级重点 福建省福州第十六中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884497479704576", "questionArticle": "<p>2．把方程 $ 2x-y=4 $ 写成用含 $ x $ 的代数式表示 $ y $ 的形式，正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ y=2x+4 $ B． $ x=\\dfrac { y } { 2 }-2 $ C． $ x=\\dfrac { y } { 2 }+2 $ D． $ y=2x-4 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东淄博 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884487744724992", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884487744724992", "title": "山东省淄博市张店区第八中学2024−2025学年七年级下学期期中考试数学试卷 【五四制】", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571884777315278848", "questionArticle": "<p>3．若关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} 2x+y=3k \\\\ x+2y=4 \\end{cases}  $ 的解满足 $ x-y=2 $ ，则<i>k</i>＝<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884753671987200", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571884753671987200", "title": "省级重点 福建省福州第十六中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571884766376534016", "questionArticle": "<p>4．《九章算术》是中国传统数学的重要著作，方程术是它的最高成就．其中记载：今有共买物，人出八，盈三；人出七，不足四，问人数、物价各几何？译文：今有人合伙购物，每人出8钱，会多3钱；每人出7钱，又会差4钱，问人数、物价各是多少？设合伙人数为<i>x</i>人，物价为<i>y</i>钱，以下列出的方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y-8x=3 \\\\ {y-7x=4} \\end{cases}  $ B． $ \\begin{cases} y-8x=3 \\\\ {7x-y=4} \\end{cases}  $ </p><p>C． $ \\begin{cases} 8x-y=3 \\\\ 7x-y=4 \\end{cases}  $ D． $ \\begin{cases} 8x-y=3 \\\\ y-7x=4 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884753671987200", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "571884753671987200", "title": "省级重点 福建省福州第十六中学2024−2025学年七年级下学期数学期中考试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571885071738642432", "questionArticle": "<p>5．解下列方程组</p><p>(1) $ \\begin{cases} x-y=-3 \\\\ 5x+y=2 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 3x-4y=1 \\\\ 5x+2y=6 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津南开中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571885045096423424", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "571885045096423424", "title": "天津市南开中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571885067183628288", "questionArticle": "<p>6．已知二元一次方程 $ 3x-4y=1 $ ，用含<i>x</i>的代数式表示<i>y</i>是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川内江市第一中学 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 3, "createTime": "2025-04-30", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575327820416589824", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "575327820416589824", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 11}, {"id": "581950860290207744", "title": "四川省内江市第一中学2024−2025学年七年级下学期期中考试数学试题", "paperCategory": 1}, {"id": "571885045096423424", "title": "天津市南开中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "571885063312285696", "questionArticle": "<p>7．下列命题中：</p><p>①对顶角相等；</p><p>②在同一平面内，如果两条直线都垂直于同一条直线，那么这两条直线互相平行；</p><p>③如果两个实数的平方相等，那么这两个实数也相等；</p><p>④立方根等于它本身的实数只有 $ 0 $ 或 $ 1 $ ；</p><p>⑤二元一次方程 $ x+y=3 $ 的整数解只有 $ 3 $ 组．</p><p>其中真命题有（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 5 $ 个　　　　B． $ 4 $ 个　　　　C． $ 3 $ 个　　　　D． $ 2 $ 个</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025天津南开中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16290|16420|16751|30377", "keyPointNames": "立方根|二元一次方程的解|判断命题真假|垂直于同一条直线的两直线平行", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571885045096423424", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "571885045096423424", "title": "天津市南开中学2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "571884936690442240", "questionArticle": "<p>8．中国古代数学专著《张邱建算经》中记载：“今有清酒一斗直粟十斗，醑酒一斗直粟三斗．今持粟三斛，得酒五斗，问清醑各几何？”意思是：现在一斗清酒价值10斗谷子，一斗醑酒价值3斗谷子，现在拿30斗谷子，共换了5斗酒，问清酒、醑酒各几斗？如果设清酒 $ x $ 斗，醑酒 $ y $ 斗，那么可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=30 \\\\ \\dfrac { x } { 10 }+\\dfrac { y } { 3 }=5 \\end{cases}  $ B． $ \\begin{cases} x+y=5 \\\\ 3x+10y=30 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=5 \\\\ 10x+3y=30 \\end{cases}  $ D． $ \\begin{cases} x+y=5 \\\\ \\dfrac { x } { 3 }+\\dfrac { y } { 5 }=30 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川成都 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-30", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "571884921242820608", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "571884921242820608", "title": "四川省成都市实验外国语学校2024—2025学年下学期第一次诊断性考试九年级数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "572226050823856128", "questionArticle": "<p>9．若关于 $ x,y $ 的方程 $ 2x{^{\\left  | { m } \\right  | }}+\\left ( { m-1 } \\right ) y=3 $ 是二元一次方程，则 $ m $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0B． $ -1 $ C．1D．2</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东广州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-04-30", "keyPointIds": "16258|16419", "keyPointNames": "绝对值方程|二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572226039591510016", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "572226039591510016", "title": "广东省广州市中山大学附属中学2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}, {"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "572596845165191168", "questionArticle": "<p>10．港珠澳大桥是一座连接香港、珠海和澳门的桥隧工程．根据规定，内地货车载重后总质量超过 $ 49 $ 吨的禁止通行，现有一辆自重 $ 6 $ 吨的货车，要运输若干套某种设备，每套设备由 $ 1 $ 个 $ A $ 部件和 $ 3 $ 个 $ B $ 部件组成，这种设备必须成套运输，已知 $ 2 $ 个 $ A $ 部件和 $ 1 $ 个 $ B $ 部件的总质量为 $ 2 $ 吨， $ 4 $ 个 $ A $ 部件和 $ 3 $ 个 $ B $ 部件的质量相等．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/01/2/1/0/0/0/573124156637618177/images/img_1.png\" style='vertical-align:middle;' width=\"177\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)求 $ 1 $ 个 $ A $ 部件和 $ 1 $ 个 $ B $ 部件的质量各为多少吨？</p><p>(2)该货车要从珠海运输这种成套设备经由港珠澳大桥到香港，一次最多可运输多少套这种设备？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西柳州 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-04-30", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "572596820309745664", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "572596820309745664", "title": "2025年广西壮族自治区柳州市中考二模数学试题", "paperCategory": 1}, {"id": "473972092804833280", "title": "湖南省长沙市华益中学2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 104, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 104, "timestamp": "2025-07-01T02:13:07.434Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}