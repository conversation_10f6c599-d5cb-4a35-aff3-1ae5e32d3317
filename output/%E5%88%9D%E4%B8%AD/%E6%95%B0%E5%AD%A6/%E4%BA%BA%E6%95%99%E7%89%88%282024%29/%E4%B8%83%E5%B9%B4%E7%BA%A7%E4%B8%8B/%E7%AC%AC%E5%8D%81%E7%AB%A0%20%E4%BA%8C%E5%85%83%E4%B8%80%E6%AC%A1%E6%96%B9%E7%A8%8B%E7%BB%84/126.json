{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 125, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "568255203868188672", "questionArticle": "<p>1．“方程”二字最早见于我国《九章算术》这部经典著作中,该书的第八章名为“方程”.如:<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/18/2/1/0/0/0/568255175900569605/images/img_5.png\" style=\"vertical-align:middle;\" width=\"144\" alt=\"试题资源网 https://stzy.com\">从左到右列出的算筹数分别表示方程中未知数<i>x</i>,<i>y</i>的系数与相应的常数项,即可表示方程<i>x</i>+4<i>y</i>=23,则<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/18/2/1/0/0/0/568255175900569606/images/img_6.png\" style=\"vertical-align:middle;\" width=\"144\" alt=\"试题资源网 https://stzy.com\">表示的方程是<u>　 　　　</u><i>.&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000|-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东济宁 · 一模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-04-21", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567478024628641792", "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": "567478024628641792", "title": "2025年山东省济宁市任城区一模检测九年级数学试题", "paperCategory": 1}, {"id": null, "title": "中考新考向备训《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "567102411875065856", "questionArticle": "<p>2．关于 $ x $ ， $ y $ 的二元一次方程 $ ax+by=c $ ，（ $ a $ ， $ b $ ， $ c $ 是常数，且 $ a\\ne 0 $ ， $ b\\ne 0 $ ）有无数组解，如果我们把每组解 $ x $ 和 $ y $ 的值都分别作为点的横坐标和纵坐标，并描在平面直角坐标系中，会得到一个图形，我们把这个图形叫做这个二元一次方程的图象．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/14/2/1/0/0/0/567102345030443022/images/img_14.png\" style=\"vertical-align:middle;\" width=\"178\" alt=\"试题资源网 https://stzy.com\"></p><p>例如：⋯ $ \\begin{cases} x=-1 \\\\ y=-5 \\end{cases}  $ 、 $ \\begin{cases} x=-\\dfrac { 1 } { 2 } \\\\ y=-4 \\end{cases}  $ 、 $ \\begin{cases} x=0 \\\\ y=-3 \\end{cases}  $ 、 $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $ 、 $ \\begin{cases} x=\\dfrac { 3 } { 2 } \\\\ y=0 \\end{cases}  $ 、 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ ⋯都是方程 $ 2x-y=3 $ 的解，对应可以得到点⋯ $ \\left ( { -1,-5 } \\right )  $ 、 $ \\left ( { -\\dfrac { 1 } { 2 },-4 } \\right )  $ 、 $ \\left ( { 0,-3 } \\right )  $ 、 $ \\left ( { 1,-1 } \\right )  $ 、 $ \\left ( { \\dfrac { 3 } { 2 },0 } \\right )  $ 、 $ \\left ( { 2,1 } \\right )  $ ⋯，把所有的解对应的点都描在坐标系内，得到了方程 $ 2x-y=3 $ 的图象．</p><p>回答下列问题：</p><p>(1) $ \\left ( { 13,2 } \\right )  $ 和 $ \\left ( { \\dfrac { 72 } { 5 },1 } \\right )  $ 是二元一次方程 $ ax+by=79 $ 图象上的两个点，求 $ a $ ， $ b $ 的值；</p><p>(2)若 $ P\\left ( { \\sqrt { m },\\left  | { n } \\right  |  } \\right )  $ 是（1）所求二元一次方程图象上的一个点，且满足 $ 3\\sqrt { m }+2k{^{2}}+5\\left  | { n } \\right  | =57 $ ， $ m $ ， $ n $ ， $ k $ 都是实数，求 $ \\sqrt { m }+5k{^{2}}+3\\left  | { n } \\right  |  $ 的最小值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "4", "diffcultName": "较难", "questionSource": "2025重庆重庆市巴蜀中学校 · 月考", "showQuestionTypeCode": "42", "showQuestionTypeName": "综合题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-21", "keyPointIds": "16424|16535|16540", "keyPointNames": "加减消元法解二元一次方程组|一次函数的图象和性质|一次函数与二元一次方程（组）", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567102378689732608", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567102378689732608", "title": "重庆市巴蜀中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567102410910375936", "questionArticle": "<p>3．列方程（组）求解下面问题．</p><p>为了表彰在七年级上期半期考试中成绩优异和进步显著的同学，某班家委会购买了“巴蜀人”口袋笔记本和“ $ { \\rm{ B } }{ \\rm{ a } }{ \\rm{ s } }{ \\rm{ h } }{ \\rm{ u } } $ ”简装笔记本两种文创产品作为奖品，每种笔记本的单价如下图所示．购买的口袋笔记本的本数的2倍比购买的简装笔记本本数的3倍少4本，共计花费198元，其中8元是运费．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/14/2/1/0/0/0/567102345030443021/images/img_13.jpg\" style=\"vertical-align:middle;\" width=\"367\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)口袋笔记本和简装笔记本各购买了多少本？</p><p>(2)本学期开学后，家委会决定购买“旗开得胜”文具8件套和“状元及第”文具4件套两种文具套装（包邮）来奖励七年级上期末考试成绩优异和进步显著的同学．其中“旗开得胜”文具8件套的单价比口袋笔记本的单价少 $ \\dfrac { 1 } { 30 } $ ，“状元及第”文具4件套的单价比简装笔记本的单价增加了 $ k{ \\rm{ \\% } } $ （ $ k $ 是个正整数），而购买的“旗开得胜”文具8件套的数量比口袋笔记本增加了 $ \\dfrac { k } { 20 } $ ，“状元及第”文具4件套的数量与简装笔记本一样多，最终期末表彰的总费用比半期表彰的总费用增加了 $ \\dfrac { k } { 45 } $ ，请求出 $ k $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市巴蜀中学校 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-21", "keyPointIds": "16406|16438", "keyPointNames": "销售盈亏问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567102378689732608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567102378689732608", "title": "重庆市巴蜀中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567841031472848896", "questionArticle": "<p>4．我国古代数学著作《九章算术》有题如下：“今有五雀、六燕，集称之衡，雀俱重，燕俱轻．一雀一燕交而处，衡适平；并燕、雀重一斤，问燕、雀一枚各重几何?”其大意是：现在有5只雀和6只燕，用秤来称它们，发现雀比较重，燕比较轻．将一只雀和一只燕交换位置，重量相等；5只雀和6只燕的重量为一斤．问每只雀和每只燕各重多少斤?设每只雀为<i>x</i>斤，每只燕为<i>y</i>斤，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} 5x+6y=1 \\\\ 4x+y=5y+x \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ B． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} 5x+6y=1 \\\\ 6x-5y=0 \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ </p><p>C． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} 5x+6y=1 \\\\ 5x+y=4y+x \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ D． $ \\left \\{\\hspace{-0.5em}  \\begin{array}{l} {\\hspace{-0.5em}  \\begin{array} {l} 5x+6y=1 \\\\ 4x-y=5y+x \\end{array} \\hspace{-0.5em} } \\end{array} \\hspace{-0.5em} \\right.  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025四川泸州 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 3, "createTime": "2025-04-21", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567841015496744960", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "567841015496744960", "title": "2025年四川省泸州市泸县第五中学中考二模数学试题", "paperCategory": 1}, {"id": "534866541696819200", "title": "安徽省淮北市部分学校2024−2025学年七年级上学期1月期末数学试题", "paperCategory": 1}, {"id": "553049394968305664", "title": "2024年四川省成都实验外国语学校中考数学二模试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567840899377438720", "questionArticle": "<p>5．某商场购进<i>A</i>，<i>B</i>两种商品，已知购进3件<i>A</i>商品比购进4件<i>B</i>商品费用多60元；购进5件<i>A</i>商品和2件<i>B</i>商品总费用为620元．</p><p>(1)求<i>A</i>，<i>B</i>两种商品每件进价各为多少元？</p><p>(2)该商场计划购进<i>A</i>，<i>B</i>两种商品共60件，且购进<i>B</i>商品的件数不少于<i>A</i>商品件数的2倍．若<i>A</i>商品按每件150元销售，<i>B</i>商品按每件80元销售，为满足销售完<i>A</i>，<i>B</i>两种商品后获得的总利润不低于1770元，则购进<i>A</i>商品的件数最多为多少？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000|510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川泸州 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 11, "referenceNum": 3, "createTime": "2025-04-21", "keyPointIds": "16437|16441|16490", "keyPointNames": "销售利润问题|其他问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "457847614844739584", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "457847614844739584", "title": "2024年四川省泸州市中考数学试题", "paperCategory": 1}, {"id": "567840876858220544", "title": "2025年四川省泸州市702教育集团中考三模冲刺数学试卷", "paperCategory": 1}, {"id": "470586339571834880", "title": "辽宁省营口市2023−2024学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "569344258634719232", "questionArticle": "<p>6．我国古代数学经典著作《九章算术》中有这样一题，原文是今有共买物，人出七，盈二；人出六，不足三．问人数、物价各几何？”意思是今有人合伙购物，每人出七钱，会多二钱；每人出六钱，又差三钱，问人数、货物总价各多少？设人数为<i>x</i>人，货物总价为<i>y</i>钱，可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y=7x-2 \\\\ y=6x+3 \\end{cases}  $　　　　B． $ \\begin{cases} y=7x+2 \\\\ y=6x-3 \\end{cases}  $</p><p>C． $ \\begin{cases} 7x=y+2 \\\\ y=6x-3 \\end{cases}  $　　　　D． $ \\begin{cases} 7x=y-2 \\\\ y=6x+3 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-04-21", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "569344245242306560", "questionFeatureName": "数学文化题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "569344245242306560", "title": "2025年广东省深圳市罗湖区部分学校中考数学二模试题", "paperCategory": 1}, {"id": "418221886151106560", "title": "四川省成都市锦江区锦江区嘉祥外国语学校2023-2024学年九年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "567102405617164288", "questionArticle": "<p>7．本周末天气晴朗，小敏和小丽两个家庭共14人相约外出旅游，决定在某特色民宿住宿一晚，该民宿有单人间（可住一人），标间（可住两人），三人间三种房型，她们准备每种房型至少选一间，共预订7间房，如果每个房间都住满，订房方案有<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>种．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市巴蜀中学校 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567102378689732608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567102378689732608", "title": "重庆市巴蜀中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567102408284741632", "questionArticle": "<p>8．解二元一次方程组</p><p>(1) $ \\begin{cases} 4x-3y=5 \\\\ 2x+y=5 \\end{cases}  $ </p><p>(2) $ \\begin{cases} \\dfrac { 4x } { 3 }+\\dfrac { 3 } { 2 }y=\\dfrac { 1 } { 6 } \\\\ 7x-3y=\\dfrac { 9 } { 2 } \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|320000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市巴蜀中学校 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-04-20", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567102378689732608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567102378689732608", "title": "重庆市巴蜀中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "572594502625435648", "title": "江苏省扬州市邗江区实验学校2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567102401791959040", "questionArticle": "<p>9．已知 $ \\left ( { 5-a } \\right ) x+y{^{\\left  | { a } \\right  | -4}}=2 $ 是关于 $ x $ ， $ y $ 的二元一次方程，则 $ a $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆市巴蜀中学校 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567102378689732608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567102378689732608", "title": "重庆市巴蜀中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567102398100971520", "questionArticle": "<p>10．已知 $ \\begin{cases} x=4 \\\\ y=2 \\end{cases}  $ 是二元一次方程组 $ \\begin{cases} mx+ny=30 \\\\ nx-my=6 \\end{cases}  $ 的解，则 $ 6n+2m $ 的平方根是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．36B． $ \\pm 36 $ C．6D． $ \\pm 6 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市巴蜀中学校 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-20", "keyPointIds": "16287|16420|16424", "keyPointNames": "平方根|二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567102378689732608", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "567102378689732608", "title": "重庆市巴蜀中学校2024−2025学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 126, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 126, "timestamp": "2025-07-01T02:15:45.414Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}