{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 65, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "579859937129439232", "questionArticle": "<p>1．已知关于x，y的二元一次方程组 $ \\begin{cases} ax+by=1 \\\\ a{^{2}}x-b{^{2}}y=ab+3 \\end{cases}  $ 的解为 $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $ ，求a、b的值．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东华南师大附中 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16420|16423|16456", "keyPointNames": "二元一次方程的解|代入消元法解二元一次方程组|因式分解法解一元二次方程", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579859900060180480", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579859900060180480", "title": "广东省广州市华南师范大学附属中学2024−2025学年下学期九年级数学二模试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "580246556617519104", "questionArticle": "<p>2．随着“低碳生活、绿色出行”理念的普及，新能源汽车正逐渐成为人们喜爱的交通工具．某汽车销售公司计划购进一批新能源汽车进行销售，据了解 $ 1 $ 辆 $ \\mathrm{ A } $ 型汽车、 $ 2 $ 辆 $ B $ 型汽车的进价共计 $ 60 $ 万元； $ 2 $ 辆 $ \\mathrm{ A } $ 型汽车、 $ 3 $ 辆 $ B $ 型汽车的进价共计 $ 95 $ 万元．</p><p>（1）求 $ A，B $ 两种型号的汽车每辆进价分别为多少万元？</p><p>（2）若该公司计划正好用 $ 200 $ 万元购进以上两种型号的新能源汽车（两种型号的汽车均购买），请你帮助该公司设计购买方案．</p><p>（3）若该汽车销售公司销售一辆 $ \\mathrm{ A } $ 型汽车可获利 $ 4000 $ 元，销售一辆<i>B</i>型汽车可获利 $ 7000 $ 元，在（ $ 2 $ ）的购买方案中，假如这些新能源汽车全部售出，哪种方案获利最大？最大利润是多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆18中 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 3, "createTime": "2025-05-21", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "580246524669505536", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "580246524669505536", "title": "重庆市第十八中学2024—2025学年下学期期中考试七年级数学试题", "paperCategory": 1}, {"id": "559469768685690880", "title": "重庆市万州二中2024−2025学年七年级下学期尖子班内部第一次月考数学卷", "paperCategory": 1}, {"id": "544972343086456832", "title": "安徽省淮北市2024-2025学年七年级上学期1月期末考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579859413541892096", "questionArticle": "<p>3．某文具店出售普通练习本和精装练习本，15本普通练习本和10本精装练习本的销售总额为145元；20本普通练习本和5本精装练习本的销售总额110元．</p><p>（1）求普通练习本和精装练习本的销售单价；</p><p>（2）已知普通练习本的进价为2元/本，精装练习本的进价为7元/本，该商店计划购进500本练习本，其中普通练习本的数量不低于精装练习本数量的2倍，请你帮文具店设计进货方案，使这500本练习本全部售完后，文具店获得利润最大，并求出最大利润．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门一中 · 临考冲刺", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-05-21", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579859378720780288", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579859378720780288", "title": "福建省厦门第一中学2024−2025学年九年级下学期模拟考试数学试题", "paperCategory": 1}, {"id": "586984919923994624", "title": "2025年福建省厦门第十一中学九年级中考数学检测试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579859747760807936", "questionArticle": "<p>4．商场计划拨款9万元，从厂家购进50台电视机．已知该厂家生产三种不同型号的电视机，出厂价分别为甲种每台1500元，乙种每台2100元，丙种每台2500元．</p><p>（1）若商场同时购进其中两种不同型号的电视机共50台，用去9万元，请你研究一下商场的进货方案．</p><p>（2）若商场用9万元同时购进三种不同型号的电视机50台，请你研究一下是否可行？若可行，请给出设计方案；若不可行，请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建厦门一中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16434|16438|16444", "keyPointNames": "方案问题|和差倍分问题|三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579859714340593664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579859714340593664", "title": "福建省厦门第一中学2024—2025学年七年级下学期数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579859742077526016", "questionArticle": "<p>5．计算与解方程（组）</p><p>（1） $ \\sqrt { {\\left( { -5 } \\right) ^ {2}} }-\\sqrt[3] { -27 }+\\left  | { \\sqrt { 3 }-2 } \\right  |  $ </p><p>（2） $ 4{\\left( { x-1 } \\right) ^ {2}}=9 $ </p><p>（3） $ \\begin{cases} x-2y=5① \\\\ 3x+4y=25② \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门一中 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16287|16290|16299|16424", "keyPointNames": "平方根|立方根|实数的运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579859714340593664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579859714340593664", "title": "福建省厦门第一中学2024—2025学年七年级下学期数学期中试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "579859733907021824", "questionArticle": "<p>6．如图，长青化工厂从 $ A $ 地购买原料运回工厂，制成产品后运到 $ B $ 地销售，该工厂与 $ A $ ， $ B $ 两地有公路、铁路相连，公路运价为 $ 1.5 $ 元/（吨·千米），铁路运价为 $ 1.2 $ 元/（吨·千米），这两次运输共支出公路运费 $ 15000 $ 元，铁路运费 $ 97200 $ 元．请问该工厂的原料和产品各重有多少吨？若设原料重 $ x $ 吨，产品重 $ y $ 吨，则可以列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/07/2/1/0/0/0/586527925140234241/images/img_1.png\" style='vertical-align:middle;' width=\"382\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} 10x+20y=15000 \\\\ 120x+110y=97200 \\end{cases}  $</p><p>B． $ \\begin{cases} 1.5\\left ( { 10x+20y } \\right ) =15000 \\\\ 1.2\\left ( { 120x+110y } \\right ) =97200 \\end{cases}  $</p><p>C． $ \\begin{cases} 20x+10y=15000 \\\\ 110x+120y=97200 \\end{cases}  $</p><p>D． $ \\begin{cases} 1.5\\left ( { 20x+10y } \\right ) =15000 \\\\ 1.2\\left ( { 110x+120y } \\right ) =97200 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-05-21", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "581951220270538752", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "581951220270538752", "title": "重庆市万州中学教育集团2024—2025学年下学期七年级数学期中考试卷", "paperCategory": 1}, {"id": "579859714340593664", "title": "福建省厦门第一中学2024—2025学年七年级下学期数学期中试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579859729033240576", "questionArticle": "<p>7．若 $ \\begin{cases} x=m \\\\ y=2 \\end{cases}  $ ，是二元一次方程 $ 5x-3y=14 $ 的一个解，则 $ m $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 1.6 $ B． $ 2 $ C． $ 3 $ D． $ 4 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建厦门一中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579859714340593664", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579859714340593664", "title": "福建省厦门第一中学2024—2025学年七年级下学期数学期中试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "579859232767389696", "questionArticle": "<p>8．编织大、小两种中国结共12个，总计用绳 $ 40{ \\rm{ m } } $ ．已知编织1个大号中国结需用绳 $ { { 4 } }{ \\rm{ m } } $ ，编织1个小号中国结需用绳 $ { { 3 } }{ \\rm{ m } } $ ．问这两种中国结各编织了多少个．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/20/2/1/0/0/0/579859167625650179/images/img_14.jpg\" style=\"vertical-align:middle;\" width=\"152\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京东城 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "579859203323375616", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "579859203323375616", "title": "2025年北京市东城区九年级下学期一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015012909785088", "questionArticle": "<p>9．为庆祝中国共产党成立100周年，我校计划举行“学党史·感党恩”知识竞答活动．并计划购置篮球、钢笔、笔记本作为奖品，采购员刘老师在某文体用品店购买了作为奖品的三种物品，回到学校后发现发票被弄花了，有几个数据变得不清楚．请根据下图所示的发票中的信息，帮助刘老师复原弄花的数据中所购置的钢笔、笔记本的数量及购置金额．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/15/2/1/0/0/0/578014960019615758/images/img_14.png\" style=\"vertical-align:middle;\" width=\"479\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京交通大学附属中学 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578014982274588672", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578014982274588672", "title": "北京交通大学附属中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "578015008719675392", "questionArticle": "<p>10．解方程（组）：</p><p>（1） $ 4(x-3){^{2}}-16=0 $ ；</p><p>（2） $ 64(x-1){^{3}}=27 $ ；</p><p>（3） $ \\begin{cases} 3x+4y=2 \\\\ 2x-y=5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025北京北京交通大学附属中学 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-21", "keyPointIds": "16287|16290|16424", "keyPointNames": "平方根|立方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "578014982274588672", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "578014982274588672", "title": "北京交通大学附属中学2024−2025学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 66, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 66, "timestamp": "2025-07-01T02:08:35.525Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}