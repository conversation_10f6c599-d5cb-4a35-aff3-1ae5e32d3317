{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 16, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "322438761157009408", "questionArticle": "<p>1．用“●”“■”“▲”分别表示三种不同的物体，如图所示，前两架天平保持平衡，若要使第三架天平也平衡，那么“？”处应放<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>个“■”．</p>\r\n<p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2023/06/2/1/0/0/0/322438643712303104/images/img_1.png\" style=\"vertical-align:middle;\" width=\"74\" alt=\"试题资源网 https://stzy.com\">　<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2023/06/2/1/0/0/0/322438643712303105/images/img_2.png\" style=\"vertical-align:middle;\" width=\"74\" alt=\"试题资源网 https://stzy.com\">　<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2023/06/2/1/0/0/0/322438643712303106/images/img_3.png\" style=\"vertical-align:middle;\" width=\"74\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第10章 *10.3 三元一次方程组 【初中必刷题】七年级下册青岛版数学", "paperCategory": 2}], "questionTypeCode": "5"}, {"questionId": "591000311164084224", "questionArticle": "<p>2．甲、乙两人进行一分钟跳绳练习，结束后，甲说：“我的跳绳个数加你的跳绳个数的 $ \\dfrac { 1 } { 4 } $ 刚好等于220个”；乙说：“我的跳绳个数加你的跳绳个数的 $ \\dfrac { 1 } { 3 } $ 刚好也等于220个”．设甲的跳绳个数为<i>x</i>个，乙的跳绳个数为<i>y</i>个，下列说法错误的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ x+\\dfrac { 1 } { 4 }y=220 $ B． $ y+\\dfrac { 1 } { 3 }x=220 $ </p><p>C． $ 8x=9y $ D． $ x=160 $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北邢台 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591000295305420800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "591000295305420800", "title": "2025年河北省邢台市信都区中考三模数学试题（6月）", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "588454174855049216", "questionArticle": "<p>3．古代数学著作《孙子算经》中有“多人共车”问题：今有五人共车，二车空；三人共车，十人步．问人与车各几何？其大意是：每车坐 $ 5 $ 人， $ 2 $ 车空出来；每车坐 $ 3 $ 人，多出 $ 10 $ 人无车坐，问人数和车数各多少？设共有 $ x $ 人， $ y $ 辆车，则可列出的方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5\\left ( { y-2 } \\right ) =x \\\\ 3y+10=x \\end{cases}  $ B． $ \\begin{cases} 5y-2=x \\\\ 3y+10=x \\end{cases}  $ C． $ \\begin{cases} 5y-2=x \\\\ 3\\left ( { y+10 } \\right ) =x \\end{cases}  $ D． $ \\begin{cases} 5\\left ( { y-2 } \\right ) =x \\\\ 3y-10=x \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "620000|210000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东深圳 · 临考冲刺", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 12, "referenceNum": 4, "createTime": "2025-06-23", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588454162649624576", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "588454162649624576", "title": "广东省深圳外国语学校2025年 九年级 第7次月考数学测试卷（6月）", "paperCategory": 1}, {"id": "565307997997539328", "title": "2025年甘肃省酒泉市九年级中考一模数学试卷", "paperCategory": 1}, {"id": "578369749999988736", "title": "2025年辽宁省铁岭市开原市中考二模数学试题", "paperCategory": 1}, {"id": "440304408066301952", "title": "2024年广东省深圳市深圳中学共同体中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "588455170826088448", "questionArticle": "<p>4．中国古代数学著作《孙子算经》中记载：“今有木，不知长短，引绳度之，余绳四尺五寸；屈绳量之，不足一尺．木长几何？”意思是：用一根绳子去量一根木头的长，绳子还剩余 $ 4.5 $ 尺；将绳子对折再量木头，则木头还剩余1尺，问木头长多少尺？可设木头长为<i>x</i>尺，绳子长为<i>y</i>尺，则所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-y=4.5 \\\\ 2x-y=1 \\end{cases}  $ B． $ \\begin{cases} x-y=4.5 \\\\ \\dfrac { y } { 2 }-x=1 \\end{cases}  $ </p><p>C． $ \\begin{cases} y-x=4.5 \\\\ x-\\dfrac { y } { 2 }=1 \\end{cases}  $ D． $ \\begin{cases} y-x=4.5 \\\\ 2x-y=1 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长沙一中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-06-23", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "588455156552871936", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "588455156552871936", "title": "湖南省长沙市一中初级中学2024−2025学年七年级下学期第三次月考数学试题", "paperCategory": 1}, {"id": "580903686232121344", "title": "四川省南充市高坪区2025年中考第二次诊断性考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589572551078096896", "questionArticle": "<p>5．某快递企业为提高工作效率，拟购买<i>A</i>、<i>B</i>两种型号智能机器人进行快递分拣．</p><p>相关信息如下：</p><p>信息一</p><table style=\"border: solid 1px;border-collapse: collapse; width:269.25pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><i>A</i>型机器人台数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\"><i>B</i>型机器人台数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">总费用（单位：万元）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">260</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p style=\"text-align:center;\">360</p></td></tr></table><p>信息二</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/15/2/1/0/0/0/589572493641293828/images/img_16.png\" style=\"vertical-align:middle;\" width=\"181\" alt=\"试题资源网 https://stzy.com\"></p><p>（1）求<i>A</i>、<i>B</i>两种型号智能机器人的单价；</p><p>（2）现该企业准备用不超过700万元购买<i>A</i>、<i>B</i>两种型号智能机器人共10台．则该企业选择哪种购买方案，能使每天分拣快递的件数最多?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市第七中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-06-23", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589572523492159488", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "589572523492159488", "title": "天津市第七中学2024-2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}, {"id": "492112732298190848", "title": "广东省深圳市盐田外国语学校2024−2025学年九年级上学期开学考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589572548045615104", "questionArticle": "<p>6．如图是由截面为同一种长方形的墙砖粘贴的部分墙面，其中三块横放的墙砖比一块竖放的墙砖高 $ 10 $ <i>cm</i>，两块横放的墙砖比两块竖放的墙砖低 $ 40 $ <i>cm</i>，求每块墙砖的截面面积．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/15/2/1/0/0/0/589572493641293827/images/img_15.png\" style=\"vertical-align:middle;\" width=\"187\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025天津天津市第七中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-06-23", "keyPointIds": "16424|16439", "keyPointNames": "加减消元法解二元一次方程组|几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589572523492159488", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "589572523492159488", "title": "天津市第七中学2024-2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}, {"id": "186873131054702592", "title": "黑龙江省哈尔滨第四十七中学2021年中考数学模拟试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589572544807612416", "questionArticle": "<p>7．某班为奖励在数学竞赛中成绩优异的同学，花费48元钱购买了甲、乙两种奖品，每种奖品至少购买1件，其中甲种奖品每件4元，乙种奖品每件3元，则有<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>种购买方案．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市第七中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-06-23", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589572523492159488", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "589572523492159488", "title": "天津市第七中学2024-2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}, {"id": "562994025243910144", "title": "河北省石家庄市第九中学2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589572533126475776", "questionArticle": "<p>8．已知 $ \\begin{cases} x=1 \\\\ y=-2 \\end{cases}  $ 是方程 $ 2x-my=8 $ 的一个解，则 $ m $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 3 $ B． $ -3 $ C． $ -2 $ D． $ -12 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|440000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025天津天津市第七中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 4, "referenceNum": 3, "createTime": "2025-06-23", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589572523492159488", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "589572523492159488", "title": "天津市第七中学2024-2025学年下学期七年级数学第二次月考试卷", "paperCategory": 1}, {"id": "426353383529816064", "title": "广东省广州市第一一三中学2023-2024学年七年级下学期开学考试数学试题", "paperCategory": 1}, {"id": "209719184451739648", "title": "广东省广州市南沙区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "591002484153294848", "questionArticle": "<p>9．读书可以让人学会思考，享受灵魂深处的愉悦，通过与书中先哲或有趣人物的对话，读者可以获得智慧和情感的支持．每年的4月23日是世界读书日，某校计划购买<i>A</i>、<i>B</i>两种图书作为“校园读书节”的奖品，已知<i>A</i>种图书的单价比<i>B</i>种图书的单价的2倍少10元，且购买4本<i>A</i>种图书和3本<i>B</i>种图书共需花费180元．</p><p>（1）<i> A</i>、<i>B</i>两种图书的单价分别为多少元？<i> </i></p><p>（2）学校计划购买这两种图书共50本，且投入总经费不超过1300元，则最多可以购买<i>A</i>种图书多少本？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽阜阳 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591002458463182848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "591002458463182848", "title": "安徽省阜阳市第十八中学2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "591002480789463040", "questionArticle": "<p>10．已知关于 $ x, y $ 的方程组 $ \\begin{cases} 2x-y=5 \\\\ ax+by=2 \\end{cases}  $ 和 $ \\begin{cases} x+y=4 \\\\ ax+2by=10 \\end{cases}  $ 有相同的解，求 $ 2a{ { + } }b $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽阜阳 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-23", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "591002458463182848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "591002458463182848", "title": "安徽省阜阳市第十八中学2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 17, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 17, "timestamp": "2025-07-01T02:02:49.602Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}