{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 172, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "555512766540324864", "questionArticle": "<p>1．方程 $ x-2y=8 $ 中，用含<i>x</i>的代数式表示<i>y</i>，则 $ y= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江杭州 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512752124502016", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555512752124502016", "title": "浙江省杭州市采荷中学2023−2024学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512875940356096", "questionArticle": "<p>2．下列方程是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ xy=3 $ B． $ 3x-y=2z $ C． $ \\dfrac { 1 } { 3 }x+\\dfrac { 1 } { 2 }y=1 $ D． $ \\dfrac { 1 } { x }+x=1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江杭州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512867790823424", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555512867790823424", "title": "浙江省杭州市萧山区2023−2024学年七年级下学期5月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555512772022280192", "questionArticle": "<p>3．解方程组：</p><p>(1) $ \\begin{cases} x=2y \\\\ 3x-2y=8 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} x+2y=2 \\\\ 3x-2y=10 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江杭州 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512752124502016", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555512752124502016", "title": "浙江省杭州市采荷中学2023−2024学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512764690636800", "questionArticle": "<p>4．若关于 $ x $ ， $ y $ 的二元一次方程组 $ \\begin{cases} x+y=5k \\\\ x-y=9k \\end{cases}  $ 的解也是二元一次方程 $ 2x+3y=6 $ 的解，则 $ k $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\dfrac { 3 } { 4 } $ B． $ -\\dfrac { 3 } { 4 } $ C． $ \\dfrac { 4 } { 3 } $ D． $ -\\dfrac { 4 } { 3 } $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江杭州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512752124502016", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555512752124502016", "title": "浙江省杭州市采荷中学2023−2024学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555512757329633280", "questionArticle": "<p>5．下列方程是二元一次方程的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 2x+3=0 $ B． $ 2x-\\dfrac { 1 } { y }=2 $ C． $ 3x-5y=1 $ D． $ xy=3 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2024浙江杭州 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512752124502016", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "555512752124502016", "title": "浙江省杭州市采荷中学2023−2024学年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "555512425048481792", "questionArticle": "<p>6．定义：把 $ ax+y=b $ （其中 $ a $ ， $ b $ 是常数， $ x $ ， $ y $ 是未知数）这样的方程称为“优美二元一次方程”．当 $ y=2x $ 时，“优美二元一次方程 $ ax+y=b $ ”中 $ x $ 的值称为“优美二元一次方程”的“优美值”．例如：当 $ y=2x $ 时，“优美二元一次方程” $ 3x-y=4 $ 化为 $ 3x-2x=4 $ ，解得， $ x=4 $ ，故其“优美值”为4．</p><p>(1)求“优美二元一次方程” $ 5x-y=1 $ 的“优美值”；</p><p>(2)若“优美二元一次方程” $ \\dfrac { 1 } { 3 }x+y=m $ 的“优美值”是﹣3，求 $ m $ 的值；</p><p>(3)是否存在 $ n $ ，使得优美二元一次方程 $ \\dfrac { 5 } { 2 }x+y=n $ 与优美二元一次方程 $ 4x-y=n-2 $ 的“优美值”相同？若存在，请求出 $ n $ 的值及此时的“优美值”；若不存在，请说明理由．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 8, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512423500783616", "questionArticle": "<p>7．若关于<i>x</i>，<i>y</i>的二元一次方程组 $ \\begin{cases} x+4y=2m-3 \\\\ -2x+y=5m-12 \\end{cases}  $ 的解<i>x</i>，<i>y</i>互为相反数，则<i>m</i>的值为 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512421978251264", "questionArticle": "<p>8．随着“低碳生活，绿色出行”理念的普及，新能源汽车正逐渐成为人们喜爱的交通工具，某汽车4S店计划购进一批新能源汽车进行销售．据了解，购进2辆<i>A</i>型新能源汽车、3辆<i>B</i>型新能源汽车共需80万元；购进3辆<i>A</i>型新能源汽车、2辆<i>B</i>型新能源汽车共需95万元．</p><p>(1)问<i>A</i>，<i>B</i>两种型号的新能源汽车每辆进价分别为多少万元？</p><p>(2)若该公司计划正好用180万元购进以上两种型号的新能源汽车（两种型号的汽车均购买），销售1辆<i>A</i>型汽车可获利1.2万元，销售1辆<i>B</i>型汽车可获利0.8万元．假如这些新能源汽车全部售出，请设计出符合要求的一种购买方案．并求出此方案所获得的利润．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16420|16434", "keyPointNames": "二元一次方程的解|方案问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512417544871936", "questionArticle": "<p>9．若关于 $ a、b $ 的二元一次方程组 $ \\begin{cases} 2a+b=5k① \\\\ a-b=4k② \\end{cases}  $ 的解也是二元一次方程 $ a+b=8 $ 的解，则 $ k $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16420|16426", "keyPointNames": "二元一次方程的解|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "555512419071598592", "questionArticle": "<p>10．解方程组</p><p>(1) $ \\begin{cases} x+2y=8 \\\\ 4x+3y=7 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} { { 2 } }x-3y=8 \\\\ -7x{ { + } }5y=5 \\end{cases}  $ ；</p><p>(3) $ \\begin{cases} x+y+z=4 \\\\ x-y+z=8 \\\\ 4x+2y+z=17 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川内江 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 10, "referenceNum": 1, "createTime": "2025-03-14", "keyPointIds": "16423|16424|16443", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组|解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "555512397999415296", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "555512397999415296", "title": "四川省内江市第一中学2023−2024学年七年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 173, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 173, "timestamp": "2025-07-01T02:21:19.079Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}