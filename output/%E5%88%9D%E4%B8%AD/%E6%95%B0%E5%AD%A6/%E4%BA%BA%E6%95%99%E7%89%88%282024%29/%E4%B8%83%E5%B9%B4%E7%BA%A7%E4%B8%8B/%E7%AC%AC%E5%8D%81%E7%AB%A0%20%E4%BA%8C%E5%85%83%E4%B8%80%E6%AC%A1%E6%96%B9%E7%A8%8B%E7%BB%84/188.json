{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 187, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "549856058526703616", "questionArticle": "<p>1．小亮、小红和笑笑三个人玩飞镖游戏，各投6支飞镖，规定在同一圆环内得分相同，三人中靶和得分情况如图，则小红得分为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>分．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/04/2/20/0/0/0/552101119649947649/images/img_1.png\" style='vertical-align:middle;' width=\"237\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄市第四十中学 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-02-28", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549856050221981696", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549856050221981696", "title": "河北省石家庄市第四十中学2024−2025学年七年级下学期数学开学考试试题", "paperCategory": 1}, {"id": "562994025243910144", "title": "河北省石家庄市第九中学2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549856059189403648", "questionArticle": "<p>2．解方程（组）：</p><p>(1) $ 5x-3=3\\left ( { 2x+6 } \\right )  $ ；</p><p>(2) $ \\dfrac { 2x+1 } { 2 }-\\dfrac { x-1 } { 3 }=1 $ ；</p><p>(3) $ \\begin{cases} x-y=0 \\\\ 2x+3y=5 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河北石家庄市第四十中学 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-02-28", "keyPointIds": "16402|16423", "keyPointNames": "解一元一次方程|代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549856050221981696", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549856050221981696", "title": "河北省石家庄市第四十中学2024−2025学年七年级下学期数学开学考试试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549095181611474944", "questionArticle": "<p>3．为降低空气污染，某市公交公司计划购买节能环保的新能源<i>A</i>型和<i>B</i>型两种公交车更换全市公交车，已知若购买1辆<i>A</i>型公交车和2辆<i>B</i>型公交车，需花费400万元；若购买2辆<i>A</i>型公交车和1辆<i>B</i>型公交车，需花费350万元．求每辆<i>A</i>型公交车和每辆<i>B</i>型公交车单价分别多少万元？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北黄石 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-27", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549095161470427136", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "549095161470427136", "title": "湖北省黄石市实验中学2024−2025学年九年级下学期开学数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "550043386423582720", "questionArticle": "<p>4．新世纪商场现销售某品牌运动套装，上衣和裤子一套售价 $ 500 $ 元．若将上衣价格下调 $ 5{ \\rm{ \\% } } $ ，将裤子价格上调 $ 8{ \\rm{ \\% } } $ ，则这样一套运动套装的售价提高 $ 0.2{ \\rm{ \\% } } $ ．设上衣和裤子在调价前单价分别为<i>x</i>和<i>y</i>元，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=500 \\\\ \\left ( { 1+5{ \\rm{ \\% } } } \\right ) x+\\left ( { 1-8{ \\rm{ \\% } } } \\right ) y=500\\times \\left ( { 1+0.2{ \\rm{ \\% } } } \\right )  \\end{cases}  $</p><p>B． $ \\begin{cases} x+y=500 \\\\ \\left ( { 1-5{ \\rm{ \\% } } } \\right ) x+\\left ( { 1+8{ \\rm{ \\% } } } \\right ) y=500\\times 0.2\\% \\end{cases}  $</p><p>C． $ \\begin{cases} x+y=500 \\\\ \\left ( { 1-5{ \\rm{ \\% } } } \\right ) x+\\left ( { 1+8{ \\rm{ \\% } } } \\right ) y=500\\times \\left ( { 1+0.2{ \\rm{ \\% } } } \\right )  \\end{cases}  $</p><p>D． $ \\begin{cases} x+y=500 \\\\ 5\\%x+8\\%y=500\\times \\left ( { 1+0.2{ \\rm{ \\% } } } \\right )  \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆八中 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-02-27", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "550043370812383232", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "550043370812383232", "title": "重庆市第八中学校2024−2025学年八年级下学期开学数学试题", "paperCategory": 1}, {"id": "367051469453828096", "title": "重庆市江北区第十八中学2023-2024学年八年级上学期9月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "550044701514047488", "questionArticle": "<p>5．如果 $ \\sqrt[{^{a+2b+5}}] { a-3b } $ 为 $ a-3b $ 的算术平方根， $ \\sqrt[2a-b-1] { 1-a{^{2}} } $ 为 $ 1-a{^{2}} $ 的立方根，求 $ 2a-3b $ 的平方根．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025浙江金华 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 9, "referenceNum": 2, "createTime": "2025-02-27", "keyPointIds": "16287|16288|16290|16424", "keyPointNames": "平方根|算术平方根|立方根|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "550044679326179328", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "550044679326179328", "title": "浙江省金华义乌宾王中学2024−2025学年七年级下学期开学检测数学试卷", "paperCategory": 1}, {"id": "404517846045204480", "title": "黑龙江省绥化市2023-2024学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549856057012559872", "questionArticle": "<p>6．《九章算术》是人类科学史上应用数学的“算经之首”，书中有这样一个问题：若2人坐一辆车，则9人需要步行，若“……”．问：人与车各多少？小明同学设有<i>x</i>辆车，人数为<i>y</i>，根据题意可列方程组为 $ \\begin{cases} {y=2x+9} \\\\ y=3\\left ( { x-2 } \\right )  \\end{cases}  $ ，根据已有信息，题中用“……”表示的缺失条件应补为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．三人坐一辆车，有一车少坐2人　　　　B．三人坐一辆车，则2人需要步行</p><p>C．三人坐一辆车，则有两辆空车　　　　D．三人坐一辆车，则还缺两辆车</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "130000|610000|410000|350000|450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南新乡 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 6, "createTime": "2025-02-26", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577861197954850816", "questionFeatureName": "数学文化题", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "577861197954850816", "title": "河南省新乡市河南师范大学附属外国语学校2024−2025学年七年级下学期5月月考数学试题", "paperCategory": 11}, {"id": "549856050221981696", "title": "河北省石家庄市第四十中学2024−2025学年七年级下学期数学开学考试试题", "paperCategory": 1}, {"id": "514473733727232000", "title": "陕西省西安市西安高新一中2024−2025学年八年级上学期期中考试数学试题", "paperCategory": 1}, {"id": "503702771499048960", "title": "广西壮族自治区南宁市第三中学2024−2025学年八年级上学期10月月考数学试题", "paperCategory": 1}, {"id": "362356147725049856", "title": "福建省福州延安中学2023-2024学年八年级上学期开学考试数学试题", "paperCategory": 1}, {"id": "220495970462638080", "title": "河南省许昌市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "549095049285378048", "questionArticle": "<p>7．“稻花香里说丰年，听取蛙声一片”桓仁稻花香大米粒似珍珠，晶莹剔透，米饭闻之清香扑鼻，口感柔软劲道，是餐桌上的佳品．某超市决定采购甲、乙两种稻花香大米，已知购买甲种稻花香大米2千克和乙种稻花香大米1千克共需56元；购买甲种稻花香大米1千克和乙种稻花香大米2千克共需要52元．</p><p>(1)求甲、乙两种稻花香大米每千克采购价分别是多少元？</p><p>(2)若该超市准备采购甲、乙两种稻花香大米共1000千克，并且采购费用不多于18000元，则超市最多采购甲种稻花香大米多少千克？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000|410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南河南实验中学 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-02-26", "keyPointIds": "16441|16486", "keyPointNames": "其他问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549095030964658176", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549095030964658176", "title": "河南省实验中学2024−2025学年八年级下学期入学测试数学试卷", "paperCategory": 1}, {"id": "535213304018411520", "title": "辽宁省本溪市2024-−2025学年上学期九年级数学期末试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549408699455938560", "questionArticle": "<p>8．（1）计算： $ \\sqrt { 16 }-(-1){^{2023}}-\\sqrt[3] { 27 }+\\left  | { 1-\\sqrt { 2 } } \\right  |  $ ；</p><p>（2）解方程组： $ \\begin{cases} x-1=\\dfrac { y+5 } { 3 } \\\\ 5\\left ( { y-1 } \\right ) =3\\left ( { x+5 } \\right )  \\end{cases}  $ .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东红岭 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-02-25", "keyPointIds": "16288|16290|16299|16424", "keyPointNames": "算术平方根|立方根|实数的运算|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549408585429590016", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549408585429590016", "title": "广东省深圳市福田区红岭中学2024−2025学年下学期八年级数学开学考试卷", "paperCategory": 1}, {"id": "549408684520022016", "title": "广东省深圳市红岭教育集团2024−2025学年八年级下学期开学考试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "549408694045286400", "questionArticle": "<p>9．在某款游戏的周边制作中，某工厂安排工人制作手办和徽章．已知一共有60名工人参与制作，每人每天能制作手办5个或者徽章8个，且每1个手办要搭配3个徽章进行套装售卖，设安排 $ x $ 名工人制作手办， $ y $ 名工人制作徽章，能恰好全部配成套装，下面所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=60 \\\\ 5x=8y \\end{cases}  $　　　　B． $ \\begin{cases} x+y=60 \\\\ 5x=3\\times 8y \\end{cases}  $</p><p>C． $ \\begin{cases} x+y=60 \\\\ 3\\times 5x=8y \\end{cases}  $　　　　D． $ \\begin{cases} x+y=60 \\\\ 8x=5y \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东红岭 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 2, "createTime": "2025-02-25", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "549408585429590016", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "549408585429590016", "title": "广东省深圳市福田区红岭中学2024−2025学年下学期八年级数学开学考试卷", "paperCategory": 1}, {"id": "549408684520022016", "title": "广东省深圳市红岭教育集团2024−2025学年八年级下学期开学考试数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "547594744513208320", "questionArticle": "<p>10．解下列方程（组）：</p><p>(1) $ 20-3\\left ( { x+4 } \\right ) =2\\left ( { x-1 } \\right )  $ </p><p>(2) $ \\dfrac { x+2 } { 4 }-\\dfrac { 2x-3 } { 6 }=1 $ </p><p>(3) $ \\begin{cases} y=2x-3 \\\\ 3x+2y=8 \\end{cases}  $ </p><p>(4) $ \\begin{cases} 3x-4y=1 \\\\ 2x+3y=12 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林长春 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-25", "keyPointIds": "16402|16423|16424", "keyPointNames": "解一元一次方程|代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "547594726871965696", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "547594726871965696", "title": "吉林省长春市新解放学校初中部2023−2024学年下学期七年级大班期初测试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 188, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 188, "timestamp": "2025-07-01T02:23:05.078Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}