{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 180, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "552322122233716736", "questionArticle": "<p>1．《孙子算经》中有一道题，原文是：“今有木，不知长短，引绳度之，余绳四尺五寸；屈绳量之，不足一尺，木长几何？”意思是：用一根绳子去量一根长木，绳子还剩余4.5尺；将绳子对折再量长木，长木还剩余1尺，则绳长多少尺？木长多少尺？</p><p>答：（1）绳长<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>尺；（2）木长<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>尺．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024湖北襄阳 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-08", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552322105427140608", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552322105427140608", "title": "2024年湖北省襄阳市枣阳市中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552322800775634944", "questionArticle": "<p>2．某服装店用2600元购进<i>A</i>，<i>B</i>两种新型服装，按标价出售后可获得利润1600元</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p><i>A</i>型</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p><i>B</i>型</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p>进价（元/件）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p>60</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p>100</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p>标价（元/件）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p>100</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 31.55pt;\"><p>160</p></td></tr></table><p>(1)问：<i>A</i>，<i>B</i>两种服装各购进多少件？</p><p>(2)如果<i>A</i>型服装按标价的7折出售，<i>B</i>型服装按标价的8折出售，那么这批服装全部售完后，服装店比按标价出售少收入多少元</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024湖南娄底 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-08", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552322776381562880", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552322776381562880", "title": "2024年湖南省娄底市第二中学中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552323296055828480", "questionArticle": "<p>3．老长沙有一首童谣：“杨裕兴的面，奇峰阁的鸭，德园的包子真好呷．”德园包子是湖南长沙地区的传统小吃之一．德园的掌案师傅历来都是技术高超的老师傅，老面发酵，所制包点皮薄馅大、面香浓郁、颜色白净、质地松软、面呈海绵状富有回弹性，口感特有嚼劲．小何到德园买早点，“阿姨，我买8个香菇肉包和5个酸菜包．阿姨说：“一共34元．”付款后，小何说：“阿姨，少买2个酸菜包，换3个香菇肉包吧．阿姨说：“可以，但还需补交5元钱．”</p><p>(1)请从他们的对话中求出香菇肉包和酸菜包的单价；</p><p>(2)如果小何一共有50元，需要买20个包子，他最多可以买几个香姑肉包呢?</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024湖南长沙 · 二模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-08", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "552323274396442624", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "552323274396442624", "title": "2024年湖南省长沙市中雅培粹学校中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "552321506744770560", "questionArticle": "<p>4．古算题：“今有甲、乙二人持钱不知其数．甲得乙半而钱五十，乙得甲太半而亦钱五十．甲、乙持钱各几何？”其大意是：甲、乙两人各带了若干钱．如果甲得到乙所有钱的一半，则甲共有钱50．如果乙得到甲所有钱的 $ \\dfrac { 2 } { 3 } $ ，则乙也共有钱50．甲、乙两人各带了多少钱？设甲、乙两人持钱的数量分别为<i>x</i>、<i>y</i>，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2x-y=50 \\\\ y-\\dfrac { 2 } { 3 }x=50 \\end{cases}  $ B． $ \\begin{cases} 2x-y=50 \\\\ x-\\dfrac { 2 } { 3 }y=50 \\end{cases}  $ C． $ \\begin{cases} x+\\dfrac { 1 } { 2 }y=50 \\\\ y+\\dfrac { 2 } { 3 }x=50 \\end{cases}  $ D． $ \\begin{cases} x-\\dfrac { 1 } { 2 }y=50 \\\\ y+\\dfrac { 2 } { 3 }x=50 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024湖北十堰 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-07", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551570059061141504", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "551570059061141504", "title": "2024年湖北省十堰市实验中学教联体中考二模数学试题", "paperCategory": 1}, {"id": "552321493151031296", "title": "2024年湖北省恩施市中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "551915023016173568", "questionArticle": "<p>5．某车间有120名工人生产一种如图所示的无盖正方体包装箱，已知1名工人每天可以生产200块侧面或150块底面（底面和侧面材料不同），4块侧面和1块底面正好可以做成一个无盖包装箱，应如何分配工人生产侧面或底面，才能使生产的侧面和底面正好配套？若设安排<i>x</i>名工人生产侧面，<i>y</i>名工人生产底面，则可列方程组（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/03/2/1/0/0/0/551914980662091778/images/img_4.png\" style=\"vertical-align:middle;\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ \\begin{cases} x+y=120 \\\\ 200x=150y \\end{cases}  $ B． $ \\begin{cases} x+y=120 \\\\ 4\\times 200x=150y \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=120 \\\\ 200x=4\\times 150y \\end{cases}  $ D． $ \\begin{cases} x+y=100 \\\\ 200x=2\\times 150y \\end{cases}  $ </p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-07", "keyPointIds": "16298|16381|16441", "keyPointNames": "估算无理数的大小|二次根式的乘法|其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551915009703452672", "questionFeatureName": "生活背景问题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "551915009703452672", "title": "重庆市实验外国语学校2024−2025学年八年级下学期入学考试数学试题", "paperCategory": 1}, {"id": "159750707796549632", "title": "重庆市第一中学校2021-2022学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "551913661125664768", "questionArticle": "<p>6．为了丰富学生的阅读资源，某校图书馆准备采购文学名著和人物传记两类图书．经了解，30本文学名著和20本人物传记共需1150元，20本文学名著比20本人物传记多100元．（注：所采购的文学名著价格都一样，所采购的人物传记价格都一样．）</p><p>(1)求每本文学名著和人物传记各多少元？</p><p>(2)若学校要求购买文学名著比人物传记多20本，总费用不超过2000元，请求出人物传记至多买多少本？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|220000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025吉林长春市第二实验中学 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 6, "referenceNum": 2, "createTime": "2025-03-07", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551913641898975232", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "551913641898975232", "title": "吉林省第二实验（高新远洋朝阳）学校2024−2025学年七年级下学期开学考试数学", "paperCategory": 1}, {"id": "570064850165997568", "title": "2025年四川省成都市郫都区中考数学第二次模拟检测试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551913659670241280", "questionArticle": "<p>7．甲乙两名同学在解方程组 $ \\begin{cases} ax+5y=10 \\\\ 4x-by=-4 \\end{cases}  $ 时，由于粗心，甲看错了方程组中的<i>a</i>，而得解为 $ \\begin{cases} x=3 \\\\ y=-1 \\end{cases}  $ ；乙看错了方程组中的<i>b</i>，而得解为 $ \\begin{cases} x=5 \\\\ y=4 \\end{cases}  $ ．</p><p>(1)甲把<i>a</i>看成了什么，乙把<i>b</i>看成了什么？</p><p>(2)请你根据以上两种结果，求出原方程组的正确解．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春市第二实验中学 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-03-07", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551913641898975232", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "551913641898975232", "title": "吉林省第二实验（高新远洋朝阳）学校2024−2025学年七年级下学期开学考试数学", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551913658202234880", "questionArticle": "<p>8．解方程或方程组．</p><p>(1) $ 7x-3\\left ( { x+2 } \\right ) =6 $ </p><p>(2) $ \\begin{cases} 2x+5y=12 \\\\ 2x+3y=6 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春市第二实验中学 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-07", "keyPointIds": "16402|16423|16424", "keyPointNames": "解一元一次方程|代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551913641898975232", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "551913641898975232", "title": "吉林省第二实验（高新远洋朝阳）学校2024−2025学年七年级下学期开学考试数学", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551913655941505024", "questionArticle": "<p>9．已知<i>a，b</i>满足方程组 $ \\begin{cases} a+5b=12 \\\\ 3a-b=4 \\end{cases}  $ ，则<i>a+b</i>的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春市第二实验中学 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-07", "keyPointIds": "16424|16426", "keyPointNames": "加减消元法解二元一次方程组|二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551913641898975232", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "551913641898975232", "title": "吉林省第二实验（高新远洋朝阳）学校2024−2025学年七年级下学期开学考试数学", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "551913653735301120", "questionArticle": "<p>10．把方程 $ 2x-3y+5=0 $ 写成用含有 $ y $ 的代数式表示 $ x $ 的形式为 $ x= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000|220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025吉林长春市第二实验中学 · 开学摸底", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 2, "createTime": "2025-03-07", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "551913641898975232", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "551913641898975232", "title": "吉林省第二实验（高新远洋朝阳）学校2024−2025学年七年级下学期开学考试数学", "paperCategory": 1}, {"id": "562773223684743168", "title": "山西省临汾一中第一附属学校2024−2025 学年七年级下学期第一次月质量监测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 181, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 181, "timestamp": "2025-07-01T02:22:16.139Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}