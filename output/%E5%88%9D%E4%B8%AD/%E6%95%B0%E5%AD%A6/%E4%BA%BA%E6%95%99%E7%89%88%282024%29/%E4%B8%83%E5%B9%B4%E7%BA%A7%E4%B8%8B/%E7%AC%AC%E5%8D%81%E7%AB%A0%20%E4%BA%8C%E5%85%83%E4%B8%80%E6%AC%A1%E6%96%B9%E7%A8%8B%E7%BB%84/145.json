{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 144, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "564940253917650944", "questionArticle": "<p>1．解下列方程组：</p><p>(1) $ \\begin{cases} 3x+2y=4 \\\\ 6x-2y=-1 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} \\dfrac { x+y } { 2 }+\\dfrac { x-y } { 3 }=6 \\\\ 4(x+y)-5(x-y)=2 \\end{cases}  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江杭州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564940232388288512", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564940232388288512", "title": "浙江省杭州市建兰中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564940251136827392", "questionArticle": "<p>2．已知方程组 $ \\begin{cases} 5x+y=3 \\\\ mx+5y=4 \\end{cases}  $ 与 $ \\begin{cases} x-2y=5 \\\\ 5x+ny=1 \\end{cases}  $ 有相同的解，则 $ {\\left( { m-n } \\right) ^ {2}} $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江杭州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16424|16427", "keyPointNames": "加减消元法解二元一次方程组|同解方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564940232388288512", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564940232388288512", "title": "浙江省杭州市建兰中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564220337849671680", "questionArticle": "<p>3．某校准备组织七年级400名学生参观公园，已知用3辆小客车和1辆大客车每次可运学生105人；用1辆小客车和2辆大客车每次可运送学生110人；每辆小客车和每辆大客车各能坐多少名学生？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽池州 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564220317897367552", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564220317897367552", "title": "2025年安徽省池州市中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564220831942877184", "questionArticle": "<p>4．小亮坚持体育锻炼，并用某种健身软件进行记录．小亮周六进行了两组运动，第一组安排30个深蹲，20个开合跳，健身软件显示消耗热量34千卡；第二组安排20个深蹲，40个开合跳，健身软件显示两组运动共消耗热量70千卡．</p><p>(1)小亮每做一个深蹲和一个开合跳分别消耗多少热量？</p><p>(2)小亮想设计一个10分钟的锻炼组合，只进行深蹲和开合跳两个动作，且深蹲的数量不少于开合跳的数量．每个深蹲用时4秒，每个开合跳用时2秒，小亮安排多少个深蹲消耗的热量最多？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南郑州 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16438|16542", "keyPointNames": "和差倍分问题|一次函数的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564220802377228288", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564220802377228288", "title": "2025年河南省郑州市九年级中考一模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564220942248878080", "questionArticle": "<p>5．某中学组织合唱比赛．某班同学自主购买 $ A $ ， $ B $ 两款文化衫，每件 $ A $ 款文化衫比每件 $ B $ 款文化衫贵10元，购买2件 $ A $ 款文化衫和3件 $ B $ 款文化衫共需要220元．</p><p>(1)求 $ A $ 款文化衫和 $ B $ 款文化衫每件各多少元；</p><p>(2)已知一共需购买48件文化衫，在实际购买时，商家让利销售， $ A $ 款七折优惠， $ B $ 款每件让利10元，现计划购买文化衫的费用不超过1530元，且 $ A $ 款文化衫不少于 $ B $ 款文化衫数量的一半，请问共有多少种购买方案？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖南长郡 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-09", "keyPointIds": "16437|16490", "keyPointNames": "销售利润问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564220917980635136", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564220917980635136", "title": "2025年湖南省长沙市长郡教育集团九年级毕业会考模拟考试数学试卷（四）", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564940247051575296", "questionArticle": "<p>6．对 $ x $ ， $ y $ 定义一种新运算 $ T $ ，规定： $ T\\left ( { x，y } \\right ) =axy+bx-4 $ （其中 $ a $ ， $ b $ 均为非零常数），这里等式右边是通常的四则运算．例如： $ T\\left ( { 0，1 } \\right ) =a\\times 0\\times 1+b\\times 0-4=-4 $ ，若 $ T\\left ( { 2，1 } \\right ) =2 $ ， $ T\\left ( { -1，2 } \\right ) =-8 $ ，则下列结论正确的个数为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>（1） $ a=1 $ ， $ b=2 $ ；</p><p>（2）若 $ T\\left ( { m，n } \\right ) =0 $ ， $ \\left ( { n\\ne -2 } \\right )  $ ，则 $ m=\\dfrac { 4 } { n+2 } $ ；</p><p>（3）若 $ T\\left ( { m，n } \\right ) =0 $ ，则 $ m $ ， $ n $ 有且仅有3组整数解；</p><p>（4）若 $ T\\left ( { kx，y } \\right ) =T\\left ( { ky，x } \\right )  $ 对任意有理数 $ x $ ， $ y $ 都成立，则 $ k=1 $ ．</p><p>A．1个</p><p>B．2个</p><p>C．3个</p><p>D．4个</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|330000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024浙江杭州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-04-09", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564940232388288512", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564940232388288512", "title": "浙江省杭州市建兰中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "425583983306514432", "title": "重庆市凤鸣山中学2022-2023学年七年级数学下学期第一次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564940241380876288", "questionArticle": "<p>7．已知 $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $ 是方程 $ 2x+m+y=0 $ 的一个解，那么<i>m</i>的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．3　　　　B．1　　　　C． $ -3 $　　　　D． $ -1 $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024浙江杭州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-04-09", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564940232388288512", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564940232388288512", "title": "浙江省杭州市建兰中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}, {"id": "392808203585626112", "title": "四川省成都市锦江区师一学校2023-2024学年八年级（上）期中数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564215606137888768", "questionArticle": "<p>8．山火烧不尽，春风吹又生，今年三月，校团委组织师生开展“汇聚青年力量·重建绿色山林”缙云山植树活动，购入了第一批树苗，经了解，购买甲、乙两种树苗共250棵，两种树苗的单价分别为20元和30元，共用去资金6000元．</p><p>(1)求第一批购入甲、乙两种树苗的数量；</p><p>(2)恰逢植树节在周末，校团委决定，购入甲树苗时，若甲树苗单价每上涨2元，购入数量就比第一批甲树苗的数量减少10棵（最后数量不超过第一批甲树苗的 $ 80\\% $ ），购入乙树苗单价与第一批相同，数量是第一批乙树苗的 $ 80\\% $ ，最终花费的总资金比第一批减少了 $ 8\\% $ ，求第二批购买树苗的总数量．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆西大附中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 3, "createTime": "2025-04-09", "keyPointIds": "16438|16463", "keyPointNames": "和差倍分问题|营销、利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564215578036051968", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564215578036051968", "title": "重庆市北碚区西南大学附属中学校2024-−2025学年八年级下学期定时练习数学试题", "paperCategory": 1}, {"id": "435208319143288832", "title": "2023年重庆市九龙坡区育才中学校中考一模数学试题", "paperCategory": 1}, {"id": "311236494550671360", "title": "重庆市九龙坡区育才中学校2022-2023学年九年级下学期4月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562405515734589440", "questionArticle": "<p>9．当今时代，科技的发展日新月异，扫地机器人受到越来越多的消费者青睐，市场需求不断增长．某公司旗下扫地机器人配件销售部门，当前负责销售 $ A{ \\rm{ \\; } }、{ \\rm{ \\; } }B $ 两种配件．已知购进10件 $ \\mathrm{ A } $ 配件和25件 $ B $ 配件需支出成本2000元；购进8件 $ \\mathrm{ A } $ 配件和8件 $ B $ 配件需支出成本1240元．</p><p>(1)求 $ \\mathrm{ A } $ 、 $ B $ 两种配件的进货单价；</p><p>(2)若该配件销售部门计划购进 $ \\mathrm{ A } $ 、 $ B $ 两种配件共80件， $ \\mathrm{ A } $ 配件进货数量不少于18件， $ B $ 配件进货件数不低于 $ \\mathrm{ A } $ 配件件数的3倍．据市场销售分析， $ \\mathrm{ A } $ 配件提价16%销售， $ B $ 配件的售价是进价的 $ \\dfrac { 4 } { 3 } $ ． $ \\mathrm{ A } $ 、 $ B $ 两种配件有哪几种进货方案？哪种方案能让本次销售的利润达到最大？最大利润是多少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|510000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025四川渠中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-04-08", "keyPointIds": "16438|16490", "keyPointNames": "和差倍分问题|一元一次不等式组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562405489432109056", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562405489432109056", "title": "四川省达州市渠县中学2024−2025学年八年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "566995852293611520", "title": "2025年广西省南宁市中考适应性训练数学试卷", "paperCategory": 11}, {"id": "550455523025395712", "title": "重庆市第一中学2024—2025学年下学期八年级开学考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564581033582043136", "questionArticle": "<p>10．某校举办了首届“数学展示”活动，为表彰在本次活动中表现优秀的学生，老师决定购买笔袋或彩色铅笔作为奖品．已知购买1个笔袋、2筒彩色铅笔共需44元；购买2个笔袋、3筒彩色铅笔共需73元．求每个笔袋、每筒彩色铅笔的单价各是多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "220000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024吉林吉林 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-08", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564581012056875008", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564581012056875008", "title": "吉林省吉林市第九中学2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 145, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 145, "timestamp": "2025-07-01T02:18:01.741Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}