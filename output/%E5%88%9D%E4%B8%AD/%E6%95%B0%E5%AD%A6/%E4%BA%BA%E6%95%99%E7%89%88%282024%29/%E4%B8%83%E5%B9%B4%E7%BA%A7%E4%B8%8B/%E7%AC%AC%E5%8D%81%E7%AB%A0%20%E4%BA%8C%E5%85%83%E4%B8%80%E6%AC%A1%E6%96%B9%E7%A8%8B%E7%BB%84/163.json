{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 162, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "557698544435503104", "questionArticle": "<p>1．某商场为迎接店庆进行促销，羊绒衫每件按标价的八折出售，每件将赚70元，后因库存太多，每件羊绒衫按标价的六折出售，每件将亏损110元，则该商场每件羊绒衫的进价为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元，标价为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557698543642779648", "questionArticle": "<p>2．甲乙两人同时解方程组 $ \\begin{cases} ax+by=2 \\\\ cx-7y=8 \\end{cases}  $ 时，甲正确解得 $ \\begin{cases} x=3 \\\\ y=-2 \\end{cases}  $ ，乙因抄错 $ c $ 而解得 $ \\begin{cases} x=-2 \\\\ y=2 \\end{cases}  $ ，则 $ a $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>， $ b $ 的值是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557698542917165056", "questionArticle": "<p>3．某工厂生产茶具每套茶具有1个茶壶和6个茶杯组成，生产这种茶具的主要材料是紫砂泥，用1千克紫砂泥可以做3个茶壶或9个茶杯，现要用9千克紫砂泥制作这些茶具．应用<i>x</i>千克紫砂泥做茶壶，用<i>y</i>千克紫砂泥做茶杯，恰好配成这种茶具多少套，请列出方程组<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557698542191550464", "questionArticle": "<p>4．如果关于 $ x、y $ 的方程组 $ \\begin{cases} 2x-y=3-k \\\\ x+y=2+2k \\end{cases}  $ 的解满足 $ x-2y=-1 $ ，则 $ k $ 的值 $ = $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557698539951792128", "questionArticle": "<p>5．若 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是方程 $ ax+3y=2 $ 的一个解，则 $ a $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557698538362150912", "questionArticle": "<p>6．已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} x+3y=4-a \\\\ x-y=3a \\end{cases}  $ ，其中 $ -3\\leqslant  a\\leqslant  1 $ ，下列结论：①当 $ a=-2 $ 时， $ x $ ， $ y $ 的值互为相反数；② $ \\begin{cases} x=5 \\\\ y=-1 \\end{cases}  $ 是方程组的解；③当 $ a=-1 $ 时，方程组的解也是方程 $ x+y=1 $ 的解，其中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ { \\rm{ ① } }{ \\rm{ ③ } } $ B． $ { \\rm{ ② } }{ \\rm{ ③ } } $ C． $ ①②③ $ D． $ ①② $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16420|16424|16482", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组|不等式的性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557698632465555456", "questionArticle": "<p>7．若关于 $ x,y $ 的方程组 $ \\begin{cases} 2x+5y=3k \\\\ x+3y=6k-9 \\end{cases}  $ 的解满足不等式 $ x+2y &gt; 0 $ ，则 $ k $ 的取值范围是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西太原 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 7, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698616468480000", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698616468480000", "title": "山西省太原市第四十八中学校2024−2025学年八年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557698532288798720", "questionArticle": "<p>8．如果 $ \\begin{cases} x=2 \\\\ y=1 \\end{cases}  $ 是方程组 $ \\begin{cases} nx-my=4 \\\\ nx+my=8 \\end{cases}  $ 的解，则 $ m,n $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）．</p><p>A． $ \\begin{cases} m=2 \\\\ n=1 \\end{cases}  $ B． $ \\begin{cases} m=2 \\\\ n=3 \\end{cases}  $ C． $ \\begin{cases} m=1 \\\\ n=8 \\end{cases}  $ D． $ \\begin{cases} m=3.5 \\\\ n=2.25 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "370000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山东东营 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698524487393280", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "557698524487393280", "title": "山东省东营市胜利第十三中学2024−2025学年七年级下学期第一次“双减”学情调查数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "557698730037649408", "questionArticle": "<p>9．某老师计划为学生购买文具，已知购买1件<i>A</i>种文具和2件<i>B</i>种文具共需17元，购买2件<i>A</i>种文具和3件<i>B</i>种文具共需29元．</p><p>(1)求<i>A</i>，<i>B</i>两种文具每件价格分别是多少元？</p><p>(2)老师计划购买<i>A</i>，<i>B</i>两种文具共30件，总费用不超过165元，那么最多可购买<i>A</i>种文具多少件？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025陕西西工大附中 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16438|16486", "keyPointNames": "和差倍分问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557698706943811584", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "557698706943811584", "title": "陕西省西安市工业大学附属中学2024−2025学年八年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "557699293592723456", "questionArticle": "<p>10．已知 $ a $ 、 $ b $ 、 $ c $ 、 $ d $ 均为常数， $ e $ 、 $ f $ 均为非零常数，若有两个整式 $ A=x{^{2}}+ex+f $ ， $ B=5x{^{3}}-6x{^{2}}+10=a(x-1){^{3}}+b(x-1){^{2}}+c(x-1)+d $ ，下列结论中，正确个数为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>①当 $ A+B $ 为关于 $ x $ 的三次三项式时，则 $ f=-10 $ ；</p><p>②当多项式 $ A\\cdot B $ 乘积不含 $ x{^{4}} $ 时，则 $ e=6 $ ；</p><p>③ $ a+b+c=19 $ ；</p><p>④当 $ \\mathrm{ A } $ 能被 $ x-2 $ 整除时， $ 2e+f=-4 $ ；</p><p>A．4B．3C．2D．1</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆等地 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-03-20", "keyPointIds": "16308|16317|16443", "keyPointNames": "多项式|整式的加减|解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "557699277142663168", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "557699277142663168", "title": "重庆市鲁能巴蜀中学校2024−2025学年九年级下学期开学考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 163, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 163, "timestamp": "2025-07-01T02:20:09.169Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}