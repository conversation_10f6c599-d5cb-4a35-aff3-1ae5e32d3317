{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 25, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "589934579726725120", "questionArticle": "<p>1．如图，用 $ 12 $ 块形状和大小均相同的小长方形拼成一个宽是 $ 40 $ 的大长方形，则每个小长方形的面积是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/06/21/2/1/0/0/0/591602845046054913/images/img_1.png\" style='vertical-align:middle;' width=\"209\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津红桥区第五中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934561456336896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589934561456336896", "title": "天津市第五中学2024−2025学年下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589934488576110592", "questionArticle": "<p>2．五一节为吸引顾客，某商场举办千元现金返现活动．顾客只要购买一定金额的商品后就可以获得一次抽奖机会．抽奖箱里有三张奖券，分别标有一等奖，二等奖，三等奖．抽到一等奖返现30元，二等奖返现20元，三等奖返现10元．三天后商场对抽奖活动进行了统计．统计如下：五月2号抽到一等奖的次数是五月一号的3倍，抽到二等奖的次数是五月一号的2倍，抽到三等奖的次数是五月一号的4倍．五月3号抽到一等奖的次数与五月一号相同，抽到二等奖的次数是五月一号的4倍，抽到三等奖的次数是五月一号的2倍．三天下来，商场返现的总金额刚好1000元，五月3号的返现金额比五月一号多220元，则五月2号的返现金额是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>元．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025天津二十五中 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16444", "keyPointNames": "三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934467113857024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589934467113857024", "title": "天津市南开区第二十五中学2024-2025学下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589934578145472512", "questionArticle": "<p>3．若 $ \\begin{cases} x=3 \\\\ y=-2 \\end{cases}  $ 是二元一次方程 $ ax+by=-2 $ 的一个解，则 $ 6a-4b+2025 $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津红桥区第五中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16305|16420", "keyPointNames": "代数式求值|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934561456336896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589934561456336896", "title": "天津市第五中学2024−2025学年下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "589934573414297600", "questionArticle": "<p>4．已知 $ \\begin{cases} x+y=2 \\\\ y+z=3 \\\\ z+x=7 \\end{cases}  $ ，则 $ x+y+z $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．5　　　　B．6　　　　C．7　　　　D．8</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津红桥区第五中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16443", "keyPointNames": "解三元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934561456336896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589934561456336896", "title": "天津市第五中学2024−2025学年下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589934572625768448", "questionArticle": "<p>5．我国古代数学名著《孙子算经》中有这样一道题：“今有木，不知长短，引绳度之，余绳四尺五寸；屈绳量之，不足一尺，木长几何？”意思是用一根绳子去量一根木条，绳子还剩余4.5尺；将绳子对折再量木条，木条剩余1尺．问木条长多少尺？设绳子长<i>x</i>尺，木条长<i>y</i>尺，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-y=4.5 \\\\ y-2x=1 \\end{cases}  $　　　　B． $ \\begin{cases} y-x=4.5 \\\\ 2x-y=1 \\end{cases}  $　　　　C． $ \\begin{cases} y-x=4.5 \\\\ \\dfrac { 1 } { 2 }x-y=1 \\end{cases}  $　　　　D． $ \\begin{cases} x-y=4.5 \\\\ y-\\dfrac { 1 } { 2 }x=1 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津红桥区第五中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934561456336896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589934561456336896", "title": "天津市第五中学2024−2025学年下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589934571765936128", "questionArticle": "<p>6．下列方程组中，解是 $ \\begin{cases} x=-2 \\\\ y=-1 \\end{cases}  $ 的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=-3 \\\\ x-2y=1 \\end{cases}  $　　　　B． $ \\begin{cases} 2x=y \\\\ x+y=-3 \\end{cases}  $</p><p>C． $ \\begin{cases} x+y=-3 \\\\ x-y=-1 \\end{cases}  $　　　　D． $ \\begin{cases} x+y=0 \\\\ 3x-y=5 \\end{cases}  $</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津红桥区第五中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934561456336896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589934561456336896", "title": "天津市第五中学2024−2025学年下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589934478132293632", "questionArticle": "<p>7．解方程组 $ \\begin{cases} 2x+3y=1① \\\\ 3x-6y=7② \\end{cases}  $ 用加减法消去<i>y</i>，需要（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ ①\\times 2-② $ B． $ ②\\times 2-① $ C． $ ①\\times 2+② $ D． $ ②\\times 2+① $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津二十五中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934467113857024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589934467113857024", "title": "天津市南开区第二十五中学2024-2025学下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589934477272461312", "questionArticle": "<p>8．用代入消元法解关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x=4y-3 \\\\ 2x-3y=-1 \\end{cases}  $ 时，代入正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ 2(4y-3)-3y=-1 $ B． $ 4y-3-3y=-1 $ </p><p>C． $ 4y-3-3y=1 $ D． $ 2(4y-3x)-3y=1 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025天津二十五中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16423", "keyPointNames": "代入消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934467113857024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589934467113857024", "title": "天津市南开区第二十五中学2024-2025学下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589934569194827776", "questionArticle": "<p>9．下列方程组中，是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=5 \\\\ xy=6 \\end{cases}  $ B． $ \\begin{cases} x-2y=4 \\\\ \\dfrac { 1 } { x }+\\dfrac { 1 } { y }=5 \\end{cases}  $ C． $ \\begin{cases} x-y=1 \\\\ x+3y=4 \\end{cases}  $ D． $ \\begin{cases} 2x+y=3 \\\\ x+z=4 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025天津天津红桥区第五中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934561456336896", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "589934561456336896", "title": "天津市第五中学2024−2025学年下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "589934474567135232", "questionArticle": "<p>10．下列方程组中，是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+2y=10 \\\\ y=2x \\end{cases}  $ B． $ \\begin{cases} x+y=0 \\\\ x+z=0 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+\\dfrac { 1 } { y }=5 \\\\ x+y=0 \\end{cases}  $ D． $ \\begin{cases} xy=0 \\\\ x+y=2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津二十五中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-19", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "589934467113857024", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "589934467113857024", "title": "天津市南开区第二十五中学2024-2025学下学期5月月考七年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 26, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 26, "timestamp": "2025-07-01T02:03:52.722Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}