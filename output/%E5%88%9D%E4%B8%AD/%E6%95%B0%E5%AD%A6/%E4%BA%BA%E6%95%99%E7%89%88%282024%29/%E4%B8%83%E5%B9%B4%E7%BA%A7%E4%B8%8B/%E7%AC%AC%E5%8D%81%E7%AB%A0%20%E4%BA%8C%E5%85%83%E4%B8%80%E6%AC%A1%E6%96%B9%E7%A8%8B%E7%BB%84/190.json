{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 189, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "546877565358612480", "questionArticle": "<p>1．2024年8月22日，山西省文化和旅游厅正式启动“跟着悟空游山西”活动，某校组织八年级学生探寻山西省历史文化的研学活动．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/22/2/20/0/0/0/548510620817072129/images/img_1.png\" style='vertical-align:middle;' width=\"226\" alt=\"试题资源网 https://stzy.com\"></p><table style=\"border: solid 1px;border-collapse: collapse; width:150.75pt;\"><tr><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 150pt;\"><p>×××景区票价一览表</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>购票人数/人</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>单价/元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>1〜50</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>18</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>51〜100</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>15</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>100以上</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>12</p></td></tr></table><p>参加活动的八年级（1）（2）两个班共101人去游览山西运城某景点，其中（1）班人数较少，不到50人，（2）班人数较多，有50多人，如果两班都以班级为单位分别购票，则一共应付1659元.</p><p>(1)两班各有学生多少人?</p><p>(2)如果两班联合起来作为一个团体购票，则可以节省不少的钱，联合起来购票能省多少钱?</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西运城 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-21", "keyPointIds": "16438|16440", "keyPointNames": "和差倍分问题|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "546877557443960832", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "546877557443960832", "title": "山西省运城市2024−2025学年上学期期末测试八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "546877564435865600", "questionArticle": "<p>2．解方程组：</p><p>(1) $ \\begin{cases} 2x+y=4 \\\\ x-y=5 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} x+y=4 \\\\ \\dfrac { x-1 } { 2 }+\\dfrac { y+1 } { 3 }=1 \\end{cases}  $ .</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西运城 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "546877557443960832", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "546877557443960832", "title": "山西省运城市2024−2025学年上学期期末测试八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "546877563647336448", "questionArticle": "<p>3．已知 $ \\begin{cases} x=1 \\\\ y=-2 \\end{cases}  $ 是方程 $ ax-y=-3 $ 的一个解，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西运城 · 期末", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-21", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "546877557443960832", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "546877557443960832", "title": "山西省运城市2024−2025学年上学期期末测试八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "546877562460348416", "questionArticle": "<p>4．若关于 $ x,y $ 的方程组 $ \\begin{cases} 2x+y=2m-1 \\\\ x+2y=m-4 \\end{cases}  $ 的解满足 $ x-y=3 $ ，则 $ m= $ （&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0　　　　B． $ \\dfrac { 8 } { 3 } $　　　　C．8　　　　D．2</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西运城 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-02-21", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "546877557443960832", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "546877557443960832", "title": "山西省运城市2024−2025学年上学期期末测试八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "547964232391761920", "questionArticle": "<p>5．（1）计算： $ (-1)\\times 3+\\sqrt { 9 }+2{^{2}}-2024{^{0}} $ ；</p><p>（2）解方程组： $ \\begin{cases} 2x+y=7 \\\\ 2x-3y=3 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "450000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广西南宁 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-02-21", "keyPointIds": "16288|16299|16323|16424", "keyPointNames": "算术平方根|实数的运算|零指数幂|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "547964210610741248", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "547964210610741248", "title": "广西南宁市第十四中学2024−2025学年下学期九年级开学考试数学", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "547963400095047680", "questionArticle": "<p>6．成都世博会吉祥物为可爱的“桐妹儿”，寓意和平友好、包容互鉴，富有深刻的文化内涵和巴蜀特色．五一假期，小明参观完世博会后，准备购买世博会纪念品送给同学，现有<i>A</i>，<i>B</i>两款吉祥物“桐妹儿”．若购买<i>A</i>款吉祥物1件和<i>B</i>款吉祥物3件，则需190元；若购买<i>A</i>款吉祥物2件和<i>B</i>款吉祥物1件，则需180元．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/02/25/2/20/0/0/0/549566387938697216/images/img_1.png\" style='vertical-align:middle;' width=\"121\" alt=\"试题资源网 https://stzy.com\"></p><p>(1)求每件<i>A</i>款吉祥物和每件<i>B</i>款吉祥物的价格；</p><p>(2)小明准备购买两款吉祥物共10件，若购买<i>A</i>款吉祥物数量为<i>m</i>件 $ (4\\leqslant  m\\leqslant  10) $ ，购买<i>A</i>，<i>B</i>两款吉祥物总费用为<i>W</i>元，请写出总费用为<i>W</i>与数量<i>m</i>之间的函数关系式，并求出总费用最少为多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025广东普宁二中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-21", "keyPointIds": "16438|16535", "keyPointNames": "和差倍分问题|一次函数的图象和性质", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "547963378343387136", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "547963378343387136", "title": "广东省普宁市第二中学2024−2025学年八年级下学期期初数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "547963396521500672", "questionArticle": "<p>7．（1）计算： $ {\\left( { 3-{ \\rm{ π } } } \\right) ^ {0}}-\\left  | { -\\dfrac { 1 } { 4 } } \\right  | +\\sqrt { 36 }+2{^{-2}} $ ；    </p><p>（2）解方程组 $ \\begin{cases} 3x+y=8 \\\\ 2x-y=7 \\end{cases}  $ ．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东普宁二中 · 开学摸底", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-21", "keyPointIds": "16299|16323|16372|16424", "keyPointNames": "实数的运算|零指数幂|负整数指数幂|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "547963378343387136", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "547963378343387136", "title": "广东省普宁市第二中学2024−2025学年八年级下学期期初数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "547963390485897216", "questionArticle": "<p>8．《四元玉鉴》是一部成就辉煌的数学名著，在中国古代数学史上有着重要地位．其中有一个“酒分醇醨”问题：务中听得语吟吟，亩道醇醨酒二盆．解酒一升醉三客，醨酒三升醉一人．共通饮了一斗七，一十九客醉醺醺．欲问高明能算士，几何醨酒几多醇？其大意为：有好酒和薄酒分别装在瓶中，好酒1升醉了3位客人，薄酒3升醉了1位客人，现在好酒和薄酒一共饮了17升，醉了19位客人，试问好酒、薄酒各有多少升？若设好酒有 $ x $ 升，薄酒有 $ y $ 升，根据题意列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）．</p><p>A． $ \\begin{cases} x+y=17 \\\\ 3x+\\dfrac { 1 } { 3 }y=19 \\end{cases}  $　　　　B． $ \\begin{cases} x+y=19 \\\\ 3x+\\dfrac { 1 } { 3 }y=17 \\end{cases}  $　　　　C． $ \\begin{cases} x+y=19 \\\\ \\dfrac { 1 } { 3 }x+3y=17 \\end{cases}  $　　　　D． $ \\begin{cases} x+y=17 \\\\ \\dfrac { 1 } { 3 }x+3y=19 \\end{cases}  $</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|620000|210000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东普宁二中 · 开学摸底", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 6, "referenceNum": 4, "createTime": "2025-02-21", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "547963378343387136", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "547963378343387136", "title": "广东省普宁市第二中学2024−2025学年八年级下学期期初数学试题", "paperCategory": 1}, {"id": "567477173243650048", "title": "2025年辽宁省大连市瓦房店庄河市九年级中考第一次模拟考试数学试卷", "paperCategory": 1}, {"id": "432842351553323008", "title": "2024年甘肃省平凉市九年级中考一模数学模拟试题", "paperCategory": 1}, {"id": "407685226581762048", "title": "重庆市沙坪坝区南开中学校2023-2024学年九年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "545400971679014912", "questionArticle": "<p>9．某一天，蔬菜经营户花90元从蔬菜批发市场批发了黄瓜和茄子共 $ 40{ \\rm{ k } }{ \\rm{ g } } $ ，到菜市场去卖，黄瓜和茄子当天的批发价和零售价如下表所示：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 91.35pt;\"><p style=\"text-align:center;\">品名</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">黄瓜</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">茄子</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 91.35pt;\"><p style=\"text-align:center;\">批发价/（元/kg）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">2</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 91.35pt;\"><p style=\"text-align:center;\">零售价/（元/kg）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 33pt;\"><p style=\"text-align:center;\">3</p></td></tr></table><p>(1)求该蔬菜经营户批发的黄瓜和茄子的数量各是多少？</p><p>(2)若该蔬菜经营户当天将购买的黄瓜和茄子全部卖完，请问他可赚多少元？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "520000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025贵州贵阳 · 期末", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-02-19", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545400965404336128", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "545400965404336128", "title": "贵州省贵阳市2024−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "545400969099517952", "questionArticle": "<p>10． $ \\begin{cases} x=1 \\\\ y=3 \\end{cases}  $ 和 $ \\begin{cases} x=0 \\\\ y=-2 \\end{cases}  $ 都是方程 $ ax-y=b $ 的解，则 $ a-b $ 的值是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -3 $</p><p>B．2</p><p>C．3</p><p>D．7</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "520000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025贵州贵阳 · 期末", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-02-19", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "545400965404336128", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "545400965404336128", "title": "贵州省贵阳市2024−2025学年八年级上学期1月期末考试数学试题", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 190, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 190, "timestamp": "2025-07-01T02:23:18.548Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}