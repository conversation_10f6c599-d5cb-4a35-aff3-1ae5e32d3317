{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 119, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "568252972221636608", "questionArticle": "<p>1．七(1)班五位同学参加学校举办的数学素养竞赛,试卷中共有20道题,规定每题答对得5分,答错扣2分,未答得0分.赛后A,B,C,D,E五位同学对照评分标准回忆并记录了自己的答题情况(E同学只记得有7道题未答),具体如下表:</p><table style=\"border: solid 1px;border-collapse: collapse; margin: auto;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>参赛同学</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>答对题数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>答错题数</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>未答题数</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>A</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>19</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>0</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>1</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>B</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>17</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>1</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>C</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>15</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>3</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>D</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>17</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>2</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>E</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p><i>/</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p><i>/</i></p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 50pt;\"><p>7</p></td></tr></table><p>最后从公布的竞赛成绩中获知A,B,C,D,E五位同学的实际成绩分别是95分,81分,57分,83分,58分.</p><p>(1)求E同学的答对题数和答错题数;</p><p>(2)若A,B,C,D四位同学中有一位同学记错了自己的答题情况.请指出哪位同学记错了,并写出他的实际答题情况.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424|16440", "keyPointNames": "加减消元法解二元一次方程组|表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时1 列二元一次方程组解决积分和行程问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253322139836416", "questionArticle": "<p>2．《九章算术》的“方程”一章里,一次方程组是由算筹布置而成的.《九章算术》中的算筹图是竖排的,为看图方便,我们把它改为横排,如图(1)、图(2).图中各行从左到右列出的算筹数分别表示未知数<i>x</i>,<i>y</i>的系数与相应的常数项.把图(1)所示的算筹图用我们现在所熟悉的方程组形式表述出来,就是 $ \\begin{cases}3x+2y=19,\\\\ x+4y=23.\\end{cases} $ 类似地,图(2)所示的算筹图我们可以表述为&nbsp;&nbsp;&nbsp;&nbsp;\t　.</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/04/18/2/1/0/0/0/568253288417632263/images/img_7.png\" style=\"vertical-align:middle;\" width=\"200\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 一次方程与方程组 全章综合训练《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253094510764032", "questionArticle": "<p>3．某零食店有甲、乙两种糖果,它们的价格分别为<i>a</i>元<i>/</i>千克,<i>b</i>元<i>/</i>千克.购买甲种糖果5千克,乙种糖果2千克,共花费25元;购买甲种糖果3千克,乙种糖果4千克,共花费29元.</p><p>(1)求<i>a</i>和<i>b</i>的值;</p><p>(2)甲种糖果每千克涨价<i>m</i>元(0&lt;<i>m</i>&lt;2),乙种糖果价格不变,小明花了45元购买了两种糖果共10千克,那么购买甲种糖果多少千克?(用含<i>m</i>的代数式表示)</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时2 列二元一次方程组解决百分率、配套和销售问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253093806120960", "questionArticle": "<p>4．端午节是中国传统节日,人们有吃粽子的习俗.利群商厦从5月12日起开始对粽子进行打折促销,其中肉粽六折,白粽七折.打折前购买4盒肉粽和5盒白粽需350元,打折后购买5盒肉粽和10盒白粽需360元.设打折前每盒肉粽的价格为<i>x</i>元,每盒白粽的价格为<i>y</i>元,则可列方程组为<u>　　　　</u><i>.&nbsp;</i></p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时2 列二元一次方程组解决百分率、配套和销售问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253093269250048", "questionArticle": "<p>5．玩具车间每天能生产甲种玩具零件24个或乙种玩具零件12个,若1个甲种玩具零件与2个乙种玩具零件能组成一个完整的玩具,怎样安排生产才能在60天内组装出最多的玩具?设生产甲种玩具零件<i>x</i>天,乙种玩具零件<i>y</i>天,则有&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}x+y=60,\\\\ 24x=12y\\end{cases} $ B． $ \\begin{cases}x+y=60,\\\\ 12x=24y\\end{cases} $ </p><p>C． $ \\begin{cases}x+y=60,\\\\ 2×24x=12y\\end{cases} $ D． $ \\begin{cases}x+y=60,\\\\ 24x=2×12y\\end{cases} $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16435", "keyPointNames": "分配问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时2 列二元一次方程组解决百分率、配套和销售问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568253320462114816", "questionArticle": "<p>6．在某学校举行的课间“桌面操”比赛中,为奖励表现突出的班级,学校计划用260元钱购买A,B,C三种奖品,A种每个10元,B种每个20元,C种每个30元,在C种奖品只能购买3个或4个且钱全部用完的情况下(注:每种方案中都有三种奖品),购买方案共有&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．12种B．13种\t</p><p>C．14种D．15种</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 单元测试", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 一次方程与方程组 全章综合训练《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568253092552024064", "questionArticle": "<p>7．某工厂第一季度生产甲、乙两种机器共480台.改进生产技术后,计划第二季度生产这两种机器共554台,其中甲种机器产量要比第一季度增加10<i>%</i>,乙种机器产量要比第一季度增加20<i>%.</i>该厂第一季度生产甲、乙两种机器各多少台?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时2 列二元一次方程组解决百分率、配套和销售问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568253089683120128", "questionArticle": "<p>8．为保护生态环境,陕西省某县响应国家“退耕还林”的号召,将某一部分耕地改为林地.改变后,林地面积和耕地面积共有180平方千米,耕地面积是林地面积的25%,求改变后林地面积和耕地面积各有多少平方千米.设改变后耕地面积有<i>x</i>平方千米,林地面积有<i>y</i>平方千米.根据题意,列出如下四个方程组,其中正确的是&nbsp;&nbsp;&nbsp;&nbsp;\t（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases}x+y=180,\\\\ y=x·25\\mathrm{\\%}\\end{cases} $ B． $ \\begin{cases}x+y=180,\\\\ x=y·25\\mathrm{\\%}\\end{cases} $  </p><p>C． $ \\begin{cases}x+y=180,\\\\ x-y=25\\mathrm{\\%}\\end{cases} $ D． $ \\begin{cases}x+y=180,\\\\ y-x=25\\mathrm{\\%}\\end{cases} $  </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时2 列二元一次方程组解决百分率、配套和销售问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "1"}, {"questionId": "568252967863754752", "questionArticle": "<p>9．小刚沿街匀速行走,发现每隔6分钟从背后驶过一辆2路公交车,每隔3分钟迎面驶来一辆2路公交车.假设每辆2路公交车行驶速度相同,并且2路公交车总站每隔固定时间发一辆车,那么发车间隔是多少分钟?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424|16430", "keyPointNames": "加减消元法解二元一次方程组|行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时1 列二元一次方程组解决积分和行程问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "568252967331078144", "questionArticle": "<p>10．一艘轮船在相距90千米的甲、乙两地之间匀速航行,从甲地到乙地顺流航行用6小时,逆流航行比顺流航行多用4小时.</p><p>(1)求该轮船在静水中的速度和水流速度;</p><p>(2)若在甲、乙两地之间建立丙码头,使该轮船从甲地到丙地和从乙地到丙地所用的航行时间相同,问甲、丙两地相距多少千米?</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "七年级 · 课时练习", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-22", "keyPointIds": "16424|16430", "keyPointNames": "加减消元法解二元一次方程组|行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "第3章 3.4 课时1 列二元一次方程组解决积分和行程问题《2023秋初中必刷题 数学七年级上册 HK》", "paperCategory": 2}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 120, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 120, "timestamp": "2025-07-01T02:14:59.682Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}