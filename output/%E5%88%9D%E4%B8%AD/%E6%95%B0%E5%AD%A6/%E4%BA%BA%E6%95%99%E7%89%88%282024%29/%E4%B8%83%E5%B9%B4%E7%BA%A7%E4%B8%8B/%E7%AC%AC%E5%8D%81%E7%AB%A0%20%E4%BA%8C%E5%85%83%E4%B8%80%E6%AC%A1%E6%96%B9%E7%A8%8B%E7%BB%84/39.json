{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 38, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "587003469543809024", "questionArticle": "<p>1．解方程组： $ \\begin{cases} 3x+y=8 \\\\ x-2y=5 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江衢州 · 临考冲刺", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587003445703385088", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "587003445703385088", "title": "2025年浙江省衢州市实验教育集团中考三模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585921589897633792", "questionArticle": "<p>2．《九章算术·盈不足》载，其文曰：“今有共买物，人出十一，盈八；人出九，不足十二．问人数、物价各几何?”意思是几个人一起去买东西，如果每人出11钱，就多了8钱；如果每人出9钱，就少了12钱．问一共有多少人?这个物品的价格是多少?设共有<i>x</i>人，物品的价格为<i>y</i>钱，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x-11y=8 \\\\ y-9x=12 \\end{cases}  $</p><p>B． $ \\begin{cases} 11x-y=8 \\\\ 9x=y+12 \\end{cases}  $</p><p>C． $ \\begin{cases} 11x-8=y \\\\ 9x+12=y \\end{cases}  $</p><p>D． $ \\begin{cases} 11x=y-8 \\\\ 9x=y+12 \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|320000|140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆重庆八中 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-06-10", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585921572000538624", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585921572000538624", "title": "重庆市第八中学校2024−2025学年九年级下学期5月月考数学试题", "paperCategory": 1}, {"id": "559118207035940864", "title": "江苏省无锡市天一实验学校2024−2025学年九年级下学期3月月考数学试卷", "paperCategory": 1}, {"id": "546877557443960832", "title": "山西省运城市2024−2025学年上学期期末测试八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "587003357694308352", "questionArticle": "<p>3．《孙子算经》是中国传统数学的重要著作，其中有一道题，原文是：“今有木，不知长短．引绳度之，余绳四尺五寸；屈绳量之，不足一尺，木长几何？”意思是：用一根绳子去量一根木头的长，绳子还剩余 $ 4.5 $ 尺；将绳子对折再量木头，则木头还剩余1尺，问木头长多少尺？可设木头长为<i>x</i>尺，绳子长为<i>y</i>尺，则所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} y-x=4.5 \\\\ 0.5y=x-1 \\end{cases}  $ B． $ \\begin{cases} y=x+4.5 \\\\ y=2x-1 \\end{cases}  $ C． $ \\begin{cases} y-x=4.5 \\\\ 0.5y=x+1 \\end{cases}  $ D． $ \\begin{cases} y=x-4.5 \\\\ y=2x-1 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江宁波 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "587003343735664640", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "587003343735664640", "title": "2025年浙江省宁波市江北区中考数学二模试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "586999069672775680", "questionArticle": "<p>4．解方程组： $ \\begin{cases} 2x+y=7 \\\\ 2x-3y=3 \\end{cases}  $ ．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 二模", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586999046117564416", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "586999046117564416", "title": "2025年浙江省杭州市滨江区中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "586999066971643904", "questionArticle": "<p>5．我国古代数学专著《九章算术》中有一道关于“分钱”的问题：甲、乙两人有钱若干，若甲给乙10钱，则甲的钱是乙的2倍；若乙给甲5钱，则乙的钱是甲的 $ \\dfrac { 1 } { 3 } $ .若设甲原有 $ x $ 钱，乙原有 $ y $ 钱，则可列方程组<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>（结果可以不化简）．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "330000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025浙江杭州 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "586999046117564416", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "586999046117564416", "title": "2025年浙江省杭州市滨江区中考二模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585960840311910400", "questionArticle": "<p>6．当今时代，科技的发展日新月异，扫地机器人受到越来越多的消费者青睐，市场需求不断增长．某公司旗下扫地机器人配件销售部门，当前负责销售 $ A $ , $ B $ 两种配件,已知购进50件 $ A $ 配件和125件 $ B $ 配件需支出成本20000元；购进40件 $ A $ 配件和40件 $ B $ 配件需支出成本12400元．</p><p>（1）求 $ A $ , $ B $ 两种配件的进货单价；</p><p>（2）若该配件销售部门计划购进 $ A $ , $ B $ 两种配件共400件， $ B $ 配件进货件数不低于 $ A $ 配件件数的3倍．据市场销售分析， $ A $ 配件提价16%销售， $ B $ 配件的售价是进价的 $ \\dfrac { 4 } { 3 } $ ．怎样安排 $ A $ , $ B $ 两种配件的进货数量，才能让本次销售的利润达到最大？最大利润是多少？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南河南实验中学 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16438|16544", "keyPointNames": "和差倍分问题|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585960812776304640", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585960812776304640", "title": "河南省实验中学2024−2025学年下学期第二次学业评价八年级数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585960957639172096", "questionArticle": "<p>7．2025年春节档，电影《哪吒之魔童闹海》掀起观影热潮，影片将封神神话中的角色（如哪吒、敖丙）赋予现代价值观，使传统文化符号与当代人民心理形成共振．某文创店果断订购了印有“哪吒”图案和“敖丙”图案的两种书签．经统计，订购30张“哪吒”书签与20张“敖丙”书签，成本共计430元；而订购45张“哪吒”书签和25张“敖丙”书签，则需花费605元．</p><p>（1）求“哪吒”、“敖丙”两种书签每张的进价分别是多少元？</p><p>（2）该文创店计划购进“哪吒”、“敖丙“两种书签共90张，“哪吒”种书签的购进数量不超过“敖丙”种书签数量 $ \\dfrac { 4 } { 5 } $ ，已知“哪吒”、“敖丙”两种书签的销售单价分别为15元和12元，如何规划购买方案，才能使文具店在这批书签全部售出后获得最大利？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585960930334253056", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "585960930334253056", "title": "河南省周口市第四初级中学2024−2025学年八年级下学期第二次素养评价数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585961046575198208", "questionArticle": "<p>8．对于未知数为 $ x $ 、 $ y $ 的二元一次方程组，如果方程组的解 $ x $ 、 $ y $ 满足 $ \\left  | { x-y } \\right  | =1 $ ，我们就说方程组的解 $ x $ 与 $ y $ 具有“邻好关系”．</p><p>（1）方程组 $ \\begin{cases} 2x+y=7 \\\\ x+2y=8 \\end{cases}  $ 的解 $ x $ 与 $ y $ 是否具有“邻好关系”？说明你的理由；</p><p>（2）若方程组 $ \\begin{cases} -x+3y=4m \\\\ x+y=6 \\end{cases}  $ 的解 $ x $ 与 $ y $ 具有“邻好关系”，求 $ m $ 的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16258|16424", "keyPointNames": "绝对值方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585961047430836224", "questionArticle": "<p>9．第 $ 24 $ 届冬季奥运会于 $ 2022 $ 年 $ 02 $ 月 $ 04 $ 日至 $ 2022 $ 年 $ 02 $ 月 $ 20 $ 日在中华人民共和国北京市和张家口市联合举行，这是中国历史上第一次举办冬季奥运会．冬奥会吉祥物“冰墩墩”和“雪容融”陶制品分为小套装和大套装两种．已知购买 $ 2 $ 个小套装比购买 $ 1 $ 个大套装少用 $ 20 $ 元；购买 $ 3 $ 个小套装和 $ 2 $ 个大套装，共需 $ 390 $ 元．</p><p>（1）求这两种套装的单价分别为多少元？</p><p>（2）某校计划正好用 $ 1500 $ 元的资金购买这种陶制品小套装和大套装作为奖品，则该校最多可以购买大套装多少个？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16420|16438", "keyPointNames": "二元一次方程的解|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "585961045123969024", "questionArticle": "<p>10．若关于 $ x $ ， $ y $ 的两个二元一次方程组 $ \\begin{cases} x+y=3 \\\\ x-y=1 \\end{cases}  $ 与 $ \\begin{cases} mx+2y=4 \\\\ 4x-ny=9 \\end{cases}  $ 的解相同．</p><p>（1）求 $ m $ 和 $ n $ 的值；</p><p>（2）求 $ 3m-n $ 的平方根．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "410000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025河南周口 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-06-10", "keyPointIds": "16288|16424|16427", "keyPointNames": "算术平方根|加减消元法解二元一次方程组|同解方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "585961025326854144", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "585961025326854144", "title": "河南省周口市第一初级中学2024−2025学年七年级下学期第二次综合测试数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 39, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 39, "timestamp": "2025-07-01T02:05:24.836Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}