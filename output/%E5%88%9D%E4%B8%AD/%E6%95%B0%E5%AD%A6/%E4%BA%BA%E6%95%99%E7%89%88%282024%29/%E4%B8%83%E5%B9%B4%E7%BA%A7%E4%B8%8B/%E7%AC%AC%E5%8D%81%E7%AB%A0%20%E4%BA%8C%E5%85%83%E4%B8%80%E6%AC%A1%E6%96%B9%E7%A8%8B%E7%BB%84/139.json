{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 138, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "564579966731460608", "questionArticle": "<p>1．如果将二元一次方程 $ y=-2x+7 $ 的一组正整数解 $ \\begin{cases} x=2 \\\\ y=3 \\end{cases}  $ 写成 $ \\left ( { 2,3 } \\right )  $ 的形式，并称 $ \\left ( { 2,3 } \\right )  $ 为方程 $ y=-2x+7 $ 的一个正整数点，请写出方程 $ y=-2x+7 $ 剩下的正整数点<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564579950881185792", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564579950881185792", "title": "山西省晋城市多校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564579962503602176", "questionArticle": "<p>2．若方程 $ mx+ny=6 $ 的两个解是 $ \\begin{cases} x=1 \\\\ y=1 \\end{cases}  $ ， $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ ，则 $ m $ ， $ n $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -4 $ ， $ -2 $ B． $ 2 $ ， $ 4 $ C． $ 4 $ ， $ 2 $ D． $ -2 $ ， $ -4 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564579950881185792", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564579950881185792", "title": "山西省晋城市多校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564579960112848896", "questionArticle": "<p>3．麦收季节，3台大收割机和6台小收割机同时工作2小时共收割小麦5.6公顷，2台大收割机和3台小收割机同时工作5小时共收割小麦8公顷．1台大收割机和1台小收割机每小时各收割小麦多少公顷？设1台大收割机和1台小收割机每小时各收割小麦 $ x $ 公顷和 $ y $ 公顷，根据题意，可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 2\\left ( { 6x+3y } \\right ) =5.6 \\\\ 5\\left ( { 2x+3y } \\right ) =8 \\end{cases}  $ B． $ \\begin{cases} 3\\left ( { 2x+6y } \\right ) =5.6 \\\\ 2\\left ( { 5x+3y } \\right ) =8 \\end{cases}  $ </p><p>C． $ \\begin{cases} 2\\left ( { 3x+6y } \\right ) =5.6 \\\\ 5\\left ( { 2x+3y } \\right ) =8 \\end{cases}  $ D． $ \\begin{cases} 2\\left ( { 6x+3y } \\right ) =5.6 \\\\ 5\\left ( { 3x+2y } \\right ) =8 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16438", "keyPointNames": "和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564579950881185792", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564579950881185792", "title": "山西省晋城市多校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564579957290082304", "questionArticle": "<p>4．下列四组数中，是二元一次方程 $ x+2y=5 $ 的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=2 \\\\ y=0 \\end{cases}  $ B． $ \\begin{cases} x=-2 \\\\ y=4 \\end{cases}  $ C． $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ D． $ \\begin{cases} x=0.5 \\\\ y=3 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024山西晋城 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564579950881185792", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564579950881185792", "title": "山西省晋城市多校2023−2024学年七年级下学期期中数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "564938746849697792", "questionArticle": "<p>5．甲、乙两人同时解方程组 $ \\begin{cases} ax+by=2 \\\\ cx-3y=-2 \\end{cases}  $ ，甲正确解得 $ \\begin{cases} x=1 \\\\ y=-1 \\end{cases}  $ ，乙因抄错<i>c</i>，解得 $ \\begin{cases} x=2 \\\\ y=-6 \\end{cases}  $ ，则<i>a</i> =<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，<i>b</i> =<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，<i>c</i> =<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024辽宁铁岭 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564938727044194304", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564938727044194304", "title": "辽宁省铁岭市2023−2024学年七年级下学期期中数学考试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564938376526209024", "questionArticle": "<p>6．解方程组：</p><p>(1) $ \\begin{cases} x+y=4 \\\\ 3x+y=2 \\end{cases}  $ ；</p><p>(2) $ \\begin{cases} 3x-5y=-3 \\\\ \\dfrac { x } { 2 }-\\dfrac { y } { 3 }=1 \\end{cases}  $ .</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏扬州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564938353419788288", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564938353419788288", "title": "江苏省扬州市梅岭中学2023−2024学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564938750129643520", "questionArticle": "<p>7．有甲、乙、丙三种货物，若购甲 $ 3 $ 件、乙 $ 7 $ 件、丙 $ 1 $ 件共需 $ 315 $ 元；若购甲 $ 4 $ 件，乙 $ 10 $ 件，丙 $ 1 $ 件，共需 $ 420 $ 元，问购甲、乙、丙各 $ 5 $ 件共需<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u> 元．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024辽宁铁岭 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16443|16444", "keyPointNames": "解三元一次方程组|三元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564938727044194304", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564938727044194304", "title": "辽宁省铁岭市2023−2024学年七年级下学期期中数学考试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564938753770299392", "questionArticle": "<p>8．已知关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} x+y=2 \\\\ ax+by=1 \\end{cases}  $ 与 $ \\begin{cases} x-2y=5 \\\\ ax-by=4 \\end{cases}  $ 的解相同，求<i>a</i>，<i>b</i>的值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024辽宁铁岭 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16424|16427", "keyPointNames": "加减消元法解二元一次方程组|同解方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564938727044194304", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564938727044194304", "title": "辽宁省铁岭市2023−2024学年七年级下学期期中数学考试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564938753061462016", "questionArticle": "<p>9．已知 $ \\left  | { 3x-2y-1 } \\right  | +\\sqrt { 5x+7y-12 }=0 $ ，求 $ 6x+3y $ 的平方根．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024辽宁铁岭 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16257|16287|16424|28421", "keyPointNames": "绝对值非负性的应用|平方根|加减消元法解二元一次方程组|算术平方根非负性的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564938727044194304", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "564938727044194304", "title": "辽宁省铁岭市2023−2024学年七年级下学期期中数学考试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "564938371165888512", "questionArticle": "<p>10．关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+y=4 \\\\ x+2y=m \\end{cases}  $ 的解满足 $ x+y=1 $ ，则<i>m</i>的值为 <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024江苏扬州 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-12", "keyPointIds": "16402|16424", "keyPointNames": "解一元一次方程|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "564938353419788288", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "564938353419788288", "title": "江苏省扬州市梅岭中学2023−2024学年七年级下学期4月期中考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 139, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 139, "timestamp": "2025-07-01T02:17:18.856Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}