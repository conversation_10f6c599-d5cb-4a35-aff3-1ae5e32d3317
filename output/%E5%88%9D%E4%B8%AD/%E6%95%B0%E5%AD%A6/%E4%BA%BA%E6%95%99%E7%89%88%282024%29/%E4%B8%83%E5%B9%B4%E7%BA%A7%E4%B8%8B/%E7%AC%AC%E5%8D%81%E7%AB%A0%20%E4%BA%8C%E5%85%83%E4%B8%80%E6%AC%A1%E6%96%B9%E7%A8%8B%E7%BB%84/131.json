{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 130, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "567105193654919168", "questionArticle": "<p>1．甲、乙两人都以不变的速度在400米的环形路上跑步．如果同时同地出发，反向而行，每隔2分钟相遇一次；如果同时同地出发，同向而行，每隔6分钟相遇一次．已知甲比乙跑得快．</p><p>(1)甲、乙两人速度分别是多少米每分钟？</p><p>(2)甲、乙两人跑一圈各需要多少分钟？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-16", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105165871849472", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567105165871849472", "title": "黑龙江省哈尔滨市第十七中学2024−2025学年七年级下学期3月考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105194326007808", "questionArticle": "<p>2．为了提倡节约用水，某市根据居民每月的用水量实行阶梯水价：每户每月用水量不超过 $ 12{ \\rm{ m } }{^{{ { 3 } }}} $ 时，按一级单价收费；超过 $ 12{ \\rm{ m } }{^{{ { 3 } }}} $ 时，超过的部分按二级单价收费．五月份张华家用水 $ 14{ \\rm{ m } }{^{{ { 3 } }}} $ ，缴费37.6元；李明家用水 $ 17{ \\rm{ m } }{^{{ { 3 } }}} $ ，缴费47.2元．</p><p>(1)那么这个市一级水费、二级水费的单价分别是多少？</p><p>(2)若小丽家3月份缴费95.2元，那么小丽家三月份用水多少立方米？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-16", "keyPointIds": "16413|16438", "keyPointNames": "电费、水费问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105165871849472", "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": "567105165871849472", "title": "黑龙江省哈尔滨市第十七中学2024−2025学年七年级下学期3月考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105196146335744", "questionArticle": "<p>3．随着“低碳生活，绿色出行”理念的普及，新能源汽车正逐渐成为人们喜爱的交通工具．某汽车销售公司计划购进一批新能源汽车尝试进行销售，据了解，6辆 $ A $ 型汽车、5辆 $ B $ 型汽车的进价共计980万元；3辆 $ A $ 型汽车、7辆 $ B $ 型汽车的进价共计940万元．</p><p>(1)求 $ A $ ， $ B $ 两种型号的汽车每辆进价分别为多少万元？</p><p>(2)若“五一”搞活动，该公司了解到 $ A $ ， $ B $ 两种型号汽车均按照原来的六折出售，所以公司计划用960万元购进以上两种型号的新能源汽车（两种型号的汽车均购买），请你帮助该公司设计购买方案；</p><p>(3)若该汽车销售公司销售1辆 $ A $ 型汽车可获利6000元，销售1辆 $ B $ 型汽车可获利4000元，在(2)中的购买方案中，假如这些新能源汽车全部售出，哪种方案获利最大？最大利润是多少元？</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000|350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 4, "referenceNum": 2, "createTime": "2025-04-16", "keyPointIds": "16434|16438", "keyPointNames": "方案问题|和差倍分问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105165871849472", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "567105165871849472", "title": "黑龙江省哈尔滨市第十七中学2024−2025学年七年级下学期3月考试数学试题", "paperCategory": 1}, {"id": "580244070536097792", "title": "福建省厦门市松柏中学2024−2025学年下学期七年级数学期中考卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105189791965184", "questionArticle": "<p>4．在大禹治水的时代，有一种神龟背负着一张神秘的图浮出洛水，吉祥献瑞，后世称之为“洛书”，当后人将“洛书”上的数填在图①的表中时发现：每行、每列、每条对角线上的三个数字之和都相等，像这样的数字方阵，称为“幻方”．若下图也是一个“幻方”，则 $ \\sqrt { x }+y $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p><table style=\"border: solid 1px;border-collapse: collapse; width:57pt;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>4</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ x $ </p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>5</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>8</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>&nbsp;</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p> $ y $ </p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-16", "keyPointIds": "16433", "keyPointNames": "数字问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105165871849472", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567105165871849472", "title": "黑龙江省哈尔滨市第十七中学2024−2025学年七年级下学期3月考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567477322342768640", "questionArticle": "<p>5．中国古代人民在生产生活中发现了许多数学问题，在（孙子算经）中记载了这样一个问题，大意为有若干人乘车，若每车乘坐3人，则2辆车无人乘坐；若每车乘坐2人，则9人无车可乘，问共有多少辆车，多少人，设共有 $ x $ 辆车， $ y $ 人，则可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 3\\left ( { x-2 } \\right ) =y \\\\ 2x+9=y \\end{cases}  $</p><p>B． $ \\begin{cases} 3\\left ( { x+2 } \\right ) =y \\\\ 2x+9=y \\end{cases}  $</p><p>C． $ \\begin{cases} 3x=y \\\\ 2x+9=y \\end{cases}  $</p><p>D． $ \\begin{cases} 3\\left ( { x+2 } \\right ) =y \\\\ 2x-9=y \\end{cases}  $</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "210000|510000|610000|150000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁铁岭 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 10, "referenceNum": 5, "createTime": "2025-04-16", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567477306513465344", "questionFeatureName": "数学文化题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567477306513465344", "title": "2025年辽宁省铁岭市中考一模数学试题", "paperCategory": 1}, {"id": "526524250452172800", "title": "陕西省西安市第三中学名校2023−2024学年八年级上学期期末数学试题", "paperCategory": 1}, {"id": "407684084061741056", "title": "四川省成都市石室天府中学2023-2024学年八年级上学期1月期末数学试题", "paperCategory": 1}, {"id": "452594284119760896", "title": "内蒙古自治区巴彦淖尔市第二中学2023-2024学年七年级下学期5月期中考试数学试题", "paperCategory": 1}, {"id": "267632922236067840", "title": "辽宁省沈阳市第七中学2022-2023学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "565307266242486272", "questionArticle": "<p>6．2025年首届逍遥津新春灯会，自2025年1月22日开幕，持续了46天，共有超55万人次观展．已知灯展有两种门票：单人票78元，双人票148元．单人票只能让1人入园观展，双人票可以让两人入园观展．假设某天有1万人次入园观展，观展的人使用了单人票或双人票入园，这1万人次使用的门票总价为75.2万元．求这1万人次使用的单人票和双人票各多少张．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽合肥 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565307241223462912", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "565307241223462912", "title": "2025年安徽省合肥市庐阳中学九年级中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105192220467200", "questionArticle": "<p>7．解下列方程组：</p><p>（1） $ \\begin{cases} 2x-5y=7 \\\\ 4x-3y=7 \\end{cases}  $ </p><p>（2） $ \\begin{cases} \\dfrac { x+y } { 3 }+\\dfrac { x-y } { 2 }=6 \\\\ 3\\left ( { x+y } \\right ) -2\\left ( { x-y } \\right ) =2 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105165871849472", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567105165871849472", "title": "黑龙江省哈尔滨市第十七中学2024−2025学年七年级下学期3月考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105186444910592", "questionArticle": "<p>8．对于 $ x $ ， $ y $ 定义一种新运算“ $ \\forall  $ ”： $ x\\forall y=ax-by $ ，其中 $ a $ ， $ b $ 为常数，已知 $ 2\\forall \\left ( { -1 } \\right ) =17 $ ， $ 1\\forall 3=5 $ ，那么 $ 2\\forall \\left ( { -3 } \\right ) = $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 3, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16426", "keyPointNames": "二元一次方程组的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105165871849472", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567105165871849472", "title": "黑龙江省哈尔滨市第十七中学2024−2025学年七年级下学期3月考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "567105185639604224", "questionArticle": "<p>9．关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} 3x-5y-2m=0 \\\\ 2x+10y-m+18=0 \\end{cases}  $ 的解 $ x $ ， $ y $ 互为相反数，则 $ m $ 的值为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "230000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025黑龙江哈尔滨市第十七中学 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-15", "keyPointIds": "16252|16424", "keyPointNames": "相反数的应用|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "567105165871849472", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "567105165871849472", "title": "黑龙江省哈尔滨市第十七中学2024−2025学年七年级下学期3月考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "565307398811852800", "questionArticle": "<p>10．某校八年级学生到郊外参加研学活动，已知用 3 辆小客车和 1 辆大客车每次可运送学生 160 人，用 2 辆小客车和 3 辆大客车每次可运送学生 235 人，则每辆小客车和每辆大客车各能坐多少名学生？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025安徽合肥 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-04-15", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "565307366993862656", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "565307366993862656", "title": "2025年安徽省合肥市寿春中学（南国校区）九年级中考一模数学试卷", "paperCategory": 1}, {"id": "565510931058302976", "title": "2025年安徽省合肥市寿春中学中考数学一模试卷", "paperCategory": 11}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 131, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 131, "timestamp": "2025-07-01T02:16:20.999Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}