{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 152, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "562035242648772608", "questionArticle": "<p>1．如果关于<i>x</i>、<i>y</i>的方程组 $ \\begin{cases} 3x+2y=m+1 \\\\ 2x+y=m-1 \\end{cases}  $ 中<i>x</i>&gt;<i>y</i>，且关于<i>x</i>的不等式组 $ \\begin{cases} \\dfrac { x-1 } { 2 }  &lt;  \\dfrac { 1+x } { 3 } \\\\ 5x+2\\geqslant  x+m \\end{cases}  $ 有且只有4个整数解，则符合条件的所有整数<i>m</i>的和为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．8B．9C．10D．11</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆重庆市长寿中学 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 1, "createTime": "2025-04-01", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562035227977097216", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "562035227977097216", "title": "重庆市长寿中学校2024−2025学年八年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562034517306810368", "questionArticle": "<p>2．某火锅店为吸引客户，推出两款双人套餐，下表是近两天两种套餐的收入统计：</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>套餐时间</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>数量</p></td><td rowspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>收入</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>A</i>套餐</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p><i>B</i>套餐</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第一天</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>20次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>10次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>2800元</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>第二天</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>15次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>20次</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse;\"><p>3350元</p></td></tr></table><p>(1)求这两款套餐的单价；</p><p>(2)<i>A</i>套餐的成本约为45元，<i>B</i>套餐的成本约为50元，受材料和餐位的限制，该火锅店每天最多供应50个套餐，且<i>A</i>套餐的数量不少于<i>B</i>套餐数量的 $ \\dfrac { 1 } { 5 } $ ，求火锅店每天在这两种套餐上获得的最大利润．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西安铁一中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-04-01", "keyPointIds": "16437|16486|16544", "keyPointNames": "销售利润问题|一元一次不等式的应用|最大利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562034489427271680", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "562034489427271680", "title": "陕西省西安市铁一中学2024−2025学年八年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562034514706341888", "questionArticle": "<p>3．已知关于 $ x $ ， $ y $ 的方程组 $ \\begin{cases} x+y=-9-a \\\\ x-y=5+3a \\end{cases}  $ 的解 $ x $ ， $ y $ 均为负数．</p><p>(1)求 $ a $ 的取值范围；</p><p>(2)在 $ a $ 的取值范围内，当 $ a $ 取何整数时，不等式 $ \\left ( { 2a+1 } \\right ) x > 2a+1 $ 的解集为 $ x  <  1 $ ？</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "610000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025陕西西安铁一中学 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-01", "keyPointIds": "16424|16489", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562034489427271680", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "562034489427271680", "title": "陕西省西安市铁一中学2024−2025学年八年级下学期第一次月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562033865826541568", "questionArticle": "<p>4．春节期间，电影《哪吒之魔童闹海》在某影院推出了 $ { { 2 } }{ \\rm{ D } }、{ { 3 } }{ \\rm{ D } } $ 和 $ { \\rm{ I } }{ \\rm{ M } }{ \\rm{ A } }{ \\rm{ X } } $ 三种放映版式．小颖调查了解到多数人选择 $ { { 3 } }{ \\rm{ D } } $ 版或 $ { \\rm{ I } }{ \\rm{ M } }{ \\rm{ A } }{ \\rm{ X } } $ 版，在该影院购买某时段的《哪吒之魔童闹海》电影票，5张 $ { { 3 } }{ \\rm{ D } } $ 电影票的费用和4张 $ { \\rm{ I } }{ \\rm{ M } }{ \\rm{ A } }{ \\rm{ X } } $ 电影票的费用一样；2张 $ { { 3 } }{ \\rm{ D } } $ 电影票和1张 $ { \\rm{ I } }{ \\rm{ M } }{ \\rm{ A } }{ \\rm{ X } } $ 电影票共需130元．请你帮助小颖求出该影院《哪吒之魔童闹海》该时段的 $ { { 3 } }{ \\rm{ D } } $ 版和 $ { \\rm{ I } }{ \\rm{ M } }{ \\rm{ A } }{ \\rm{ X } } $ 版的电影票单价．</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/31/2/1/0/0/0/562033771022688261/images/img_22.jpg\" style=\"vertical-align:middle;\" width=\"75\" alt=\"试题资源网 https://stzy.com\"></p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西晋中 · 一模", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-04-01", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562033841218560000", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "562033841218560000", "title": "2025年山西省晋中市榆次区九年级中考一模数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562034036115283968", "questionArticle": "<p>5．若关于<i>x</i>的方程(<i>k</i>﹣2)<i>x</i><i><sup>|</sup></i><i><sup>k</sup></i><i><sup>|</sup></i><i><sup>﹣</sup></i><sup>1</sup>−7<i>y</i>＝8是二元一次方程，则<i>k</i>＝<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西临汾 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-01", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562034021342945280", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "562034021342945280", "title": "山西省临汾市霍州市多校联考2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562034035423223808", "questionArticle": "<p>6．语句“ $ x $ 的3倍比 $ y $ 大5”用方程表示为<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025山西临汾 · 月考", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-01", "keyPointIds": "16419", "keyPointNames": "二元一次方程的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562034021342945280", "proofreadStatus": 6, "downloadCount": 0, "questionSourceList": [{"id": "562034021342945280", "title": "山西省临汾市霍州市多校联考2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "562034033237991424", "questionArticle": "<p>7．若表格中每对<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/31/2/1/0/0/0/562033980171657217/images/img_2.png\" style=\"vertical-align:middle;\" width=\"13\" alt=\"试题资源网 https://stzy.com\">，<img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/31/2/1/0/0/0/562033980171657218/images/img_3.png\" style=\"vertical-align:middle;\" width=\"15\" alt=\"试题资源网 https://stzy.com\">的值都是同一个二元一次方程的解，则这个方程为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/03/31/2/1/0/0/0/562033980171657219/images/img_4.png\" style=\"vertical-align:middle;\" width=\"148\" alt=\"试题资源网 https://stzy.com\"></p><p>A． $ 5x+y=3 $ B． $ x+y=5 $ </p><p>C． $ 2x-y=0 $ D． $ 3x+y=5 $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西临汾 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-01", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562034021342945280", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "562034021342945280", "title": "山西省临汾市霍州市多校联考2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "562034031711264768", "questionArticle": "<p>8．若 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是关于<i>x</i>，<i>y</i>的二元一次方程<i>ax</i>+<i>y</i>＝3的解，则<i>a</i>的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0　　　　B．1　　　　C．2　　　　D．无法确定</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "140000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025山西临汾 · 月考", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-01", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "562034021342945280", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "562034021342945280", "title": "山西省临汾市霍州市多校联考2024−2025学年七年级下学期3月月考数学试卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "559854545993506816", "questionArticle": "<p>9．解方程：</p><p>(1) $ {\\left( { x-1 } \\right) ^ {2}}-4=0 $ ；</p><p>(2)解方程组 $ \\begin{cases} 3x+y=8 \\\\ 2x-y=7 \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025广东茂名 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-04-01", "keyPointIds": "16424|16452", "keyPointNames": "加减消元法解二元一次方程组|直接开平方法解一元二次方程", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559854526766817280", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "559854526766817280", "title": "广东省茂名市高州市联考2024−2025学年九年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "559470823351820288", "questionArticle": "<p>10．定义：若一个方程（组）的解也是一个一元一次不等式的解，我们称这个方程（组）的解是这个不等式的“友好解”．例如：方程 $ 2x-1=1 $ 的解是 $ x=1 $ ，同时 $ x=1 $ 也是不等式 $ x+1 &gt; 0 $ 的解，则称方程 $ 2x-1=1 $ 的解 $ x=1 $ 是不等式 $ x+1 &gt; 0 $ 的“友好解”．</p><p>(1)方程 $ 2\\left ( { x+3 } \\right ) =12 $ 的解一元一次不等式 $ \\dfrac { x-1 } { 2 }\\geqslant  1 $ 的“友好解”；（填“是”或“不是”）</p><p>(2)若关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2x+3y=5k+1 \\\\ 5x-y=4k-3 \\end{cases}  $ 的解是不等式 $ 6x-8y &gt; -9 $ 的“友好解”，求<i>k</i>的取值范围；</p><p>(3)方程 $ 2\\left ( { x-2 } \\right ) =2 $ 的解是不等式 $ 4x+2  &lt;  x+2m $ 的“友好解”，求<i>m</i>的最小整数值．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "340000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025安徽蚌埠 · 月考", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-04-01", "keyPointIds": "16424|16485", "keyPointNames": "加减消元法解二元一次方程组|解一元一次不等式", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "559470800492863488", "questionFeatureName": "新定义问题", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "559470800492863488", "title": "安徽省蚌埠市2024−2025学年七年级下学期第一次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 153, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 153, "timestamp": "2025-07-01T02:18:59.687Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}