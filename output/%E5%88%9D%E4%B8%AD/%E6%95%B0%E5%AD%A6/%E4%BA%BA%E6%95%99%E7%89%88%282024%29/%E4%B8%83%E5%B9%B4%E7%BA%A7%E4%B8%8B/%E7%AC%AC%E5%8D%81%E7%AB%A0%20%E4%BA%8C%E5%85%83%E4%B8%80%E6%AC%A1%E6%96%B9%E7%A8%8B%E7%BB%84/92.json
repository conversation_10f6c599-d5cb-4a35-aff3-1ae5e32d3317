{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 91, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "574750174431453184", "questionArticle": "<p>1．下面4组数值中，是二元一次方程 $ 3x-y=1 $ 的解的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x=0 \\\\ y=1 \\end{cases}  $ B． $ \\begin{cases} x=-1 \\\\ y=2 \\end{cases}  $ C． $ \\begin{cases} x=\\dfrac { 1 } { 3 } \\\\ y=0 \\end{cases}  $ D． $ \\begin{cases} x=2 \\\\ y=-5 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "110000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025北京 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16420", "keyPointNames": "二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574750160636387328", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574750160636387328", "title": "北京市第五十七中学2024−2025学年七年级下学期数学期中试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574750468842233856", "questionArticle": "<p>2．根据以下素材，完成任务．</p><table style=\"border: solid 1px;border-collapse: collapse; width:394.5pt;\"><tr><td colspan=\"3\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 393.75pt;\"><p>如何生产纸盒</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>素材1</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 141.75pt;\"><p>某工厂需制作如图所示的竖式与横式两种无盖纸盒（单位 $ { \\rm{ c } }{ \\rm{ m } } $ ）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 177pt;\"><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/06/2/1/0/0/0/574750415025119246/images/img_14.jpg\" style=\"vertical-align:middle;\" width=\"191\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>素材2</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 141.75pt;\"><p>工厂仓库内现存有 $ 35 { \\rm{ c } }{ \\rm{ m } }\\times 35 { \\rm{ c } }{ \\rm{ m } } $ 的正方形纸板150张， $ 35 { \\rm{ c } }{ \\rm{ m } }\\times 50 { \\rm{ c } }{ \\rm{ m } } $ 的长方形纸板300张，用库存纸板制作两种无盖纸盒．</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 177pt;\"><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/06/2/1/0/0/0/574750415025119247/images/img_15.jpg\" style=\"vertical-align:middle;\" width=\"147\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>素材3</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 141.75pt;\"><p>库存纸板用完后，采购部重新采购了如图规格的纸板，甲纸板尺寸为 $ 50 { \\rm{ c } }{ \\rm{ m } }\\times 70{ \\rm{ c } }{ \\rm{ m } } $ ，乙纸板尺寸为 $ 35 { \\rm{ c } }{ \\rm{ m } }\\times 85 { \\rm{ c } }{ \\rm{ m } } $ ，丙纸板尺寸为 $ 35 { \\rm{ c } }{ \\rm{ m } }\\times 70 { \\rm{ c } }{ \\rm{ m } } $ ．采购甲纸板有400张，乙纸板有300张，因采购单被墨水污染，导致丙种纸板的具体数字已经模糊不清，只知道百位和十位数字分别为1和4．纸板裁剪后可制作两种无盖纸盒．</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 177pt;\"><p><img src=\"https://cdn.stzy.com/zhizhi/Paper/0/2025/05/06/2/1/0/0/0/574750415025119248/images/img_16.jpg\" style=\"vertical-align:middle;\" width=\"191\" alt=\"试题资源网 https://stzy.com\"></p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>任务一</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 318.75pt;\"><p>求两种纸盒各做多少个，恰好将库存纸板用完，且每张纸板利用率均为 $ 100\\% $ ．</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 75pt;\"><p>任务二</p></td><td colspan=\"2\" class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 318.75pt;\"><p>若用本次重新采购的纸板裁剪做成竖式和横式无盖纸盒，纸板恰好用完，且每张纸板利用率均为 $ 100\\% $ ．请你帮助工厂确定丙纸板的张数．</p></td></tr></table><p>&nbsp;</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16439", "keyPointNames": "几何问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574750439108812800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574750439108812800", "title": "福建省福州屏东中学等2024−2025学年七年级下学联考数学期中卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574751847912611840", "questionArticle": "<p>3．推进中国式现代化，必须坚持不懈夯实农业基础，推进乡村全面振兴．某合作社着力发展乡村水果网络销售，在水果收获的季节，该合作社用17500元从农户处购进<i>A</i>，<i>B</i>两种水果共 $ 1500{ \\rm{ k } }{ \\rm{ g } } $ 进行销售，其中<i>A</i>种水果收购单价10元/ $ { \\rm{ k } }{ \\rm{ g } } {\\rm ，\\mathit{B}} $ 种水果收购单价15元/ $ { \\rm{ k } }{ \\rm{ g } } $ ．</p><p>(1)求<i>A</i>，<i>B</i>两种水果各购进多少千克；</p><p>(2)已知<i>A</i>种水果运输和仓储过程中质量损失 $ 4{ \\rm{ \\% } } $ ，若合作社计划<i>A</i>种水果至少要获得 $ 20{ \\rm{ \\% } } $ 的利润，不计其他费用，求<i>A</i>种水果的最低销售单价．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|360000|430000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2024四川成都 · 中考真题", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 4, "createTime": "2025-05-09", "keyPointIds": "16437|16486", "keyPointNames": "销售利润问题|一元一次不等式的应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "457847809808572416", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "457847809808572416", "title": "2024年四川省成都市中考数学试题", "paperCategory": 1}, {"id": "574751822075699200", "title": "江西省吉安市十校联盟学校2024−2025学年九年级下学期4月期中考试数学试题", "paperCategory": 1}, {"id": "575335369685639168", "title": "四川省眉山市东坡区苏辙中学共同体2024−2025学年七年级下学期4月期中考试数学试题", "paperCategory": 11}, {"id": "555176006874603520", "title": "湖南省长沙市立信中学2024—2025学年下学期九年级第一次月考数学试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574750463284781056", "questionArticle": "<p>4．一条船顺流航行，每小时行 $ 20 $ 千米；逆流航行，每小时行 $ 16 $ 千米.求船在静水中的速度与水流的速度.</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574750439108812800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574750439108812800", "title": "福建省福州屏东中学等2024−2025学年七年级下学联考数学期中卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574750461095354368", "questionArticle": "<p>5．解下列二元一次方程组：</p><p>(1) $ \\begin{cases} y=2x-3 \\\\ 3x+2y=8 \\end{cases}  $ ；</p><p>(2) $ \\left \\{\\hspace{-0.5em}  \\begin{array} {l} 5x+2y=16 \\\\ \\dfrac { x } { 2 }+\\dfrac { y } { 3 }=2 \\end{array} \\hspace{-0.5em} { { &nbsp; } }\\right.  $ ．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "43", "showQuestionTypeName": "计算题", "downloadNum": 4, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16423|16424", "keyPointNames": "代入消元法解二元一次方程组|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574750439108812800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574750439108812800", "title": "福建省福州屏东中学等2024−2025学年七年级下学联考数学期中卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "574750453528829952", "questionArticle": "<p>6．解关于<i>x</i>，<i>y</i>的方程组 $ \\begin{cases} 2\\left ( { a+1 } \\right ) x+3by=3 \\\\ 5bx-2ay=7 \\end{cases}  $ 可以用① $ \\times 3- $ ②，消去未知数 $ x $ ，也可以用 $ ①+②\\times 4 $ 消去未知数 $ y $ ，则 $ a+b $ 的值为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ -1 $ B．0C．1D．3</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16424", "keyPointNames": "加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574750439108812800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574750439108812800", "title": "福建省福州屏东中学等2024−2025学年七年级下学联考数学期中卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574750452140515328", "questionArticle": "<p>7．《算法统宗》里有这样一道题：“我问开店李三公，众客都来到店中，一房七客多七客，一房九客一房空．”李三公家的店有多少间客房，来了多少房客？若设该店有客房<i>x</i>间，房客<i>y</i>人，根据题意可列方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 7x+7=y \\\\ 9\\left ( { x-1 } \\right ) =y \\end{cases}  $ B． $ \\begin{cases} 7x-7=y \\\\ 9\\left ( { x-1 } \\right ) =y \\end{cases}  $ C． $ \\begin{cases} 7x+7=y \\\\ 9x-1=y \\end{cases}  $ D． $ \\begin{cases} 7x-7=y \\\\ 9x-1=y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-09", "keyPointIds": "16441", "keyPointNames": "其他问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574750439108812800", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574750439108812800", "title": "福建省福州屏东中学等2024−2025学年七年级下学联考数学期中卷", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "575479802968186880", "questionArticle": "<p>8．某工厂有26名工人，一个工人每天可加800个螺栓或1000个螺帽，1个螺栓与2个螺帽配套．现要求工人每天加工的螺栓和螺帽完整配套且没有剩余．若设安排 $ x $ 个工人加工螺栓， $ y $ 个工人加工螺帽，则列出正确的二元一次方程组为（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=26 \\\\ 1600x-1000y=0 \\end{cases}  $ B． $ \\begin{cases} x+y=26 \\\\ 800x-2000y=0 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=26 \\\\ 3200x-1000y=0 \\end{cases}  $ D． $ \\begin{cases} x+y=26 \\\\ 1600x-2000y=0 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "510000|350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建厦门六中 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 7, "referenceNum": 2, "createTime": "2025-05-09", "keyPointIds": "16432", "keyPointNames": "配套问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575479786685898752", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "575479786685898752", "title": "福建省厦门第六中学2024—2025学年七年级下学期期中检测数学试题", "paperCategory": 1}, {"id": "208635586231443456", "title": "四川省巴中市2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574751320520826880", "questionArticle": "<p>9．在解方程组 $ \\begin{cases} 2ax+y=5 \\\\ 2x-by=13 \\end{cases}  $ 时，由于粗心，甲看错了方程组中的<i>a</i>，得解为 $ \\begin{cases} x=\\dfrac { 7 } { 2 } \\\\ y=-2 \\end{cases}  $ ；乙看错了方程组中的<i>b</i>，得解为 $ \\begin{cases} x=3 \\\\ y=-7 \\end{cases}  $ ．求出原方程组的正确解．</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "360000|420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025湖北黄冈 · 期中", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 16, "referenceNum": 2, "createTime": "2025-05-09", "keyPointIds": "16420|16424", "keyPointNames": "二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574751294113488896", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "574751294113488896", "title": "湖北省黄冈市2024−2025学年八年级下学期4月期中数学试题", "paperCategory": 1}, {"id": "532312655622610944", "title": "江西省九江市同文中学2024−2025学年八年级上学期12月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "575480326450880512", "questionArticle": "<p>10．下列方程组不是二元一次方程组的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 4x+3y=6 \\\\ 2x+y=4 \\end{cases}  $ B． $ \\begin{cases} x+y=4 \\\\ x-y=4 \\end{cases}  $ C． $ \\begin{cases} \\dfrac { 1 } { x }+y=4 \\\\ x-y=1 \\end{cases}  $ D． $ \\begin{cases} 3x+5y=25 \\\\ x+10y=25 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "-1|440000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025广东湛江 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-05-09", "keyPointIds": "16421", "keyPointNames": "二元一次方程组的定义", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575480316673957888", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "575480316673957888", "title": "广东省湛江市雷州市四校联考2024−2025学年七年级下学期4月期中数学试题", "paperCategory": 1}, {"id": "362266881984602112", "title": "2023-2024学年七年级上册沪科版数学第三章 一次方程与方程组单元测试", "paperCategory": 1}], "questionTypeCode": "1"}]}}, "requestData": {"pageNum": 92, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 92, "timestamp": "2025-07-01T02:11:41.843Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}