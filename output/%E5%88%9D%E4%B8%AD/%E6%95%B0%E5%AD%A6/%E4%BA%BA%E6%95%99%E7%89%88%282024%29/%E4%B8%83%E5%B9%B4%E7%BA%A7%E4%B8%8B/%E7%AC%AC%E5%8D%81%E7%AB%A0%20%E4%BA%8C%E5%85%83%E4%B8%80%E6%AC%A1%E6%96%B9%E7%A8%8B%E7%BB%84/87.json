{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 86, "pageSize": 10, "totalPage": 1348, "total": "13475", "list": [{"questionId": "575478884851818496", "questionArticle": "<p>1．我国古代《四元玉鉴》中记载“二果问价”问题，其内容如下：九百九十九文钱，甜果苦果买一千，甜果九个十一文，苦果七个四文钱，试问甜苦果几个，又问各该几个钱？若设买甜果<i>x</i>个，买苦果<i>y</i>个，则下列关于<i>x</i>、<i>y</i>的二元一次方程组中符合题意的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} x+y=999 \\\\ \\dfrac { 11 } { 9 }x+\\dfrac { 4 } { 7 }y=1000 \\end{cases}  $ </p><p>B． $ \\begin{cases} x+y=1000 \\\\ \\dfrac { 11 } { 9 }x+\\dfrac { 4 } { 7 }y=999 \\end{cases}  $ </p><p>C． $ \\begin{cases} x+y=999 \\\\ \\dfrac { 9 } { 11 }x+\\dfrac { 7 } { 4 }y=1000 \\end{cases}  $ </p><p>D． $ \\begin{cases} x+y=1000 \\\\ \\dfrac { 9 } { 11 }x+\\dfrac { 7 } { 4 }y=999 \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025福建福州 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 2, "createTime": "2025-05-13", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575478869882347520", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "575478869882347520", "title": "福建省福州第十九中学2024−2025学年下学期期中测试七年级数学试题", "paperCategory": 1}, {"id": "158650432386015232", "title": "广东省揭阳市普宁市2021-2022学年八年级上学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577296800157052928", "questionArticle": "<p>2．关于 $ x $ 、 $ y $ 的方程组 $ \\begin{cases} mx-y=3 \\\\ 3x+ny=14 \\end{cases}  $ 的解为 $ \\begin{cases} x=2 \\\\ y=-1 \\end{cases}  $ ，则 $ m-n $ 的平方根是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．9B． $ \\pm 3 $ C． $ \\sqrt { 7 } $ D． $ \\pm \\sqrt { 7 } $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|420000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市育贤中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-05-13", "keyPointIds": "16287|16420|16424", "keyPointNames": "平方根|二元一次方程的解|加减消元法解二元一次方程组", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577296783820238848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "577296783820238848", "title": "天津市南开区育贤中学2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}, {"id": "461314278387130368", "title": "湖北省恩施州清江外国语学校2023-2024学年七年级下学期期末数学试题", "paperCategory": 1}, {"id": "459113189126479872", "title": "湖北省武汉市江岸区2022--2023学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577296798621937664", "questionArticle": "<p>3．甲、乙两人练习跑步，如果乙先跑10米，则甲跑5秒就可追上乙；如果乙先跑2秒，则甲跑4秒就可追上乙，若设甲的速度为<i>x</i>米/秒，乙的速度为<i>y</i>米/秒，则下列方程组中正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x=5y+10 \\\\ 4x=4y+2y \\end{cases}  $ B． $ \\begin{cases} 5x-5y=10 \\\\ 4x-2x=4y \\end{cases}  $ </p><p>C． $ \\begin{cases} 5x+10=5y \\\\ 4x-4y=2 \\end{cases}  $ D． $ \\begin{cases} 5x-5y=10 \\\\ 4x-2=4y \\end{cases}  $ </p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000|340000|440000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津天津市育贤中学 · 期中", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 2, "referenceNum": 3, "createTime": "2025-05-13", "keyPointIds": "16430", "keyPointNames": "行程问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "577296783820238848", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "577296783820238848", "title": "天津市南开区育贤中学2024−2025学年下学期七年级数学期中试卷", "paperCategory": 1}, {"id": "129881875624009728", "title": "广州市铁一中学2020年九年级下学期中考一模数学试题", "paperCategory": 1}, {"id": "128534860453421056", "title": "安徽省芜湖市2020年中考三模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "577163597568061440", "questionArticle": "<p>4．已知等腰三角形的周长为 $ 15\\mathrm{c}\\mathrm{m} $ ，一腰上的中线把等腰三角形分成周长之差为 $ 3\\mathrm{c}\\mathrm{m} $ 的两个三角形，求这个等腰三角形的腰长.</p>", "gradeCode": "8", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "320000|-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "八年级 · 专题模块", "showQuestionTypeCode": "30", "showQuestionTypeName": "解答题", "downloadNum": 5, "referenceNum": 2, "createTime": "2025-05-12", "keyPointIds": "16439|16661|30379", "keyPointNames": "几何问题|等腰三角形的性质|三角形的中线", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 2, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "上分专题（三） 等腰三角形中的分类讨论2025秋初中上分卷 数学八年级上册 人教版", "paperCategory": 2}, {"id": "427939015960076288", "title": "江苏省苏州市苏州高新区实验初级中学2023-2024年七年级下学期3月月考数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "576950981172174848", "questionArticle": "<p>5．《九章算术》是我国古代数学名著，卷七“盈不足”中有题译文如下：今有人合伙买羊，每人出5钱，会差45钱：每人出7钱，会差3钱．问合伙人数、羊价各是多少？设合伙人数为<i>x</i>人，羊价<i>y</i>钱，下面所列方程组正确的是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A． $ \\begin{cases} 5x=y+45 \\\\ 7x=y+3 \\end{cases}  $ B． $ \\begin{cases} 5x+45=y \\\\ 7x+3=y \\end{cases}  $ </p><p>C． $ \\begin{cases} 5y=x+45 \\\\ 7y=x+3 \\end{cases}  $ D． $ \\begin{cases} 5y+45=x \\\\ 7y+3=x \\end{cases}  $ </p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000|420000", "diffcultCode": "1", "diffcultName": "易", "questionSource": "2025湖北鄂州 · 一模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 3, "referenceNum": 3, "createTime": "2025-05-12", "keyPointIds": "16437", "keyPointNames": "销售利润问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576950965082824704", "questionFeatureName": "数学文化题", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "576950965082824704", "title": "2025年湖北省鄂州市中考一模数学试题", "paperCategory": 1}, {"id": "425582896210026496", "title": "重庆市北碚区西南大学附属中学校2022-2023学年七年级下学期3月月考数学试题", "paperCategory": 1}, {"id": "209694703855378432", "title": "重庆市铜梁区2021-2022学年七年级下学期期末数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "574753126114172928", "questionArticle": "<p>6．已知 $ \\begin{cases} x=1 \\\\ y=2 \\end{cases}  $ 是关于 $ x，y $ 的二元一次方程 $ ax-2y=10 $ 的一个解，则 $ a= $ <u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "120000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025天津二十五中 · 期中", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-12", "keyPointIds": "16402|16420", "keyPointNames": "解一元一次方程|二元一次方程的解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "574753109164990464", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "574753109164990464", "title": "天津市南开区第二十五中学2024-−2025学年七年级下册数学期中测试卷", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "576952952998375424", "questionArticle": "<p>7．在一组互不相等的正整数 $ a{{}_{ 1 } },a{{}_{ 2 } },a{{}_{ 3 } },\\cdot \\cdot \\cdot \\cdot \\cdot \\cdot ,a{{}_{ n } } $ 中任意提取 $ m\\left ( { 1  <  m  <  n } \\right )  $ 个数，若这 $ m $ 个数的和与积相加正好等于这 $ n $ 个数的和，则称这样的提取为完美提取．</p><p>例如：在1，2，3，4，5中，因为 $ 1+2+3+4+5=15 $ ， $ \\left ( { 1+2+4 } \\right ) +1\\times 2\\times 4=15 $ ，所以提取1，2，4这三个数就是完美提取．若要在1，2，3，4，5，6，7，8，9，10这十个数中实现完美提取，则提取的数字可以是<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>（写一种情况即可），共有<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>种完美提取（注：提取的数字相同，排序不同，属于同一种提取）．</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆渝中 · 二模", "showQuestionTypeCode": "5", "showQuestionTypeName": "填空题", "downloadNum": 1, "referenceNum": 1, "createTime": "2025-05-12", "keyPointIds": "16402|16420|28551", "keyPointNames": "解一元一次方程|二元一次方程的解|代数推理", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576952923374006272", "questionFeatureName": "新定义问题", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "576952923374006272", "title": "2025年重庆市渝中区中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "576952786534834176", "questionArticle": "<p>8．某食品加工厂根据订单的需求会不定期采购<i>A</i>，<i>B</i>两种食材（单位：件），而两种食材的单价会根据市场变化波动．</p><p>（1）第一周，该食品加工厂花费7650元一次性采购<i>A</i>，<i>B</i>两种食材共100件，此时<i>A</i>，<i>B</i>两种食材的单价分别是60元、90元，求食品加工厂采购了<i>A</i>，<i>B</i>两种食材各多少件？</p><p>（2）第二周，由于采购价格发生了变化，食品加工厂分别花费2000元、4200元一次性购买<i>A</i>，<i>B</i>两种食材，已知采购<i>B</i>种食材的数量是 $ {\\rm \\mathit{A}} { \\rm{ A } } $ 种食材数量的 $ 1.5 $ 倍，每件 $ {\\rm \\mathit{A}} { \\rm{ A } } $ 种食材的单价比每件<i>B</i>种食材的单价少20元，求食品加工厂第二周采购<i>A</i>种食材多少件？</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025重庆渝北 · 一模", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-12", "keyPointIds": "16438|16476", "keyPointNames": "和差倍分问题|分式方程的实际应用", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576952752334479360", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "576952752334479360", "title": "2025年重庆市渝北区中考一模考试数学试题", "paperCategory": 1}], "questionTypeCode": "6"}, {"questionId": "576952943565385728", "questionArticle": "<p>9．已知 $ m $ ， $ n $ 是常数，化简 $ \\left ( { x+m } \\right ) \\left ( { 3x-n } \\right )  $ 和 $ \\left ( { 3x+m } \\right ) \\left ( { x-n } \\right )  $ 的结果中 $ x $ 的一次项系数分别为 $ a $ 和 $ b $ ，且 $ a+b=8 $ ．下列说法：</p><p>①若 $ a=b $ ，则 $ m $ 与 $ n $ 互为相反数；</p><p>②若 $ a\\geqslant  2b $ ，则 $ mn $ 的最小值为 $ -\\dfrac { 5 } { 9 } $ ；</p><p>③若 $ a $ ， $ b $ 均为正整数，则 $ \\dfrac { m } { n } $ 有6个不相等的值．</p><p>其中正确的个数是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．0　　　　B．1　　　　C．2　　　　D．3</p>", "gradeCode": "9", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "500000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025重庆渝中 · 二模", "showQuestionTypeCode": "1", "showQuestionTypeName": "单选题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-05-12", "keyPointIds": "16327|16420|16556", "keyPointNames": "多项式乘多项式|二元一次方程的解|二次函数的最值", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "576952923374006272", "proofreadStatus": 5, "downloadCount": 0, "questionSourceList": [{"id": "576952923374006272", "title": "2025年重庆市渝中区中考二模数学试题", "paperCategory": 1}], "questionTypeCode": "1"}, {"questionId": "575479819560853504", "questionArticle": "<p>10．水是生命之源，“节约用水，人人有责”，为了加强公民的节水意识，合理利用水资源，我市居民生活用水按阶梯式水价计费，下表是找市居民“一户一表”生活用水及阶梯计费价格表的部分信息</p><table style=\"border: solid 1px;border-collapse: collapse;\"><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>阶梯</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 127.5pt;\"><p>年用水量（吨/户）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p>水价（元/吨）</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 119.95pt;\"><p>污水处理价格（元/吨）</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>第一阶梯</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 127.5pt;\"><p>不超过250吨</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p>2.20</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 119.95pt;\"><p>1.00</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>第二阶梯</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 127.5pt;\"><p>超过250吨不超过350吨</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p>3.30</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 119.95pt;\"><p>1.00</p></td></tr><tr><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 54pt;\"><p>第三阶梯</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 127.5pt;\"><p>超过350吨</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 77.95pt;\"><p>6.60</p></td><td class=\"timu-table-td\" style=\"border: solid 1px;border-collapse: collapse; width: 119.95pt;\"><p>1.00</p></td></tr></table><p>①每户产生的污水量等于该户自来水用水量：水费=自来水费用+污水处理费用； </p><p>②每月用水量会计入全年总量，决定当月每吨水的价格．</p><p>（例如：前几个月累计用水260吨，则当月水价均按3.3元/吨计算；若前几个月累计用水240吨，当月用水量20吨，则当月的水价中10吨按照2.2元/吨，另外10吨按照3.3元/吨计算）</p><p>（1）若小明家2024年前三个季度累计用水量达230吨，10月预计用水35吨，则小明家10月份预计应缴纳水费多少元？</p><p>（2）若小明家2024年全年一共用水300吨，其中下半年比上半年多缴费119元，设上半年用水量<i>x</i>吨，下半年用水量<i>y</i>吨，列方程组解应用题，求上半年用水量．</p><p>（3）若小红家2024年全年用水400吨，其中下半年比上半年少缴费76元，求上半年用水量．</p>", "gradeCode": "7", "subjectCode": "2", "studyPhaseCode": "200", "provinceCode": "350000", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2025福建厦门六中 · 期中", "showQuestionTypeCode": "298", "showQuestionTypeName": "应用题", "downloadNum": 2, "referenceNum": 1, "createTime": "2025-05-12", "keyPointIds": "16440", "keyPointNames": "表格或图示问题", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "575479786685898752", "proofreadStatus": 3, "downloadCount": 0, "questionSourceList": [{"id": "575479786685898752", "title": "福建省厦门第六中学2024—2025学年七年级下学期期中检测数学试题", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 87, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "2", "textbookVersionCode": "179", "ceciCode": "72", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "diffcultCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["146497"], "categoryId": ""}}, "crawlInfo": {"pageNum": 87, "timestamp": "2025-07-01T02:11:06.837Z", "combination": {"studyPhaseName": "初中", "subjectName": "数学", "textbookVersionName": "人教版(2024)", "ceciName": "七年级下", "catalogName": "第十章 二元一次方程组"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E6%95%B0%E5%AD%A6/%E4%BA%BA%E6%95%99%E7%89%88%282024%29/%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84", "originalPath": "初中/数学/人教版(2024)/七年级下/第十章 二元一次方程组", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "数学", "encoded": "%E6%95%B0%E5%AD%A6"}, {"original": "人教版(2024)", "encoded": "%E4%BA%BA%E6%95%99%E7%89%88%282024%29"}, {"original": "七年级下", "encoded": "%E4%B8%83%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第十章 二元一次方程组", "encoded": "%E7%AC%AC%E5%8D%81%E7%AB%A0%20%E4%BA%8C%E5%85%83%E4%B8%80%E6%AC%A1%E6%96%B9%E7%A8%8B%E7%BB%84"}]}}}