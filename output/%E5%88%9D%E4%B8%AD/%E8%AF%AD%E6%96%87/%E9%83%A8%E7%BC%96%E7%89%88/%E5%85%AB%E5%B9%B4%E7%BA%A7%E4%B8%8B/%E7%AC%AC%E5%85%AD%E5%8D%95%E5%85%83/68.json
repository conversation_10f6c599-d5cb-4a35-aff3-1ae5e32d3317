{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 67, "pageSize": 10, "totalPage": 450, "total": "4492", "list": [{"questionId": "317294907739119616", "questionArticle": "<p>1．人间之可爱，就在于它的有情有爱有<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>。</p>\r\n<p>A.羁绊　B．牵绊</p>", "gradeCode": "8", "subjectCode": "1", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "八年级 · 专题模块", "showQuestionTypeCode": "269", "showQuestionTypeName": "基础单题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "24163", "keyPointNames": "近义词", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "期末总复习专项 专项训练二 词语理解【初中必刷题】八年级下册部编版语文", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "317294907667816448", "questionArticle": "<p>2．听了他说的话，大家这才<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，原来老人不是被扶他起来的小伙撞倒的。</p>\r\n<p>A.大彻大悟　B．恍然大悟</p>", "gradeCode": "8", "subjectCode": "1", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "八年级 · 专题模块", "showQuestionTypeCode": "269", "showQuestionTypeName": "基础单题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "24163", "keyPointNames": "近义词", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "期末总复习专项 专项训练二 词语理解【初中必刷题】八年级下册部编版语文", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "317294907269357568", "questionArticle": "<p>3．中国像光芒四射的朝阳，<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>在世界东方。</p>\r\n<p>A.矗立　B．屹立</p>", "gradeCode": "8", "subjectCode": "1", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "八年级 · 专题模块", "showQuestionTypeCode": "269", "showQuestionTypeName": "基础单题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "24163", "keyPointNames": "近义词", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "期末总复习专项 专项训练二 词语理解【初中必刷题】八年级下册部编版语文", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "317294907130945536", "questionArticle": "<p>4．你要一鼓作气冲过难关，一<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>可能就气馁了。</p>\r\n<p>A.迟疑　B．踌躇</p>", "gradeCode": "8", "subjectCode": "1", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "八年级 · 专题模块", "showQuestionTypeCode": "269", "showQuestionTypeName": "基础单题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "24163", "keyPointNames": "近义词", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "期末总复习专项 专项训练二 词语理解【初中必刷题】八年级下册部编版语文", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "317294908875776000", "questionArticle": "<p>5．童年像一个<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>的梦，使人留恋，使人向往。</p>\r\n<p>A.五彩斑斓　B．五颜六色</p>", "gradeCode": "8", "subjectCode": "1", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "八年级 · 专题模块", "showQuestionTypeCode": "269", "showQuestionTypeName": "基础单题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "24163", "keyPointNames": "近义词", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "期末总复习专项 专项训练二 词语理解【初中必刷题】八年级下册部编版语文", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "317294908741558274", "questionArticle": "<p>6．他想也想不出的幻境，那么惊心动魄，美到令人<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>。</p>\r\n<p>A.眼花缭乱　B．目眩神迷</p>", "gradeCode": "8", "subjectCode": "1", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "八年级 · 专题模块", "showQuestionTypeCode": "269", "showQuestionTypeName": "基础单题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "24163", "keyPointNames": "近义词", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "期末总复习专项 专项训练二 词语理解【初中必刷题】八年级下册部编版语文", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "317294908678643712", "questionArticle": "<p>7．庙会张灯结彩，游人<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>。</p>\r\n<p>A.纷至沓来　B．连绵不断</p>", "gradeCode": "8", "subjectCode": "1", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "八年级 · 专题模块", "showQuestionTypeCode": "269", "showQuestionTypeName": "基础单题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "24163", "keyPointNames": "近义词", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "期末总复习专项 专项训练二 词语理解【初中必刷题】八年级下册部编版语文", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "317294908540231682", "questionArticle": "<p>8．春节前夕，县城里<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，一片繁忙的景象。</p>\r\n<p>A.络绎不绝　B．车水马龙</p>", "gradeCode": "8", "subjectCode": "1", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "八年级 · 专题模块", "showQuestionTypeCode": "269", "showQuestionTypeName": "基础单题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "24163", "keyPointNames": "近义词", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "期末总复习专项 专项训练二 词语理解【初中必刷题】八年级下册部编版语文", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "317294908477317120", "questionArticle": "<p>9．尽管明文规定都贴在了地铁里，有些人却<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>，照样我行我素。</p>\r\n<p>A.袖手旁观　B．视而不见</p>", "gradeCode": "8", "subjectCode": "1", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "1", "diffcultName": "易", "questionSource": "八年级 · 专题模块", "showQuestionTypeCode": "269", "showQuestionTypeName": "基础单题", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-16", "keyPointIds": "24163", "keyPointNames": "近义词", "isInBasket": false, "collectFlag": false, "jumpType": 0, "proofreadStatus": 8, "downloadCount": 0, "questionSourceList": [{"id": null, "title": "期末总复习专项 专项训练二 词语理解【初中必刷题】八年级下册部编版语文", "paperCategory": 2}], "questionTypeCode": "6"}, {"questionId": "534853167231574016", "questionArticle": "<p>10．经典寻途，启思明智</p><p>书香能致远，读书重在践悟明志。物人合一，尽显风骨。请从下面备选人物和意象的组合中任选一组，说说他们的相通之处。</p><p>备选人物：江姐——梅&nbsp;&nbsp;&nbsp;&nbsp;唐僧——莲&nbsp;&nbsp;&nbsp;保尔——松</p>", "gradeCode": "9", "subjectCode": "1", "studyPhaseCode": "200", "provinceCode": "210000", "diffcultCode": "2", "diffcultName": "较易", "questionSource": "2025辽宁鞍山 · 期末", "showQuestionTypeCode": "103", "showQuestionTypeName": "名著阅读", "downloadNum": 1, "referenceNum": 2, "createTime": "2025-01-16", "keyPointIds": "24387|24818|25002|25211", "keyPointNames": "分析文学形象|《西游记》|《红岩》|《钢铁是怎样炼成的》", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "534853163347648512", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "534853163347648512", "title": "辽宁省鞍山市海城市西部集团2024-2025学年九年级上学期期末语文试题", "paperCategory": 1}, {"id": "524882705697251328", "title": "辽宁省鞍山市海城市西部集团2024-2025学年九年级上学期12月月考语文试题", "paperCategory": 11}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 68, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "1", "textbookVersionCode": "2", "ceciCode": "82", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "propositionalFormCode": "", "textTypeCode": "", "diffcultCode": "", "questionCategoryCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["103783"], "categoryId": ""}}, "crawlInfo": {"pageNum": 68, "timestamp": "2025-06-30T11:51:47.726Z", "combination": {"studyPhaseName": "初中", "subjectName": "语文", "textbookVersionName": "部编版", "ceciName": "八年级下", "catalogName": "第六单元"}, "recordCount": 10, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88/%E5%85%AB%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E5%85%AD%E5%8D%95%E5%85%83", "originalPath": "初中/语文/部编版/八年级下/第六单元", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "语文", "encoded": "%E8%AF%AD%E6%96%87"}, {"original": "部编版", "encoded": "%E9%83%A8%E7%BC%96%E7%89%88"}, {"original": "八年级下", "encoded": "%E5%85%AB%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第六单元", "encoded": "%E7%AC%AC%E5%85%AD%E5%8D%95%E5%85%83"}]}}}