{"fullResponse": {"code": "200", "message": "操作成功", "data": {"pageNum": 0, "pageSize": 10, "totalPage": 1, "total": "1", "list": [{"questionId": "531100060098011136", "questionArticle": "<p>1．青春是诗，岁月如歌，三年的时光转瞬即逝。九年级3班同学准备制作以“岁月如歌——我的初中生活”为主题的班级纪念册，现分小组进行准备。</p><p>(1)【名言引领】求真组准备写一句诗词在纪念册的扉页上，祝愿同学们友情常在，学有所成，以下名言<point-tag>不适宜</point-tag>选入的一句是（&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;）</p><p>A．长风破浪会有时，直挂云帆济沧海。</p><p>B．春蚕到死丝方尽，蜡炬成灰泪始干。</p><p>C．大鹏一日同风起，扶摇直上九万里。</p><p>D．海内存知己，天涯若比邻。</p><p>(2)【岁月有痕】乐学组负责纪念册的班史编制，计划分三个板块。请你帮助他们补充板块三的名称及主要内容。</p><p>板块一：教师风采——介绍班级科任老师。</p><p>板块二：班级剪影——展示班级活动图片。</p><p>板块三：<u>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</u>。</p><p>(3)【致敬师长】善思组负责在纪念册的封底写一段致敬老师的话，表达对老师的敬意与感谢。要求：真情实感，至少有一处修辞。</p>", "gradeCode": "9", "subjectCode": "1", "studyPhaseCode": "200", "provinceCode": "-1", "diffcultCode": "3", "diffcultName": "中", "questionSource": "2024全国 · 期末", "showQuestionTypeCode": "171", "showQuestionTypeName": "综合性学习", "downloadNum": 0, "referenceNum": 1, "createTime": "2025-01-09", "keyPointIds": "24324|24344|24412", "keyPointNames": "开放探究|活动策划|诗歌内容理解", "isInBasket": false, "collectFlag": false, "jumpType": 1, "paperId": "531100057199747072", "proofreadStatus": 1, "downloadCount": 0, "questionSourceList": [{"id": "531100057199747072", "title": "2024-2025学年九年级上学期语文期末模拟卷（五四制通用）", "paperCategory": 1}], "questionTypeCode": "6"}]}}, "requestData": {"pageNum": 1, "pageSize": 10, "params": {"studyPhaseCode": "200", "subjectCode": "1", "textbookVersionCode": "177", "ceciCode": "92", "searchType": 1, "sort": 0, "yearCode": "", "gradeCode": "", "provinceCode": "", "cityCode": "", "areaCode": "", "organizationCode": "", "termCode": "", "paperType": "", "showQuestionTypeCode": "", "questionParentCode": "", "propositionalFormCode": "", "textTypeCode": "", "diffcultCode": "", "questionCategoryCode": "", "keyWord": "", "filterQuestionFlag": false, "searchScope": 0, "treeIds": ["133862"], "categoryId": ""}}, "crawlInfo": {"pageNum": 1, "timestamp": "2025-06-30T12:33:03.617Z", "combination": {"studyPhaseName": "初中", "subjectName": "语文", "textbookVersionName": "部编版（五四制）", "ceciName": "九年级下", "catalogName": "第二单元"}, "recordCount": 1, "pathInfo": {"encodedPath": "output/%E5%88%9D%E4%B8%AD/%E8%AF%AD%E6%96%87/%E9%83%A8%E7%BC%96%E7%89%88%EF%BC%88%E4%BA%94%E5%9B%9B%E5%88%B6%EF%BC%89/%E4%B9%9D%E5%B9%B4%E7%BA%A7%E4%B8%8B/%E7%AC%AC%E4%BA%8C%E5%8D%95%E5%85%83", "originalPath": "初中/语文/部编版（五四制）/九年级下/第二单元", "pathComponents": [{"original": "初中", "encoded": "%E5%88%9D%E4%B8%AD"}, {"original": "语文", "encoded": "%E8%AF%AD%E6%96%87"}, {"original": "部编版（五四制）", "encoded": "%E9%83%A8%E7%BC%96%E7%89%88%EF%BC%88%E4%BA%94%E5%9B%9B%E5%88%B6%EF%BC%89"}, {"original": "九年级下", "encoded": "%E4%B9%9D%E5%B9%B4%E7%BA%A7%E4%B8%8B"}, {"original": "第二单元", "encoded": "%E7%AC%AC%E4%BA%8C%E5%8D%95%E5%85%83"}]}}}